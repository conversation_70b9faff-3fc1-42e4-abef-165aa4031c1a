defmodule Cypridina.Seeds.EnhancedFreeBonusTasks do
  @moduledoc """
  增强版免费积分任务种子数据
  
  包含更丰富的任务类型和更好的用户体验：
  - 多样化的任务类型
  - 阶梯式奖励系统
  - 游戏专属任务
  - 每日任务循环
  - 成就系统集成
  """

  alias Teen.ActivitySystem.FreeBonusTask

  def run do
    # 清理现有的默认任务
    clear_default_tasks()
    
    # 创建增强版任务
    create_enhanced_tasks()
  end

  defp clear_default_tasks do
    # 删除原有的简单任务配置
    default_titles = ["分享任务", "游戏任务", "提现任务"]
    
    Enum.each(default_titles, fn title ->
      case FreeBonusTask.read() do
        {:ok, tasks} ->
          tasks
          |> Enum.filter(&(&1.title == title))
          |> Enum.each(&FreeBonusTask.destroy/1)
        _ -> :ok
      end
    end)
  end

  defp create_enhanced_tasks do
    # 1. 新手引导任务
    novice_tasks()
    
    # 2. 每日分享任务（阶梯式）
    daily_share_tasks()
    
    # 3. 游戏体验任务
    game_experience_tasks()
    
    # 4. 提现激励任务
    withdrawal_incentive_tasks()
    
    # 5. 游戏专属任务
    game_specific_tasks()
    
    # 6. 成就任务
    achievement_tasks()
  end

  defp novice_tasks do
    [
      %{
        title: "新手首次分享",
        share_count: 1,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 0,
        reward_amount: Decimal.new("100"),
        status: :enabled
      },
      %{
        title: "新手体验游戏",
        share_count: 0,
        game_id: "all",
        required_win_coins: Decimal.new("100"),
        withdraw_count: 0,
        reward_amount: Decimal.new("50"),
        status: :enabled
      }
    ]
    |> Enum.each(&create_task/1)
  end

  defp daily_share_tasks do
    [
      %{
        title: "每日分享 - 入门",
        share_count: 1,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 0,
        reward_amount: Decimal.new("30"),
        status: :enabled
      },
      %{
        title: "每日分享 - 进阶",
        share_count: 3,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 0,
        reward_amount: Decimal.new("100"),
        status: :enabled
      },
      %{
        title: "每日分享 - 专家",
        share_count: 5,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 0,
        reward_amount: Decimal.new("200"),
        status: :enabled
      }
    ]
    |> Enum.each(&create_task/1)
  end

  defp game_experience_tasks do
    [
      %{
        title: "游戏新手 - 赢取100金币",
        share_count: 0,
        game_id: "all",
        required_win_coins: Decimal.new("100"),
        withdraw_count: 0,
        reward_amount: Decimal.new("50"),
        status: :enabled
      },
      %{
        title: "游戏进阶 - 赢取1000金币",
        share_count: 0,
        game_id: "all",
        required_win_coins: Decimal.new("1000"),
        withdraw_count: 0,
        reward_amount: Decimal.new("150"),
        status: :enabled
      },
      %{
        title: "游戏高手 - 赢取5000金币",
        share_count: 0,
        game_id: "all",
        required_win_coins: Decimal.new("5000"),
        withdraw_count: 0,
        reward_amount: Decimal.new("500"),
        status: :enabled
      }
    ]
    |> Enum.each(&create_task/1)
  end

  defp withdrawal_incentive_tasks do
    [
      %{
        title: "首次提现激励",
        share_count: 0,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 1,
        reward_amount: Decimal.new("300"),
        status: :enabled
      },
      %{
        title: "提现达人",
        share_count: 0,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 3,
        reward_amount: Decimal.new("800"),
        status: :enabled
      }
    ]
    |> Enum.each(&create_task/1)
  end

  defp game_specific_tasks do
    game_configs = [
      {"teen_patti", "青少年帕蒂", Decimal.new("500"), Decimal.new("200")},
      {"dragon_tiger", "龙虎斗", Decimal.new("300"), Decimal.new("150")},
      {"baccarat", "百家乐", Decimal.new("800"), Decimal.new("300")},
      {"crash", "崩溃游戏", Decimal.new("400"), Decimal.new("180")},
      {"slot777", "777老虎机", Decimal.new("600"), Decimal.new("250")}
    ]

    Enum.each(game_configs, fn {game_id, game_name, win_requirement, reward} ->
      %{
        title: "#{game_name}专家",
        share_count: 0,
        game_id: game_id,
        game_name: game_name,
        required_win_coins: win_requirement,
        withdraw_count: 0,
        reward_amount: reward,
        status: :enabled
      }
      |> create_task()
    end)
  end

  defp achievement_tasks do
    [
      %{
        title: "社交达人 - 分享10次",
        share_count: 10,
        game_id: nil,
        required_win_coins: Decimal.new("0"),
        withdraw_count: 0,
        reward_amount: Decimal.new("500"),
        status: :enabled
      },
      %{
        title: "游戏大师 - 赢取10000金币",
        share_count: 0,
        game_id: "all",
        required_win_coins: Decimal.new("10000"),
        withdraw_count: 0,
        reward_amount: Decimal.new("1000"),
        status: :enabled
      },
      %{
        title: "完美玩家 - 分享+游戏+提现",
        share_count: 5,
        game_id: "all",
        required_win_coins: Decimal.new("2000"),
        withdraw_count: 1,
        reward_amount: Decimal.new("1500"),
        status: :enabled
      }
    ]
    |> Enum.each(&create_task/1)
  end

  defp create_task(attrs) do
    case FreeBonusTask.create(attrs) do
      {:ok, task} ->
        IO.puts("✅ 创建任务: #{task.title}")
      {:error, error} ->
        IO.puts("❌ 创建任务失败: #{attrs.title} - #{inspect(error)}")
    end
  end
end

# 运行种子数据
Cypridina.Seeds.EnhancedFreeBonusTasks.run()