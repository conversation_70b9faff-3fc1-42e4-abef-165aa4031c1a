# Direct SQL insertion for VIP levels
IO.puts("Creating VIP levels with SQL...")

# Define VIP levels with the correct structure
vip_levels = [
  {0, "普通用户", 0, 0, 0, 0, 1, "普通用户等级", []},
  {1, "青铜VIP", 100, 10, 0.5, 0.5, 1, "青铜VIP等级", ["每日奖励", "充值奖励"]},
  {2, "白银VIP", 500, 20, 1.0, 1.0, 1, "白银VIP等级", ["每日奖励", "充值奖励", "兑换加成"]},
  {3, "黄金VIP", 1000, 50, 1.5, 1.5, 1, "黄金VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服"]},
  {4, "白金VIP", 2000, 100, 2.0, 2.0, 1, "白金VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限"]},
  {5, "钻石VIP", 5000, 200, 2.5, 2.5, 1, "钻石VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动"]},
  {6, "皇冠VIP", 10000, 500, 3.0, 3.0, 1, "皇冠VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品"]},
  {7, "至尊VIP", 20000, 1000, 3.5, 3.5, 1, "至尊VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物"]},
  {8, "传说VIP", 50000, 2000, 4.0, 4.0, 1, "传说VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物", "专属聊天室"]},
  {9, "神话VIP", 100000, 5000, 4.5, 4.5, 1, "神话VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物", "专属聊天室", "专属游戏"]},
  {10, "超凡VIP", 200000, 10000, 5.0, 5.0, 1, "超凡VIP等级", ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物", "专属聊天室", "专属游戏", "终身特权"]}
]

# Clear existing data first
IO.puts("Clearing existing VIP levels...")
{:ok, _} = Ecto.Adapters.SQL.query(
  Cypridina.Repo,
  "DELETE FROM vip_levels",
  []
)

# Insert new data
Enum.each(vip_levels, fn {level, level_name, recharge_requirement, daily_bonus, exchange_rate_bonus, recharge_bonus, status, description, privileges} ->
  {:ok, _} = Ecto.Adapters.SQL.query(
    Cypridina.Repo,
    """
    INSERT INTO vip_levels (
      id, level, level_name, recharge_requirement, daily_bonus, 
      exchange_rate_bonus, recharge_bonus, status, description, privileges,
      inserted_at, updated_at
    ) VALUES (
      gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()
    )
    """,
    [level, level_name, recharge_requirement, daily_bonus, exchange_rate_bonus, recharge_bonus, status, description, privileges]
  )
  IO.puts("✓ Created VIP level #{level}: #{level_name}")
end)

IO.puts("VIP levels created successfully!")