# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     Cypridina.Repo.insert!(%Cypridina.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

IO.puts("🌱 开始执行数据库种子文件...")

# 商店系统初始化
IO.puts("🛍️ 初始化商店系统...")
Code.eval_file("priv/repo/seeds/shop_system_seeds.exs")

# 支付系统初始化
IO.puts("💳 初始化支付系统...")
Code.eval_file("priv/repo/seeds/master_pay_seeds.exs")

# 奖池配置初始化
IO.puts("🎰 初始化奖池配置...")
Code.eval_file("priv/repo/seeds/jackpot_configs.exs")

# 刮卡活动初始化
IO.puts("🎲 初始化30次刮卡活动...")
Code.eval_file("priv/repo/seeds/scratch_card_activity_seeds.exs")

IO.puts("✅ 所有种子文件执行完成！")
require Logger
alias <PERSON>pridina.Accounts

Logger.info("开始初始化数据库...")

# 初始化超级管理员
Logger.info("=== 初始化超级管理员 ===")

case Cypridina.Accounts.initialize_super_admin(%{
       username: "super_admin",
       password: "123456",
       password_confirmation: "123456"
     }) do
  {:ok, super_admin} ->
    Logger.info("✅ 超级管理员初始化成功: #{super_admin.username} (ID: #{super_admin.numeric_id})")

  {:error, error} ->
    Logger.error("❌ 超级管理员初始化失败: #{inspect(error)}")
end

# 初始化渠道数据
Logger.info("=== 初始化渠道数据 ===")

case Cypridina.Accounts.Channel
     |> Ash.Changeset.for_create(:create, %{
       channel_id: "501",
       channel_name: "默认渠道",
       package_name: "com.cypridina.default",
       description: "系统默认渠道",
       status: 1
     })
     |> Ash.create() do
  {:ok, channel} ->
    Logger.info("✅ 渠道初始化成功: #{channel.channel_name} (ID: #{channel.channel_id})")

  {:error, error} ->
    Logger.error("❌ 渠道初始化失败: #{inspect(error)}")
end

# 运行游戏种子数据
Logger.info("=== 初始化游戏数据 ===")

# 运行完整的游戏系统默认配置
Logger.info("运行完整游戏系统配置...")

case Teen.GameManagement.seed_all_defaults() do
  {:ok, :completed} ->
    Logger.info("✅ 完整游戏系统配置初始化成功")

  {:error, error} ->
    Logger.error("❌ 完整游戏系统配置初始化失败: #{inspect(error)}")
end

# 运行 Slot777 详细游戏种子数据
seed_files = [
  "priv/repo/seeds/slot777_seeds.exs"
]

Enum.each(seed_files, fn seed_file ->
  if File.exists?(seed_file) do
    Logger.info("运行种子文件: #{seed_file}")
    Code.eval_file(seed_file)
  else
    Logger.warn("种子文件不存在: #{seed_file}")
  end
end)

Logger.info("✅ 数据库初始化完成！")

# 显示创建的账号信息
Logger.info("""

=== 创建的账号信息 ===
超级管理员: super_admin / 123456
默认渠道: 501 (默认渠道)

请妥善保管这些账号信息！
其他管理员和用户可以通过管理界面或Mix任务创建。
""")
