(()=>{var pi=Object.create;var At=Object.defineProperty;var gi=Object.getOwnPropertyDescriptor;var mi=Object.getOwnPropertyNames;var vi=Object.getPrototypeOf,bi=Object.prototype.hasOwnProperty;var wi=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var yi=(t,e,i,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of mi(e))!bi.call(t,n)&&n!==i&&At(t,n,{get:()=>e[n],enumerable:!(s=gi(e,n))||s.enumerable});return t};var ki=(t,e,i)=>(i=t!=null?pi(vi(t)):{},yi(e||!t||!t.__esModule?At(i,"default",{value:t,enumerable:!0}):i,t));var hi=wi((li,ze)=>{(function(t,e){"use strict";var i,s,n,r=null,o=null,a=null,l=function(g,c,v){g.addEventListener?g.addEventListener(c,v,!1):g.attachEvent?g.attachEvent("on"+c,v):g["on"+c]=v},h={autoRun:!0,barThickness:3,barColors:{0:"rgba(26,  188, 156, .9)",".25":"rgba(52,  152, 219, .9)",".50":"rgba(241, 196, 15,  .9)",".75":"rgba(230, 126, 34,  .9)","1.0":"rgba(211, 84,  0,   .9)"},shadowBlur:10,shadowColor:"rgba(0,   0,   0,   .6)",className:null},u=function(){i.width=t.innerWidth,i.height=h.barThickness*5;var g=i.getContext("2d");g.shadowBlur=h.shadowBlur,g.shadowColor=h.shadowColor;var c=g.createLinearGradient(0,0,i.width,0);for(var v in h.barColors)c.addColorStop(v,h.barColors[v]);g.lineWidth=h.barThickness,g.beginPath(),g.moveTo(0,h.barThickness/2),g.lineTo(Math.ceil(s*i.width),h.barThickness/2),g.strokeStyle=c,g.stroke()},p=function(){i=e.createElement("canvas");var g=i.style;g.position="fixed",g.top=g.left=g.right=g.margin=g.padding=0,g.zIndex=100001,g.display="none",h.className&&i.classList.add(h.className),l(t,"resize",u)},m={config:function(g){for(var c in g)h.hasOwnProperty(c)&&(h[c]=g[c])},show:function(g){if(!n)if(g){if(a)return;a=setTimeout(()=>m.show(),g)}else n=!0,o!==null&&t.cancelAnimationFrame(o),i||p(),i.parentElement||e.body.appendChild(i),i.style.opacity=1,i.style.display="block",m.progress(0),h.autoRun&&function c(){r=t.requestAnimationFrame(c),m.progress("+"+.05*Math.pow(1-Math.sqrt(s),2))}()},progress:function(g){return typeof g>"u"||(typeof g=="string"&&(g=(g.indexOf("+")>=0||g.indexOf("-")>=0?s:0)+parseFloat(g)),s=g>1?1:g,u()),s},hide:function(){clearTimeout(a),a=null,n&&(n=!1,r!=null&&(t.cancelAnimationFrame(r),r=null),function g(){if(m.progress("+.1")>=1&&(i.style.opacity-=.05,i.style.opacity<=.05)){i.style.display="none",o=null;return}o=t.requestAnimationFrame(g)}())}};typeof ze=="object"&&typeof ze.exports=="object"?ze.exports=m:typeof define=="function"&&define.amd?define(function(){return m}):this.topbar=m}).call(li,window,document)});(function(){var t=e();function e(){if(typeof window.CustomEvent=="function")return window.CustomEvent;function n(r,o){o=o||{bubbles:!1,cancelable:!1,detail:void 0};var a=document.createEvent("CustomEvent");return a.initCustomEvent(r,o.bubbles,o.cancelable,o.detail),a}return n.prototype=window.Event.prototype,n}function i(n,r){var o=document.createElement("input");return o.type="hidden",o.name=n,o.value=r,o}function s(n,r){var o=n.getAttribute("data-to"),a=i("_method",n.getAttribute("data-method")),l=i("_csrf_token",n.getAttribute("data-csrf")),h=document.createElement("form"),u=document.createElement("input"),p=n.getAttribute("target");h.method=n.getAttribute("data-method")==="get"?"get":"post",h.action=o,h.style.display="none",p?h.target=p:r&&(h.target="_blank"),h.appendChild(l),h.appendChild(a),document.body.appendChild(h),u.type="submit",h.appendChild(u),u.click()}window.addEventListener("click",function(n){var r=n.target;if(!n.defaultPrevented)for(;r&&r.getAttribute;){var o=new t("phoenix.link.click",{bubbles:!0,cancelable:!0});if(!r.dispatchEvent(o))return n.preventDefault(),n.stopImmediatePropagation(),!1;if(r.getAttribute("data-method")&&r.getAttribute("data-to"))return s(r,n.metaKey||n.shiftKey),n.preventDefault(),!1;r=r.parentNode}},!1),window.addEventListener("phoenix.link.click",function(n){var r=n.target.getAttribute("data-confirm");r&&!window.confirm(r)&&n.preventDefault()},!1)})();var we=t=>typeof t=="function"?t:function(){return t},Ai=typeof self<"u"?self:null,be=typeof window<"u"?window:null,ue=Ai||be||globalThis,Si="2.0.0",W={connecting:0,open:1,closing:2,closed:3},Ci=1e4,Ei=1e3,U={closed:"closed",errored:"errored",joined:"joined",joining:"joining",leaving:"leaving"},G={close:"phx_close",error:"phx_error",join:"phx_join",reply:"phx_reply",leave:"phx_leave"},Ze={longpoll:"longpoll",websocket:"websocket"},Ti={complete:4},et="base64url.bearer.phx.",Oe=class{constructor(t,e,i,s){this.channel=t,this.event=e,this.payload=i||function(){return{}},this.receivedResp=null,this.timeout=s,this.timeoutTimer=null,this.recHooks=[],this.sent=!1}resend(t){this.timeout=t,this.reset(),this.send()}send(){this.hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload(),ref:this.ref,join_ref:this.channel.joinRef()}))}receive(t,e){return this.hasReceived(t)&&e(this.receivedResp.response),this.recHooks.push({status:t,callback:e}),this}reset(){this.cancelRefEvent(),this.ref=null,this.refEvent=null,this.receivedResp=null,this.sent=!1}matchReceive({status:t,response:e,_ref:i}){this.recHooks.filter(s=>s.status===t).forEach(s=>s.callback(e))}cancelRefEvent(){this.refEvent&&this.channel.off(this.refEvent)}cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=null}startTimeout(){this.timeoutTimer&&this.cancelTimeout(),this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,t=>{this.cancelRefEvent(),this.cancelTimeout(),this.receivedResp=t,this.matchReceive(t)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}trigger(t,e){this.channel.trigger(this.refEvent,{status:t,response:e})}},St=class{constructor(t,e){this.callback=t,this.timerCalc=e,this.timer=null,this.tries=0}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}},_i=class{constructor(t,e,i){this.state=U.closed,this.topic=t,this.params=we(e||{}),this.socket=i,this.bindings=[],this.bindingRef=0,this.timeout=this.socket.timeout,this.joinedOnce=!1,this.joinPush=new Oe(this,G.join,this.params,this.timeout),this.pushBuffer=[],this.stateChangeRefs=[],this.rejoinTimer=new St(()=>{this.socket.isConnected()&&this.rejoin()},this.socket.rejoinAfterMs),this.stateChangeRefs.push(this.socket.onError(()=>this.rejoinTimer.reset())),this.stateChangeRefs.push(this.socket.onOpen(()=>{this.rejoinTimer.reset(),this.isErrored()&&this.rejoin()})),this.joinPush.receive("ok",()=>{this.state=U.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this.joinPush.receive("error",()=>{this.state=U.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.onClose(()=>{this.rejoinTimer.reset(),this.socket.hasLogger()&&this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=U.closed,this.socket.remove(this)}),this.onError(s=>{this.socket.hasLogger()&&this.socket.log("channel",`error ${this.topic}`,s),this.isJoining()&&this.joinPush.reset(),this.state=U.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.joinPush.receive("timeout",()=>{this.socket.hasLogger()&&this.socket.log("channel",`timeout ${this.topic} (${this.joinRef()})`,this.joinPush.timeout),new Oe(this,G.leave,we({}),this.timeout).send(),this.state=U.errored,this.joinPush.reset(),this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.on(G.reply,(s,n)=>{this.trigger(this.replyEventName(n),s)})}join(t=this.timeout){if(this.joinedOnce)throw new Error("tried to join multiple times. 'join' can only be called a single time per channel instance");return this.timeout=t,this.joinedOnce=!0,this.rejoin(),this.joinPush}onClose(t){this.on(G.close,t)}onError(t){return this.on(G.error,e=>t(e))}on(t,e){let i=this.bindingRef++;return this.bindings.push({event:t,ref:i,callback:e}),i}off(t,e){this.bindings=this.bindings.filter(i=>!(i.event===t&&(typeof e>"u"||e===i.ref)))}canPush(){return this.socket.isConnected()&&this.isJoined()}push(t,e,i=this.timeout){if(e=e||{},!this.joinedOnce)throw new Error(`tried to push '${t}' to '${this.topic}' before joining. Use channel.join() before pushing events`);let s=new Oe(this,t,function(){return e},i);return this.canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}leave(t=this.timeout){this.rejoinTimer.reset(),this.joinPush.cancelTimeout(),this.state=U.leaving;let e=()=>{this.socket.hasLogger()&&this.socket.log("channel",`leave ${this.topic}`),this.trigger(G.close,"leave")},i=new Oe(this,G.leave,we({}),t);return i.receive("ok",()=>e()).receive("timeout",()=>e()),i.send(),this.canPush()||i.trigger("ok",{}),i}onMessage(t,e,i){return e}isMember(t,e,i,s){return this.topic!==t?!1:s&&s!==this.joinRef()?(this.socket.hasLogger()&&this.socket.log("channel","dropping outdated message",{topic:t,event:e,payload:i,joinRef:s}),!1):!0}joinRef(){return this.joinPush.ref}rejoin(t=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=U.joining,this.joinPush.resend(t))}trigger(t,e,i,s){let n=this.onMessage(t,e,i,s);if(e&&!n)throw new Error("channel onMessage callbacks must return the payload, modified or unmodified");let r=this.bindings.filter(o=>o.event===t);for(let o=0;o<r.length;o++)r[o].callback(n,i,s||this.joinRef())}replyEventName(t){return`chan_reply_${t}`}isClosed(){return this.state===U.closed}isErrored(){return this.state===U.errored}isJoined(){return this.state===U.joined}isJoining(){return this.state===U.joining}isLeaving(){return this.state===U.leaving}},He=class{static request(t,e,i,s,n,r,o){if(ue.XDomainRequest){let a=new ue.XDomainRequest;return this.xdomainRequest(a,t,e,s,n,r,o)}else{let a=new ue.XMLHttpRequest;return this.xhrRequest(a,t,e,i,s,n,r,o)}}static xdomainRequest(t,e,i,s,n,r,o){return t.timeout=n,t.open(e,i),t.onload=()=>{let a=this.parseJSON(t.responseText);o&&o(a)},r&&(t.ontimeout=r),t.onprogress=()=>{},t.send(s),t}static xhrRequest(t,e,i,s,n,r,o,a){t.open(e,i,!0),t.timeout=r;for(let[l,h]of Object.entries(s))t.setRequestHeader(l,h);return t.onerror=()=>a&&a(null),t.onreadystatechange=()=>{if(t.readyState===Ti.complete&&a){let l=this.parseJSON(t.responseText);a(l)}},o&&(t.ontimeout=o),t.send(n),t}static parseJSON(t){if(!t||t==="")return null;try{return JSON.parse(t)}catch{return console&&console.log("failed to parse JSON response",t),null}}static serialize(t,e){let i=[];for(var s in t){if(!Object.prototype.hasOwnProperty.call(t,s))continue;let n=e?`${e}[${s}]`:s,r=t[s];typeof r=="object"?i.push(this.serialize(r,n)):i.push(encodeURIComponent(n)+"="+encodeURIComponent(r))}return i.join("&")}static appendParams(t,e){if(Object.keys(e).length===0)return t;let i=t.match(/\?/)?"&":"?";return`${t}${i}${this.serialize(e)}`}},Ri=t=>{let e="",i=new Uint8Array(t),s=i.byteLength;for(let n=0;n<s;n++)e+=String.fromCharCode(i[n]);return btoa(e)},ve=class{constructor(t,e){e&&e.length===2&&e[1].startsWith(et)&&(this.authToken=atob(e[1].slice(et.length))),this.endPoint=null,this.token=null,this.skipHeartbeat=!0,this.reqs=new Set,this.awaitingBatchAck=!1,this.currentBatch=null,this.currentBatchTimer=null,this.batchBuffer=[],this.onopen=function(){},this.onerror=function(){},this.onmessage=function(){},this.onclose=function(){},this.pollEndpoint=this.normalizeEndpoint(t),this.readyState=W.connecting,setTimeout(()=>this.poll(),0)}normalizeEndpoint(t){return t.replace("ws://","http://").replace("wss://","https://").replace(new RegExp("(.*)/"+Ze.websocket),"$1/"+Ze.longpoll)}endpointURL(){return He.appendParams(this.pollEndpoint,{token:this.token})}closeAndRetry(t,e,i){this.close(t,e,i),this.readyState=W.connecting}ontimeout(){this.onerror("timeout"),this.closeAndRetry(1005,"timeout",!1)}isActive(){return this.readyState===W.open||this.readyState===W.connecting}poll(){let t={Accept:"application/json"};this.authToken&&(t["X-Phoenix-AuthToken"]=this.authToken),this.ajax("GET",t,null,()=>this.ontimeout(),e=>{if(e){var{status:i,token:s,messages:n}=e;this.token=s}else i=0;switch(i){case 200:n.forEach(r=>{setTimeout(()=>this.onmessage({data:r}),0)}),this.poll();break;case 204:this.poll();break;case 410:this.readyState=W.open,this.onopen({}),this.poll();break;case 403:this.onerror(403),this.close(1008,"forbidden",!1);break;case 0:case 500:this.onerror(500),this.closeAndRetry(1011,"internal server error",500);break;default:throw new Error(`unhandled poll status ${i}`)}})}send(t){typeof t!="string"&&(t=Ri(t)),this.currentBatch?this.currentBatch.push(t):this.awaitingBatchAck?this.batchBuffer.push(t):(this.currentBatch=[t],this.currentBatchTimer=setTimeout(()=>{this.batchSend(this.currentBatch),this.currentBatch=null},0))}batchSend(t){this.awaitingBatchAck=!0,this.ajax("POST",{"Content-Type":"application/x-ndjson"},t.join(`
`),()=>this.onerror("timeout"),e=>{this.awaitingBatchAck=!1,!e||e.status!==200?(this.onerror(e&&e.status),this.closeAndRetry(1011,"internal server error",!1)):this.batchBuffer.length>0&&(this.batchSend(this.batchBuffer),this.batchBuffer=[])})}close(t,e,i){for(let n of this.reqs)n.abort();this.readyState=W.closed;let s=Object.assign({code:1e3,reason:void 0,wasClean:!0},{code:t,reason:e,wasClean:i});this.batchBuffer=[],clearTimeout(this.currentBatchTimer),this.currentBatchTimer=null,typeof CloseEvent<"u"?this.onclose(new CloseEvent("close",s)):this.onclose(s)}ajax(t,e,i,s,n){let r,o=()=>{this.reqs.delete(r),s()};r=He.request(t,this.endpointURL(),e,i,this.timeout,o,a=>{this.reqs.delete(r),this.isActive()&&n(a)}),this.reqs.add(r)}};var De={HEADER_LENGTH:1,META_LENGTH:4,KINDS:{push:0,reply:1,broadcast:2},encode(t,e){if(t.payload.constructor===ArrayBuffer)return e(this.binaryEncode(t));{let i=[t.join_ref,t.ref,t.topic,t.event,t.payload];return e(JSON.stringify(i))}},decode(t,e){if(t.constructor===ArrayBuffer)return e(this.binaryDecode(t));{let[i,s,n,r,o]=JSON.parse(t);return e({join_ref:i,ref:s,topic:n,event:r,payload:o})}},binaryEncode(t){let{join_ref:e,ref:i,event:s,topic:n,payload:r}=t,o=this.META_LENGTH+e.length+i.length+n.length+s.length,a=new ArrayBuffer(this.HEADER_LENGTH+o),l=new DataView(a),h=0;l.setUint8(h++,this.KINDS.push),l.setUint8(h++,e.length),l.setUint8(h++,i.length),l.setUint8(h++,n.length),l.setUint8(h++,s.length),Array.from(e,p=>l.setUint8(h++,p.charCodeAt(0))),Array.from(i,p=>l.setUint8(h++,p.charCodeAt(0))),Array.from(n,p=>l.setUint8(h++,p.charCodeAt(0))),Array.from(s,p=>l.setUint8(h++,p.charCodeAt(0)));var u=new Uint8Array(a.byteLength+r.byteLength);return u.set(new Uint8Array(a),0),u.set(new Uint8Array(r),a.byteLength),u.buffer},binaryDecode(t){let e=new DataView(t),i=e.getUint8(0),s=new TextDecoder;switch(i){case this.KINDS.push:return this.decodePush(t,e,s);case this.KINDS.reply:return this.decodeReply(t,e,s);case this.KINDS.broadcast:return this.decodeBroadcast(t,e,s)}},decodePush(t,e,i){let s=e.getUint8(1),n=e.getUint8(2),r=e.getUint8(3),o=this.HEADER_LENGTH+this.META_LENGTH-1,a=i.decode(t.slice(o,o+s));o=o+s;let l=i.decode(t.slice(o,o+n));o=o+n;let h=i.decode(t.slice(o,o+r));o=o+r;let u=t.slice(o,t.byteLength);return{join_ref:a,ref:null,topic:l,event:h,payload:u}},decodeReply(t,e,i){let s=e.getUint8(1),n=e.getUint8(2),r=e.getUint8(3),o=e.getUint8(4),a=this.HEADER_LENGTH+this.META_LENGTH,l=i.decode(t.slice(a,a+s));a=a+s;let h=i.decode(t.slice(a,a+n));a=a+n;let u=i.decode(t.slice(a,a+r));a=a+r;let p=i.decode(t.slice(a,a+o));a=a+o;let m=t.slice(a,t.byteLength),g={status:p,response:m};return{join_ref:l,ref:h,topic:u,event:G.reply,payload:g}},decodeBroadcast(t,e,i){let s=e.getUint8(1),n=e.getUint8(2),r=this.HEADER_LENGTH+2,o=i.decode(t.slice(r,r+s));r=r+s;let a=i.decode(t.slice(r,r+n));r=r+n;let l=t.slice(r,t.byteLength);return{join_ref:null,ref:null,topic:o,event:a,payload:l}}},Ct=class{constructor(t,e={}){this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.channels=[],this.sendBuffer=[],this.ref=0,this.timeout=e.timeout||Ci,this.transport=e.transport||ue.WebSocket||ve,this.primaryPassedHealthCheck=!1,this.longPollFallbackMs=e.longPollFallbackMs,this.fallbackTimer=null,this.sessionStore=e.sessionStorage||ue&&ue.sessionStorage,this.establishedConnections=0,this.defaultEncoder=De.encode.bind(De),this.defaultDecoder=De.decode.bind(De),this.closeWasClean=!1,this.disconnecting=!1,this.binaryType=e.binaryType||"arraybuffer",this.connectClock=1,this.transport!==ve?(this.encode=e.encode||this.defaultEncoder,this.decode=e.decode||this.defaultDecoder):(this.encode=this.defaultEncoder,this.decode=this.defaultDecoder);let i=null;be&&be.addEventListener&&(be.addEventListener("pagehide",s=>{this.conn&&(this.disconnect(),i=this.connectClock)}),be.addEventListener("pageshow",s=>{i===this.connectClock&&(i=null,this.connect())})),this.heartbeatIntervalMs=e.heartbeatIntervalMs||3e4,this.rejoinAfterMs=s=>e.rejoinAfterMs?e.rejoinAfterMs(s):[1e3,2e3,5e3][s-1]||1e4,this.reconnectAfterMs=s=>e.reconnectAfterMs?e.reconnectAfterMs(s):[10,50,100,150,200,250,500,1e3,2e3][s-1]||5e3,this.logger=e.logger||null,!this.logger&&e.debug&&(this.logger=(s,n,r)=>{console.log(`${s}: ${n}`,r)}),this.longpollerTimeout=e.longpollerTimeout||2e4,this.params=we(e.params||{}),this.endPoint=`${t}/${Ze.websocket}`,this.vsn=e.vsn||Si,this.heartbeatTimeoutTimer=null,this.heartbeatTimer=null,this.pendingHeartbeatRef=null,this.reconnectTimer=new St(()=>{this.teardown(()=>this.connect())},this.reconnectAfterMs),this.authToken=e.authToken}getLongPollTransport(){return ve}replaceTransport(t){this.connectClock++,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.conn&&(this.conn.close(),this.conn=null),this.transport=t}protocol(){return location.protocol.match(/^https/)?"wss":"ws"}endPointURL(){let t=He.appendParams(He.appendParams(this.endPoint,this.params()),{vsn:this.vsn});return t.charAt(0)!=="/"?t:t.charAt(1)==="/"?`${this.protocol()}:${t}`:`${this.protocol()}://${location.host}${t}`}disconnect(t,e,i){this.connectClock++,this.disconnecting=!0,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.teardown(()=>{this.disconnecting=!1,t&&t()},e,i)}connect(t){t&&(console&&console.log("passing params to connect is deprecated. Instead pass :params to the Socket constructor"),this.params=we(t)),!(this.conn&&!this.disconnecting)&&(this.longPollFallbackMs&&this.transport!==ve?this.connectWithFallback(ve,this.longPollFallbackMs):this.transportConnect())}log(t,e,i){this.logger&&this.logger(t,e,i)}hasLogger(){return this.logger!==null}onOpen(t){let e=this.makeRef();return this.stateChangeCallbacks.open.push([e,t]),e}onClose(t){let e=this.makeRef();return this.stateChangeCallbacks.close.push([e,t]),e}onError(t){let e=this.makeRef();return this.stateChangeCallbacks.error.push([e,t]),e}onMessage(t){let e=this.makeRef();return this.stateChangeCallbacks.message.push([e,t]),e}ping(t){if(!this.isConnected())return!1;let e=this.makeRef(),i=Date.now();this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:e});let s=this.onMessage(n=>{n.ref===e&&(this.off([s]),t(Date.now()-i))});return!0}transportConnect(){this.connectClock++,this.closeWasClean=!1;let t;this.authToken&&(t=["phoenix",`${et}${btoa(this.authToken).replace(/=/g,"")}`]),this.conn=new this.transport(this.endPointURL(),t),this.conn.binaryType=this.binaryType,this.conn.timeout=this.longpollerTimeout,this.conn.onopen=()=>this.onConnOpen(),this.conn.onerror=e=>this.onConnError(e),this.conn.onmessage=e=>this.onConnMessage(e),this.conn.onclose=e=>this.onConnClose(e)}getSession(t){return this.sessionStore&&this.sessionStore.getItem(t)}storeSession(t,e){this.sessionStore&&this.sessionStore.setItem(t,e)}connectWithFallback(t,e=2500){clearTimeout(this.fallbackTimer);let i=!1,s=!0,n,r,o=a=>{this.log("transport",`falling back to ${t.name}...`,a),this.off([n,r]),s=!1,this.replaceTransport(t),this.transportConnect()};if(this.getSession(`phx:fallback:${t.name}`))return o("memorized");this.fallbackTimer=setTimeout(o,e),r=this.onError(a=>{this.log("transport","error",a),s&&!i&&(clearTimeout(this.fallbackTimer),o(a))}),this.onOpen(()=>{if(i=!0,!s)return this.primaryPassedHealthCheck||this.storeSession(`phx:fallback:${t.name}`,"true"),this.log("transport",`established ${t.name} fallback`);clearTimeout(this.fallbackTimer),this.fallbackTimer=setTimeout(o,e),this.ping(a=>{this.log("transport","connected to primary after",a),this.primaryPassedHealthCheck=!0,clearTimeout(this.fallbackTimer)})}),this.transportConnect()}clearHeartbeats(){clearTimeout(this.heartbeatTimer),clearTimeout(this.heartbeatTimeoutTimer)}onConnOpen(){this.hasLogger()&&this.log("transport",`${this.transport.name} connected to ${this.endPointURL()}`),this.closeWasClean=!1,this.disconnecting=!1,this.establishedConnections++,this.flushSendBuffer(),this.reconnectTimer.reset(),this.resetHeartbeat(),this.stateChangeCallbacks.open.forEach(([,t])=>t())}heartbeatTimeout(){this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null,this.hasLogger()&&this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.triggerChanError(),this.closeWasClean=!1,this.teardown(()=>this.reconnectTimer.scheduleTimeout(),Ei,"heartbeat timeout"))}resetHeartbeat(){this.conn&&this.conn.skipHeartbeat||(this.pendingHeartbeatRef=null,this.clearHeartbeats(),this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs))}teardown(t,e,i){if(!this.conn)return t&&t();let s=this.connectClock;this.waitForBufferDone(()=>{s===this.connectClock&&(this.conn&&(e?this.conn.close(e,i||""):this.conn.close()),this.waitForSocketClosed(()=>{s===this.connectClock&&(this.conn&&(this.conn.onopen=function(){},this.conn.onerror=function(){},this.conn.onmessage=function(){},this.conn.onclose=function(){},this.conn=null),t&&t())}))})}waitForBufferDone(t,e=1){if(e===5||!this.conn||!this.conn.bufferedAmount){t();return}setTimeout(()=>{this.waitForBufferDone(t,e+1)},150*e)}waitForSocketClosed(t,e=1){if(e===5||!this.conn||this.conn.readyState===W.closed){t();return}setTimeout(()=>{this.waitForSocketClosed(t,e+1)},150*e)}onConnClose(t){let e=t&&t.code;this.hasLogger()&&this.log("transport","close",t),this.triggerChanError(),this.clearHeartbeats(),!this.closeWasClean&&e!==1e3&&this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(([,i])=>i(t))}onConnError(t){this.hasLogger()&&this.log("transport",t);let e=this.transport,i=this.establishedConnections;this.stateChangeCallbacks.error.forEach(([,s])=>{s(t,e,i)}),(e===this.transport||i>0)&&this.triggerChanError()}triggerChanError(){this.channels.forEach(t=>{t.isErrored()||t.isLeaving()||t.isClosed()||t.trigger(G.error)})}connectionState(){switch(this.conn&&this.conn.readyState){case W.connecting:return"connecting";case W.open:return"open";case W.closing:return"closing";default:return"closed"}}isConnected(){return this.connectionState()==="open"}remove(t){this.off(t.stateChangeRefs),this.channels=this.channels.filter(e=>e!==t)}off(t){for(let e in this.stateChangeCallbacks)this.stateChangeCallbacks[e]=this.stateChangeCallbacks[e].filter(([i])=>t.indexOf(i)===-1)}channel(t,e={}){let i=new _i(t,e,this);return this.channels.push(i),i}push(t){if(this.hasLogger()){let{topic:e,event:i,payload:s,ref:n,join_ref:r}=t;this.log("push",`${e} ${i} (${r}, ${n})`,s)}this.isConnected()?this.encode(t,e=>this.conn.send(e)):this.sendBuffer.push(()=>this.encode(t,e=>this.conn.send(e)))}makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}sendHeartbeat(){this.pendingHeartbeatRef&&!this.isConnected()||(this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatTimeoutTimer=setTimeout(()=>this.heartbeatTimeout(),this.heartbeatIntervalMs))}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}onConnMessage(t){this.decode(t.data,e=>{let{topic:i,event:s,payload:n,ref:r,join_ref:o}=e;r&&r===this.pendingHeartbeatRef&&(this.clearHeartbeats(),this.pendingHeartbeatRef=null,this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)),this.hasLogger()&&this.log("receive",`${n.status||""} ${i} ${s} ${r&&"("+r+")"||""}`,n);for(let a=0;a<this.channels.length;a++){let l=this.channels[a];l.isMember(i,s,n,o)&&l.trigger(s,n,r,o)}for(let a=0;a<this.stateChangeCallbacks.message.length;a++){let[,l]=this.stateChangeCallbacks.message[a];l(e)}})}leaveOpenTopic(t){let e=this.channels.find(i=>i.topic===t&&(i.isJoined()||i.isJoining()));e&&(this.hasLogger()&&this.log("transport",`leaving duplicate topic "${t}"`),e.leave())}};var Qt="consecutive-reloads",Pi=10,xi=5e3,Li=1e4,Ii=3e4,Zt=["phx-click-loading","phx-change-loading","phx-submit-loading","phx-keydown-loading","phx-keyup-loading","phx-blur-loading","phx-focus-loading","phx-hook-loading"],Q="data-phx-component",tt="data-phx-link",Oi="track-static",Di="data-phx-link-state",me="data-phx-ref-loading",B="data-phx-ref-src",P="data-phx-ref-lock",ei="track-uploads",Z="data-phx-upload-ref",gt="data-phx-preflighted-refs",Hi="data-phx-done-refs",Et="drop-target",lt="data-phx-active-refs",Ve="phx:live-file:updated",ti="data-phx-skip",ii="data-phx-id",Tt="data-phx-prune",_t="phx-connected",re="phx-loading",fe="phx-error",Rt="phx-client-error",ye="phx-server-error",le="data-phx-parent-id",mt="data-phx-main",ne="data-phx-root-id",ht="viewport-top",dt="viewport-bottom",Ni="trigger-action",We="phx-has-focused",Fi=["text","textarea","number","email","password","search","tel","url","date","time","datetime-local","color","range"],si=["checkbox","radio"],xe="phx-has-submitted",z="data-phx-session",he=`[${z}]`,ct="data-phx-sticky",ae="data-phx-static",ut="data-phx-readonly",ge="data-phx-disabled",Pt="disable-with",Xe="data-phx-disable-with-restore",ke="hook",Mi="debounce",Ui="throttle",qe="update",it="stream",Ae="data-phx-stream",$i="key",X="phxPrivate",xt="auto-recover",Ne="phx:live-socket:debug",st="phx:live-socket:profiling",nt="phx:live-socket:latency-sim",Fe="phx:nav-history-position",ji="progress",Lt="mounted",It="__phoenix_reload_status__",Bi=1,Ot=3,Vi=200,Ji=500,Wi="phx-",Xi=3e4,Se="debounce-trigger",Ce="throttled",Dt="debounce-prev-key",qi={debounce:300,throttle:300},Ht=[me,B,P],Me="d",q="s",rt="r",H="c",Nt="e",Ft="r",Mt="t",zi="p",Ut="stream",Ki=class{constructor(t,e,i){let{chunk_size:s,chunk_timeout:n}=e;this.liveSocket=i,this.entry=t,this.offset=0,this.chunkSize=s,this.chunkTimeout=n,this.chunkTimer=null,this.errored=!1,this.uploadChannel=i.channel(`lvu:${t.ref}`,{token:t.metadata()})}error(t){this.errored||(this.uploadChannel.leave(),this.errored=!0,clearTimeout(this.chunkTimer),this.entry.error(t))}upload(){this.uploadChannel.onError(t=>this.error(t)),this.uploadChannel.join().receive("ok",t=>this.readNextChunk()).receive("error",t=>this.error(t))}isDone(){return this.offset>=this.entry.file.size}readNextChunk(){let t=new window.FileReader,e=this.entry.file.slice(this.offset,this.chunkSize+this.offset);t.onload=i=>{if(i.target.error===null)this.offset+=i.target.result.byteLength,this.pushChunk(i.target.result);else return _("Read error: "+i.target.error)},t.readAsArrayBuffer(e)}pushChunk(t){this.uploadChannel.isJoined()&&this.uploadChannel.push("chunk",t,this.chunkTimeout).receive("ok",()=>{this.entry.progress(this.offset/this.entry.file.size*100),this.isDone()||(this.chunkTimer=setTimeout(()=>this.readNextChunk(),this.liveSocket.getLatencySim()||0))}).receive("error",({reason:e})=>this.error(e))}},_=(t,e)=>console.error&&console.error(t,e),Y=t=>{let e=typeof t;return e==="number"||e==="string"&&/^(0|[1-9]\d*)$/.test(t)};function Gi(){let t=new Set,e=document.querySelectorAll("*[id]");for(let i=0,s=e.length;i<s;i++)t.has(e[i].id)?console.error(`Multiple IDs detected: ${e[i].id}. Ensure unique element ids.`):t.add(e[i].id)}function Yi(t){let e=new Set;Object.keys(t).forEach(i=>{let s=document.getElementById(i);s&&s.parentElement&&s.parentElement.getAttribute("phx-update")!=="stream"&&e.add(`The stream container with id "${s.parentElement.id}" is missing the phx-update="stream" attribute. Ensure it is set for streams to work properly.`)}),e.forEach(i=>console.error(i))}var Qi=(t,e,i,s)=>{t.liveSocket.isDebugEnabled()&&console.log(`${t.id} ${e}: ${i} - `,s)},Ee=t=>typeof t=="function"?t:function(){return t},Je=t=>JSON.parse(JSON.stringify(t)),Pe=(t,e,i)=>{do{if(t.matches(`[${e}]`)&&!t.disabled)return t;t=t.parentElement||t.parentNode}while(t!==null&&t.nodeType===1&&!(i&&i.isSameNode(t)||t.matches(he)));return null},pe=t=>t!==null&&typeof t=="object"&&!(t instanceof Array),Zi=(t,e)=>JSON.stringify(t)===JSON.stringify(e),$t=t=>{for(let e in t)return!1;return!0},se=(t,e)=>t&&e(t),es=function(t,e,i,s){t.forEach(n=>{new Ki(n,i.config,s).upload()})},ts={canPushState(){return typeof history.pushState<"u"},dropLocal(t,e,i){return t.removeItem(this.localKey(e,i))},updateLocal(t,e,i,s,n){let r=this.getLocal(t,e,i),o=this.localKey(e,i),a=r===null?s:n(r);return t.setItem(o,JSON.stringify(a)),a},getLocal(t,e,i){return JSON.parse(t.getItem(this.localKey(e,i)))},updateCurrentState(t){this.canPushState()&&history.replaceState(t(history.state||{}),"",window.location.href)},pushState(t,e,i){if(this.canPushState()){if(i!==window.location.href){if(e.type=="redirect"&&e.scroll){let s=history.state||{};s.scroll=e.scroll,history.replaceState(s,"",window.location.href)}delete e.scroll,history[t+"State"](e,"",i||null),window.requestAnimationFrame(()=>{let s=this.getHashTargetEl(window.location.hash);s?s.scrollIntoView():e.type==="redirect"&&window.scroll(0,0)})}}else this.redirect(i)},setCookie(t,e,i){let s=typeof i=="number"?` max-age=${i};`:"";document.cookie=`${t}=${e};${s} path=/`},getCookie(t){return document.cookie.replace(new RegExp(`(?:(?:^|.*;s*)${t}s*=s*([^;]*).*$)|^.*$`),"$1")},deleteCookie(t){document.cookie=`${t}=; max-age=-1; path=/`},redirect(t,e){e&&this.setCookie("__phoenix_flash__",e,60),window.location=t},localKey(t,e){return`${t}-${e}`},getHashTargetEl(t){let e=t.toString().substring(1);if(e!=="")return document.getElementById(e)||document.querySelector(`a[name="${e}"]`)}},F=ts,oe={byId(t){return document.getElementById(t)||_(`no id found for ${t}`)},removeClass(t,e){t.classList.remove(e),t.classList.length===0&&t.removeAttribute("class")},all(t,e,i){if(!t)return[];let s=Array.from(t.querySelectorAll(e));return i?s.forEach(i):s},childNodeLength(t){let e=document.createElement("template");return e.innerHTML=t,e.content.childElementCount},isUploadInput(t){return t.type==="file"&&t.getAttribute(Z)!==null},isAutoUpload(t){return t.hasAttribute("data-phx-auto-upload")},findUploadInputs(t){let e=t.id,i=this.all(document,`input[type="file"][${Z}][form="${e}"]`);return this.all(t,`input[type="file"][${Z}]`).concat(i)},findComponentNodeList(t,e){return this.filterWithinSameLiveView(this.all(t,`[${Q}="${e}"]`),t)},isPhxDestroyed(t){return!!(t.id&&oe.private(t,"destroyed"))},wantsNewTab(t){let e=t.ctrlKey||t.shiftKey||t.metaKey||t.button&&t.button===1,i=t.target instanceof HTMLAnchorElement&&t.target.hasAttribute("download"),s=t.target.hasAttribute("target")&&t.target.getAttribute("target").toLowerCase()==="_blank",n=t.target.hasAttribute("target")&&!t.target.getAttribute("target").startsWith("_");return e||s||i||n},isUnloadableFormSubmit(t){return t.target&&t.target.getAttribute("method")==="dialog"||t.submitter&&t.submitter.getAttribute("formmethod")==="dialog"?!1:!t.defaultPrevented&&!this.wantsNewTab(t)},isNewPageClick(t,e){let i=t.target instanceof HTMLAnchorElement?t.target.getAttribute("href"):null,s;if(t.defaultPrevented||i===null||this.wantsNewTab(t)||i.startsWith("mailto:")||i.startsWith("tel:")||t.target.isContentEditable)return!1;try{s=new URL(i)}catch{try{s=new URL(i,e)}catch{return!0}}return s.host===e.host&&s.protocol===e.protocol&&s.pathname===e.pathname&&s.search===e.search?s.hash===""&&!s.href.endsWith("#"):s.protocol.startsWith("http")},markPhxChildDestroyed(t){this.isPhxChild(t)&&t.setAttribute(z,""),this.putPrivate(t,"destroyed",!0)},findPhxChildrenInFragment(t,e){let i=document.createElement("template");return i.innerHTML=t,this.findPhxChildren(i.content,e)},isIgnored(t,e){return(t.getAttribute(e)||t.getAttribute("data-phx-update"))==="ignore"},isPhxUpdate(t,e,i){return t.getAttribute&&i.indexOf(t.getAttribute(e))>=0},findPhxSticky(t){return this.all(t,`[${ct}]`)},findPhxChildren(t,e){return this.all(t,`${he}[${le}="${e}"]`)},findExistingParentCIDs(t,e){let i=new Set,s=new Set;return e.forEach(n=>{this.filterWithinSameLiveView(this.all(t,`[${Q}="${n}"]`),t).forEach(r=>{i.add(n),this.filterWithinSameLiveView(this.all(r,`[${Q}]`),r).map(o=>parseInt(o.getAttribute(Q))).forEach(o=>s.add(o))})}),s.forEach(n=>i.delete(n)),i},filterWithinSameLiveView(t,e){return e.querySelector(he)?t.filter(i=>this.withinSameLiveView(i,e)):t},withinSameLiveView(t,e){for(;t=t.parentNode;){if(t.isSameNode(e))return!0;if(t.getAttribute(z)!==null)return!1}},private(t,e){return t[X]&&t[X][e]},deletePrivate(t,e){t[X]&&delete t[X][e]},putPrivate(t,e,i){t[X]||(t[X]={}),t[X][e]=i},updatePrivate(t,e,i,s){let n=this.private(t,e);n===void 0?this.putPrivate(t,e,s(i)):this.putPrivate(t,e,s(n))},syncPendingAttrs(t,e){t.hasAttribute(B)&&(Zt.forEach(i=>{t.classList.contains(i)&&e.classList.add(i)}),Ht.filter(i=>t.hasAttribute(i)).forEach(i=>{e.setAttribute(i,t.getAttribute(i))}))},copyPrivates(t,e){e[X]&&(t[X]=e[X])},putTitle(t){let e=document.querySelector("title");if(e){let{prefix:i,suffix:s,default:n}=e.dataset,r=typeof t!="string"||t.trim()==="";if(r&&typeof n!="string")return;let o=r?n:t;document.title=`${i||""}${o||""}${s||""}`}else document.title=t},debounce(t,e,i,s,n,r,o,a){let l=t.getAttribute(i),h=t.getAttribute(n);l===""&&(l=s),h===""&&(h=r);let u=l||h;switch(u){case null:return a();case"blur":this.incCycle(t,"debounce-blur-cycle",()=>{o()&&a()}),this.once(t,"debounce-blur")&&t.addEventListener("blur",()=>this.triggerCycle(t,"debounce-blur-cycle"));return;default:let p=parseInt(u),m=()=>h?this.deletePrivate(t,Ce):a(),g=this.incCycle(t,Se,m);if(isNaN(p))return _(`invalid throttle/debounce value: ${u}`);if(h){let v=!1;if(e.type==="keydown"){let w=this.private(t,Dt);this.putPrivate(t,Dt,e.key),v=w!==e.key}if(!v&&this.private(t,Ce))return!1;{a();let w=setTimeout(()=>{o()&&this.triggerCycle(t,Se)},p);this.putPrivate(t,Ce,w)}}else setTimeout(()=>{o()&&this.triggerCycle(t,Se,g)},p);let c=t.form;c&&this.once(c,"bind-debounce")&&c.addEventListener("submit",()=>{Array.from(new FormData(c).entries(),([v])=>{let w=c.querySelector(`[name="${v}"]`);this.incCycle(w,Se),this.deletePrivate(w,Ce)})}),this.once(t,"bind-debounce")&&t.addEventListener("blur",()=>{clearTimeout(this.private(t,Ce)),this.triggerCycle(t,Se)})}},triggerCycle(t,e,i){let[s,n]=this.private(t,e);i||(i=s),i===s&&(this.incCycle(t,e),n())},once(t,e){return this.private(t,e)===!0?!1:(this.putPrivate(t,e,!0),!0)},incCycle(t,e,i=function(){}){let[s]=this.private(t,e)||[0,i];return s++,this.putPrivate(t,e,[s,i]),s},maintainPrivateHooks(t,e,i,s){t.hasAttribute&&t.hasAttribute("data-phx-hook")&&!e.hasAttribute("data-phx-hook")&&e.setAttribute("data-phx-hook",t.getAttribute("data-phx-hook")),e.hasAttribute&&(e.hasAttribute(i)||e.hasAttribute(s))&&e.setAttribute("data-phx-hook","Phoenix.InfiniteScroll")},putCustomElHook(t,e){t.isConnected?t.setAttribute("data-phx-hook",""):console.error(`
        hook attached to non-connected DOM element
        ensure you are calling createHook within your connectedCallback. ${t.outerHTML}
      `),this.putPrivate(t,"custom-el-hook",e)},getCustomElHook(t){return this.private(t,"custom-el-hook")},isUsedInput(t){return t.nodeType===Node.ELEMENT_NODE&&(this.private(t,We)||this.private(t,xe))},resetForm(t){Array.from(t.elements).forEach(e=>{this.deletePrivate(e,We),this.deletePrivate(e,xe)})},isPhxChild(t){return t.getAttribute&&t.getAttribute(le)},isPhxSticky(t){return t.getAttribute&&t.getAttribute(ct)!==null},isChildOfAny(t,e){return!!e.find(i=>i.contains(t))},firstPhxChild(t){return this.isPhxChild(t)?t:this.all(t,`[${le}]`)[0]},dispatchEvent(t,e,i={}){let s=!0;t.nodeName==="INPUT"&&t.type==="file"&&e==="click"&&(s=!1);let o={bubbles:i.bubbles===void 0?s:!!i.bubbles,cancelable:!0,detail:i.detail||{}},a=e==="click"?new MouseEvent("click",o):new CustomEvent(e,o);t.dispatchEvent(a)},cloneNode(t,e){if(typeof e>"u")return t.cloneNode(!0);{let i=t.cloneNode(!1);return i.innerHTML=e,i}},mergeAttrs(t,e,i={}){let s=new Set(i.exclude||[]),n=i.isIgnored,r=e.attributes;for(let a=r.length-1;a>=0;a--){let l=r[a].name;if(s.has(l))l==="value"&&t.value===e.value&&t.setAttribute("value",e.getAttribute(l));else{let h=e.getAttribute(l);t.getAttribute(l)!==h&&(!n||n&&l.startsWith("data-"))&&t.setAttribute(l,h)}}let o=t.attributes;for(let a=o.length-1;a>=0;a--){let l=o[a].name;n?l.startsWith("data-")&&!e.hasAttribute(l)&&!Ht.includes(l)&&t.removeAttribute(l):e.hasAttribute(l)||t.removeAttribute(l)}},mergeFocusedInput(t,e){t instanceof HTMLSelectElement||oe.mergeAttrs(t,e,{exclude:["value"]}),e.readOnly?t.setAttribute("readonly",!0):t.removeAttribute("readonly")},hasSelectionRange(t){return t.setSelectionRange&&(t.type==="text"||t.type==="textarea")},restoreFocus(t,e,i){if(t instanceof HTMLSelectElement&&t.focus(),!oe.isTextualInput(t))return;t.matches(":focus")||t.focus(),this.hasSelectionRange(t)&&t.setSelectionRange(e,i)},isFormInput(t){return/^(?:input|select|textarea)$/i.test(t.tagName)&&t.type!=="button"},syncAttrsToProps(t){t instanceof HTMLInputElement&&si.indexOf(t.type.toLocaleLowerCase())>=0&&(t.checked=t.getAttribute("checked")!==null)},isTextualInput(t){return Fi.indexOf(t.type)>=0},isNowTriggerFormExternal(t,e){return t.getAttribute&&t.getAttribute(e)!==null&&document.body.contains(t)},cleanChildNodes(t,e){if(oe.isPhxUpdate(t,e,["append","prepend"])){let i=[];t.childNodes.forEach(s=>{s.id||(!(s.nodeType===Node.TEXT_NODE&&s.nodeValue.trim()==="")&&s.nodeType!==Node.COMMENT_NODE&&_(`only HTML element tags with an id are allowed inside containers with phx-update.

removing illegal node: "${(s.outerHTML||s.nodeValue).trim()}"

`),i.push(s))}),i.forEach(s=>s.remove())}},replaceRootContainer(t,e,i){let s=new Set(["id",z,ae,mt,ne]);if(t.tagName.toLowerCase()===e.toLowerCase())return Array.from(t.attributes).filter(n=>!s.has(n.name.toLowerCase())).forEach(n=>t.removeAttribute(n.name)),Object.keys(i).filter(n=>!s.has(n.toLowerCase())).forEach(n=>t.setAttribute(n,i[n])),t;{let n=document.createElement(e);return Object.keys(i).forEach(r=>n.setAttribute(r,i[r])),s.forEach(r=>n.setAttribute(r,t.getAttribute(r))),n.innerHTML=t.innerHTML,t.replaceWith(n),n}},getSticky(t,e,i){let s=(oe.private(t,"sticky")||[]).find(([n])=>e===n);if(s){let[n,r,o]=s;return o}else return typeof i=="function"?i():i},deleteSticky(t,e){this.updatePrivate(t,"sticky",[],i=>i.filter(([s,n])=>s!==e))},putSticky(t,e,i){let s=i(t);this.updatePrivate(t,"sticky",[],n=>{let r=n.findIndex(([o])=>e===o);return r>=0?n[r]=[e,i,s]:n.push([e,i,s]),n})},applyStickyOperations(t){let e=oe.private(t,"sticky");e&&e.forEach(([i,s,n])=>this.putSticky(t,i,s))},isLocked(t){return t.hasAttribute&&t.hasAttribute(P)}},d=oe,Te=class{static isActive(t,e){let i=e._phxRef===void 0,n=t.getAttribute(lt).split(",").indexOf(L.genFileRef(e))>=0;return e.size>0&&(i||n)}static isPreflighted(t,e){return t.getAttribute(gt).split(",").indexOf(L.genFileRef(e))>=0&&this.isActive(t,e)}static isPreflightInProgress(t){return t._preflightInProgress===!0}static markPreflightInProgress(t){t._preflightInProgress=!0}constructor(t,e,i,s){this.ref=L.genFileRef(e),this.fileEl=t,this.file=e,this.view=i,this.meta=null,this._isCancelled=!1,this._isDone=!1,this._progress=0,this._lastProgressSent=-1,this._onDone=function(){},this._onElUpdated=this.onElUpdated.bind(this),this.fileEl.addEventListener(Ve,this._onElUpdated),this.autoUpload=s}metadata(){return this.meta}progress(t){this._progress=Math.floor(t),this._progress>this._lastProgressSent&&(this._progress>=100?(this._progress=100,this._lastProgressSent=100,this._isDone=!0,this.view.pushFileProgress(this.fileEl,this.ref,100,()=>{L.untrackFile(this.fileEl,this.file),this._onDone()})):(this._lastProgressSent=this._progress,this.view.pushFileProgress(this.fileEl,this.ref,this._progress)))}isCancelled(){return this._isCancelled}cancel(){this.file._preflightInProgress=!1,this._isCancelled=!0,this._isDone=!0,this._onDone()}isDone(){return this._isDone}error(t="failed"){this.fileEl.removeEventListener(Ve,this._onElUpdated),this.view.pushFileProgress(this.fileEl,this.ref,{error:t}),this.isAutoUpload()||L.clearFiles(this.fileEl)}isAutoUpload(){return this.autoUpload}onDone(t){this._onDone=()=>{this.fileEl.removeEventListener(Ve,this._onElUpdated),t()}}onElUpdated(){this.fileEl.getAttribute(lt).split(",").indexOf(this.ref)===-1&&(L.untrackFile(this.fileEl,this.file),this.cancel())}toPreflightPayload(){return{last_modified:this.file.lastModified,name:this.file.name,relative_path:this.file.webkitRelativePath,size:this.file.size,type:this.file.type,ref:this.ref,meta:typeof this.file.meta=="function"?this.file.meta():void 0}}uploader(t){if(this.meta.uploader){let e=t[this.meta.uploader]||_(`no uploader configured for ${this.meta.uploader}`);return{name:this.meta.uploader,callback:e}}else return{name:"channel",callback:es}}zipPostFlight(t){this.meta=t.entries[this.ref],this.meta||_(`no preflight upload response returned with ref ${this.ref}`,{input:this.fileEl,response:t})}},is=0,L=class ft{static genFileRef(e){let i=e._phxRef;return i!==void 0?i:(e._phxRef=(is++).toString(),e._phxRef)}static getEntryDataURL(e,i,s){let n=this.activeFiles(e).find(r=>this.genFileRef(r)===i);s(URL.createObjectURL(n))}static hasUploadsInProgress(e){let i=0;return d.findUploadInputs(e).forEach(s=>{s.getAttribute(gt)!==s.getAttribute(Hi)&&i++}),i>0}static serializeUploads(e){let i=this.activeFiles(e),s={};return i.forEach(n=>{let r={path:e.name},o=e.getAttribute(Z);s[o]=s[o]||[],r.ref=this.genFileRef(n),r.last_modified=n.lastModified,r.name=n.name||r.ref,r.relative_path=n.webkitRelativePath,r.type=n.type,r.size=n.size,typeof n.meta=="function"&&(r.meta=n.meta()),s[o].push(r)}),s}static clearFiles(e){e.value=null,e.removeAttribute(Z),d.putPrivate(e,"files",[])}static untrackFile(e,i){d.putPrivate(e,"files",d.private(e,"files").filter(s=>!Object.is(s,i)))}static trackFiles(e,i,s){if(e.getAttribute("multiple")!==null){let n=i.filter(r=>!this.activeFiles(e).find(o=>Object.is(o,r)));d.updatePrivate(e,"files",[],r=>r.concat(n)),e.value=null}else s&&s.files.length>0&&(e.files=s.files),d.putPrivate(e,"files",i)}static activeFileInputs(e){let i=d.findUploadInputs(e);return Array.from(i).filter(s=>s.files&&this.activeFiles(s).length>0)}static activeFiles(e){return(d.private(e,"files")||[]).filter(i=>Te.isActive(e,i))}static inputsAwaitingPreflight(e){let i=d.findUploadInputs(e);return Array.from(i).filter(s=>this.filesAwaitingPreflight(s).length>0)}static filesAwaitingPreflight(e){return this.activeFiles(e).filter(i=>!Te.isPreflighted(e,i)&&!Te.isPreflightInProgress(i))}static markPreflightInProgress(e){e.forEach(i=>Te.markPreflightInProgress(i.file))}constructor(e,i,s){this.autoUpload=d.isAutoUpload(e),this.view=i,this.onComplete=s,this._entries=Array.from(ft.filesAwaitingPreflight(e)||[]).map(n=>new Te(e,n,i,this.autoUpload)),ft.markPreflightInProgress(this._entries),this.numEntriesInProgress=this._entries.length}isAutoUpload(){return this.autoUpload}entries(){return this._entries}initAdapterUpload(e,i,s){this._entries=this._entries.map(r=>(r.isCancelled()?(this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()):(r.zipPostFlight(e),r.onDone(()=>{this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()})),r));let n=this._entries.reduce((r,o)=>{if(!o.meta)return r;let{name:a,callback:l}=o.uploader(s.uploaders);return r[a]=r[a]||{callback:l,entries:[]},r[a].entries.push(o),r},{});for(let r in n){let{callback:o,entries:a}=n[r];o(a,i,e,s)}}},ss={anyOf(t,e){return e.find(i=>t instanceof i)},isFocusable(t,e){return t instanceof HTMLAnchorElement&&t.rel!=="ignore"||t instanceof HTMLAreaElement&&t.href!==void 0||!t.disabled&&this.anyOf(t,[HTMLInputElement,HTMLSelectElement,HTMLTextAreaElement,HTMLButtonElement])||t instanceof HTMLIFrameElement||t.tabIndex>=0||!e&&t.getAttribute("tabindex")!==null&&t.getAttribute("aria-hidden")!=="true"},attemptFocus(t,e){if(this.isFocusable(t,e))try{t.focus()}catch{}return!!document.activeElement&&document.activeElement.isSameNode(t)},focusFirstInteractive(t){let e=t.firstElementChild;for(;e;){if(this.attemptFocus(e,!0)||this.focusFirstInteractive(e,!0))return!0;e=e.nextElementSibling}},focusFirst(t){let e=t.firstElementChild;for(;e;){if(this.attemptFocus(e)||this.focusFirst(e))return!0;e=e.nextElementSibling}},focusLast(t){let e=t.lastElementChild;for(;e;){if(this.attemptFocus(e)||this.focusLast(e))return!0;e=e.previousElementSibling}}},$=ss,ni={LiveFileUpload:{activeRefs(){return this.el.getAttribute(lt)},preflightedRefs(){return this.el.getAttribute(gt)},mounted(){this.preflightedWas=this.preflightedRefs()},updated(){let t=this.preflightedRefs();this.preflightedWas!==t&&(this.preflightedWas=t,t===""&&this.__view().cancelSubmit(this.el.form)),this.activeRefs()===""&&(this.el.value=null),this.el.dispatchEvent(new CustomEvent(Ve))}},LiveImgPreview:{mounted(){this.ref=this.el.getAttribute("data-phx-entry-ref"),this.inputEl=document.getElementById(this.el.getAttribute(Z)),L.getEntryDataURL(this.inputEl,this.ref,t=>{this.url=t,this.el.src=t})},destroyed(){URL.revokeObjectURL(this.url)}},FocusWrap:{mounted(){this.focusStart=this.el.firstElementChild,this.focusEnd=this.el.lastElementChild,this.focusStart.addEventListener("focus",t=>{if(!t.relatedTarget||!this.el.contains(t.relatedTarget)){let e=t.target.nextElementSibling;$.attemptFocus(e)||$.focusFirst(e)}else $.focusLast(this.el)}),this.focusEnd.addEventListener("focus",t=>{if(!t.relatedTarget||!this.el.contains(t.relatedTarget)){let e=t.target.previousElementSibling;$.attemptFocus(e)||$.focusLast(e)}else $.focusFirst(this.el)}),this.el.addEventListener("phx:show-end",()=>this.el.focus()),window.getComputedStyle(this.el).display!=="none"&&$.focusFirst(this.el)}}},ri=t=>["HTML","BODY"].indexOf(t.nodeName.toUpperCase())>=0?null:["scroll","auto"].indexOf(getComputedStyle(t).overflowY)>=0?t:ri(t.parentElement),jt=t=>t?t.scrollTop:document.documentElement.scrollTop||document.body.scrollTop,vt=t=>t?t.getBoundingClientRect().bottom:window.innerHeight||document.documentElement.clientHeight,bt=t=>t?t.getBoundingClientRect().top:0,ns=(t,e)=>{let i=t.getBoundingClientRect();return Math.ceil(i.top)>=bt(e)&&Math.ceil(i.left)>=0&&Math.floor(i.top)<=vt(e)},rs=(t,e)=>{let i=t.getBoundingClientRect();return Math.ceil(i.bottom)>=bt(e)&&Math.ceil(i.left)>=0&&Math.floor(i.bottom)<=vt(e)},Bt=(t,e)=>{let i=t.getBoundingClientRect();return Math.ceil(i.top)>=bt(e)&&Math.ceil(i.left)>=0&&Math.floor(i.top)<=vt(e)};ni.InfiniteScroll={mounted(){this.scrollContainer=ri(this.el);let t=jt(this.scrollContainer),e=!1,i=500,s=null,n=this.throttle(i,(a,l)=>{s=()=>!0,this.liveSocket.execJSHookPush(this.el,a,{id:l.id,_overran:!0},()=>{s=null})}),r=this.throttle(i,(a,l)=>{s=()=>l.scrollIntoView({block:"start"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{s=null,window.requestAnimationFrame(()=>{Bt(l,this.scrollContainer)||l.scrollIntoView({block:"start"})})})}),o=this.throttle(i,(a,l)=>{s=()=>l.scrollIntoView({block:"end"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{s=null,window.requestAnimationFrame(()=>{Bt(l,this.scrollContainer)||l.scrollIntoView({block:"end"})})})});this.onScroll=a=>{let l=jt(this.scrollContainer);if(s)return t=l,s();let h=this.el.getBoundingClientRect(),u=this.el.getAttribute(this.liveSocket.binding("viewport-top")),p=this.el.getAttribute(this.liveSocket.binding("viewport-bottom")),m=this.el.lastElementChild,g=this.el.firstElementChild,c=l<t,v=l>t;c&&u&&!e&&h.top>=0?(e=!0,n(u,g)):v&&e&&h.top<=0&&(e=!1),u&&c&&ns(g,this.scrollContainer)?r(u,g):p&&v&&rs(m,this.scrollContainer)&&o(p,m),t=l},this.scrollContainer?this.scrollContainer.addEventListener("scroll",this.onScroll):window.addEventListener("scroll",this.onScroll)},destroyed(){this.scrollContainer?this.scrollContainer.removeEventListener("scroll",this.onScroll):window.removeEventListener("scroll",this.onScroll)},throttle(t,e){let i=0,s;return(...n)=>{let r=Date.now(),o=t-(r-i);o<=0||o>t?(s&&(clearTimeout(s),s=null),i=r,e(...n)):s||(s=setTimeout(()=>{i=Date.now(),s=null,e(...n)},o))}}};var os=ni,pt=class{static onUnlock(t,e){if(!d.isLocked(t)&&!t.closest(`[${P}]`))return e();let i=t.closest(`[${P}]`),s=i.closest(`[${P}]`).getAttribute(P);i.addEventListener(`phx:undo-lock:${s}`,()=>{e()},{once:!0})}constructor(t){this.el=t,this.loadingRef=t.hasAttribute(me)?parseInt(t.getAttribute(me),10):null,this.lockRef=t.hasAttribute(P)?parseInt(t.getAttribute(P),10):null}maybeUndo(t,e,i){this.isWithin(t)&&(this.undoLocks(t,e,i),this.undoLoading(t,e),this.isFullyResolvedBy(t)&&this.el.removeAttribute(B))}isWithin(t){return!(this.loadingRef!==null&&this.loadingRef>t&&this.lockRef!==null&&this.lockRef>t)}undoLocks(t,e,i){if(!this.isLockUndoneBy(t))return;let s=d.private(this.el,P);s&&(i(s),d.deletePrivate(this.el,P)),this.el.removeAttribute(P);let n={detail:{ref:t,event:e},bubbles:!0,cancelable:!1};this.el.dispatchEvent(new CustomEvent(`phx:undo-lock:${this.lockRef}`,n))}undoLoading(t,e){if(!this.isLoadingUndoneBy(t)){this.canUndoLoading(t)&&this.el.classList.contains("phx-submit-loading")&&this.el.classList.remove("phx-change-loading");return}if(this.canUndoLoading(t)){this.el.removeAttribute(me);let i=this.el.getAttribute(ge),s=this.el.getAttribute(ut);s!==null&&(this.el.readOnly=s==="true",this.el.removeAttribute(ut)),i!==null&&(this.el.disabled=i==="true",this.el.removeAttribute(ge));let n=this.el.getAttribute(Xe);n!==null&&(this.el.innerText=n,this.el.removeAttribute(Xe));let r={detail:{ref:t,event:e},bubbles:!0,cancelable:!1};this.el.dispatchEvent(new CustomEvent(`phx:undo-loading:${this.loadingRef}`,r))}Zt.forEach(i=>{(i!=="phx-submit-loading"||this.canUndoLoading(t))&&d.removeClass(this.el,i)})}isLoadingUndoneBy(t){return this.loadingRef===null?!1:this.loadingRef<=t}isLockUndoneBy(t){return this.lockRef===null?!1:this.lockRef<=t}isFullyResolvedBy(t){return(this.loadingRef===null||this.loadingRef<=t)&&(this.lockRef===null||this.lockRef<=t)}canUndoLoading(t){return this.lockRef===null||this.lockRef<=t}},as=class{constructor(t,e,i){let s=new Set,n=new Set([...e.children].map(o=>o.id)),r=[];Array.from(t.children).forEach(o=>{if(o.id&&(s.add(o.id),n.has(o.id))){let a=o.previousElementSibling&&o.previousElementSibling.id;r.push({elementId:o.id,previousElementId:a})}}),this.containerId=e.id,this.updateType=i,this.elementsToModify=r,this.elementIdsToAdd=[...n].filter(o=>!s.has(o))}perform(){let t=d.byId(this.containerId);this.elementsToModify.forEach(e=>{e.previousElementId?se(document.getElementById(e.previousElementId),i=>{se(document.getElementById(e.elementId),s=>{s.previousElementSibling&&s.previousElementSibling.id==i.id||i.insertAdjacentElement("afterend",s)})}):se(document.getElementById(e.elementId),i=>{i.previousElementSibling==null||t.insertAdjacentElement("afterbegin",i)})}),this.updateType=="prepend"&&this.elementIdsToAdd.reverse().forEach(e=>{se(document.getElementById(e),i=>t.insertAdjacentElement("afterbegin",i))})}},Vt=11;function ls(t,e){var i=e.attributes,s,n,r,o,a;if(!(e.nodeType===Vt||t.nodeType===Vt)){for(var l=i.length-1;l>=0;l--)s=i[l],n=s.name,r=s.namespaceURI,o=s.value,r?(n=s.localName||n,a=t.getAttributeNS(r,n),a!==o&&(s.prefix==="xmlns"&&(n=s.name),t.setAttributeNS(r,n,o))):(a=t.getAttribute(n),a!==o&&t.setAttribute(n,o));for(var h=t.attributes,u=h.length-1;u>=0;u--)s=h[u],n=s.name,r=s.namespaceURI,r?(n=s.localName||n,e.hasAttributeNS(r,n)||t.removeAttributeNS(r,n)):e.hasAttribute(n)||t.removeAttribute(n)}}var Ue,hs="http://www.w3.org/1999/xhtml",M=typeof document>"u"?void 0:document,ds=!!M&&"content"in M.createElement("template"),cs=!!M&&M.createRange&&"createContextualFragment"in M.createRange();function us(t){var e=M.createElement("template");return e.innerHTML=t,e.content.childNodes[0]}function fs(t){Ue||(Ue=M.createRange(),Ue.selectNode(M.body));var e=Ue.createContextualFragment(t);return e.childNodes[0]}function ps(t){var e=M.createElement("body");return e.innerHTML=t,e.childNodes[0]}function gs(t){return t=t.trim(),ds?us(t):cs?fs(t):ps(t)}function $e(t,e){var i=t.nodeName,s=e.nodeName,n,r;return i===s?!0:(n=i.charCodeAt(0),r=s.charCodeAt(0),n<=90&&r>=97?i===s.toUpperCase():r<=90&&n>=97?s===i.toUpperCase():!1)}function ms(t,e){return!e||e===hs?M.createElement(t):M.createElementNS(e,t)}function vs(t,e){for(var i=t.firstChild;i;){var s=i.nextSibling;e.appendChild(i),i=s}return e}function ot(t,e,i){t[i]!==e[i]&&(t[i]=e[i],t[i]?t.setAttribute(i,""):t.removeAttribute(i))}var Jt={OPTION:function(t,e){var i=t.parentNode;if(i){var s=i.nodeName.toUpperCase();s==="OPTGROUP"&&(i=i.parentNode,s=i&&i.nodeName.toUpperCase()),s==="SELECT"&&!i.hasAttribute("multiple")&&(t.hasAttribute("selected")&&!e.selected&&(t.setAttribute("selected","selected"),t.removeAttribute("selected")),i.selectedIndex=-1)}ot(t,e,"selected")},INPUT:function(t,e){ot(t,e,"checked"),ot(t,e,"disabled"),t.value!==e.value&&(t.value=e.value),e.hasAttribute("value")||t.removeAttribute("value")},TEXTAREA:function(t,e){var i=e.value;t.value!==i&&(t.value=i);var s=t.firstChild;if(s){var n=s.nodeValue;if(n==i||!i&&n==t.placeholder)return;s.nodeValue=i}},SELECT:function(t,e){if(!e.hasAttribute("multiple")){for(var i=-1,s=0,n=t.firstChild,r,o;n;)if(o=n.nodeName&&n.nodeName.toUpperCase(),o==="OPTGROUP")r=n,n=r.firstChild;else{if(o==="OPTION"){if(n.hasAttribute("selected")){i=s;break}s++}n=n.nextSibling,!n&&r&&(n=r.nextSibling,r=null)}t.selectedIndex=i}}},_e=1,Wt=11,Xt=3,qt=8;function ie(){}function bs(t){if(t)return t.getAttribute&&t.getAttribute("id")||t.id}function ws(t){return function(i,s,n){if(n||(n={}),typeof s=="string")if(i.nodeName==="#document"||i.nodeName==="HTML"||i.nodeName==="BODY"){var r=s;s=M.createElement("html"),s.innerHTML=r}else s=gs(s);else s.nodeType===Wt&&(s=s.firstElementChild);var o=n.getNodeKey||bs,a=n.onBeforeNodeAdded||ie,l=n.onNodeAdded||ie,h=n.onBeforeElUpdated||ie,u=n.onElUpdated||ie,p=n.onBeforeNodeDiscarded||ie,m=n.onNodeDiscarded||ie,g=n.onBeforeElChildrenUpdated||ie,c=n.skipFromChildren||ie,v=n.addChild||function(y,k){return y.appendChild(k)},w=n.childrenOnly===!0,I=Object.create(null),C=[];function O(y){C.push(y)}function V(y,k){if(y.nodeType===_e)for(var T=y.firstChild;T;){var A=void 0;k&&(A=o(T))?O(A):(m(T),T.firstChild&&V(T,k)),T=T.nextSibling}}function D(y,k,T){p(y)!==!1&&(k&&k.removeChild(y),m(y),V(y,T))}function f(y){if(y.nodeType===_e||y.nodeType===Wt)for(var k=y.firstChild;k;){var T=o(k);T&&(I[T]=k),f(k),k=k.nextSibling}}f(i);function b(y){l(y);for(var k=y.firstChild;k;){var T=k.nextSibling,A=o(k);if(A){var S=I[A];S&&$e(k,S)?(k.parentNode.replaceChild(S,k),j(S,k)):b(k)}else b(k);k=T}}function J(y,k,T){for(;k;){var A=k.nextSibling;(T=o(k))?O(T):D(k,y,!0),k=A}}function j(y,k,T){var A=o(k);if(A&&delete I[A],!T){var S=h(y,k);if(S===!1||(S instanceof HTMLElement&&(y=S,f(y)),t(y,k),u(y),g(y,k)===!1))return}y.nodeName!=="TEXTAREA"?x(y,k):Jt.TEXTAREA(y,k)}function x(y,k){var T=c(y,k),A=k.firstChild,S=y.firstChild,de,K,ce,Le,ee;e:for(;A;){for(Le=A.nextSibling,de=o(A);!T&&S;){if(ce=S.nextSibling,A.isSameNode&&A.isSameNode(S)){A=Le,S=ce;continue e}K=o(S);var Ie=S.nodeType,te=void 0;if(Ie===A.nodeType&&(Ie===_e?(de?de!==K&&((ee=I[de])?ce===ee?te=!1:(y.insertBefore(ee,S),K?O(K):D(S,y,!0),S=ee,K=o(S)):te=!1):K&&(te=!1),te=te!==!1&&$e(S,A),te&&j(S,A)):(Ie===Xt||Ie==qt)&&(te=!0,S.nodeValue!==A.nodeValue&&(S.nodeValue=A.nodeValue))),te){A=Le,S=ce;continue e}K?O(K):D(S,y,!0),S=ce}if(de&&(ee=I[de])&&$e(ee,A))T||v(y,ee),j(ee,A);else{var Qe=a(A);Qe!==!1&&(Qe&&(A=Qe),A.actualize&&(A=A.actualize(y.ownerDocument||M)),v(y,A),b(A))}A=Le,S=ce}J(y,S,K);var kt=Jt[y.nodeName];kt&&kt(y,k)}var R=i,N=R.nodeType,yt=s.nodeType;if(!w){if(N===_e)yt===_e?$e(i,s)||(m(i),R=vs(i,ms(s.nodeName,s.namespaceURI))):R=s;else if(N===Xt||N===qt){if(yt===N)return R.nodeValue!==s.nodeValue&&(R.nodeValue=s.nodeValue),R;R=s}}if(R===s)m(i);else{if(s.isSameNode&&s.isSameNode(R))return;if(j(R,s,w),C)for(var Ge=0,fi=C.length;Ge<fi;Ge++){var Ye=I[C[Ge]];Ye&&D(Ye,Ye.parentNode,!1)}}return!w&&R!==i&&i.parentNode&&(R.actualize&&(R=R.actualize(i.ownerDocument||M)),i.parentNode.replaceChild(R,i)),R}}var ys=ws(ls),ks=ys,je=class{constructor(t,e,i,s,n,r,o={}){this.view=t,this.liveSocket=t.liveSocket,this.container=e,this.id=i,this.rootID=t.root.id,this.html=s,this.streams=n,this.streamInserts={},this.streamComponentRestore={},this.targetCID=r,this.cidPatch=Y(this.targetCID),this.pendingRemoves=[],this.phxRemove=this.liveSocket.binding("remove"),this.targetContainer=this.isCIDPatch()?this.targetCIDContainer(s):e,this.callbacks={beforeadded:[],beforeupdated:[],beforephxChildAdded:[],afteradded:[],afterupdated:[],afterdiscarded:[],afterphxChildAdded:[],aftertransitionsDiscarded:[]},this.withChildren=o.withChildren||o.undoRef||!1,this.undoRef=o.undoRef}before(t,e){this.callbacks[`before${t}`].push(e)}after(t,e){this.callbacks[`after${t}`].push(e)}trackBefore(t,...e){this.callbacks[`before${t}`].forEach(i=>i(...e))}trackAfter(t,...e){this.callbacks[`after${t}`].forEach(i=>i(...e))}markPrunableContentForRemoval(){let t=this.liveSocket.binding(qe);d.all(this.container,`[${t}=append] > *, [${t}=prepend] > *`,e=>{e.setAttribute(Tt,"")})}perform(t){let{view:e,liveSocket:i,html:s,container:n,targetContainer:r}=this;if(this.isCIDPatch()&&!r)return;let o=i.getActiveElement(),{selectionStart:a,selectionEnd:l}=o&&d.hasSelectionRange(o)?o:{},h=i.binding(qe),u=i.binding(ht),p=i.binding(dt),m=i.binding(Ni),g=[],c=[],v=[],w=null;function I(C,O,V=this.withChildren){let D={childrenOnly:C.getAttribute(Q)===null&&!V,getNodeKey:f=>d.isPhxDestroyed(f)?null:t?f.id:f.id||f.getAttribute&&f.getAttribute(ii),skipFromChildren:f=>f.getAttribute(h)===it,addChild:(f,b)=>{let{ref:J,streamAt:j}=this.getStreamInsert(b);if(J===void 0)return f.appendChild(b);if(this.setStreamRef(b,J),j===0)f.insertAdjacentElement("afterbegin",b);else if(j===-1){let x=f.lastElementChild;if(x&&!x.hasAttribute(Ae)){let R=Array.from(f.children).find(N=>!N.hasAttribute(Ae));f.insertBefore(b,R)}else f.appendChild(b)}else if(j>0){let x=Array.from(f.children)[j];f.insertBefore(b,x)}},onBeforeNodeAdded:f=>{d.maintainPrivateHooks(f,f,u,p),this.trackBefore("added",f);let b=f;return this.streamComponentRestore[f.id]&&(b=this.streamComponentRestore[f.id],delete this.streamComponentRestore[f.id],I.call(this,b,f,!0)),b},onNodeAdded:f=>{f.getAttribute&&this.maybeReOrderStream(f,!0),f instanceof HTMLImageElement&&f.srcset?f.srcset=f.srcset:f instanceof HTMLVideoElement&&f.autoplay&&f.play(),d.isNowTriggerFormExternal(f,m)&&(w=f),(d.isPhxChild(f)&&e.ownsElement(f)||d.isPhxSticky(f)&&e.ownsElement(f.parentNode))&&this.trackAfter("phxChildAdded",f),g.push(f)},onNodeDiscarded:f=>this.onNodeDiscarded(f),onBeforeNodeDiscarded:f=>f.getAttribute&&f.getAttribute(Tt)!==null?!0:!(f.parentElement!==null&&f.id&&d.isPhxUpdate(f.parentElement,h,[it,"append","prepend"])||this.maybePendingRemove(f)||this.skipCIDSibling(f)),onElUpdated:f=>{d.isNowTriggerFormExternal(f,m)&&(w=f),c.push(f),this.maybeReOrderStream(f,!1)},onBeforeElUpdated:(f,b)=>{if(f.id&&f.isSameNode(C)&&f.id!==b.id)return D.onNodeDiscarded(f),f.replaceWith(b),D.onNodeAdded(b);if(d.syncPendingAttrs(f,b),d.maintainPrivateHooks(f,b,u,p),d.cleanChildNodes(b,h),this.skipCIDSibling(b))return this.maybeReOrderStream(f),!1;if(d.isPhxSticky(f))return[z,ae,ne].map(x=>[x,f.getAttribute(x),b.getAttribute(x)]).forEach(([x,R,N])=>{N&&R!==N&&f.setAttribute(x,N)}),!1;if(d.isIgnored(f,h)||f.form&&f.form.isSameNode(w))return this.trackBefore("updated",f,b),d.mergeAttrs(f,b,{isIgnored:d.isIgnored(f,h)}),c.push(f),d.applyStickyOperations(f),!1;if(f.type==="number"&&f.validity&&f.validity.badInput)return!1;let J=o&&f.isSameNode(o)&&d.isFormInput(f),j=J&&this.isChangedSelect(f,b);if(f.hasAttribute(B)){let x=new pt(f);if(x.lockRef&&(!this.undoRef||!x.isLockUndoneBy(this.undoRef))){d.isUploadInput(f)&&(d.mergeAttrs(f,b,{isIgnored:!0}),this.trackBefore("updated",f,b),c.push(f)),d.applyStickyOperations(f);let N=f.hasAttribute(P)?d.private(f,P)||f.cloneNode(!0):null;N&&(d.putPrivate(f,P,N),J||(f=N))}}if(d.isPhxChild(b)){let x=f.getAttribute(z);return d.mergeAttrs(f,b,{exclude:[ae]}),x!==""&&f.setAttribute(z,x),f.setAttribute(ne,this.rootID),d.applyStickyOperations(f),!1}return this.undoRef&&d.private(b,P)&&d.putPrivate(f,P,d.private(b,P)),d.copyPrivates(b,f),J&&f.type!=="hidden"&&!j?(this.trackBefore("updated",f,b),d.mergeFocusedInput(f,b),d.syncAttrsToProps(f),c.push(f),d.applyStickyOperations(f),!1):(j&&f.blur(),d.isPhxUpdate(b,h,["append","prepend"])&&v.push(new as(f,b,b.getAttribute(h))),d.syncAttrsToProps(b),d.applyStickyOperations(b),this.trackBefore("updated",f,b),f)}};ks(C,O,D)}return this.trackBefore("added",n),this.trackBefore("updated",n,n),i.time("morphdom",()=>{this.streams.forEach(([C,O,V,D])=>{O.forEach(([f,b,J])=>{this.streamInserts[f]={ref:C,streamAt:b,limit:J,reset:D}}),D!==void 0&&d.all(n,`[${Ae}="${C}"]`,f=>{this.removeStreamChildElement(f)}),V.forEach(f=>{let b=n.querySelector(`[id="${f}"]`);b&&this.removeStreamChildElement(b)})}),t&&d.all(this.container,`[${h}=${it}]`).filter(C=>this.view.ownsElement(C)).forEach(C=>{Array.from(C.children).forEach(O=>{this.removeStreamChildElement(O,!0)})}),I.call(this,r,s)}),i.isDebugEnabled()&&(Gi(),Yi(this.streamInserts),Array.from(document.querySelectorAll("input[name=id]")).forEach(C=>{C.form&&console.error(`Detected an input with name="id" inside a form! This will cause problems when patching the DOM.
`,C)})),v.length>0&&i.time("post-morph append/prepend restoration",()=>{v.forEach(C=>C.perform())}),i.silenceEvents(()=>d.restoreFocus(o,a,l)),d.dispatchEvent(document,"phx:update"),g.forEach(C=>this.trackAfter("added",C)),c.forEach(C=>this.trackAfter("updated",C)),this.transitionPendingRemoves(),w&&(i.unload(),Object.getPrototypeOf(w).submit.call(w)),!0}onNodeDiscarded(t){(d.isPhxChild(t)||d.isPhxSticky(t))&&this.liveSocket.destroyViewByEl(t),this.trackAfter("discarded",t)}maybePendingRemove(t){return t.getAttribute&&t.getAttribute(this.phxRemove)!==null?(this.pendingRemoves.push(t),!0):!1}removeStreamChildElement(t,e=!1){!e&&!this.view.ownsElement(t)||(this.streamInserts[t.id]?(this.streamComponentRestore[t.id]=t,t.remove()):this.maybePendingRemove(t)||(t.remove(),this.onNodeDiscarded(t)))}getStreamInsert(t){return(t.id?this.streamInserts[t.id]:{})||{}}setStreamRef(t,e){d.putSticky(t,Ae,i=>i.setAttribute(Ae,e))}maybeReOrderStream(t,e){let{ref:i,streamAt:s,reset:n}=this.getStreamInsert(t);if(s!==void 0&&(this.setStreamRef(t,i),!(!n&&!e)&&t.parentElement)){if(s===0)t.parentElement.insertBefore(t,t.parentElement.firstElementChild);else if(s>0){let r=Array.from(t.parentElement.children),o=r.indexOf(t);if(s>=r.length-1)t.parentElement.appendChild(t);else{let a=r[s];o>s?t.parentElement.insertBefore(t,a):t.parentElement.insertBefore(t,a.nextElementSibling)}}this.maybeLimitStream(t)}}maybeLimitStream(t){let{limit:e}=this.getStreamInsert(t),i=e!==null&&Array.from(t.parentElement.children);e&&e<0&&i.length>e*-1?i.slice(0,i.length+e).forEach(s=>this.removeStreamChildElement(s)):e&&e>=0&&i.length>e&&i.slice(e).forEach(s=>this.removeStreamChildElement(s))}transitionPendingRemoves(){let{pendingRemoves:t,liveSocket:e}=this;t.length>0&&e.transitionRemoves(t,()=>{t.forEach(i=>{let s=d.firstPhxChild(i);s&&e.destroyViewByEl(s),i.remove()}),this.trackAfter("transitionsDiscarded",t)})}isChangedSelect(t,e){return!(t instanceof HTMLSelectElement)||t.multiple?!1:t.options.length!==e.options.length?!0:(e.value=t.value,!t.isEqualNode(e))}isCIDPatch(){return this.cidPatch}skipCIDSibling(t){return t.nodeType===Node.ELEMENT_NODE&&t.hasAttribute(ti)}targetCIDContainer(t){if(!this.isCIDPatch())return;let[e,...i]=d.findComponentNodeList(this.container,this.targetCID);return i.length===0&&d.childNodeLength(t)===1?e:e&&e.parentNode}indexOf(t,e){return Array.from(t.children).indexOf(e)}},As=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),Ss=new Set(["'",'"']),zt=(t,e,i)=>{let s=0,n=!1,r,o,a,l,h,u,p=t.match(/^(\s*(?:<!--.*?-->\s*)*)<([^\s\/>]+)/);if(p===null)throw new Error(`malformed html ${t}`);for(s=p[0].length,r=p[1],a=p[2],l=s,s;s<t.length&&t.charAt(s)!==">";s++)if(t.charAt(s)==="="){let c=t.slice(s-3,s)===" id";s++;let v=t.charAt(s);if(Ss.has(v)){let w=s;for(s++,s;s<t.length&&t.charAt(s)!==v;s++);if(c){h=t.slice(w+1,s);break}}}let m=t.length-1;for(n=!1;m>=r.length+a.length;){let c=t.charAt(m);if(n)c==="-"&&t.slice(m-3,m)==="<!-"?(n=!1,m-=4):m-=1;else if(c===">"&&t.slice(m-2,m)==="--")n=!0,m-=3;else{if(c===">")break;m-=1}}o=t.slice(m+1,t.length);let g=Object.keys(e).map(c=>e[c]===!0?c:`${c}="${e[c]}"`).join(" ");if(i){let c=h?` id="${h}"`:"";As.has(a)?u=`<${a}${c}${g===""?"":" "}${g}/>`:u=`<${a}${c}${g===""?"":" "}${g}></${a}>`}else{let c=t.slice(l,m+1);u=`<${a}${g===""?"":" "}${g}${c}`}return[u,r,o]},Kt=class{static extract(t){let{[Ft]:e,[Nt]:i,[Mt]:s}=t;return delete t[Ft],delete t[Nt],delete t[Mt],{diff:t,title:s,reply:e||null,events:i||[]}}constructor(t,e){this.viewId=t,this.rendered={},this.magicId=0,this.mergeDiff(e)}parentViewId(){return this.viewId}toString(t){let[e,i]=this.recursiveToString(this.rendered,this.rendered[H],t,!0,{});return[e,i]}recursiveToString(t,e=t[H],i,s,n){i=i?new Set(i):null;let r={buffer:"",components:e,onlyCids:i,streams:new Set};return this.toOutputBuffer(t,null,r,s,n),[r.buffer,r.streams]}componentCIDs(t){return Object.keys(t[H]||{}).map(e=>parseInt(e))}isComponentOnlyDiff(t){return t[H]?Object.keys(t).length===1:!1}getComponent(t,e){return t[H][e]}resetRender(t){this.rendered[H][t]&&(this.rendered[H][t].reset=!0)}mergeDiff(t){let e=t[H],i={};if(delete t[H],this.rendered=this.mutableMerge(this.rendered,t),this.rendered[H]=this.rendered[H]||{},e){let s=this.rendered[H];for(let n in e)e[n]=this.cachedFindComponent(n,e[n],s,e,i);for(let n in e)s[n]=e[n];t[H]=e}}cachedFindComponent(t,e,i,s,n){if(n[t])return n[t];{let r,o,a=e[q];if(Y(a)){let l;a>0?l=this.cachedFindComponent(a,s[a],i,s,n):l=i[-a],o=l[q],r=this.cloneMerge(l,e,!0),r[q]=o}else r=e[q]!==void 0||i[t]===void 0?e:this.cloneMerge(i[t],e,!1);return n[t]=r,r}}mutableMerge(t,e){return e[q]!==void 0?e:(this.doMutableMerge(t,e),t)}doMutableMerge(t,e){for(let i in e){let s=e[i],n=t[i];pe(s)&&s[q]===void 0&&pe(n)?this.doMutableMerge(n,s):t[i]=s}t[rt]&&(t.newRender=!0)}cloneMerge(t,e,i){let s={...t,...e};for(let n in s){let r=e[n],o=t[n];pe(r)&&r[q]===void 0&&pe(o)?s[n]=this.cloneMerge(o,r,i):r===void 0&&pe(o)&&(s[n]=this.cloneMerge(o,{},i))}return i?(delete s.magicId,delete s.newRender):t[rt]&&(s.newRender=!0),s}componentToString(t){let[e,i]=this.recursiveCIDToString(this.rendered[H],t,null),[s,n,r]=zt(e,{});return[s,i]}pruneCIDs(t){t.forEach(e=>delete this.rendered[H][e])}get(){return this.rendered}isNewFingerprint(t={}){return!!t[q]}templateStatic(t,e){return typeof t=="number"?e[t]:t}nextMagicID(){return this.magicId++,`m${this.magicId}-${this.parentViewId()}`}toOutputBuffer(t,e,i,s,n={}){if(t[Me])return this.comprehensionToBuffer(t,e,i);let{[q]:r}=t;r=this.templateStatic(r,e);let o=t[rt],a=i.buffer;o&&(i.buffer=""),s&&o&&!t.magicId&&(t.newRender=!0,t.magicId=this.nextMagicID()),i.buffer+=r[0];for(let l=1;l<r.length;l++)this.dynamicToBuffer(t[l-1],e,i,s),i.buffer+=r[l];if(o){let l=!1,h;s||t.magicId?(l=s&&!t.newRender,h={[ii]:t.magicId,...n}):h=n,l&&(h[ti]=!0);let[u,p,m]=zt(i.buffer,h,l);t.newRender=!1,i.buffer=a+p+u+m}}comprehensionToBuffer(t,e,i){let{[Me]:s,[q]:n,[Ut]:r}=t,[o,a,l,h]=r||[null,{},[],null];n=this.templateStatic(n,e);let u=e||t[zi];for(let p=0;p<s.length;p++){let m=s[p];i.buffer+=n[0];for(let g=1;g<n.length;g++){let c=!1;this.dynamicToBuffer(m[g-1],u,i,c),i.buffer+=n[g]}}r!==void 0&&(t[Me].length>0||l.length>0||h)&&(delete t[Ut],t[Me]=[],i.streams.add(r))}dynamicToBuffer(t,e,i,s){if(typeof t=="number"){let[n,r]=this.recursiveCIDToString(i.components,t,i.onlyCids);i.buffer+=n,i.streams=new Set([...i.streams,...r])}else pe(t)?this.toOutputBuffer(t,e,i,s,{}):i.buffer+=t}recursiveCIDToString(t,e,i){let s=t[e]||_(`no component for CID ${e}`,t),n={[Q]:e},r=i&&!i.has(e);s.newRender=!r,s.magicId=`c${e}-${this.parentViewId()}`;let o=!s.reset,[a,l]=this.recursiveToString(s,t,i,o,n);return delete s.reset,[a,l]}},Gt=[],Yt=200,Cs={exec(t,e,i,s,n,r){let[o,a]=r||[null,{callback:r&&r.callback}];(i.charAt(0)==="["?JSON.parse(i):[[o,a]]).forEach(([h,u])=>{h===o&&(u={...a,...u},u.callback=u.callback||a.callback),this.filterToEls(s.liveSocket,n,u).forEach(p=>{this[`exec_${h}`](t,e,i,s,n,p,u)})})},isVisible(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length>0)},isInViewport(t){let e=t.getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight,s=window.innerWidth||document.documentElement.clientWidth;return e.right>0&&e.bottom>0&&e.left<s&&e.top<i},exec_exec(t,e,i,s,n,r,{attr:o,to:a}){let l=r.getAttribute(o);if(!l)throw new Error(`expected ${o} to contain JS command on "${a}"`);s.liveSocket.execJS(r,l,e)},exec_dispatch(t,e,i,s,n,r,{event:o,detail:a,bubbles:l}){a=a||{},a.dispatcher=n,d.dispatchEvent(r,o,{detail:a,bubbles:l})},exec_push(t,e,i,s,n,r,o){let{event:a,data:l,target:h,page_loading:u,loading:p,value:m,dispatcher:g,callback:c}=o,v={loading:p,value:m,target:h,page_loading:!!u},w=e==="change"&&g?g:n,I=h||w.getAttribute(s.binding("target"))||w,C=(O,V)=>{if(O.isConnected())if(e==="change"){let{newCid:D,_target:f}=o;f=f||(d.isFormInput(n)?n.name:void 0),f&&(v._target=f),O.pushInput(n,V,D,a||i,v,c)}else if(e==="submit"){let{submitter:D}=o;O.submitForm(n,V,a||i,D,v,c)}else O.pushEvent(e,n,V,a||i,l,v,c)};o.targetView&&o.targetCtx?C(o.targetView,o.targetCtx):s.withinTargets(I,C)},exec_navigate(t,e,i,s,n,r,{href:o,replace:a}){s.liveSocket.historyRedirect(t,o,a?"replace":"push",null,n)},exec_patch(t,e,i,s,n,r,{href:o,replace:a}){s.liveSocket.pushHistoryPatch(t,o,a?"replace":"push",n)},exec_focus(t,e,i,s,n,r){$.attemptFocus(r),window.requestAnimationFrame(()=>{window.requestAnimationFrame(()=>$.attemptFocus(r))})},exec_focus_first(t,e,i,s,n,r){$.focusFirstInteractive(r)||$.focusFirst(r),window.requestAnimationFrame(()=>{window.requestAnimationFrame(()=>$.focusFirstInteractive(r)||$.focusFirst(r))})},exec_push_focus(t,e,i,s,n,r){Gt.push(r||n)},exec_pop_focus(t,e,i,s,n,r){let o=Gt.pop();o&&(o.focus(),window.requestAnimationFrame(()=>{window.requestAnimationFrame(()=>o.focus())}))},exec_add_class(t,e,i,s,n,r,{names:o,transition:a,time:l,blocking:h}){this.addOrRemoveClasses(r,o,[],a,l,s,h)},exec_remove_class(t,e,i,s,n,r,{names:o,transition:a,time:l,blocking:h}){this.addOrRemoveClasses(r,[],o,a,l,s,h)},exec_toggle_class(t,e,i,s,n,r,{names:o,transition:a,time:l,blocking:h}){this.toggleClasses(r,o,a,l,s,h)},exec_toggle_attr(t,e,i,s,n,r,{attr:[o,a,l]}){this.toggleAttr(r,o,a,l)},exec_transition(t,e,i,s,n,r,{time:o,transition:a,blocking:l}){this.addOrRemoveClasses(r,[],[],a,o,s,l)},exec_toggle(t,e,i,s,n,r,{display:o,ins:a,outs:l,time:h,blocking:u}){this.toggle(e,s,r,o,a,l,h,u)},exec_show(t,e,i,s,n,r,{display:o,transition:a,time:l,blocking:h}){this.show(e,s,r,o,a,l,h)},exec_hide(t,e,i,s,n,r,{display:o,transition:a,time:l,blocking:h}){this.hide(e,s,r,o,a,l,h)},exec_set_attr(t,e,i,s,n,r,{attr:[o,a]}){this.setOrRemoveAttrs(r,[[o,a]],[])},exec_remove_attr(t,e,i,s,n,r,{attr:o}){this.setOrRemoveAttrs(r,[],[o])},show(t,e,i,s,n,r,o){this.isVisible(i)||this.toggle(t,e,i,s,n,null,r,o)},hide(t,e,i,s,n,r,o){this.isVisible(i)&&this.toggle(t,e,i,s,null,n,r,o)},toggle(t,e,i,s,n,r,o,a){o=o||Yt;let[l,h,u]=n||[[],[],[]],[p,m,g]=r||[[],[],[]];if(l.length>0||p.length>0)if(this.isVisible(i)){let c=()=>{this.addOrRemoveClasses(i,m,l.concat(h).concat(u)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,p,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,g,m))})},v=()=>{this.addOrRemoveClasses(i,[],p.concat(g)),d.putSticky(i,"toggle",w=>w.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))};i.dispatchEvent(new Event("phx:hide-start")),a===!1?(c(),setTimeout(v,o)):e.transition(o,c,v)}else{if(t==="remove")return;let c=()=>{this.addOrRemoveClasses(i,h,p.concat(m).concat(g));let w=s||this.defaultDisplay(i);window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,l,[]),window.requestAnimationFrame(()=>{d.putSticky(i,"toggle",I=>I.style.display=w),this.addOrRemoveClasses(i,u,h)})})},v=()=>{this.addOrRemoveClasses(i,[],l.concat(u)),i.dispatchEvent(new Event("phx:show-end"))};i.dispatchEvent(new Event("phx:show-start")),a===!1?(c(),setTimeout(v,o)):e.transition(o,c,v)}else this.isVisible(i)?window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:hide-start")),d.putSticky(i,"toggle",c=>c.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))}):window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:show-start"));let c=s||this.defaultDisplay(i);d.putSticky(i,"toggle",v=>v.style.display=c),i.dispatchEvent(new Event("phx:show-end"))})},toggleClasses(t,e,i,s,n,r){window.requestAnimationFrame(()=>{let[o,a]=d.getSticky(t,"classes",[[],[]]),l=e.filter(u=>o.indexOf(u)<0&&!t.classList.contains(u)),h=e.filter(u=>a.indexOf(u)<0&&t.classList.contains(u));this.addOrRemoveClasses(t,l,h,i,s,n,r)})},toggleAttr(t,e,i,s){t.hasAttribute(e)?s!==void 0?t.getAttribute(e)===i?this.setOrRemoveAttrs(t,[[e,s]],[]):this.setOrRemoveAttrs(t,[[e,i]],[]):this.setOrRemoveAttrs(t,[],[e]):this.setOrRemoveAttrs(t,[[e,i]],[])},addOrRemoveClasses(t,e,i,s,n,r,o){n=n||Yt;let[a,l,h]=s||[[],[],[]];if(a.length>0){let u=()=>{this.addOrRemoveClasses(t,l,[].concat(a).concat(h)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(t,a,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(t,h,l))})},p=()=>this.addOrRemoveClasses(t,e.concat(h),i.concat(a).concat(l));o===!1?(u(),setTimeout(p,n)):r.transition(n,u,p);return}window.requestAnimationFrame(()=>{let[u,p]=d.getSticky(t,"classes",[[],[]]),m=e.filter(w=>u.indexOf(w)<0&&!t.classList.contains(w)),g=i.filter(w=>p.indexOf(w)<0&&t.classList.contains(w)),c=u.filter(w=>i.indexOf(w)<0).concat(m),v=p.filter(w=>e.indexOf(w)<0).concat(g);d.putSticky(t,"classes",w=>(w.classList.remove(...v),w.classList.add(...c),[c,v]))})},setOrRemoveAttrs(t,e,i){let[s,n]=d.getSticky(t,"attrs",[[],[]]),r=e.map(([l,h])=>l).concat(i),o=s.filter(([l,h])=>!r.includes(l)).concat(e),a=n.filter(l=>!r.includes(l)).concat(i);d.putSticky(t,"attrs",l=>(a.forEach(h=>l.removeAttribute(h)),o.forEach(([h,u])=>l.setAttribute(h,u)),[o,a]))},hasAllClasses(t,e){return e.every(i=>t.classList.contains(i))},isToggledOut(t,e){return!this.isVisible(t)||this.hasAllClasses(t,e)},filterToEls(t,e,{to:i}){let s=()=>{if(typeof i=="string")return document.querySelectorAll(i);if(i.closest){let n=e.closest(i.closest);return n?[n]:[]}else if(i.inner)return e.querySelectorAll(i.inner)};return i?t.jsQuerySelectorAll(e,i,s):[e]},defaultDisplay(t){return{tr:"table-row",td:"table-cell"}[t.tagName.toLowerCase()]||"block"},transitionClasses(t){if(!t)return null;let[e,i,s]=Array.isArray(t)?t:[t.split(" "),[],[]];return e=Array.isArray(e)?e:e.split(" "),i=Array.isArray(i)?i:i.split(" "),s=Array.isArray(s)?s:s.split(" "),[e,i,s]}},E=Cs,at="hookId",Es=1,Re=class{static makeID(){return Es++}static elementID(t){return d.private(t,at)}constructor(t,e,i){this.el=e,this.__attachView(t),this.__callbacks=i,this.__listeners=new Set,this.__isDisconnected=!1,d.putPrivate(this.el,at,this.constructor.makeID());for(let s in this.__callbacks)this[s]=this.__callbacks[s]}__attachView(t){t?(this.__view=()=>t,this.liveSocket=t.liveSocket):(this.__view=()=>{throw new Error(`hook not yet attached to a live view: ${this.el.outerHTML}`)},this.liveSocket=null)}__mounted(){this.mounted&&this.mounted()}__updated(){this.updated&&this.updated()}__beforeUpdate(){this.beforeUpdate&&this.beforeUpdate()}__destroyed(){this.destroyed&&this.destroyed(),d.deletePrivate(this.el,at)}__reconnected(){this.__isDisconnected&&(this.__isDisconnected=!1,this.reconnected&&this.reconnected())}__disconnected(){this.__isDisconnected=!0,this.disconnected&&this.disconnected()}js(){let t=this;return{exec(e){t.__view().liveSocket.execJS(t.el,e,"hook")},show(e,i={}){let s=t.__view().liveSocket.owner(e);E.show("hook",s,e,i.display,i.transition,i.time,i.blocking)},hide(e,i={}){let s=t.__view().liveSocket.owner(e);E.hide("hook",s,e,null,i.transition,i.time,i.blocking)},toggle(e,i={}){let s=t.__view().liveSocket.owner(e);i.in=E.transitionClasses(i.in),i.out=E.transitionClasses(i.out),E.toggle("hook",s,e,i.display,i.in,i.out,i.time,i.blocking)},addClass(e,i,s={}){i=Array.isArray(i)?i:i.split(" ");let n=t.__view().liveSocket.owner(e);E.addOrRemoveClasses(e,i,[],s.transition,s.time,n,s.blocking)},removeClass(e,i,s={}){s.transition=E.transitionClasses(s.transition),i=Array.isArray(i)?i:i.split(" ");let n=t.__view().liveSocket.owner(e);E.addOrRemoveClasses(e,[],i,s.transition,s.time,n,s.blocking)},toggleClass(e,i,s={}){s.transition=E.transitionClasses(s.transition),i=Array.isArray(i)?i:i.split(" ");let n=t.__view().liveSocket.owner(e);E.toggleClasses(e,i,s.transition,s.time,n,s.blocking)},transition(e,i,s={}){let n=t.__view().liveSocket.owner(e);E.addOrRemoveClasses(e,[],[],E.transitionClasses(i),s.time,n,s.blocking)},setAttribute(e,i,s){E.setOrRemoveAttrs(e,[[i,s]],[])},removeAttribute(e,i){E.setOrRemoveAttrs(e,[],[i])},toggleAttribute(e,i,s,n){E.toggleAttr(e,i,s,n)}}}pushEvent(t,e={},i){return i===void 0?new Promise((s,n)=>{try{this.__view().pushHookEvent(this.el,null,t,e,(o,a)=>s(o))===!1&&n(new Error("unable to push hook event. LiveView not connected"))}catch(r){n(r)}}):this.__view().pushHookEvent(this.el,null,t,e,i)}pushEventTo(t,e,i={},s){return s===void 0?new Promise((n,r)=>{try{this.__view().withinTargets(t,(o,a)=>{o.pushHookEvent(this.el,a,e,i,(h,u)=>n(h))===!1&&r(new Error("unable to push hook event. LiveView not connected"))})}catch(o){r(o)}}):this.__view().withinTargets(t,(n,r)=>n.pushHookEvent(this.el,r,e,i,s))}handleEvent(t,e){let i=(s,n)=>n?t:e(s.detail);return window.addEventListener(`phx:${t}`,i),this.__listeners.add(i),i}removeHandleEvent(t){let e=t(null,!0);window.removeEventListener(`phx:${e}`,t),this.__listeners.delete(t)}upload(t,e){return this.__view().dispatchUploads(null,t,e)}uploadTo(t,e,i){return this.__view().withinTargets(t,(s,n)=>{s.dispatchUploads(n,e,i)})}__cleanup__(){this.__listeners.forEach(t=>this.removeHandleEvent(t))}},Ts=(t,e)=>{let i=t.endsWith("[]"),s=i?t.slice(0,-2):t;return s=s.replace(/([^\[\]]+)(\]?$)/,`${e}$1$2`),i&&(s+="[]"),s},Be=(t,e,i=[])=>{let{submitter:s}=e,n;if(s&&s.name){let u=document.createElement("input");u.type="hidden";let p=s.getAttribute("form");p&&u.setAttribute("form",p),u.name=s.name,u.value=s.value,s.parentElement.insertBefore(u,s),n=u}let r=new FormData(t),o=[];r.forEach((u,p,m)=>{u instanceof File&&o.push(p)}),o.forEach(u=>r.delete(u));let a=new URLSearchParams,{inputsUnused:l,onlyHiddenInputs:h}=Array.from(t.elements).reduce((u,p)=>{let{inputsUnused:m,onlyHiddenInputs:g}=u,c=p.name;if(!c)return u;m[c]===void 0&&(m[c]=!0),g[c]===void 0&&(g[c]=!0);let v=d.private(p,We)||d.private(p,xe),w=p.type==="hidden";return m[c]=m[c]&&!v,g[c]=g[c]&&w,u},{inputsUnused:{},onlyHiddenInputs:{}});for(let[u,p]of r.entries())if(i.length===0||i.indexOf(u)>=0){let m=l[u],g=h[u];m&&!(s&&s.name==u)&&!g&&a.append(Ts(u,"_unused_"),""),a.append(u,p)}return s&&n&&s.parentElement.removeChild(n),a.toString()},_s=class oi{static closestView(e){let i=e.closest(he);return i?d.private(i,"view"):null}constructor(e,i,s,n,r){this.isDead=!1,this.liveSocket=i,this.flash=n,this.parent=s,this.root=s?s.root:this,this.el=e,d.putPrivate(this.el,"view",this),this.id=this.el.id,this.ref=0,this.lastAckRef=null,this.childJoins=0,this.loaderTimer=null,this.disconnectedTimer=null,this.pendingDiffs=[],this.pendingForms=new Set,this.redirect=!1,this.href=null,this.joinCount=this.parent?this.parent.joinCount-1:0,this.joinAttempts=0,this.joinPending=!0,this.destroyed=!1,this.joinCallback=function(o){o&&o()},this.stopCallback=function(){},this.pendingJoinOps=this.parent?null:[],this.viewHooks={},this.formSubmits=[],this.children=this.parent?null:{},this.root.children[this.id]={},this.formsForRecovery={},this.channel=this.liveSocket.channel(`lv:${this.id}`,()=>{let o=this.href&&this.expandURL(this.href);return{redirect:this.redirect?o:void 0,url:this.redirect?void 0:o||void 0,params:this.connectParams(r),session:this.getSession(),static:this.getStatic(),flash:this.flash,sticky:this.el.hasAttribute(ct)}})}setHref(e){this.href=e}setRedirect(e){this.redirect=!0,this.href=e}isMain(){return this.el.hasAttribute(mt)}connectParams(e){let i=this.liveSocket.params(this.el),s=d.all(document,`[${this.binding(Oi)}]`).map(n=>n.src||n.href).filter(n=>typeof n=="string");return s.length>0&&(i._track_static=s),i._mounts=this.joinCount,i._mount_attempts=this.joinAttempts,i._live_referer=e,this.joinAttempts++,i}isConnected(){return this.channel.canPush()}getSession(){return this.el.getAttribute(z)}getStatic(){let e=this.el.getAttribute(ae);return e===""?null:e}destroy(e=function(){}){this.destroyAllChildren(),this.destroyed=!0,delete this.root.children[this.id],this.parent&&delete this.root.children[this.parent.id][this.id],clearTimeout(this.loaderTimer);let i=()=>{e();for(let s in this.viewHooks)this.destroyHook(this.viewHooks[s])};d.markPhxChildDestroyed(this.el),this.log("destroyed",()=>["the child has been removed from the parent"]),this.channel.leave().receive("ok",i).receive("error",i).receive("timeout",i)}setContainerClasses(...e){this.el.classList.remove(_t,re,fe,Rt,ye),this.el.classList.add(...e)}showLoader(e){if(clearTimeout(this.loaderTimer),e)this.loaderTimer=setTimeout(()=>this.showLoader(),e);else{for(let i in this.viewHooks)this.viewHooks[i].__disconnected();this.setContainerClasses(re)}}execAll(e){d.all(this.el,`[${e}]`,i=>this.liveSocket.execJS(i,i.getAttribute(e)))}hideLoader(){clearTimeout(this.loaderTimer),clearTimeout(this.disconnectedTimer),this.setContainerClasses(_t),this.execAll(this.binding("connected"))}triggerReconnected(){for(let e in this.viewHooks)this.viewHooks[e].__reconnected()}log(e,i){this.liveSocket.log(this,e,i)}transition(e,i,s=function(){}){this.liveSocket.transition(e,i,s)}withinTargets(e,i,s=document,n){if(e instanceof HTMLElement||e instanceof SVGElement)return this.liveSocket.owner(e,r=>i(r,e));if(Y(e))d.findComponentNodeList(n||this.el,e).length===0?_(`no component found matching phx-target of ${e}`):i(this,parseInt(e));else{let r=Array.from(s.querySelectorAll(e));r.length===0&&_(`nothing found matching the phx-target selector "${e}"`),r.forEach(o=>this.liveSocket.owner(o,a=>i(a,o)))}}applyDiff(e,i,s){this.log(e,()=>["",Je(i)]);let{diff:n,reply:r,events:o,title:a}=Kt.extract(i);s({diff:n,reply:r,events:o}),(typeof a=="string"||e=="mount")&&window.requestAnimationFrame(()=>d.putTitle(a))}onJoin(e){let{rendered:i,container:s,liveview_version:n}=e;if(s){let[r,o]=s;this.el=d.replaceRootContainer(this.el,r,o)}this.childJoins=0,this.joinPending=!0,this.flash=null,this.root===this&&(this.formsForRecovery=this.getFormsForRecovery()),this.isMain()&&window.history.state===null&&F.pushState("replace",{type:"patch",id:this.id,position:this.liveSocket.currentHistoryPosition}),n!==this.liveSocket.version()&&console.error(`LiveView asset version mismatch. JavaScript version ${this.liveSocket.version()} vs. server ${n}. To avoid issues, please ensure that your assets use the same version as the server.`),F.dropLocal(this.liveSocket.localStorage,window.location.pathname,Qt),this.applyDiff("mount",i,({diff:r,events:o})=>{this.rendered=new Kt(this.id,r);let[a,l]=this.renderContainer(null,"join");this.dropPendingRefs(),this.joinCount++,this.joinAttempts=0,this.maybeRecoverForms(a,()=>{this.onJoinComplete(e,a,l,o)})})}dropPendingRefs(){d.all(document,`[${B}="${this.refSrc()}"]`,e=>{e.removeAttribute(me),e.removeAttribute(B),e.removeAttribute(P)})}onJoinComplete({live_patch:e},i,s,n){if(this.joinCount>1||this.parent&&!this.parent.isJoinPending())return this.applyJoinPatch(e,i,s,n);d.findPhxChildrenInFragment(i,this.id).filter(o=>{let a=o.id&&this.el.querySelector(`[id="${o.id}"]`),l=a&&a.getAttribute(ae);return l&&o.setAttribute(ae,l),a&&a.setAttribute(ne,this.root.id),this.joinChild(o)}).length===0?this.parent?(this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,i,s,n)]),this.parent.ackJoin(this)):(this.onAllChildJoinsComplete(),this.applyJoinPatch(e,i,s,n)):this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,i,s,n)])}attachTrueDocEl(){this.el=d.byId(this.id),this.el.setAttribute(ne,this.root.id)}execNewMounted(e=this.el){let i=this.binding(ht),s=this.binding(dt);d.all(e,`[${i}], [${s}]`,n=>{this.ownsElement(n)&&(d.maintainPrivateHooks(n,n,i,s),this.maybeAddNewHook(n))}),d.all(e,`[${this.binding(ke)}], [data-phx-${ke}]`,n=>{this.ownsElement(n)&&this.maybeAddNewHook(n)}),d.all(e,`[${this.binding(Lt)}]`,n=>{this.ownsElement(n)&&this.maybeMounted(n)})}applyJoinPatch(e,i,s,n){this.attachTrueDocEl();let r=new je(this,this.el,this.id,i,s,null);if(r.markPrunableContentForRemoval(),this.performPatch(r,!1,!0),this.joinNewChildren(),this.execNewMounted(),this.joinPending=!1,this.liveSocket.dispatchEvents(n),this.applyPendingUpdates(),e){let{kind:o,to:a}=e;this.liveSocket.historyPatch(a,o)}this.hideLoader(),this.joinCount>1&&this.triggerReconnected(),this.stopCallback()}triggerBeforeUpdateHook(e,i){this.liveSocket.triggerDOM("onBeforeElUpdated",[e,i]);let s=this.getHook(e),n=s&&d.isIgnored(e,this.binding(qe));if(s&&!e.isEqualNode(i)&&!(n&&Zi(e.dataset,i.dataset)))return s.__beforeUpdate(),s}maybeMounted(e){let i=e.getAttribute(this.binding(Lt)),s=i&&d.private(e,"mounted");i&&!s&&(this.liveSocket.execJS(e,i),d.putPrivate(e,"mounted",!0))}maybeAddNewHook(e){let i=this.addHook(e);i&&i.__mounted()}performPatch(e,i,s=!1){let n=[],r=!1,o=new Set;return this.liveSocket.triggerDOM("onPatchStart",[e.targetContainer]),e.after("added",a=>{this.liveSocket.triggerDOM("onNodeAdded",[a]);let l=this.binding(ht),h=this.binding(dt);d.maintainPrivateHooks(a,a,l,h),this.maybeAddNewHook(a),a.getAttribute&&this.maybeMounted(a)}),e.after("phxChildAdded",a=>{d.isPhxSticky(a)?this.liveSocket.joinRootViews():r=!0}),e.before("updated",(a,l)=>{this.triggerBeforeUpdateHook(a,l)&&o.add(a.id)}),e.after("updated",a=>{o.has(a.id)&&this.getHook(a).__updated()}),e.after("discarded",a=>{a.nodeType===Node.ELEMENT_NODE&&n.push(a)}),e.after("transitionsDiscarded",a=>this.afterElementsRemoved(a,i)),e.perform(s),this.afterElementsRemoved(n,i),this.liveSocket.triggerDOM("onPatchEnd",[e.targetContainer]),r}afterElementsRemoved(e,i){let s=[];e.forEach(n=>{let r=d.all(n,`[${Q}]`),o=d.all(n,`[${this.binding(ke)}], [data-phx-hook]`);r.concat(n).forEach(a=>{let l=this.componentID(a);Y(l)&&s.indexOf(l)===-1&&s.push(l)}),o.concat(n).forEach(a=>{let l=this.getHook(a);l&&this.destroyHook(l)})}),i&&this.maybePushComponentsDestroyed(s)}joinNewChildren(){d.findPhxChildren(this.el,this.id).forEach(e=>this.joinChild(e))}maybeRecoverForms(e,i){let s=this.binding("change"),n=this.root.formsForRecovery,r=document.createElement("template");r.innerHTML=e;let o=r.content.firstElementChild;o.id=this.id,o.setAttribute(ne,this.root.id),o.setAttribute(z,this.getSession()),o.setAttribute(ae,this.getStatic()),o.setAttribute(le,this.parent?this.parent.id:null);let a=d.all(r.content,"form").filter(l=>l.id&&n[l.id]).filter(l=>!this.pendingForms.has(l.id)).filter(l=>n[l.id].getAttribute(s)===l.getAttribute(s)).map(l=>[n[l.id],l]);if(a.length===0)return i();a.forEach(([l,h],u)=>{this.pendingForms.add(h.id),this.pushFormRecovery(l,h,r.content.firstElementChild,()=>{this.pendingForms.delete(h.id),u===a.length-1&&i()})})}getChildById(e){return this.root.children[this.id][e]}getDescendentByEl(e){return e.id===this.id?this:this.children[e.getAttribute(le)]?.[e.id]}destroyDescendent(e){for(let i in this.root.children)for(let s in this.root.children[i])if(s===e)return this.root.children[i][s].destroy()}joinChild(e){if(!this.getChildById(e.id)){let s=new oi(e,this.liveSocket,this);return this.root.children[this.id][s.id]=s,s.join(),this.childJoins++,!0}}isJoinPending(){return this.joinPending}ackJoin(e){this.childJoins--,this.childJoins===0&&(this.parent?this.parent.ackJoin(this):this.onAllChildJoinsComplete())}onAllChildJoinsComplete(){this.pendingForms.clear(),this.formsForRecovery={},this.joinCallback(()=>{this.pendingJoinOps.forEach(([e,i])=>{e.isDestroyed()||i()}),this.pendingJoinOps=[]})}update(e,i){if(this.isJoinPending()||this.liveSocket.hasPendingLink()&&this.root.isMain())return this.pendingDiffs.push({diff:e,events:i});this.rendered.mergeDiff(e);let s=!1;this.rendered.isComponentOnlyDiff(e)?this.liveSocket.time("component patch complete",()=>{d.findExistingParentCIDs(this.el,this.rendered.componentCIDs(e)).forEach(r=>{this.componentPatch(this.rendered.getComponent(e,r),r)&&(s=!0)})}):$t(e)||this.liveSocket.time("full patch complete",()=>{let[n,r]=this.renderContainer(e,"update"),o=new je(this,this.el,this.id,n,r,null);s=this.performPatch(o,!0)}),this.liveSocket.dispatchEvents(i),s&&this.joinNewChildren()}renderContainer(e,i){return this.liveSocket.time(`toString diff (${i})`,()=>{let s=this.el.tagName,n=e?this.rendered.componentCIDs(e):null,[r,o]=this.rendered.toString(n);return[`<${s}>${r}</${s}>`,o]})}componentPatch(e,i){if($t(e))return!1;let[s,n]=this.rendered.componentToString(i),r=new je(this,this.el,this.id,s,n,i);return this.performPatch(r,!0)}getHook(e){return this.viewHooks[Re.elementID(e)]}addHook(e){let i=Re.elementID(e);if(!(e.getAttribute&&!this.ownsElement(e)))if(i&&!this.viewHooks[i]){let s=d.getCustomElHook(e)||_(`no hook found for custom element: ${e.id}`);return this.viewHooks[i]=s,s.__attachView(this),s}else{if(i||!e.getAttribute)return;{let s=e.getAttribute(`data-phx-${ke}`)||e.getAttribute(this.binding(ke)),n=this.liveSocket.getHookCallbacks(s);if(n){e.id||_(`no DOM ID for hook "${s}". Hooks require a unique ID on each element.`,e);let r=new Re(this,e,n);return this.viewHooks[Re.elementID(r.el)]=r,r}else s!==null&&_(`unknown hook found for "${s}"`,e)}}}destroyHook(e){let i=Re.elementID(e.el);e.__destroyed(),e.__cleanup__(),delete this.viewHooks[i]}applyPendingUpdates(){this.liveSocket.hasPendingLink()&&this.root.isMain()||(this.pendingDiffs.forEach(({diff:e,events:i})=>this.update(e,i)),this.pendingDiffs=[],this.eachChild(e=>e.applyPendingUpdates()))}eachChild(e){let i=this.root.children[this.id]||{};for(let s in i)e(this.getChildById(s))}onChannel(e,i){this.liveSocket.onChannel(this.channel,e,s=>{this.isJoinPending()?this.root.pendingJoinOps.push([this,()=>i(s)]):this.liveSocket.requestDOMUpdate(()=>i(s))})}bindChannel(){this.liveSocket.onChannel(this.channel,"diff",e=>{this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",e,({diff:i,events:s})=>this.update(i,s))})}),this.onChannel("redirect",({to:e,flash:i})=>this.onRedirect({to:e,flash:i})),this.onChannel("live_patch",e=>this.onLivePatch(e)),this.onChannel("live_redirect",e=>this.onLiveRedirect(e)),this.channel.onError(e=>this.onError(e)),this.channel.onClose(e=>this.onClose(e))}destroyAllChildren(){this.eachChild(e=>e.destroy())}onLiveRedirect(e){let{to:i,kind:s,flash:n}=e,r=this.expandURL(i),o=new CustomEvent("phx:server-navigate",{detail:{to:i,kind:s,flash:n}});this.liveSocket.historyRedirect(o,r,s,n)}onLivePatch(e){let{to:i,kind:s}=e;this.href=this.expandURL(i),this.liveSocket.historyPatch(i,s)}expandURL(e){return e.startsWith("/")?`${window.location.protocol}//${window.location.host}${e}`:e}onRedirect({to:e,flash:i,reloadToken:s}){this.liveSocket.redirect(e,i,s)}isDestroyed(){return this.destroyed}joinDead(){this.isDead=!0}joinPush(){return this.joinPush=this.joinPush||this.channel.join(),this.joinPush}join(e){this.showLoader(this.liveSocket.loaderTimeout),this.bindChannel(),this.isMain()&&(this.stopCallback=this.liveSocket.withPageLoading({to:this.href,kind:"initial"})),this.joinCallback=i=>{i=i||function(){},e?e(this.joinCount,i):i()},this.wrapPush(()=>this.channel.join(),{ok:i=>this.liveSocket.requestDOMUpdate(()=>this.onJoin(i)),error:i=>this.onJoinError(i),timeout:()=>this.onJoinError({reason:"timeout"})})}onJoinError(e){if(e.reason==="reload"){this.log("error",()=>[`failed mount with ${e.status}. Falling back to page reload`,e]),this.onRedirect({to:this.root.href,reloadToken:e.token});return}else if(e.reason==="unauthorized"||e.reason==="stale"){this.log("error",()=>["unauthorized live_redirect. Falling back to page request",e]),this.onRedirect({to:this.root.href,flash:this.flash});return}if((e.redirect||e.live_redirect)&&(this.joinPending=!1,this.channel.leave()),e.redirect)return this.onRedirect(e.redirect);if(e.live_redirect)return this.onLiveRedirect(e.live_redirect);if(this.log("error",()=>["unable to join",e]),this.isMain())this.displayError([re,fe,ye]),this.liveSocket.isConnected()&&this.liveSocket.reloadWithJitter(this);else{this.joinAttempts>=Ot&&(this.root.displayError([re,fe,ye]),this.log("error",()=>[`giving up trying to mount after ${Ot} tries`,e]),this.destroy());let i=d.byId(this.el.id);i?(d.mergeAttrs(i,this.el),this.displayError([re,fe,ye]),this.el=i):this.destroy()}}onClose(e){if(!this.isDestroyed()){if(this.isMain()&&this.liveSocket.hasPendingLink()&&e!=="leave")return this.liveSocket.reloadWithJitter(this);this.destroyAllChildren(),this.liveSocket.dropActiveElement(this),document.activeElement&&document.activeElement.blur(),this.liveSocket.isUnloaded()&&this.showLoader(Vi)}}onError(e){this.onClose(e),this.liveSocket.isConnected()&&this.log("error",()=>["view crashed",e]),this.liveSocket.isUnloaded()||(this.liveSocket.isConnected()?this.displayError([re,fe,ye]):this.displayError([re,fe,Rt]))}displayError(e){this.isMain()&&d.dispatchEvent(window,"phx:page-loading-start",{detail:{to:this.href,kind:"error"}}),this.showLoader(),this.setContainerClasses(...e),this.delayedDisconnected()}delayedDisconnected(){this.disconnectedTimer=setTimeout(()=>{this.execAll(this.binding("disconnected"))},this.liveSocket.disconnectedTimeout)}wrapPush(e,i){let s=this.liveSocket.getLatencySim(),n=s?r=>setTimeout(()=>!this.isDestroyed()&&r(),s):r=>!this.isDestroyed()&&r();n(()=>{e().receive("ok",r=>n(()=>i.ok&&i.ok(r))).receive("error",r=>n(()=>i.error&&i.error(r))).receive("timeout",()=>n(()=>i.timeout&&i.timeout()))})}pushWithReply(e,i,s){if(!this.isConnected())return Promise.reject({error:"noconnection"});let[n,[r],o]=e?e():[null,[],{}],a=this.joinCount,l=function(){};return o.page_loading&&(l=this.liveSocket.withPageLoading({kind:"element",target:r})),typeof s.cid!="number"&&delete s.cid,new Promise((h,u)=>{this.wrapPush(()=>this.channel.push(i,s,Xi),{ok:p=>{n!==null&&(this.lastAckRef=n);let m=g=>{p.redirect&&this.onRedirect(p.redirect),p.live_patch&&this.onLivePatch(p.live_patch),p.live_redirect&&this.onLiveRedirect(p.live_redirect),l(),h({resp:p,reply:g})};p.diff?this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",p.diff,({diff:g,reply:c,events:v})=>{n!==null&&this.undoRefs(n,s.event),this.update(g,v),m(c)})}):(n!==null&&this.undoRefs(n,s.event),m(null))},error:p=>u({error:p}),timeout:()=>{u({timeout:!0}),this.joinCount===a&&this.liveSocket.reloadWithJitter(this,()=>{this.log("timeout",()=>["received timeout while communicating with server. Falling back to hard refresh for recovery"])})}})})}undoRefs(e,i,s){if(!this.isConnected())return;let n=`[${B}="${this.refSrc()}"]`;s?(s=new Set(s),d.all(document,n,r=>{s&&!s.has(r)||(d.all(r,n,o=>this.undoElRef(o,e,i)),this.undoElRef(r,e,i))})):d.all(document,n,r=>this.undoElRef(r,e,i))}undoElRef(e,i,s){new pt(e).maybeUndo(i,s,r=>{let o=new je(this,e,this.id,r,[],null,{undoRef:i}),a=this.performPatch(o,!0);d.all(e,`[${B}="${this.refSrc()}"]`,l=>this.undoElRef(l,i,s)),a&&this.joinNewChildren()})}refSrc(){return this.el.id}putRef(e,i,s,n={}){let r=this.ref++,o=this.binding(Pt);if(n.loading){let a=d.all(document,n.loading).map(l=>({el:l,lock:!0,loading:!0}));e=e.concat(a)}for(let{el:a,lock:l,loading:h}of e){if(!l&&!h)throw new Error("putRef requires lock or loading");if(a.setAttribute(B,this.refSrc()),h&&a.setAttribute(me,r),l&&a.setAttribute(P,r),!h||n.submitter&&!(a===n.submitter||a===n.form))continue;let u=new Promise(c=>{a.addEventListener(`phx:undo-lock:${r}`,()=>c(g),{once:!0})}),p=new Promise(c=>{a.addEventListener(`phx:undo-loading:${r}`,()=>c(g),{once:!0})});a.classList.add(`phx-${s}-loading`);let m=a.getAttribute(o);m!==null&&(a.getAttribute(Xe)||a.setAttribute(Xe,a.innerText),m!==""&&(a.innerText=m),a.setAttribute(ge,a.getAttribute(ge)||a.disabled),a.setAttribute("disabled",""));let g={event:i,eventType:s,ref:r,isLoading:h,isLocked:l,lockElements:e.filter(({lock:c})=>c).map(({el:c})=>c),loadingElements:e.filter(({loading:c})=>c).map(({el:c})=>c),unlock:c=>{c=Array.isArray(c)?c:[c],this.undoRefs(r,i,c)},lockComplete:u,loadingComplete:p,lock:c=>new Promise(v=>{if(this.isAcked(r))return v(g);c.setAttribute(P,r),c.setAttribute(B,this.refSrc()),c.addEventListener(`phx:lock-stop:${r}`,()=>v(g),{once:!0})})};a.dispatchEvent(new CustomEvent("phx:push",{detail:g,bubbles:!0,cancelable:!1})),i&&a.dispatchEvent(new CustomEvent(`phx:push:${i}`,{detail:g,bubbles:!0,cancelable:!1}))}return[r,e.map(({el:a})=>a),n]}isAcked(e){return this.lastAckRef!==null&&this.lastAckRef>=e}componentID(e){let i=e.getAttribute&&e.getAttribute(Q);return i?parseInt(i):null}targetComponentID(e,i,s={}){if(Y(i))return i;let n=s.target||e.getAttribute(this.binding("target"));return Y(n)?parseInt(n):i&&(n!==null||s.target)?this.closestComponentID(i):null}closestComponentID(e){return Y(e)?e:e?se(e.closest(`[${Q}]`),i=>this.ownsElement(i)&&this.componentID(i)):null}pushHookEvent(e,i,s,n,r){if(!this.isConnected())return this.log("hook",()=>["unable to push hook event. LiveView not connected",s,n]),!1;let[o,a,l]=this.putRef([{el:e,loading:!0,lock:!0}],s,"hook");return this.pushWithReply(()=>[o,a,l],"event",{type:"hook",event:s,value:n,cid:this.closestComponentID(i)}).then(({resp:h,reply:u})=>r(u,o)),o}extractMeta(e,i,s){let n=this.binding("value-");for(let r=0;r<e.attributes.length;r++){i||(i={});let o=e.attributes[r].name;o.startsWith(n)&&(i[o.replace(n,"")]=e.getAttribute(o))}if(e.value!==void 0&&!(e instanceof HTMLFormElement)&&(i||(i={}),i.value=e.value,e.tagName==="INPUT"&&si.indexOf(e.type)>=0&&!e.checked&&delete i.value),s){i||(i={});for(let r in s)i[r]=s[r]}return i}pushEvent(e,i,s,n,r,o={},a){this.pushWithReply(()=>this.putRef([{el:i,loading:!0,lock:!0}],n,e,o),"event",{type:e,event:n,value:this.extractMeta(i,r,o.value),cid:this.targetComponentID(i,s,o)}).then(({reply:l})=>a&&a(l)).catch(l=>_("Failed to push event",l))}pushFileProgress(e,i,s,n=function(){}){this.liveSocket.withinOwners(e.form,(r,o)=>{r.pushWithReply(null,"progress",{event:e.getAttribute(r.binding(ji)),ref:e.getAttribute(Z),entry_ref:i,progress:s,cid:r.targetComponentID(e.form,o)}).then(({resp:a})=>n(a)).catch(a=>_("Failed to push file progress",a))})}pushInput(e,i,s,n,r,o){if(!e.form)throw new Error("form events require the input to be inside a form");let a,l=Y(s)?s:this.targetComponentID(e.form,i,r),h=()=>this.putRef([{el:e,loading:!0,lock:!0},{el:e.form,loading:!0,lock:!0}],n,"change",r),u,p=this.extractMeta(e.form,{},r.value),m={};e instanceof HTMLButtonElement&&(m.submitter=e),e.getAttribute(this.binding("change"))?u=Be(e.form,m,[e.name]):u=Be(e.form,m),d.isUploadInput(e)&&e.files&&e.files.length>0&&L.trackFiles(e,Array.from(e.files)),a=L.serializeUploads(e);let g={type:"form",event:n,value:u,meta:{_target:r._target||"undefined",...p},uploads:a,cid:l};this.pushWithReply(h,"event",g).then(({resp:c})=>{d.isUploadInput(e)&&d.isAutoUpload(e)?pt.onUnlock(e,()=>{if(L.filesAwaitingPreflight(e).length>0){let[v,w]=h();this.undoRefs(v,n,[e.form]),this.uploadFiles(e.form,n,i,v,l,I=>{o&&o(c),this.triggerAwaitingSubmit(e.form,n),this.undoRefs(v,n)})}}):o&&o(c)}).catch(c=>_("Failed to push input event",c))}triggerAwaitingSubmit(e,i){let s=this.getScheduledSubmit(e);if(s){let[n,r,o,a]=s;this.cancelSubmit(e,i),a()}}getScheduledSubmit(e){return this.formSubmits.find(([i,s,n,r])=>i.isSameNode(e))}scheduleSubmit(e,i,s,n){if(this.getScheduledSubmit(e))return!0;this.formSubmits.push([e,i,s,n])}cancelSubmit(e,i){this.formSubmits=this.formSubmits.filter(([s,n,r,o])=>s.isSameNode(e)?(this.undoRefs(n,i),!1):!0)}disableForm(e,i,s={}){let n=c=>!(Pe(c,`${this.binding(qe)}=ignore`,c.form)||Pe(c,"data-phx-update=ignore",c.form)),r=c=>c.hasAttribute(this.binding(Pt)),o=c=>c.tagName=="BUTTON",a=c=>["INPUT","TEXTAREA","SELECT"].includes(c.tagName),l=Array.from(e.elements),h=l.filter(r),u=l.filter(o).filter(n),p=l.filter(a).filter(n);u.forEach(c=>{c.setAttribute(ge,c.disabled),c.disabled=!0}),p.forEach(c=>{c.setAttribute(ut,c.readOnly),c.readOnly=!0,c.files&&(c.setAttribute(ge,c.disabled),c.disabled=!0)});let m=h.concat(u).concat(p).map(c=>({el:c,loading:!0,lock:!0})),g=[{el:e,loading:!0,lock:!1}].concat(m).reverse();return this.putRef(g,i,"submit",s)}pushFormSubmit(e,i,s,n,r,o){let a=()=>this.disableForm(e,s,{...r,form:e,submitter:n}),l=this.targetComponentID(e,i);if(L.hasUploadsInProgress(e)){let[h,u]=a(),p=()=>this.pushFormSubmit(e,i,s,n,r,o);return this.scheduleSubmit(e,h,r,p)}else if(L.inputsAwaitingPreflight(e).length>0){let[h,u]=a(),p=()=>[h,u,r];this.uploadFiles(e,s,i,h,l,m=>{if(L.inputsAwaitingPreflight(e).length>0)return this.undoRefs(h,s);let g=this.extractMeta(e,{},r.value),c=Be(e,{submitter:n});this.pushWithReply(p,"event",{type:"form",event:s,value:c,meta:g,cid:l}).then(({resp:v})=>o(v)).catch(v=>_("Failed to push form submit",v))})}else if(!(e.hasAttribute(B)&&e.classList.contains("phx-submit-loading"))){let h=this.extractMeta(e,{},r.value),u=Be(e,{submitter:n});this.pushWithReply(a,"event",{type:"form",event:s,value:u,meta:h,cid:l}).then(({resp:p})=>o(p)).catch(p=>_("Failed to push form submit",p))}}uploadFiles(e,i,s,n,r,o){let a=this.joinCount,l=L.activeFileInputs(e),h=l.length;l.forEach(u=>{let p=new L(u,this,()=>{h--,h===0&&o()}),m=p.entries().map(c=>c.toPreflightPayload());if(m.length===0){h--;return}let g={ref:u.getAttribute(Z),entries:m,cid:this.targetComponentID(u.form,s)};this.log("upload",()=>["sending preflight request",g]),this.pushWithReply(null,"allow_upload",g).then(({resp:c})=>{if(this.log("upload",()=>["got preflight response",c]),p.entries().forEach(v=>{c.entries&&!c.entries[v.ref]&&this.handleFailedEntryPreflight(v.ref,"failed preflight",p)}),c.error||Object.keys(c.entries).length===0)this.undoRefs(n,i),(c.error||[]).map(([w,I])=>{this.handleFailedEntryPreflight(w,I,p)});else{let v=w=>{this.channel.onError(()=>{this.joinCount===a&&w()})};p.initAdapterUpload(c,v,this.liveSocket)}}).catch(c=>_("Failed to push upload",c))})}handleFailedEntryPreflight(e,i,s){if(s.isAutoUpload()){let n=s.entries().find(r=>r.ref===e.toString());n&&n.cancel()}else s.entries().map(n=>n.cancel());this.log("upload",()=>[`error for entry ${e}`,i])}dispatchUploads(e,i,s){let n=this.targetCtxElement(e)||this.el,r=d.findUploadInputs(n).filter(o=>o.name===i);r.length===0?_(`no live file inputs found matching the name "${i}"`):r.length>1?_(`duplicate live file inputs found matching the name "${i}"`):d.dispatchEvent(r[0],ei,{detail:{files:s}})}targetCtxElement(e){if(Y(e)){let[i]=d.findComponentNodeList(this.el,e);return i}else return e||null}pushFormRecovery(e,i,s,n){let r=this.binding("change"),o=i.getAttribute(this.binding("target"))||i,a=i.getAttribute(this.binding(xt))||i.getAttribute(this.binding("change")),l=Array.from(e.elements).filter(p=>d.isFormInput(p)&&p.name&&!p.hasAttribute(r));if(l.length===0)return;l.forEach(p=>p.hasAttribute(Z)&&L.clearFiles(p));let h=l.find(p=>p.type!=="hidden")||l[0],u=0;this.withinTargets(o,(p,m)=>{let g=this.targetComponentID(i,m);u++;let c=new CustomEvent("phx:form-recovery",{detail:{sourceElement:e}});E.exec(c,"change",a,this,h,["push",{_target:h.name,targetView:p,targetCtx:m,newCid:g,callback:()=>{u--,u===0&&n()}}])},s,s)}pushLinkPatch(e,i,s,n){let r=this.liveSocket.setPendingLink(i),o=e.isTrusted&&e.type!=="popstate",a=s?()=>this.putRef([{el:s,loading:o,lock:!0}],null,"click"):null,l=()=>this.liveSocket.redirect(window.location.href),h=i.startsWith("/")?`${location.protocol}//${location.host}${i}`:i;this.pushWithReply(a,"live_patch",{url:h}).then(({resp:u})=>{this.liveSocket.requestDOMUpdate(()=>{u.link_redirect?this.liveSocket.replaceMain(i,null,n,r):(this.liveSocket.commitPendingLink(r)&&(this.href=i),this.applyPendingUpdates(),n&&n(r))})},({error:u,timeout:p})=>l())}getFormsForRecovery(){if(this.joinCount===0)return{};let e=this.binding("change");return d.all(this.el,`form[${e}]`).filter(i=>i.id).filter(i=>i.elements.length>0).filter(i=>i.getAttribute(this.binding(xt))!=="ignore").map(i=>i.cloneNode(!0)).reduce((i,s)=>(i[s.id]=s,i),{})}maybePushComponentsDestroyed(e){let i=e.filter(s=>d.findComponentNodeList(this.el,s).length===0);i.length>0&&(i.forEach(s=>this.rendered.resetRender(s)),this.pushWithReply(null,"cids_will_destroy",{cids:i}).then(()=>{this.liveSocket.requestDOMUpdate(()=>{let s=i.filter(n=>d.findComponentNodeList(this.el,n).length===0);s.length>0&&this.pushWithReply(null,"cids_destroyed",{cids:s}).then(({resp:n})=>{this.rendered.pruneCIDs(n.cids)}).catch(n=>_("Failed to push components destroyed",n))})}).catch(s=>_("Failed to push components destroyed",s)))}ownsElement(e){let i=e.closest(he);return e.getAttribute(le)===this.id||i&&i.id===this.id||!i&&this.isDead}submitForm(e,i,s,n,r={}){d.putPrivate(e,xe,!0),Array.from(e.elements).forEach(a=>d.putPrivate(a,xe,!0)),this.liveSocket.blurActiveElement(this),this.pushFormSubmit(e,i,s,n,r,()=>{this.liveSocket.restorePreviouslyActiveFocus()})}binding(e){return this.liveSocket.binding(e)}};var ai=class{constructor(t,e,i={}){if(this.unloaded=!1,!e||e.constructor.name==="Object")throw new Error(`
      a phoenix Socket must be provided as the second argument to the LiveSocket constructor. For example:

          import {Socket} from "phoenix"
          import {LiveSocket} from "phoenix_live_view"
          let liveSocket = new LiveSocket("/live", Socket, {...})
      `);this.socket=new e(t,i),this.bindingPrefix=i.bindingPrefix||Wi,this.opts=i,this.params=Ee(i.params||{}),this.viewLogger=i.viewLogger,this.metadataCallbacks=i.metadata||{},this.defaults=Object.assign(Je(qi),i.defaults||{}),this.activeElement=null,this.prevActive=null,this.silenced=!1,this.main=null,this.outgoingMainEl=null,this.clickStartedAtTarget=null,this.linkRef=1,this.roots={},this.href=window.location.href,this.pendingLink=null,this.currentLocation=Je(window.location),this.hooks=i.hooks||{},this.uploaders=i.uploaders||{},this.loaderTimeout=i.loaderTimeout||Bi,this.disconnectedTimeout=i.disconnectedTimeout||Ji,this.reloadWithJitterTimer=null,this.maxReloads=i.maxReloads||Pi,this.reloadJitterMin=i.reloadJitterMin||xi,this.reloadJitterMax=i.reloadJitterMax||Li,this.failsafeJitter=i.failsafeJitter||Ii,this.localStorage=i.localStorage||window.localStorage,this.sessionStorage=i.sessionStorage||window.sessionStorage,this.boundTopLevelEvents=!1,this.boundEventNames=new Set,this.serverCloseRef=null,this.domCallbacks=Object.assign({jsQuerySelectorAll:null,onPatchStart:Ee(),onPatchEnd:Ee(),onNodeAdded:Ee(),onBeforeElUpdated:Ee()},i.dom||{}),this.transitions=new Rs,this.currentHistoryPosition=parseInt(this.sessionStorage.getItem(Fe))||0,window.addEventListener("pagehide",s=>{this.unloaded=!0}),this.socket.onOpen(()=>{this.isUnloaded()&&window.location.reload()})}version(){return"1.0.11"}isProfileEnabled(){return this.sessionStorage.getItem(st)==="true"}isDebugEnabled(){return this.sessionStorage.getItem(Ne)==="true"}isDebugDisabled(){return this.sessionStorage.getItem(Ne)==="false"}enableDebug(){this.sessionStorage.setItem(Ne,"true")}enableProfiling(){this.sessionStorage.setItem(st,"true")}disableDebug(){this.sessionStorage.setItem(Ne,"false")}disableProfiling(){this.sessionStorage.removeItem(st)}enableLatencySim(t){this.enableDebug(),console.log("latency simulator enabled for the duration of this browser session. Call disableLatencySim() to disable"),this.sessionStorage.setItem(nt,t)}disableLatencySim(){this.sessionStorage.removeItem(nt)}getLatencySim(){let t=this.sessionStorage.getItem(nt);return t?parseInt(t):null}getSocket(){return this.socket}connect(){window.location.hostname==="localhost"&&!this.isDebugDisabled()&&this.enableDebug();let t=()=>{this.resetReloadStatus(),this.joinRootViews()?(this.bindTopLevelEvents(),this.socket.connect()):this.main?this.socket.connect():this.bindTopLevelEvents({dead:!0}),this.joinDeadView()};["complete","loaded","interactive"].indexOf(document.readyState)>=0?t():document.addEventListener("DOMContentLoaded",()=>t())}disconnect(t){clearTimeout(this.reloadWithJitterTimer),this.serverCloseRef&&(this.socket.off(this.serverCloseRef),this.serverCloseRef=null),this.socket.disconnect(t)}replaceTransport(t){clearTimeout(this.reloadWithJitterTimer),this.socket.replaceTransport(t),this.connect()}execJS(t,e,i=null){let s=new CustomEvent("phx:exec",{detail:{sourceElement:t}});this.owner(t,n=>E.exec(s,i,e,n,t))}execJSHookPush(t,e,i,s){this.withinOwners(t,n=>{let r=new CustomEvent("phx:exec",{detail:{sourceElement:t}});E.exec(r,"hook",e,n,t,["push",{data:i,callback:s}])})}unload(){this.unloaded||(this.main&&this.isConnected()&&this.log(this.main,"socket",()=>["disconnect for page nav"]),this.unloaded=!0,this.destroyAllViews(),this.disconnect())}triggerDOM(t,e){this.domCallbacks[t](...e)}time(t,e){if(!this.isProfileEnabled()||!console.time)return e();console.time(t);let i=e();return console.timeEnd(t),i}log(t,e,i){if(this.viewLogger){let[s,n]=i();this.viewLogger(t,e,s,n)}else if(this.isDebugEnabled()){let[s,n]=i();Qi(t,e,s,n)}}requestDOMUpdate(t){this.transitions.after(t)}transition(t,e,i=function(){}){this.transitions.addTransition(t,e,i)}onChannel(t,e,i){t.on(e,s=>{let n=this.getLatencySim();n?setTimeout(()=>i(s),n):i(s)})}reloadWithJitter(t,e){clearTimeout(this.reloadWithJitterTimer),this.disconnect();let i=this.reloadJitterMin,s=this.reloadJitterMax,n=Math.floor(Math.random()*(s-i+1))+i,r=F.updateLocal(this.localStorage,window.location.pathname,Qt,0,o=>o+1);r>=this.maxReloads&&(n=this.failsafeJitter),this.reloadWithJitterTimer=setTimeout(()=>{t.isDestroyed()||t.isConnected()||(t.destroy(),e?e():this.log(t,"join",()=>[`encountered ${r} consecutive reloads`]),r>=this.maxReloads&&this.log(t,"join",()=>[`exceeded ${this.maxReloads} consecutive reloads. Entering failsafe mode`]),this.hasPendingLink()?window.location=this.pendingLink:window.location.reload())},n)}getHookCallbacks(t){return t&&t.startsWith("Phoenix.")?os[t.split(".")[1]]:this.hooks[t]}isUnloaded(){return this.unloaded}isConnected(){return this.socket.isConnected()}getBindingPrefix(){return this.bindingPrefix}binding(t){return`${this.getBindingPrefix()}${t}`}channel(t,e){return this.socket.channel(t,e)}joinDeadView(){let t=document.body;if(t&&!this.isPhxView(t)&&!this.isPhxView(document.firstElementChild)){let e=this.newRootView(t);e.setHref(this.getHref()),e.joinDead(),this.main||(this.main=e),window.requestAnimationFrame(()=>{e.execNewMounted(),this.maybeScroll(history.state?.scroll)})}}joinRootViews(){let t=!1;return d.all(document,`${he}:not([${le}])`,e=>{if(!this.getRootById(e.id)){let i=this.newRootView(e);d.isPhxSticky(e)||i.setHref(this.getHref()),i.join(),e.hasAttribute(mt)&&(this.main=i)}t=!0}),t}redirect(t,e,i){i&&F.setCookie(It,i,60),this.unload(),F.redirect(t,e)}replaceMain(t,e,i=null,s=this.setPendingLink(t)){let n=this.currentLocation.href;this.outgoingMainEl=this.outgoingMainEl||this.main.el;let r=d.findPhxSticky(document)||[],o=d.all(this.outgoingMainEl,`[${this.binding("remove")}]`).filter(l=>!d.isChildOfAny(l,r)),a=d.cloneNode(this.outgoingMainEl,"");this.main.showLoader(this.loaderTimeout),this.main.destroy(),this.main=this.newRootView(a,e,n),this.main.setRedirect(t),this.transitionRemoves(o),this.main.join((l,h)=>{l===1&&this.commitPendingLink(s)&&this.requestDOMUpdate(()=>{o.forEach(u=>u.remove()),r.forEach(u=>a.appendChild(u)),this.outgoingMainEl.replaceWith(a),this.outgoingMainEl=null,i&&i(s),h()})})}transitionRemoves(t,e){let i=this.binding("remove"),s=n=>{n.preventDefault(),n.stopImmediatePropagation()};t.forEach(n=>{for(let r of this.boundEventNames)n.addEventListener(r,s,!0);this.execJS(n,n.getAttribute(i),"remove")}),this.requestDOMUpdate(()=>{t.forEach(n=>{for(let r of this.boundEventNames)n.removeEventListener(r,s,!0)}),e&&e()})}isPhxView(t){return t.getAttribute&&t.getAttribute(z)!==null}newRootView(t,e,i){let s=new _s(t,this,null,e,i);return this.roots[s.id]=s,s}owner(t,e){let i=se(t.closest(he),s=>this.getViewByEl(s))||this.main;return i&&e?e(i):i}withinOwners(t,e){this.owner(t,i=>e(i,t))}getViewByEl(t){let e=t.getAttribute(ne);return se(this.getRootById(e),i=>i.getDescendentByEl(t))}getRootById(t){return this.roots[t]}destroyAllViews(){for(let t in this.roots)this.roots[t].destroy(),delete this.roots[t];this.main=null}destroyViewByEl(t){let e=this.getRootById(t.getAttribute(ne));e&&e.id===t.id?(e.destroy(),delete this.roots[e.id]):e&&e.destroyDescendent(t.id)}getActiveElement(){return document.activeElement}dropActiveElement(t){this.prevActive&&t.ownsElement(this.prevActive)&&(this.prevActive=null)}restorePreviouslyActiveFocus(){this.prevActive&&this.prevActive!==document.body&&this.prevActive.focus()}blurActiveElement(){this.prevActive=this.getActiveElement(),this.prevActive!==document.body&&this.prevActive.blur()}bindTopLevelEvents({dead:t}={}){this.boundTopLevelEvents||(this.boundTopLevelEvents=!0,this.serverCloseRef=this.socket.onClose(e=>{if(e&&e.code===1e3&&this.main)return this.reloadWithJitter(this.main)}),document.body.addEventListener("click",function(){}),window.addEventListener("pageshow",e=>{e.persisted&&(this.getSocket().disconnect(),this.withPageLoading({to:window.location.href,kind:"redirect"}),window.location.reload())},!0),t||this.bindNav(),this.bindClicks(),t||this.bindForms(),this.bind({keyup:"keyup",keydown:"keydown"},(e,i,s,n,r,o)=>{let a=n.getAttribute(this.binding($i)),l=e.key&&e.key.toLowerCase();if(a&&a.toLowerCase()!==l)return;let h={key:e.key,...this.eventMeta(i,e,n)};E.exec(e,i,r,s,n,["push",{data:h}])}),this.bind({blur:"focusout",focus:"focusin"},(e,i,s,n,r,o)=>{if(!o){let a={key:e.key,...this.eventMeta(i,e,n)};E.exec(e,i,r,s,n,["push",{data:a}])}}),this.bind({blur:"blur",focus:"focus"},(e,i,s,n,r,o)=>{if(o==="window"){let a=this.eventMeta(i,e,n);E.exec(e,i,r,s,n,["push",{data:a}])}}),this.on("dragover",e=>e.preventDefault()),this.on("drop",e=>{e.preventDefault();let i=se(Pe(e.target,this.binding(Et)),r=>r.getAttribute(this.binding(Et))),s=i&&document.getElementById(i),n=Array.from(e.dataTransfer.files||[]);!s||s.disabled||n.length===0||!(s.files instanceof FileList)||(L.trackFiles(s,n,e.dataTransfer),s.dispatchEvent(new Event("input",{bubbles:!0})))}),this.on(ei,e=>{let i=e.target;if(!d.isUploadInput(i))return;let s=Array.from(e.detail.files||[]).filter(n=>n instanceof File||n instanceof Blob);L.trackFiles(i,s),i.dispatchEvent(new Event("input",{bubbles:!0}))}))}eventMeta(t,e,i){let s=this.metadataCallbacks[t];return s?s(e,i):{}}setPendingLink(t){return this.linkRef++,this.pendingLink=t,this.resetReloadStatus(),this.linkRef}resetReloadStatus(){F.deleteCookie(It)}commitPendingLink(t){return this.linkRef!==t?!1:(this.href=this.pendingLink,this.pendingLink=null,!0)}getHref(){return this.href}hasPendingLink(){return!!this.pendingLink}bind(t,e){for(let i in t){let s=t[i];this.on(s,n=>{let r=this.binding(i),o=this.binding(`window-${i}`),a=n.target.getAttribute&&n.target.getAttribute(r);a?this.debounce(n.target,n,s,()=>{this.withinOwners(n.target,l=>{e(n,i,l,n.target,a,null)})}):d.all(document,`[${o}]`,l=>{let h=l.getAttribute(o);this.debounce(l,n,s,()=>{this.withinOwners(l,u=>{e(n,i,u,l,h,"window")})})})})}}bindClicks(){this.on("mousedown",t=>this.clickStartedAtTarget=t.target),this.bindClick("click","click")}bindClick(t,e){let i=this.binding(e);window.addEventListener(t,s=>{let n=null;s.detail===0&&(this.clickStartedAtTarget=s.target);let r=this.clickStartedAtTarget||s.target;n=Pe(s.target,i),this.dispatchClickAway(s,r),this.clickStartedAtTarget=null;let o=n&&n.getAttribute(i);if(!o){d.isNewPageClick(s,window.location)&&this.unload();return}n.getAttribute("href")==="#"&&s.preventDefault(),!n.hasAttribute(B)&&this.debounce(n,s,"click",()=>{this.withinOwners(n,a=>{E.exec(s,"click",o,a,n,["push",{data:this.eventMeta("click",s,n)}])})})},!1)}dispatchClickAway(t,e){let i=this.binding("click-away");d.all(document,`[${i}]`,s=>{s.isSameNode(e)||s.contains(e)||this.withinOwners(s,n=>{let r=s.getAttribute(i);E.isVisible(s)&&E.isInViewport(s)&&E.exec(t,"click",r,n,s,["push",{data:this.eventMeta("click",t,t.target)}])})})}bindNav(){if(!F.canPushState())return;history.scrollRestoration&&(history.scrollRestoration="manual");let t=null;window.addEventListener("scroll",e=>{clearTimeout(t),t=setTimeout(()=>{F.updateCurrentState(i=>Object.assign(i,{scroll:window.scrollY}))},100)}),window.addEventListener("popstate",e=>{if(!this.registerNewLocation(window.location))return;let{type:i,backType:s,id:n,scroll:r,position:o}=e.state||{},a=window.location.href,l=o>this.currentHistoryPosition;i=l?i:s||i,this.currentHistoryPosition=o||0,this.sessionStorage.setItem(Fe,this.currentHistoryPosition.toString()),d.dispatchEvent(window,"phx:navigate",{detail:{href:a,patch:i==="patch",pop:!0,direction:l?"forward":"backward"}}),this.requestDOMUpdate(()=>{let h=()=>{this.maybeScroll(r)};this.main.isConnected()&&i==="patch"&&n===this.main.id?this.main.pushLinkPatch(e,a,null,h):this.replaceMain(a,null,h)})},!1),window.addEventListener("click",e=>{let i=Pe(e.target,tt),s=i&&i.getAttribute(tt);if(!s||!this.isConnected()||!this.main||d.wantsNewTab(e))return;let n=i.href instanceof SVGAnimatedString?i.href.baseVal:i.href,r=i.getAttribute(Di);e.preventDefault(),e.stopImmediatePropagation(),this.pendingLink!==n&&this.requestDOMUpdate(()=>{if(s==="patch")this.pushHistoryPatch(e,n,r,i);else if(s==="redirect")this.historyRedirect(e,n,r,null,i);else throw new Error(`expected ${tt} to be "patch" or "redirect", got: ${s}`);let o=i.getAttribute(this.binding("click"));o&&this.requestDOMUpdate(()=>this.execJS(i,o,"click"))})},!1)}maybeScroll(t){typeof t=="number"&&requestAnimationFrame(()=>{window.scrollTo(0,t)})}dispatchEvent(t,e={}){d.dispatchEvent(window,`phx:${t}`,{detail:e})}dispatchEvents(t){t.forEach(([e,i])=>this.dispatchEvent(e,i))}withPageLoading(t,e){d.dispatchEvent(window,"phx:page-loading-start",{detail:t});let i=()=>d.dispatchEvent(window,"phx:page-loading-stop",{detail:t});return e?e(i):i}pushHistoryPatch(t,e,i,s){if(!this.isConnected()||!this.main.isMain())return F.redirect(e);this.withPageLoading({to:e,kind:"patch"},n=>{this.main.pushLinkPatch(t,e,s,r=>{this.historyPatch(e,i,r),n()})})}historyPatch(t,e,i=this.setPendingLink(t)){this.commitPendingLink(i)&&(this.currentHistoryPosition++,this.sessionStorage.setItem(Fe,this.currentHistoryPosition.toString()),F.updateCurrentState(s=>({...s,backType:"patch"})),F.pushState(e,{type:"patch",id:this.main.id,position:this.currentHistoryPosition},t),d.dispatchEvent(window,"phx:navigate",{detail:{patch:!0,href:t,pop:!1,direction:"forward"}}),this.registerNewLocation(window.location))}historyRedirect(t,e,i,s,n){let r=n&&t.isTrusted&&t.type!=="popstate";if(r&&n.classList.add("phx-click-loading"),!this.isConnected()||!this.main.isMain())return F.redirect(e,s);if(/^\/$|^\/[^\/]+.*$/.test(e)){let{protocol:a,host:l}=window.location;e=`${a}//${l}${e}`}let o=window.scrollY;this.withPageLoading({to:e,kind:"redirect"},a=>{this.replaceMain(e,s,l=>{l===this.linkRef&&(this.currentHistoryPosition++,this.sessionStorage.setItem(Fe,this.currentHistoryPosition.toString()),F.updateCurrentState(h=>({...h,backType:"redirect"})),F.pushState(i,{type:"redirect",id:this.main.id,scroll:o,position:this.currentHistoryPosition},e),d.dispatchEvent(window,"phx:navigate",{detail:{href:e,patch:!1,pop:!1,direction:"forward"}}),this.registerNewLocation(window.location)),r&&n.classList.remove("phx-click-loading"),a()})})}registerNewLocation(t){let{pathname:e,search:i}=this.currentLocation;return e+i===t.pathname+t.search?!1:(this.currentLocation=Je(t),!0)}bindForms(){let t=0,e=!1;this.on("submit",i=>{let s=i.target.getAttribute(this.binding("submit")),n=i.target.getAttribute(this.binding("change"));!e&&n&&!s&&(e=!0,i.preventDefault(),this.withinOwners(i.target,r=>{r.disableForm(i.target),window.requestAnimationFrame(()=>{d.isUnloadableFormSubmit(i)&&this.unload(),i.target.submit()})}))}),this.on("submit",i=>{let s=i.target.getAttribute(this.binding("submit"));if(!s){d.isUnloadableFormSubmit(i)&&this.unload();return}i.preventDefault(),i.target.disabled=!0,this.withinOwners(i.target,n=>{E.exec(i,"submit",s,n,i.target,["push",{submitter:i.submitter}])})});for(let i of["change","input"])this.on(i,s=>{if(s instanceof CustomEvent&&s.target.form===void 0){if(s.detail&&s.detail.dispatcher)throw new Error(`dispatching a custom ${i} event is only supported on input elements inside a form`);return}let n=this.binding("change"),r=s.target;if(s.isComposing){let g=`composition-listener-${i}`;d.private(r,g)||(d.putPrivate(r,g,!0),r.addEventListener("compositionend",()=>{r.dispatchEvent(new Event(i,{bubbles:!0})),d.deletePrivate(r,g)},{once:!0}));return}let o=r.getAttribute(n),a=r.form&&r.form.getAttribute(n),l=o||a;if(!l||r.type==="number"&&r.validity&&r.validity.badInput)return;let h=o?r:r.form,u=t;t++;let{at:p,type:m}=d.private(r,"prev-iteration")||{};p===u-1&&i==="change"&&m==="input"||(d.putPrivate(r,"prev-iteration",{at:u,type:i}),this.debounce(r,s,i,()=>{this.withinOwners(h,g=>{d.putPrivate(r,We,!0),E.exec(s,"change",l,g,r,["push",{_target:s.target.name,dispatcher:h}])})}))});this.on("reset",i=>{let s=i.target;d.resetForm(s);let n=Array.from(s.elements).find(r=>r.type==="reset");n&&window.requestAnimationFrame(()=>{n.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))})})}debounce(t,e,i,s){if(i==="blur"||i==="focusout")return s();let n=this.binding(Mi),r=this.binding(Ui),o=this.defaults.debounce.toString(),a=this.defaults.throttle.toString();this.withinOwners(t,l=>{let h=()=>!l.isDestroyed()&&document.body.contains(t);d.debounce(t,e,n,o,r,a,h,()=>{s()})})}silenceEvents(t){this.silenced=!0,t(),this.silenced=!1}on(t,e){this.boundEventNames.add(t),window.addEventListener(t,i=>{this.silenced||e(i)})}jsQuerySelectorAll(t,e,i){let s=this.domCallbacks.jsQuerySelectorAll;return s?s(t,e,i):i()}},Rs=class{constructor(){this.transitions=new Set,this.pendingOps=[]}reset(){this.transitions.forEach(t=>{clearTimeout(t),this.transitions.delete(t)}),this.flushPendingOps()}after(t){this.size()===0?t():this.pushPendingOp(t)}addTransition(t,e,i){e();let s=setTimeout(()=>{this.transitions.delete(s),i(),this.flushPendingOps()},t);this.transitions.add(s)}pushPendingOp(t){this.pendingOps.push(t)}size(){return this.transitions.size}flushPendingOps(){if(this.size()>0)return;let t=this.pendingOps.shift();t&&(t(),this.flushPendingOps())}};var Ke=ki(hi());function wt(t,e,i){if(!e||e.length===0||!i||i.length===0)return console.error("\u6CA1\u6709\u8DB3\u591F\u7684\u6570\u636E\u6765\u7ED8\u5236\u56FE\u8868"),null;let s=Ps(e,i),n=e.map(a=>a.issue.slice(-4)),r={series:s,chart:{height:350,type:"line",zoom:{enabled:!1},toolbar:{show:!1},fontFamily:"inherit"},id:"realtime",animations:{enabled:!0,easing:"linear",dynamicAnimation:{speed:1e3}},colors:["#FF4560","#008FFB","#FEB019","#00E396","#775DD0","#F86624"],dataLabels:{enabled:!1},stroke:{curve:"straight",width:2},grid:{row:{colors:["#f3f3f3","transparent"],opacity:.5}},xaxis:{categories:n,title:{text:"\u671F\u53F7"}},yaxis:{title:{text:"\u8EAB\u4EF7"},labels:{formatter:function(a){return a.toFixed(0)}}},tooltip:{y:{formatter:function(a){return` ${a} `}}},legend:{position:"top"}};return new ApexCharts(t,r)}function Ps(t,e){return e.map(i=>{let s=t.map(n=>n.bet_amount_map[i.id]);return console.log("\u6392\u540D\u6570\u636E",s),{name:i.name,data:s}})}var di={mounted(){let t=this.el,e=JSON.parse(this.el.dataset.chartData),i=JSON.parse(this.el.dataset.animals);this.chart=wt(t,e,i),this.chart&&this.chart.render()},updated(){let t=JSON.parse(this.el.dataset.chartData),e=JSON.parse(this.el.dataset.animals);this.chart&&this.chart.destroy(),this.chart=wt(this.el,t,e),this.chart&&this.chart.render()},destroyed(){this.chart&&this.chart.destroy()}},ci={mounted(){this.setupFlashMessages(),this.observer=new MutationObserver(t=>{t.forEach(e=>{e.type==="childList"&&e.addedNodes.length>0&&this.setupFlashMessages()})}),this.observer.observe(this.el,{childList:!0,subtree:!0})},updated(){this.setupFlashMessages()},setupFlashMessages(){this.el.querySelectorAll(".flash-message").forEach(e=>{if(e.dataset.processed)return;e.dataset.processed=!0;let i=e.classList.contains("info")?"info":"error";setTimeout(()=>{e.parentNode&&(e.style.opacity="0",e.style.transform="translateY(-20px)",this.pushEventTo("#flash-messages","lv:clear-flash",{key:i}),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},300))},2e3);let s=e.querySelector(".flash-close");s&&s.addEventListener("click",n=>{n.stopPropagation(),e.style.opacity="0",e.style.transform="translateY(-20px)",setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},300)})})}};var xs=document.querySelector("meta[name='csrf-token']").getAttribute("content"),Ls={RacingChart:di,FlashMessages:ci},ui=new ai("/live",Ct,{longPollFallbackMs:2500,params:{_csrf_token:xs},hooks:Ls});Ke.default.config({barColors:{0:"#29d"},shadowColor:"rgba(0, 0, 0, .3)"});window.addEventListener("phx:page-loading-start",t=>Ke.default.show(300));window.addEventListener("phx:page-loading-stop",t=>Ke.default.hide());ui.connect();window.liveSocket=ui;})();
/**
 * @license MIT
 * topbar 3.0.0
 * http://buunguyen.github.io/topbar
 * Copyright (c) 2024 Buu Nguyen
 */
