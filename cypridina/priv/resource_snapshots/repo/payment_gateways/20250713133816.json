{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "gateway_type", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "gateway_url", "type": "text"}, {"allow_nil?": false, "default": "\"/v1.0/api/order/create\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "create_order_path", "type": "text"}, {"allow_nil?": false, "default": "\"/v1.0/api/order/query\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "query_order_path", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "channel_id", "type": "text"}, {"allow_nil?": false, "default": "100", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "priority", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "min_amount", "type": "decimal"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "max_amount", "type": "decimal"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "supported_currencies", "type": ["array", "text"]}, {"allow_nil?": false, "default": "true", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "enabled", "type": "boolean"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "rate_limit_per_minute", "type": "bigint"}, {"allow_nil?": false, "default": "30", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "timeout_seconds", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "config_data", "type": "map"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_test_at", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "926C76B18271190AC9EBF8BD587C406164B941FD303CFA2CF39CB69D16222026", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "payment_gateways_unique_gateway_name_index", "keys": [{"type": "atom", "value": "name"}], "name": "unique_gateway_name", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "payment_gateways_unique_gateway_type_channel_index", "keys": [{"type": "atom", "value": "gateway_type"}, {"type": "atom", "value": "channel_id"}], "name": "unique_gateway_type_channel", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "payment_gateways"}