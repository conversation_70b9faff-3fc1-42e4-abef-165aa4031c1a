{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "bank_code", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "icon_url", "type": "text"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "sort_order", "type": "bigint"}, {"allow_nil?": false, "default": "\"100\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "min_amount", "type": "decimal"}, {"allow_nil?": false, "default": "\"100000\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "max_amount", "type": "decimal"}, {"allow_nil?": false, "default": "\"0\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "fee_rate", "type": "decimal"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "config_data", "type": "map"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "5B3F93B69EB6C6B372739088278BCD5F036973F3C3B56247320F45B553E47919", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "bank_configs_unique_bank_code_index", "keys": [{"type": "atom", "value": "bank_code"}], "name": "unique_bank_code", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "bank_configs"}