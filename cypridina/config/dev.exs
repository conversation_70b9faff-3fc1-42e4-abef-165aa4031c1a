import Config

database_url =
  System.get_env("DATABASE_URL") ||
    raise """
    environment variable DATABASE_URL is missing.
    For example: ecto://USER:PASS@HOST/DATABASE
    """

# Configure your database
config :cypridina, Cypridina.Repo,
  url: database_url,
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

config :cypridina, CypridinaWeb.Endpoint,
  http: [ip: {0, 0, 0, 0}, port: String.to_integer(System.get_env("PORT") || "4000")],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base:
    System.get_env("SECRET_KEY_BASE") ||
      "WlYBmT9t/xowdqF0srgFeOLybsyAGGS6Z4ItDnBKH9mPPpGTLJJv2FcX+m2flbjm",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:cypridina, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:cypridina, ~w(--watch)]}
  ]

config :cypridina, CypridinaWeb.Endpoint,
  live_reload: [
    web_console_logger: true,
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/cypridina_web/(controllers|live|components)/.*(ex|heex)$"
    ]
  ]

# 生产环境禁用模拟支付
config :cypridina, :use_mock_payment, true

config :cypridina, :racing_game,
  url:
    System.get_env("RACING_GAME_URL") ||
      raise("""
      environment variable RACING_GAME_URL is missing.
      """)

# Enable dev routes for dashboard and mailbox
config :cypridina,
  dev_routes: true,
  token_signing_secret:
    System.get_env("TOKEN_SIGNING_SECRET") || "TeRJ2hFegTbByzX2H4hQ/60/v/ejitQZ"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Include HEEx debug annotations as HTML comments in rendered markup.
  # Changing this configuration will require mix clean and a full recompile.
  debug_heex_annotations: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

# MinIO 开发环境配置
config :ex_aws,
  access_key_id: System.get_env("MINIO_ROOT_USER") || "minioadmin",
  secret_access_key: System.get_env("MINIO_ROOT_PASSWORD") || "minioadmin"

config :ex_aws, :s3,
  host: System.get_env("MINIO_HOST") || "minio",
  bucket: System.get_env("MINIO_BUCKET") || "cypridina"

config :ash, :policies, show_policy_breakdowns?: true
config :ash_authentication, debug_authentication_failures?: true

config :lettuce,
  folders_to_watch: ["lib"],
  compiler_opts: [
    "--ignore-module-conflict",
    # "--verbose"
  ],
  debug: false
