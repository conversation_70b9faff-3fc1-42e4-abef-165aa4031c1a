/* See the Tailwind configuration guide for advanced usage
   https://tailwindcss.com/docs/configuration */

@import "tailwindcss" source(none);
@source "../../deps/cinder";
@source "../../deps/ash_authentication_phoenix";
@source "../../deps/backpex/**/*.*ex";
@source "../../deps/backpex/assets/js/**/*.*js";

@source "../css";
@source "../js";
@source "../../lib/cypridina_web";

/* A Tailwind plugin that makes "hero-#{ICON}" classes available.
   The heroicons installation itself is managed by your mix.exs */
@plugin "../vendor/heroicons";
@plugin "./tailwind.heroicons.js";

/* daisyUI Tailwind Plugin. You can update this file by fetching the latest version with:
   curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui.js
   Make sure to look at the daisyUI changelog: https://daisyui.com/docs/changelog/ */
@plugin "../vendor/daisyui" {
  /* themes: false; */
  themes: light --default, dark --prefersdark, cupcake, cyberpunk;
}

/* daisyUI theme plugin. You can update this file by fetching the latest version with:
  curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui-theme.js
  We ship with two themes, a light one inspired on Phoenix colors and a dark one inspired
  on Elixir colors. Build your own at: https://daisyui.com/theme-generator/ */
@plugin "../vendor/daisyui-theme" {
  name: "dark";
  default: false;
  prefersdark: true;
  color-scheme: "dark";
  --color-base-100: oklch(30.33% 0.016 252.42);
  --color-base-200: oklch(25.26% 0.014 253.1);
  --color-base-300: oklch(20.15% 0.012 254.09);
  --color-base-content: oklch(97.807% 0.029 256.847);
  --color-primary: oklch(58% 0.233 277.117);
  --color-primary-content: oklch(96% 0.018 272.314);
  --color-secondary: oklch(58% 0.233 277.117);
  --color-secondary-content: oklch(96% 0.018 272.314);
  --color-accent: oklch(60% 0.25 292.717);
  --color-accent-content: oklch(96% 0.016 293.756);
  --color-neutral: oklch(37% 0.044 257.287);
  --color-neutral-content: oklch(98% 0.003 247.858);
  --color-info: oklch(58% 0.158 241.966);
  --color-info-content: oklch(97% 0.013 236.62);
  --color-success: oklch(60% 0.118 184.704);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(66% 0.179 58.318);
  --color-warning-content: oklch(98% 0.022 95.277);
  --color-error: oklch(58% 0.253 17.585);
  --color-error-content: oklch(96% 0.015 12.422);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.21875rem;
  --size-field: 0.21875rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;
}

@plugin "../vendor/daisyui-theme" {
  name: "light";
  default: true;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(98% 0 0);
  --color-base-200: oklch(96% 0.001 286.375);
  --color-base-300: oklch(92% 0.004 286.32);
  --color-base-content: oklch(21% 0.006 285.885);
  --color-primary: oklch(70% 0.213 47.604);
  --color-primary-content: oklch(98% 0.016 73.684);
  --color-secondary: oklch(55% 0.027 264.364);
  --color-secondary-content: oklch(98% 0.002 247.839);
  --color-accent: oklch(0% 0 0);
  --color-accent-content: oklch(100% 0 0);
  --color-neutral: oklch(44% 0.017 285.786);
  --color-neutral-content: oklch(98% 0 0);
  --color-info: oklch(62% 0.214 259.815);
  --color-info-content: oklch(97% 0.014 254.604);
  --color-success: oklch(70% 0.14 182.503);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(66% 0.179 58.318);
  --color-warning-content: oklch(98% 0.022 95.277);
  --color-error: oklch(65% 0.241 354.308);
  --color-error-content: oklch(97% 0.014 343.198);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.21875rem;
  --size-field: 0.21875rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;
}

/* Add variants based on LiveView classes */
@custom-variant phx-click-loading (.phx-click-loading&, .phx-click-loading &);
@custom-variant phx-submit-loading (.phx-submit-loading&, .phx-submit-loading &);
@custom-variant phx-change-loading (.phx-change-loading&, .phx-change-loading &);

/* Make LiveView wrapper divs transparent for layout */
[data-phx-session] {
  display: contents
}

/* This file is for your main application CSS */

/* 管理后台布局优化样式 */

/* 顶部导航栏增强 */
.admin-topbar {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .admin-topbar {
  background: rgba(0, 0, 0, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 侧边栏增强样式 */
.admin-sidebar {
  background: linear-gradient(180deg,
    oklch(var(--color-base-100)) 0%,
    oklch(var(--color-base-200)) 100%);
  border-right: 1px solid oklch(var(--color-base-300));
}

/* 侧边栏菜单项悬停效果 */
.sidebar-item-enhanced {
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  margin: 0.125rem 0.5rem;
}

.sidebar-item-enhanced:hover {
  background: oklch(var(--color-primary) / 0.1);
  transform: translateX(4px);
  box-shadow: 0 2px 8px oklch(var(--color-primary) / 0.2);
}

/* 侧边栏分组标题样式 */
.sidebar-section-title {
  position: relative;
  padding: 0.75rem 1rem;
  margin: 0.5rem 0;
}

.sidebar-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1rem;
  right: 1rem;
  height: 1px;
  background: linear-gradient(90deg,
    oklch(var(--color-primary) / 0.3) 0%,
    transparent 100%);
}

/* 子菜单缩进线条 */
.sidebar-submenu-line {
  position: relative;
}

.sidebar-submenu-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg,
    oklch(var(--color-base-300)) 0%,
    oklch(var(--color-primary) / 0.3) 50%,
    oklch(var(--color-base-300)) 100%);
  border-radius: 1px;
}

/* 通知徽章动画 */
.notification-badge {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 oklch(var(--color-error) / 0.7);
  }
  50% {
    box-shadow: 0 0 0 4px oklch(var(--color-error) / 0);
  }
}

/* 搜索框增强 */
.admin-search-input {
  transition: all 0.3s ease;
}

.admin-search-input:focus {
  box-shadow: 0 0 0 3px oklch(var(--color-primary) / 0.2);
  transform: scale(1.02);
}

/* 用户头像增强 */
.user-avatar-enhanced {
  transition: all 0.3s ease;
  position: relative;
}

.user-avatar-enhanced:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px oklch(var(--color-primary) / 0.3);
}

.user-avatar-enhanced::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg,
    oklch(var(--color-primary)),
    oklch(var(--color-secondary)));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-avatar-enhanced:hover::after {
  opacity: 1;
}

/* 主内容区域渐变背景 */
.admin-main-content {
  background: linear-gradient(135deg,
    oklch(var(--color-base-100)) 0%,
    oklch(var(--color-base-200)) 50%,
    oklch(var(--color-base-100)) 100%);
  min-height: 100vh;
}

/* 状态指示器 */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: oklch(var(--color-success));
  border: 2px solid oklch(var(--color-base-100));
  animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 仪表盘专用样式 */

/* 统计卡片增强 */
.stat-card {
  @apply card bg-base-100 shadow-xl p-6 hover:shadow-2xl transition-all duration-300 cursor-pointer;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg,
    oklch(var(--color-primary)) 0%,
    oklch(var(--color-secondary)) 50%,
    oklch(var(--color-accent)) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-4px);
}

/* 统计数值动画 */
.stat-value {
  @apply font-bold;
  background: linear-gradient(135deg, currentColor 0%, currentColor 100%);
  background-clip: text;
  -webkit-background-clip: text;
  animation: stat-glow 3s ease-in-out infinite;
}

@keyframes stat-glow {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.2);
  }
}

/* 图表容器样式 */
.chart-container {
  @apply relative overflow-hidden rounded-lg;
  background: linear-gradient(135deg,
    oklch(var(--color-base-100)) 0%,
    oklch(var(--color-base-200)) 100%);
  border: 1px solid oklch(var(--color-base-300));
}

.chart-placeholder {
  @apply flex items-center justify-center h-64 text-base-content/40;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    oklch(var(--color-base-300) / 0.1) 10px,
    oklch(var(--color-base-300) / 0.1) 20px
  );
}

/* 活动列表样式 */
.activity-item {
  @apply flex items-center justify-between p-3 rounded-lg transition-all duration-200;
  border-left: 3px solid transparent;
}

.activity-item:hover {
  @apply bg-base-200;
  border-left-color: oklch(var(--color-primary));
  transform: translateX(4px);
}

/* 快速操作按钮增强 */
.quick-action-btn {
  @apply btn btn-outline transition-all duration-300 relative overflow-hidden;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    oklch(var(--color-primary) / 0.2) 50%,
    transparent 100%);
  transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px oklch(var(--color-primary) / 0.3);
}

/* 系统状态警告样式 */
.system-alert {
  @apply alert transition-all duration-300;
  border-left: 4px solid currentColor;
}

.system-alert:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px oklch(var(--color-base-content) / 0.1);
}

/* 收入分析卡片 */
.revenue-stat {
  @apply stat bg-gradient-to-br from-base-200 to-base-300 rounded-lg p-4 hover:shadow-md transition-all duration-300;
  position: relative;
}

.revenue-stat::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 20px 20px 0;
  border-color: transparent oklch(var(--color-primary) / 0.1) transparent transparent;
  transition: all 0.3s ease;
}

.revenue-stat:hover::after {
  border-width: 0 30px 30px 0;
}

/* 表格增强样式 */
.dashboard-table {
  @apply table table-zebra table-sm;
}

.dashboard-table th {
  @apply bg-base-200 text-base-content font-semibold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.dashboard-table tr:hover {
  @apply bg-primary/5;
  transform: scale(1.01);
}

/* 加载动画增强 */
.loading-enhanced {
  @apply loading loading-spinner loading-lg;
  background: linear-gradient(45deg,
    oklch(var(--color-primary)),
    oklch(var(--color-secondary)));
  border-radius: 50%;
  animation: loading-pulse 2s ease-in-out infinite;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stat-card {
    @apply p-4;
  }

  .stat-value {
    @apply text-xl;
  }

  .quick-action-btn {
    @apply btn-sm;
  }

  .chart-container {
    height: 200px;
  }
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-base-300 {
  scrollbar-color: oklch(var(--color-base-300)) transparent;
}

.scrollbar-track-base-100 {
  scrollbar-color: oklch(var(--color-base-300)) oklch(var(--color-base-100));
}

/* Webkit 浏览器滚动条样式 */
.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: oklch(var(--color-base-100));
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: oklch(var(--color-base-300));
  border-radius: 4px;
  border: 1px solid oklch(var(--color-base-100));
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--color-base-content) / 0.3);
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background: oklch(var(--color-base-100));
}

/* 积分流水统一样式 */

/* 积分流水模态框样式 */
.points-history-modal-box {
  width: 95vw;
  max-width: 1400px;
  max-height: 95vh;
  min-height: 600px;
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
  z-index: 10000;
}

/* 模态框背景遮罩 */
.modal-backdrop {
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 积分流水按钮样式 */
.points-history-btn {
  transition: all 0.2s ease;
}

.points-history-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 积分流水表格容器 */
.points-history-table-container {
  min-height: 400px;
  max-height: calc(95vh - 300px);
}

/* 积分流水表格样式 */
.points-history-table {
  font-size: 0.9rem;
  position: relative;
  min-height: 100%;
}

.points-history-table th {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 1rem 0.75rem;
  white-space: nowrap;
  box-shadow: 0 2px 4px oklch(var(--color-base-content) / 0.1);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.points-history-table td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
}

/* 积分流水滚动区域 */
.points-history-scroll {
  min-height: 400px;
  max-height: calc(95vh - 300px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.points-history-scroll::-webkit-scrollbar {
  width: 6px;
}

.points-history-scroll::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.points-history-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.points-history-scroll::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 统计卡片样式 */
.stat-item {
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .points-history-modal-box {
    width: 98vw;
    max-width: none;
    max-height: 98vh;
    min-height: 500px;
    margin: 1vh auto;
  }

  .points-history-table {
    font-size: 0.8rem;
  }

  .points-history-table th,
  .points-history-table td {
    padding: 0.5rem 0.25rem;
  }

  .points-history-table-container {
    min-height: 300px;
    max-height: calc(98vh - 250px);
  }

  .points-history-scroll {
    min-height: 300px;
    max-height: calc(98vh - 250px);
  }
}

@media (min-width: 1200px) {
  .points-history-modal-box {
    width: 90vw;
    max-width: 1600px;
  }
}

/* 滚动指示器动画 */
@keyframes scroll-hint {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(2px); }
}

.scroll-indicator {
  animation: scroll-hint 2s ease-in-out infinite;
}

/* JSON编辑器样式 */
.json-editor-container {
  @apply w-full;
}

.json-editor-container .field-row {
  @apply transition-all duration-200 ease-in-out;
}

.json-editor-container .field-row:hover {
  @apply bg-blue-50 border-blue-200;
}

.json-editor-container .add-field-btn {
  @apply inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200;
}

.json-editor-container .remove-field-btn {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200;
}

.json-editor-container .switch-mode-btn {
  @apply inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.json-editor-container .form-input {
  @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.json-editor-container .form-select {
  @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.json-editor-container .json-preview {
  @apply w-full px-3 py-2 text-sm font-mono bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none;
}

.json-editor-container .error-message {
  @apply text-sm text-red-600 bg-red-50 border border-red-200 rounded-md px-3 py-2 mt-2;
}

/* JSON编辑器动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.json-editor-container .field-row {
  animation: slideIn 0.3s ease-out;
}

.json-editor-container .mode-switch {
  @apply transition-all duration-300 ease-in-out;
}

.json-editor-container .json-text-mode {
  animation: slideIn 0.3s ease-out;
}

.json-editor-container .form-edit-mode {
  animation: slideIn 0.3s ease-out;
}
