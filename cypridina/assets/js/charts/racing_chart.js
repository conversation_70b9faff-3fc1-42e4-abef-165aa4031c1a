// 图表工具函数
import ApexCharts from 'apexcharts'

// 创建排名走势图
export function createRankingChart(element, historyData, animals) {
    // 如果没有数据，直接返回
    if (!historyData || historyData.length === 0 || !animals || animals.length === 0) {
        console.error("没有足够的数据来绘制图表");
        return null;
    }

    // 创建切换按钮容器
    const chartContainer = element.parentElement;
    let toggleContainer = chartContainer.querySelector('.chart-toggle-container');

    if (!toggleContainer) {
        toggleContainer = document.createElement('div');
        toggleContainer.className = 'chart-toggle-container';
        toggleContainer.style.cssText = `
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
            gap: 10px;
        `;

        const toggleButton = document.createElement('button');
        toggleButton.className = 'chart-toggle-btn';
        toggleButton.textContent = '切换到名次';
        toggleButton.style.cssText = `
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        `;

        toggleButton.addEventListener('mouseenter', () => {
            toggleButton.style.backgroundColor = '#0056b3';
        });

        toggleButton.addEventListener('mouseleave', () => {
            toggleButton.style.backgroundColor = '#007bff';
        });

        toggleContainer.appendChild(toggleButton);
        chartContainer.insertBefore(toggleContainer, element);
    }

    // 初始化显示模式（身价）
    let currentMode = 'value'; // 'value' 或 'rank'

    // 处理数据为 ApexCharts 需要的格式
    const series = prepareSeriesData(historyData, animals, currentMode);
    const categories = historyData.map(race => race.issue.slice(-4)); // 仅显示期号后四位

    // 获取Y轴配置的函数
    const getYAxisConfig = (mode) => {
        if (mode === 'rank') {
            return {
                title: {
                    text: '名次'
                },
                min: 1,
                max: 6,
                reversed: true, // 反转Y轴，使得第一名在最上方
                tickAmount: 5,
                labels: {
                    formatter: function (val) {
                        return val.toFixed(0); // 显示整数
                    }
                }
            };
        } else {
            return {
                title: {
                    text: '身价'
                },
                labels: {
                    formatter: function (val) {
                        return val.toFixed(0); // 显示整数
                    }
                }
            };
        }
    };

    // 配置图表选项
    const options = {
        series: series,
        chart: {
            height: 350,
            type: 'line',
            zoom: {
                enabled: false
            },
            toolbar: {
                show: false
            },
            fontFamily: 'inherit'
        },
        id: "realtime",
        animations: {
            enabled: true,
            easing: 'linear',
            dynamicAnimation: {
                speed: 1000
            }
        },
        colors: [
            '#FF4560', // 红色
            '#008FFB', // 蓝色
            '#FEB019', // 黄色
            '#00E396', // 绿色
            '#775DD0', // 紫色
            '#F86624'  // 橙色
        ],
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'straight',
            width: 2
        },
        grid: {
            row: {
                colors: ['#f3f3f3', 'transparent'],
                opacity: 0.5
            }
        },
        xaxis: {
            categories: categories,
            title: {
                text: '期号'
            },
        },
        yaxis: getYAxisConfig(currentMode),
        tooltip: {
            y: {
                formatter: function (val) {
                    if (currentMode === 'rank') {
                        return `第 ${val} 名`;
                    } else {
                        return `${val}`;
                    }
                }
            }
        },
        legend: {
            position: 'top'
        }
    };

    // 创建图表实例
    const chart = new ApexCharts(element, options);

    // 添加切换功能
    const toggleButton = toggleContainer.querySelector('.chart-toggle-btn');
    toggleButton.addEventListener('click', () => {
        // 切换模式
        currentMode = currentMode === 'value' ? 'rank' : 'value';

        // 更新按钮文本
        toggleButton.textContent = currentMode === 'value' ? '切换到名次' : '切换到身价';

        // 重新准备数据
        const newSeries = prepareSeriesData(historyData, animals, currentMode);

        // 更新图表
        chart.updateOptions({
            series: newSeries,
            yaxis: getYAxisConfig(currentMode),
            tooltip: {
                y: {
                    formatter: function (val) {
                        if (currentMode === 'rank') {
                            return `第 ${val} 名`;
                        } else {
                            return `${val}`;
                        }
                    }
                }
            }
        });
    });

    // 为图表实例添加自定义方法
    chart.updateRacingData = function(newHistoryData, newAnimals) {
        const newSeries = prepareSeriesData(newHistoryData, newAnimals, currentMode);
        const newCategories = newHistoryData.map(race => race.issue.slice(-4));

        this.updateOptions({
            series: newSeries,
            xaxis: {
                categories: newCategories,
                title: {
                    text: '期号'
                }
            }
        });
    };

    return chart;
}

// 准备排名数据
function prepareSeriesData(historyData, animals, mode = 'value') {
    return animals.map(animal => {
        let data;

        if (mode === 'rank') {
            // 名次模式：获取每场比赛中该动物的排名
            data = historyData.map(race => {
                // 检查动物在各名次中的位置（直接比较动物ID）
                if (race.champion === animal.id) return 1;
                if (race.second === animal.id) return 2;
                if (race.third === animal.id) return 3;
                if (race.fourth === animal.id) return 4;
                if (race.fifth === animal.id) return 5;
                if (race.sixth === animal.id) return 6;
                return null; // 如果没有找到，返回null
            });
        } else {
            // 身价模式：获取每场比赛中该动物的身价（投注金额）
            data = historyData.map(race => {
                const bet_amount_map = race.bet_amount_map;
                return bet_amount_map[animal.id] || 0;
            });
        }

        console.log(`${mode}数据 - ${animal.name}:`, data);

        // 返回该动物的系列数据
        return {
            name: animal.name,
            data: data
        };
    });
}


