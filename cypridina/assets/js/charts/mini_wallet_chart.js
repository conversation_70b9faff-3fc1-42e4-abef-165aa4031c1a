// 迷你钱包图表 - 用于主页面显示
export function createMiniWalletChart(element, chartData) {
    if (!chartData || !chartData.labels || !chartData.values) {
        console.error("迷你图表数据无效");
        return null;
    }

    // 提取余额数据
    const balanceData = chartData.values || [];
    
    // 格式化时间标签 - 迷你图表只显示简单时间
    const timeRange = window.currentTimeRange || 'hour';
    const formattedLabels = (chartData.labels || []).map(label => {
        if (typeof label === 'string' && label.includes('T')) {
            const date = new Date(label);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            
            if (timeRange === 'minute') {
                return `${hours}:${minutes}`;
            } else if (timeRange === 'hour') {
                return `${hours}时`;
            } else if (timeRange === 'day') {
                return `${day}日`;
            }
            return `${hours}:${minutes}`;
        }
        return label;
    });
    
    // 计算数据范围用于Y轴设置
    const minValue = Math.min(...balanceData, 0); // 包含0点
    const maxValue = Math.max(...balanceData, 0);
    const dataRange = Math.max(maxValue - minValue, 1); // 避免除零
    const padding = dataRange * 0.1; // 10% 的边距

    // 设置Y轴范围，确保包含0轴
    const yAxisMin = minValue - padding;
    const yAxisMax = maxValue + padding;

    // 迷你图表配置 - 简化版本
    const options = {
        series: [
            {
                name: '钱包余额',
                data: balanceData
            }
        ],
        chart: {
            height: 200, // 更小的高度
            type: 'line',
            background: 'transparent',
            foreColor: '#6B7280',
            fontFamily: 'inherit',
            toolbar: {
                show: false // 迷你图表不显示工具栏
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 600
            },
            sparkline: {
                enabled: false
            }
        },
        colors: ['#3B82F6'], // 蓝色主题
        dataLabels: {
            enabled: false
        },
        markers: {
            size: 0,
            hover: {
                size: 4,
                sizeOffset: 2
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2,
            lineCap: 'round'
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.3,
                gradientToColors: ['#60A5FA'],
                inverseColors: false,
                opacityFrom: 0.6,
                opacityTo: 0.05,
                stops: [0, 100]
            }
        },
        grid: {
            borderColor: 'rgba(59, 130, 246, 0.08)',
            strokeDashArray: 2,
            xaxis: {
                lines: {
                    show: false // 迷你图表不显示X轴网格线
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            },
            padding: {
                top: 5,
                right: 5,
                bottom: 5,
                left: 5
            }
        },
        xaxis: {
            categories: formattedLabels,
            labels: {
                rotate: 0,
                style: {
                    fontSize: '10px',
                    colors: '#9CA3AF',
                    fontWeight: 400
                },
                offsetY: 3,
                formatter: function(value) {
                    // 迷你图表只显示部分标签
                    return value;
                }
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            min: yAxisMin,
            max: yAxisMax,
            labels: {
                style: {
                    fontSize: '10px',
                    colors: '#9CA3AF',
                    fontWeight: 400
                },
                formatter: function(value) {
                    // 简化的余额显示
                    if (Math.abs(value) >= 1000) {
                        return (value / 1000).toFixed(1) + 'k';
                    }
                    return value.toFixed(0);
                }
            },
            axisBorder: {
                show: false
            }
        },
        tooltip: {
            enabled: true,
            shared: true,
            intersect: false,
            theme: 'light',
            style: {
                fontSize: '12px',
                fontFamily: 'inherit'
            },
            marker: {
                show: true
            },
            custom: function({series, dataPointIndex}) {
                const balanceValue = series[0][dataPointIndex];
                const time = formattedLabels[dataPointIndex];

                return `
                    <div class="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
                        <div class="text-xs font-semibold text-gray-800 mb-2 flex items-center">
                            <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            ${time}
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">余额:</span>
                            <span class="font-bold text-blue-600">¥${balanceValue.toFixed(1)}</span>
                        </div>
                    </div>
                `;
            }
        },
        legend: {
            show: false // 迷你图表不显示图例
        }
    };

    // 创建图表
    const chart = new ApexCharts(element, options);

    // 添加更新方法
    chart.updateWalletData = function(newChartData) {
        // 提取新的余额数据
        const newBalanceData = newChartData.values || [];
        
        // 格式化新的时间标签
        const timeRange = window.currentTimeRange || 'hour';
        const newFormattedLabels = (newChartData.labels || []).map(label => {
            if (typeof label === 'string' && label.includes('T')) {
                const date = new Date(label);
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                
                if (timeRange === 'minute') {
                    return `${hours}:${minutes}`;
                } else if (timeRange === 'hour') {
                    return `${hours}时`;
                } else if (timeRange === 'day') {
                    return `${day}日`;
                }
                return `${hours}:${minutes}`;
            }
            return label;
        });

        chart.updateOptions({
            xaxis: {
                categories: newFormattedLabels
            }
        });

        chart.updateSeries([{
            name: '钱包余额',
            data: newBalanceData
        }]);
    };

    return chart;
}
