// 钱包余额历史展示图表 - 基于真实交易记录
import ApexCharts from 'apexcharts'

// 创建钱包余额历史波动图表
export function createWalletChart(element, chartData) {
    // 如果没有数据，直接返回
    if (!chartData || !chartData.labels || chartData.labels.length === 0) {
        console.error("没有足够的数据来绘制钱包图表");
        return null;
    }

    // 提取余额数据 - 只显示真实的余额波动
    const balanceData = chartData.values || [];

    // 格式化时间标签 - 直接在这里处理，避免ApexCharts的格式化问题
    const timeRange = window.currentTimeRange || 'hour';
    const formattedLabels = (chartData.labels || []).map(label => {
        if (typeof label === 'string' && label.includes('T')) {
            const date = new Date(label);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');

            if (timeRange === 'minute') {
                return `${hours}:${minutes}`;
            } else if (timeRange === 'hour') {
                return `${hours}时`;
            } else if (timeRange === 'day') {
                return `${day}日`;
            }
            return `${hours}:${minutes}`;
        }
        return label;
    });



    // 计算数据范围用于Y轴设置
    const minValue = Math.min(...balanceData, 0); // 包含0点
    const maxValue = Math.max(...balanceData, 0);
    const dataRange = Math.max(maxValue - minValue, 1); // 避免除零
    const padding = dataRange * 0.2; // 20% 的边距

    // 设置Y轴范围，确保包含0轴
    const yAxisMin = minValue - padding;
    const yAxisMax = maxValue + padding;



    // 美化的图表配置
    const options = {
        series: [
            {
                name: '钱包余额',
                data: balanceData
            }
        ],
        chart: {
            height: 400,
            type: 'line',
            background: 'transparent',
            foreColor: '#374151',
            fontFamily: 'inherit',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#3B82F6'], // 蓝色主题
        dataLabels: {
            enabled: false
        },
        markers: {
            size: 0,
            hover: {
                size: 6,
                sizeOffset: 3
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3,
            lineCap: 'round'
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.5,
                gradientToColors: ['#60A5FA'],
                inverseColors: false,
                opacityFrom: 0.8,
                opacityTo: 0.1,
                stops: [0, 100]
            }
        },
        grid: {
            borderColor: 'rgba(59, 130, 246, 0.1)',
            strokeDashArray: 3,
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            },
            padding: {
                top: 10,
                right: 10,
                bottom: 10,
                left: 10
            }
        },

        xaxis: {
            categories: formattedLabels,
            labels: {
                rotate: 0,  // 横着显示，不旋转
                style: {
                    fontSize: '12px',
                    colors: '#6B7280',
                    fontWeight: 500
                },
                offsetY: 5,
                formatter: function(value) {
                    console.log("🕐 格式化时间:", value);
                    console.log("🕐 当前时间范围:", window.currentTimeRange);

                    // 根据时间范围格式化显示 - 只显示时间，不显示年月日
                    if (typeof value === 'string' && value.includes('T')) {
                        const date = new Date(value);
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        const day = date.getDate().toString().padStart(2, '0');

                        // 检查数据间隔来判断时间范围类型
                        const timeRange = window.currentTimeRange || 'hour';

                        console.log(`🕐 解析时间: ${value} -> ${hours}:${minutes}, 范围: ${timeRange}`);

                        if (timeRange === 'minute') {
                            // 分钟级别：显示 HH:MM
                            const result = `${hours}:${minutes}`;
                            console.log("🕐 分钟格式结果:", result);
                            return result;
                        } else if (timeRange === 'hour') {
                            // 小时级别：显示 HH时
                            const result = `${hours}时`;
                            console.log("🕐 小时格式结果:", result);
                            return result;
                        } else if (timeRange === 'day') {
                            // 天级别：显示 日期
                            const result = `${day}日`;
                            console.log("🕐 天格式结果:", result);
                            return result;
                        }

                        // 默认显示 HH:MM
                        const result = `${hours}:${minutes}`;
                        console.log("🕐 默认格式结果:", result);
                        return result;
                    }
                    console.log("🕐 直接返回原值:", value);
                    return value;
                }
            },
            axisBorder: {
                show: true,
                color: '#E5E7EB',
                height: 1
            },
            axisTicks: {
                show: true,
                color: '#E5E7EB',
                height: 6
            }
        },
        yaxis: {
            title: {
                text: '余额 (¥)',
                style: {
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#374151'
                }
            },
            labels: {
                style: {
                    fontSize: '12px',
                    colors: '#6B7280',
                    fontWeight: 500
                },
                formatter: function(value) {
                    // 格式化显示余额
                    if (value >= 0) {
                        return '+¥' + value.toFixed(1);
                    } else {
                        return '-¥' + Math.abs(value).toFixed(1);
                    }
                }
            },
            axisBorder: {
                show: true,
                color: '#E5E7EB'
            },
            crosshairs: {
                show: true,
                position: 'back',
                stroke: {
                    color: '#3B82F6',
                    width: 1,
                    dashArray: 3
                }
            }
        },
        tooltip: {
            enabled: true,
            shared: true,
            intersect: false,
            theme: 'light',
            style: {
                fontSize: '13px',
                fontFamily: 'inherit'
            },
            marker: {
                show: true
            },
            custom: function({series, dataPointIndex, w}) {
                const balanceValue = series[0][dataPointIndex];
                const time = formattedLabels[dataPointIndex];

                // 计算相对于起始值的变化
                const firstValue = balanceData[0] || 0;
                const change = balanceValue - firstValue;
                const changePercent = Math.abs(firstValue) > 0.01 ?
                    ((Math.abs(change) / Math.abs(firstValue)) * 100).toFixed(2) : '0.00';

                const changeColor = change >= 0 ? '#10B981' : '#EF4444';
                const changeIcon = change >= 0 ? '↗' : '↘';
                const changeText = change >= 0 ? `+¥${change.toFixed(1)}` : `-¥${Math.abs(change).toFixed(1)}`;

                return `
                    <div class="bg-white p-4 rounded-lg shadow-xl border border-gray-200">
                        <div class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                            <span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                            ${time}
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">钱包余额:</span>
                                <span class="font-bold text-blue-600 text-lg">¥${balanceValue.toFixed(1)}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">涨跌:</span>
                                <span class="font-semibold flex items-center" style="color: ${changeColor}">
                                    ${changeIcon} ${changeText} (${changePercent}%)
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }
        },
        legend: {
            show: true,
            position: 'top',
            horizontalAlign: 'left',
            fontSize: '13px',
            fontWeight: 500,
            markers: {
                width: 12,
                height: 12,
                radius: 6
            },
            itemMargin: {
                horizontal: 15,
                vertical: 8
            }
        }

    };

    // 创建图表实例
    console.log("📈 创建 ApexCharts 实例");
    console.log("📈 Options:", options);

    const chart = new ApexCharts(element, options);
    console.log("✅ ApexCharts 实例创建成功:", chart);

    // 添加更新方法
    chart.updateWalletData = function(newChartData) {
        // 提取新的余额数据
        const newBalanceData = newChartData.values || [];

        // 格式化新的时间标签
        const timeRange = window.currentTimeRange || 'hour';
        const newFormattedLabels = (newChartData.labels || []).map(label => {
            if (typeof label === 'string' && label.includes('T')) {
                const date = new Date(label);
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');

                if (timeRange === 'minute') {
                    return `${hours}:${minutes}`;
                } else if (timeRange === 'hour') {
                    return `${hours}时`;
                } else if (timeRange === 'day') {
                    return `${day}日`;
                }
                return `${hours}:${minutes}`;
            }
            return label;
        });

        chart.updateOptions({
            xaxis: {
                categories: newFormattedLabels
            }
        });

        chart.updateSeries([
            {
                name: '钱包余额',
                data: newBalanceData
            }
        ]);
    };

    return chart;
}


