// Chat scroll functionality for customer service chat
export const ChatScroll = {
  mounted() {
    this.scrollToBottom();
    
    // 监听滚动到底部事件
    this.handleEvent("scroll_to_bottom", () => {
      this.scrollToBottom();
    });

    // 监听新消息添加
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 检查是否添加了新的消息元素
          const hasNewMessage = Array.from(mutation.addedNodes).some(node => 
            node.nodeType === Node.ELEMENT_NODE && 
            (node.classList?.contains('mb-6') || node.querySelector?.('.mb-6'))
          );
          
          if (hasNewMessage) {
            setTimeout(() => this.scrollToBottom(), 100);
          }
        }
      });
    });

    observer.observe(this.el, {
      childList: true,
      subtree: true
    });

    this.observer = observer;
  },

  destroyed() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },

  scrollToBottom() {
    const bottom = this.el.querySelector('#messages-bottom');
    if (bottom) {
      bottom.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'end' 
      });
    } else {
      // 如果找不到锚点，直接滚动到底部
      this.el.scrollTop = this.el.scrollHeight;
    }
  }
};
