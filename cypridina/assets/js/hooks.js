// ApexCharts 排名走势图
import { createRankingChart } from "./charts/racing_chart";
// ApexCharts 钱包控制图表
import { createWalletChart } from "./charts/wallet_chart";
import { createMiniWalletChart } from "./charts/mini_wallet_chart";

// 将 Hook 暴露给 app.js 使用
export const RacingChartHook = {
  mounted() {
    // 获取元素参考
    const chartElement = this.el;
    const chartData = JSON.parse(this.el.dataset.chartData);
    const animals = JSON.parse(this.el.dataset.animals);
    
    // 创建图表
    this.chart = createRankingChart(chartElement, chartData, animals);
    // 渲染图表
    if (this.chart) {
      this.chart.render();
    }
  },
  
  updated() {
    // 当数据更新时，更新图表
    const chartData = JSON.parse(this.el.dataset.chartData);
    const animals = JSON.parse(this.el.dataset.animals);

    // 如果图表已经存在且有自定义更新方法，使用它来更新数据
    if (this.chart && this.chart.updateRacingData) {
      this.chart.updateRacingData(chartData, animals);
    } else {
      // 否则销毁并重新创建图表
      if (this.chart) {
        this.chart.destroy();
      }

      this.chart = createRankingChart(this.el, chartData, animals);
      // 渲染图表
      if (this.chart) {
        this.chart.render();
      }
    }
  },
  
  destroyed() {
    // 当组件被销毁时，销毁图表
    if (this.chart) {
      this.chart.destroy();
    }
  }
};

// 钱包控制图表 Hook
export const MiniWalletChartHook = {
  mounted() {
    // 获取元素参考
    const chartElement = this.el;
    const chartData = JSON.parse(this.el.dataset.chartData);
    const timeRange = this.el.dataset.timeRange || 'hour';

    // 设置全局时间范围变量供图表格式化使用
    window.currentTimeRange = timeRange;

    // 创建迷你图表
    this.chart = createMiniWalletChart(chartElement, chartData);

    // 渲染图表
    if (this.chart) {
      this.chart.render();
    }
  },

  updated() {
    // 当数据更新时，更新图表
    const chartData = JSON.parse(this.el.dataset.chartData);
    const timeRange = this.el.dataset.timeRange || 'hour';

    // 更新全局时间范围变量
    window.currentTimeRange = timeRange;

    // 如果图表已经存在且有自定义更新方法，使用它来更新数据
    if (this.chart && this.chart.updateWalletData) {
      this.chart.updateWalletData(chartData);
    } else {
      // 否则销毁并重新创建图表
      if (this.chart) {
        this.chart.destroy();
      }

      this.chart = createMiniWalletChart(this.el, chartData);
      // 渲染图表
      if (this.chart) {
        this.chart.render();
      }
    }
  },

  destroyed() {
    if (this.chart) {
      this.chart.destroy();
    }
  }
};

export const WalletChartHook = {
  mounted() {
    // 获取元素参考
    const chartElement = this.el;
    const chartData = JSON.parse(this.el.dataset.chartData);
    const timeRange = this.el.dataset.timeRange || 'hour';

    // 设置全局时间范围变量供图表格式化使用
    window.currentTimeRange = timeRange;

    // 创建图表
    this.chart = createWalletChart(chartElement, chartData);

    // 渲染图表
    if (this.chart) {
      this.chart.render();
    }
  },

  updated() {
    // 当数据更新时，更新图表
    const chartData = JSON.parse(this.el.dataset.chartData);
    const timeRange = this.el.dataset.timeRange || 'hour';

    // 更新全局时间范围变量
    window.currentTimeRange = timeRange;

    // 如果图表已经存在且有自定义更新方法，使用它来更新数据
    if (this.chart && this.chart.updateWalletData) {
      this.chart.updateWalletData(chartData);
    } else {
      // 否则销毁并重新创建图表
      if (this.chart) {
        this.chart.destroy();
      }

      this.chart = createWalletChart(this.el, chartData);
      // 渲染图表
      if (this.chart) {
        this.chart.render();
      }
    }
  },

  destroyed() {
    // 当组件被销毁时，销毁图表
    if (this.chart) {
      this.chart.destroy();
    }
  }
};

// Flash消息处理的Hook
export const FlashMessagesHook = {
  mounted() {
    // 初始处理已存在的flash消息
    this.setupFlashMessages();
    
    // 监听DOM变化，处理新添加的flash消息
    this.observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          this.setupFlashMessages();
        }
      });
    });
    
    this.observer.observe(this.el, { childList: true, subtree: true });
  },
  
  updated() {
    // 在DOM更新时处理新的flash消息
    this.setupFlashMessages();
  },
  
  setupFlashMessages() {
    // 设置Flash消息自动消失的定时器
    const flashMessages = this.el.querySelectorAll('.flash-message');
    flashMessages.forEach(message => {
      // 如果消息已经设置了处理程序，则跳过
      if (message.dataset.processed) return;
      message.dataset.processed = true;
      
      // 获取flash类型 (info 或 error)
      const flashType = message.classList.contains('info') ? 'info' : 'error';
      
      // 2秒后自动消失
      setTimeout(() => {
        if (message.parentNode) {
          message.style.opacity = '0';
          message.style.transform = 'translateY(-20px)';
          
          // 同时从LiveView状态中清除flash消息
          this.pushEventTo('#flash-messages', 'lv:clear-flash', { key: flashType });
          
          setTimeout(() => {
            if (message.parentNode) {
              message.parentNode.removeChild(message);
            }
          }, 300);
        }
      }, 2000);
      
      // 点击关闭按钮时移除消息
      const closeBtn = message.querySelector('.flash-close');
      if (closeBtn) {
        closeBtn.addEventListener('click', (e) => {
          e.stopPropagation(); // 阻止冒泡，避免触发消息的点击事件
          message.style.opacity = '0';
          message.style.transform = 'translateY(-20px)';
          setTimeout(() => {
            if (message.parentNode) {
              message.parentNode.removeChild(message);
            }
          }, 300);
        });
      }
    });
  }
};
