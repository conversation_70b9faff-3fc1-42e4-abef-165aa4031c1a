# 测试数据管理系统使用说明

## 概述

测试数据管理系统是专门为测试环境设计的活动数据清除和重置工具，帮助开发团队高效地进行活动功能测试。

## 功能特性

### 1. 环境安全保护
- 仅在开发和测试环境中启用
- 生产环境完全禁用
- 多重环境检查机制

### 2. 权限控制
- 管理员权限验证
- 操作类型权限分级
- 二次确认机制

### 3. 数据清除功能
- 用户活动数据清除
- 批量用户数据清除
- 按活动类型清除
- 预览功能

### 4. 时间模拟
- 模拟时间推进
- 测试时间相关逻辑
- 活动重置测试

### 5. 备份与回滚
- 操作前自动备份
- 支持数据回滚
- 备份文件管理

## 架构设计

### 核心模块

```
Teen.ActivitySystem.TestDataManager     # 主要操作逻辑
Teen.ActivitySystem.TestDataAuth        # 权限验证
Teen.ActivitySystem.TestDataBackup      # 备份回滚
TeenWeb.Admin.TestDataController        # Web界面控制器
```

### 数据清除范围

1. **用户活动参与记录** (`user_activity_participations`)
   - 活动参与状态
   - 进度信息
   - 参与时间
   - 自定义数据

2. **用户活动记录** (`user_activity_records`)
   - 活动历史记录
   - 奖励领取状态
   - 参与数据

3. **奖励领取记录** (`reward_claim_records`)
   - 各类奖励领取记录
   - 领取时间
   - 奖励详情

4. **保留数据**
   - 用户基本信息（ID、昵称、余额等）
   - VIP等级信息
   - 核心账户数据

## 使用指南

### 1. 访问管理界面

```
URL: http://localhost:4000/admin/test-data
```

确保：
- 已登录管理员账户
- 具有测试数据管理权限
- 当前环境为开发或测试环境

### 2. 用户数据清除

#### 步骤：
1. 选择目标用户（输入用户ID或昵称）
2. 选择要清除的活动类型（或全选）
3. 执行数据预览，确认影响范围
4. 获取确认令牌
5. 执行清除操作

#### 示例：
```javascript
// 预览数据
const previewResult = await previewClearData(userId, activityTypes);
console.log('将删除记录数:', previewResult.total_records);

// 执行清除
const result = await executeClearOperation(userId, activityTypes, confirmationToken);
```

### 3. 批量数据清除

适用于测试用户组的批量清除：

```
用户ID列表（每行一个）:
test_user_1
test_user_2
test_user_3
```

### 4. 时间模拟

模拟时间推进，测试基于时间的活动逻辑：

```javascript
// 模拟7天后
const result = await simulateTimeAdvance(userId, 7);
```

### 5. 备份与回滚

系统自动创建备份：

```elixir
# 创建备份
{:ok, backup_id} = TestDataBackup.create_backup(user_id, activity_types, operator)

# 执行回滚
{:ok, _} = TestDataBackup.rollback_data(backup_id, operator)
```

## API 接口

### 数据预览
```http
POST /admin/test-data/preview-clear
Content-Type: application/json

{
  "user_id": "uuid",
  "activity_types": ["sign_in_activity", "weekly_card"]
}
```

### 执行清除
```http
POST /admin/test-data/execute-clear
Content-Type: application/json

{
  "user_id": "uuid",
  "activity_types": ["sign_in_activity"],
  "confirmation_token": "generated_token"
}
```

### 时间模拟
```http
POST /admin/test-data/execute-time-simulation
Content-Type: application/json

{
  "user_id": "uuid",
  "days_forward": 7,
  "confirmation_token": "generated_token"
}
```

## 安全措施

### 1. 环境限制
```elixir
# 配置文件 config/dev.exs
config :cypridina, :environment, :dev

# 配置文件 config/test.exs  
config :cypridina, :environment, :test

# 生产环境 config/prod.exs
config :cypridina, :environment, :prod  # 自动禁用
```

### 2. 权限验证
- 管理员账户验证
- 特定权限检查
- 操作类型授权

### 3. 操作确认
- 二次确认机制
- 确认令牌验证
- 危险操作警告

### 4. 操作日志
所有操作都会记录详细日志：
- 操作者信息
- 操作时间
- 影响范围
- 操作结果

## 测试流程示例

### 场景1: 测试签到活动重置逻辑

```bash
1. 创建测试用户并完成几天签到
2. 使用时间模拟推进到下个月
3. 验证签到状态是否正确重置
4. 清除测试数据，准备下轮测试
```

### 场景2: 测试周卡奖励发放

```bash
1. 购买周卡并领取几天奖励
2. 清除奖励领取记录
3. 验证是否可以重新领取
4. 使用时间模拟测试到期逻辑
```

## 故障排除

### 1. 权限错误
```
错误: "用户没有测试数据管理权限"
解决: 联系管理员添加相应权限
```

### 2. 环境错误
```
错误: "测试数据管理功能仅在开发和测试环境中可用"
解决: 检查环境配置，确保不在生产环境
```

### 3. 确认令牌错误
```
错误: "确认令牌无效"
解决: 重新获取确认令牌，检查操作参数是否变更
```

## 最佳实践

### 1. 操作前预览
始终在执行清除前使用预览功能确认影响范围

### 2. 分批操作
对于大量用户，建议分批进行操作

### 3. 记录测试用例
记录清除的数据范围，便于重现测试场景

### 4. 定期清理
定期清理过期的备份文件

## 配置选项

```elixir
# config/config.exs
config :cypridina,
  test_data_backup_dir: "./tmp/test_data_backups",  # 备份目录
  backup_retention_days: 7,                        # 备份保留天数
  max_batch_size: 100                              # 批量操作最大数量
```

## 监控和报告

### 操作统计
- 每日操作次数
- 清除数据量统计
- 操作成功率

### 性能监控
- 操作执行时间
- 数据库负载
- 备份文件大小

## 开发和扩展

### 添加新的活动类型
1. 在 `@activity_modules` 中添加模块映射
2. 更新活动类型列表
3. 添加相应的清除逻辑

### 自定义备份策略
继承 `TestDataBackup` 模块，实现自定义备份逻辑

### 集成测试框架
可以与现有测试框架集成，实现自动化数据准备和清理

## 联系和支持

如有问题或建议，请联系开发团队：
- 创建 GitHub Issue
- 团队内部沟通渠道

---

**注意：此系统仅用于测试环境，请勿在生产环境中使用！**