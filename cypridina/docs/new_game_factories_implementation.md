# 新游戏工厂实现文档

## 📋 **任务概述**

根据在 `websocket_handler.ex` 中添加的新游戏，为每个游戏创建对应的游戏工厂类，并在 `config/games.exs` 中注册这些游戏。

## 🎮 **已实现的游戏工厂**

### **1. Explorer 探险者游戏**
- **文件路径**: `lib/teen/game_system/games/explorer/explorer_game.ex`
- **模块名**: `Cypridina.Teen.GameSystem.Games.Explorer.ExplorerGame`
- **游戏类型**: `:explorer`
- **Game ID**: 50

#### **游戏特色**
- 🗺️ **多区域探险** - 森林、山脉、沙漠、海洋、火山、太空
- 💎 **宝藏发现系统** - 普通、稀有、史诗、传说宝藏
- ⚠️ **风险事件机制** - 陷阱、怪物、风暴、诅咒
- 👥 **多人竞技模式** - 最多8人同时探险
- 🤖 **智能机器人** - 4个机器人参与

#### **配置参数**
```elixir
%{
  max_players: 8,
  min_players: 2,
  auto_start_delay: 5000,
  enable_robots: true,
  robot_count: 4,
  game_config: %{
    bet_time: 30,
    explore_time: 15,
    settle_time: 10,
    min_bet: 50,
    max_bet: 5000
  }
}
```

### **2. Jhandi Munda 印度骰子游戏**
- **文件路径**: `lib/teen/game_system/games/jhandi_munda/jhandi_munda_game.ex`
- **模块名**: `Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGame`
- **游戏类型**: `:jhandi_munda`
- **Game ID**: 51

#### **游戏特色**
- 🎲 **传统印度骰子** - 6个骰子，6种符号
- ♥️ **经典符号** - 红心、黑桃、方块、梅花、旗帜、人脸
- 🎯 **多种赔率组合** - 根据出现次数计算赔率
- 🏆 **特殊组合奖励** - 全部相同、三对、顺子
- 🎮 **多种游戏模式** - 经典、快速、奖池模式

#### **配置参数**
```elixir
%{
  max_players: 8,
  min_players: 2,
  auto_start_delay: 5000,
  enable_robots: true,
  robot_count: 4,
  game_config: %{
    bet_time: 25,
    roll_time: 8,
    settle_time: 10,
    min_bet: 20,
    max_bet: 2000,
    dice_count: 6
  }
}
```

### **3. AK47 Teen Patti 特殊三张牌**
- **文件路径**: `lib/teen/game_system/games/ak47_teen_patti/ak47_teen_patti_game.ex`
- **模块名**: `Cypridina.Teen.GameSystem.Games.Ak47TeenPatti.Ak47TeenPattiGame`
- **游戏类型**: `:ak47_teen_patti`
- **Game ID**: 52

#### **游戏特色**
- 🃏 **AK47特殊规则** - A、K、4、7为特殊牌
- 💰 **特殊牌型奖励** - AK47组合享受高倍奖金
- 📈 **增强手牌排名** - 特殊牌型优先级更高
- 🎲 **多倍奖金系统** - 最高100倍奖金
- 🎯 **经典Teen Patti玩法** - 保留传统规则

#### **配置参数**
```elixir
%{
  max_players: 6,
  min_players: 2,
  auto_start_delay: 5000,
  enable_robots: true,
  robot_count: 3,
  game_config: %{
    min_bet: 100,
    max_bet: 10000,
    ante: 50,
    max_rounds: 50,
    show_cost_multiplier: 2
  }
}
```

### **4. Pot Blind 盲注底池**
- **文件路径**: `lib/teen/game_system/games/pot_blind/pot_blind_game.ex`
- **模块名**: `Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindGame`
- **游戏类型**: `:pot_blind`
- **Game ID**: 53

#### **游戏特色**
- 💰 **盲注底池系统** - 小盲注、大盲注机制
- 🔄 **多阶段下注** - 翻牌前、翻牌、转牌、河牌
- 🎯 **边池支持** - 支持多个边池
- ⏰ **自动盲注升级** - 定时升级盲注级别
- 🛡️ **全押保护机制** - 断线保护和全押保护

#### **配置参数**
```elixir
%{
  max_players: 6,
  min_players: 2,
  auto_start_delay: 5000,
  enable_robots: true,
  robot_count: 3,
  game_config: %{
    min_bet: 50,
    max_bet: 5000,
    small_blind: 25,
    big_blind: 50,
    level_duration: 10
  }
}
```

### **5. Safari of Wealth 财富狩猎**
- **文件路径**: `lib/teen/game_system/games/safari_of_wealth/safari_of_wealth_game.ex`
- **模块名**: `Cypridina.Teen.GameSystem.Games.SafariOfWealth.SafariOfWealthGame`
- **游戏类型**: `:safari_of_wealth`
- **Game ID**: 54

#### **游戏特色**
- 🦁 **非洲野生动物主题** - 狮子、大象、犀牛等
- 🎰 **25条支付线** - 5转轴3行老虎机
- 🎁 **免费旋转功能** - 宝藏地图触发免费旋转
- 🎯 **狩猎奖励游戏** - 选择奖励的互动游戏
- 💎 **累积奖池系统** - 渐进式累积奖池
- 🔄 **自动旋转功能** - 多种自动旋转选项

#### **配置参数**
```elixir
%{
  max_players: 1,
  min_players: 1,
  auto_start_delay: 0,
  enable_robots: false,
  robot_count: 0,
  game_config: %{
    min_bet: 100,
    max_bet: 10000,
    reels: %{count: 5, rows: 3},
    paylines: %{count: 25, adjustable: true},
    rtp_config: %{total_rtp: 100.0}
  }
}
```

## 📝 **配置文件更新**

### **games.exs 配置更新**

#### **内置游戏列表**
```elixir
builtin: [
  Cypridina.Teen.GameSystem.Games.TeenPatti.SlotNiuGame,
  Cypridina.Teen.GameSystem.Games.Slot777.Slot777Game,
  Cypridina.Teen.GameSystem.Games.Explorer.ExplorerGame,
  Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGame,
  Cypridina.Teen.GameSystem.Games.Ak47TeenPatti.Ak47TeenPattiGame,
  Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindGame,
  Cypridina.Teen.GameSystem.Games.SafariOfWealth.SafariOfWealthGame
]
```

#### **游戏特定配置**
为每个新游戏添加了配置覆盖选项，允许在不修改代码的情况下调整游戏参数。

## 🏗️ **架构设计**

### **GameFactory 行为实现**
所有新游戏工厂都实现了 `Cypridina.RoomSystem.GameFactory` 行为：

```elixir
@behaviour Cypridina.RoomSystem.GameFactory

@impl true
def game_type, do: :game_name

@impl true
def game_name, do: "游戏显示名称"

@impl true
def room_module, do: GameRoomModule

@impl true
def default_config, do: %{...}

@impl true
def is_lobby_game?, do: false

@impl true
def supported_game_ids, do: [game_id]
```

### **统一的功能接口**
每个游戏工厂都提供了统一的功能接口：
- `get_game_stats/0` - 获取游戏统计信息
- `validate_config/1` - 验证游戏配置
- `version_info/0` - 获取游戏版本信息

## ✅ **验证结果**

### **编译状态**
- ✅ **代码编译成功** - 所有新游戏工厂编译无错误
- ✅ **配置文件正确** - games.exs 配置格式正确
- ✅ **模块路径正确** - 所有模块路径符合项目结构

### **功能完整性**
- ✅ **GameFactory 行为** - 所有必需的回调函数都已实现
- ✅ **游戏配置** - 每个游戏都有详细的默认配置
- ✅ **扩展性** - 预留了统计、验证等扩展接口

## 🚀 **部署状态**

- ✅ **游戏工厂创建完成** - 5个新游戏工厂已创建
- ✅ **配置文件更新完成** - games.exs 已更新
- ✅ **编译验证通过** - 代码编译无错误
- ✅ **架构设计合理** - 遵循现有的游戏工厂模式
- ✅ **文档完整** - 详细记录所有实现细节

## 📋 **下一步工作**

1. **创建房间模块** - 为每个游戏创建对应的房间逻辑模块
2. **实现游戏逻辑** - 编写具体的游戏规则和逻辑
3. **前端集成** - 确保前端能正确加载和显示这些游戏
4. **测试验证** - 编写单元测试和集成测试

现在您的游戏系统已经拥有了完整的游戏工厂架构，支持所有要求的游戏类型！🎮✨
