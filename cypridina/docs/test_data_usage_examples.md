# 测试数据管理系统 - 使用示例

## 快速开始

### 1. 系统设置
```bash
# 运行设置脚本
mix setup_test_data_management

# 启动开发服务器
mix phx.server
```

### 2. 访问管理界面
```
http://localhost:4000/admin/test-data
```

## 常见测试场景

### 场景1: 测试每日签到重置逻辑

```elixir
# 1. 通过编程接口创建测试用户和签到数据
alias Teen.ActivitySystem.TestDataManager
alias Cypridina.Accounts.User

# 创建测试用户
{:ok, user} = User.create(%{
  email: "<EMAIL>",
  nickname: "signin_tester"
})

# 创建操作员信息
operator = %{
  id: "admin_user_id",
  username: "developer",
  ip_address: "127.0.0.1",
  user_agent: "Test Script"
}

# 2. 让用户完成几天签到（通过游戏逻辑）
# ... 用户签到逻辑 ...

# 3. 使用时间模拟推进到下个月
{:ok, result} = TestDataManager.simulate_time_advance(user.id, 31, operator)
IO.puts("时间推进完成: #{inspect(result)}")

# 4. 验证签到状态是否正确重置
# ... 检查签到状态 ...

# 5. 清除测试数据
{:ok, _} = TestDataManager.clear_user_activity_data(user.id, [:sign_in_activity], operator)
```

### 场景2: 测试周卡奖励机制

```elixir
# 1. 准备测试数据
user_id = "test_weekly_card_user"
operator = create_test_operator()

# 2. 预览当前数据
{:ok, preview} = TestDataManager.preview_clear_data(user_id, [:weekly_card])
IO.puts("当前周卡数据: #{inspect(preview)}")

# 3. 清除之前的奖励领取记录
{:ok, _} = TestDataManager.clear_user_activity_data(user_id, [:weekly_card], operator)

# 4. 重新测试周卡购买和奖励领取
# ... 周卡测试逻辑 ...

# 5. 使用时间模拟测试到期逻辑
{:ok, _} = TestDataManager.simulate_time_advance(user_id, 7, operator)
```

### 场景3: 批量清理测试用户

```elixir
# 批量清理多个测试用户
test_users = [
  "test_user_1",
  "test_user_2", 
  "test_user_3"
]

operator = create_test_operator()

# 批量清除所有活动数据
{:ok, results} = TestDataManager.batch_clear_user_data(test_users, [], operator)

# 查看结果
Enum.each(results, fn {user_id, status, result} ->
  case status do
    :ok -> IO.puts("✅ #{user_id}: 清除成功 - #{result.total_deleted} 条记录")
    :error -> IO.puts("❌ #{user_id}: 清除失败 - #{result}")
  end
end)
```

## Web界面使用示例

### 单用户数据清除流程

1. **用户选择**
   ```
   用户ID: test_user_123
   或昵称: TestPlayer
   ```

2. **活动类型选择**
   ```
   ☑️ 签到活动
   ☑️ 周卡任务  
   ☐ VIP礼包
   ☐ 充值任务
   ```

3. **数据预览**
   ```
   活动参与记录: 15 条
   活动记录: 8 条
   奖励记录: 12 条
   总计: 35 条
   ```

4. **确认执行**
   ```
   确认令牌: a1b2c3d4e5f6...
   点击"确认执行清除操作"
   ```

### 时间模拟操作

1. **选择用户**
   ```
   用户ID: test_user_456
   ```

2. **设置推进天数**
   ```
   推进天数: 7
   ```

3. **执行模拟**
   ```
   模拟时间: 2024-01-08 10:00:00
   影响记录: 23 条
   ```

## API 使用示例

### 数据预览 API

```bash
curl -X POST http://localhost:4000/admin/test-data/preview-clear \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: your_csrf_token" \
  -d '{
    "user_id": "user_uuid_here",
    "activity_types": ["sign_in_activity", "weekly_card"]
  }'
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user_id": "user_uuid_here",
    "activity_types": ["sign_in_activity", "weekly_card"],
    "data_counts": {
      "participations": 15,
      "records": 8,
      "rewards": 12
    },
    "total_records": 35
  }
}
```

### 执行清除 API

```bash
curl -X POST http://localhost:4000/admin/test-data/execute-clear \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: your_csrf_token" \
  -d '{
    "user_id": "user_uuid_here",
    "activity_types": ["sign_in_activity"],
    "confirmation_token": "generated_token_here"
  }'
```

### 时间模拟 API

```bash
curl -X POST http://localhost:4000/admin/test-data/execute-time-simulation \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: your_csrf_token" \
  -d '{
    "user_id": "user_uuid_here",
    "days_forward": 7,
    "confirmation_token": "generated_token_here"
  }'
```

## 测试脚本示例

### 创建测试辅助模块

```elixir
defmodule TestDataHelper do
  alias Teen.ActivitySystem.TestDataManager
  
  def create_test_operator(username \\ "test_admin") do
    %{
      id: Ash.UUID.generate(),
      username: username,
      ip_address: "127.0.0.1",
      user_agent: "TestScript"
    }
  end
  
  def clean_test_user(user_id) do
    operator = create_test_operator()
    TestDataManager.clear_user_activity_data(user_id, [], operator)
  end
  
  def setup_test_scenario(scenario_name) do
    case scenario_name do
      :signin_test ->
        user_id = create_signin_test_user()
        operator = create_test_operator()
        {user_id, operator}
        
      :weekly_card_test ->
        user_id = create_weekly_card_test_user()
        operator = create_test_operator()
        {user_id, operator}
        
      _ ->
        {:error, "Unknown scenario"}
    end
  end
  
  defp create_signin_test_user do
    # 创建签到测试用户的逻辑
    "signin_test_user_#{System.unique_integer()}"
  end
  
  defp create_weekly_card_test_user do
    # 创建周卡测试用户的逻辑
    "weekly_card_test_user_#{System.unique_integer()}"
  end
end
```

### 在测试中使用

```elixir
defmodule MyAppTest do
  use ExUnit.Case
  
  setup do
    # 设置测试场景
    {user_id, operator} = TestDataHelper.setup_test_scenario(:signin_test)
    
    on_exit(fn ->
      # 清理测试数据
      TestDataHelper.clean_test_user(user_id)
    end)
    
    %{user_id: user_id, operator: operator}
  end
  
  test "signin logic works correctly", %{user_id: user_id, operator: operator} do
    # 测试签到逻辑
    # ...
    
    # 推进时间测试重置
    {:ok, _} = TestDataManager.simulate_time_advance(user_id, 1, operator)
    
    # 验证重置逻辑
    # ...
  end
end
```

## 故障排除示例

### 常见错误及解决方案

1. **环境错误**
   ```
   错误: "测试数据管理功能仅在开发和测试环境中可用"
   
   解决:
   # config/dev.exs 或 config/test.exs
   config :cypridina, :environment, :dev  # 或 :test
   ```

2. **权限错误**
   ```
   错误: "用户没有测试数据管理权限"
   
   解决:
   # 确保管理员账户有正确权限
   # 或在TestDataAuth模块中临时添加测试权限
   ```

3. **确认令牌错误**
   ```
   错误: "确认令牌无效"
   
   解决:
   # 重新获取确认令牌
   # 确保操作参数没有变更
   ```

## 性能注意事项

### 大量数据处理

```elixir
# 对于大量用户的批量操作，建议分批处理
defmodule BatchProcessor do
  def process_large_user_list(user_ids, batch_size \\ 50) do
    user_ids
    |> Enum.chunk_every(batch_size)
    |> Enum.map(fn batch ->
      operator = TestDataHelper.create_test_operator()
      TestDataManager.batch_clear_user_data(batch, [], operator)
    end)
  end
end

# 使用示例
large_user_list = 1..1000 |> Enum.map(&"user_#{&1}")
BatchProcessor.process_large_user_list(large_user_list)
```

### 监控和日志

```elixir
# 启用详细日志
Logger.configure(level: :debug)

# 监控操作执行时间
{time, result} = :timer.tc(fn ->
  TestDataManager.clear_user_activity_data(user_id, [], operator)
end)

IO.puts("操作耗时: #{time / 1000} 毫秒")
```

## 最佳实践总结

1. **始终预览后执行** - 使用预览功能确认影响范围
2. **分批处理大量数据** - 避免一次性处理过多用户
3. **保留重要测试数据** - 对关键测试数据进行备份
4. **定期清理** - 定期清理过期的备份和日志
5. **文档记录** - 记录测试场景和清理步骤

---

有任何问题或需要更多示例，请联系开发团队！