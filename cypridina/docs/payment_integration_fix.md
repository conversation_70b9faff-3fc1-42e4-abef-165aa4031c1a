# MasterPay88 支付集成修复说明

## 问题描述
系统在开发环境中始终返回模拟支付链接，而不是调用真实的 MasterPay88 API。

## 修复内容

### 1. 移除开发环境的强制模拟支付
- 原代码在 `Mix.env() == :dev` 时自动使用模拟支付
- 现在改为通过配置项 `:use_mock_payment` 控制
- 默认情况下使用真实的支付网关

### 2. 修正 API 参数名称
根据 MasterPay88 的实际 API 文档，修正了以下参数：
- `channelId` → `productId` (产品ID，区分代收/代付)
- `merchantId` → `mchId` (商户号)
- `orderId` → `mchOrderNo` (商户订单号)
- 添加必需参数：`clientIp`、`subject`、`body`

### 3. 修正响应处理
- 响应状态码字段：`code` → `retCode`
- 响应消息字段：`msg` → `retMsg`
- 支付订单ID字段：`orderId` → `payOrderId`

### 4. 修正签名生成
- 签名密钥参数：`key` → `secretKey`
- 添加空值和sign字段的过滤逻辑

## 配置说明

### 使用真实支付（默认）
无需额外配置，系统默认使用真实的 MasterPay88 API。

### 切换到模拟支付
在配置文件中添加：
```elixir
config :cypridina, :use_mock_payment, true
```

## 重要提醒
1. 确保服务器 IP 在 MasterPay88 的白名单中
2. 确保回调 URL 正确配置（通过 `:public_url` 设置）
3. 建议将商户密钥等敏感信息通过环境变量管理

## 文件修改
- `/app/cypridina/lib/teen/resources/payment_system/payment_service.ex`