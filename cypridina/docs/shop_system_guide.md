# 商品系统使用指南

## 概述

商品系统为游戏提供了完整的商品销售功能，支持月卡、次卡、金币礼包等多种商品类型。系统集成了 Backpex 后台管理和完整的 API 接口。

## 系统架构

### 核心资源

1. **Product** - 商品资源
   - 管理商品基本信息、价格、状态
   - 支持多种商品类型和货币
   - 灵活的配置系统

2. **ProductTemplate** - 商品模板资源
   - 预定义商品配置模板
   - 快速创建标准化商品
   - 配置字段说明和验证

3. **UserPurchase** - 用户购买记录资源
   - 记录完整的购买流程
   - 支付状态和发放状态管理
   - 购买历史和统计

### 支持的商品类型

| 类型 | 说明 | 配置示例 |
|------|------|----------|
| `monthly_card` | 月卡 | 每日奖励、有效天数 |
| `weekly_card` | 周卡 | 每日奖励、有效天数 |
| `play_card` | 次卡 | 游戏次数、奖励倍率 |
| `coin_package` | 金币礼包 | 金币数量、奖励百分比 |
| `vip_package` | VIP礼包 | VIP等级、时长、包含物品 |
| `special_item` | 特殊道具 | 道具类型、数量、效果 |
| `recharge_bonus` | 充值奖励包 | 奖励金额、过期时间 |

## 后台管理

### 商品管理 (`/admin/products`)

**功能特性：**
- ✅ 商品列表查看和筛选
- ✅ 创建、编辑、删除商品
- ✅ 商品状态管理（上架/下架）
- ✅ 价格和配置管理
- ✅ 销售统计查看

**字段说明：**
- **商品名称** - 显示给用户的商品名称
- **商品类型** - 选择商品类型（月卡、次卡等）
- **SKU** - 商品唯一标识（自动生成）
- **价格** - 商品价格（以分为单位）
- **货币类型** - 支持 INR、USD、CNY
- **商品配置** - JSON格式的商品参数
- **显示配置** - 图标、颜色等显示设置

### 商品模板管理 (`/admin/product-templates`)

**功能特性：**
- ✅ 预设模板管理
- ✅ 配置字段说明
- ✅ 基于模板创建商品
- ✅ 默认模板设置

### 购买记录管理 (`/admin/user-purchases`)

**功能特性：**
- ✅ 购买记录查看和筛选
- ✅ 支付状态管理
- ✅ 商品发放操作
- ✅ 退款处理
- ✅ 购买统计

## API 接口

### 商品相关接口

```http
# 获取商品列表
GET /api/shop/products
Query Parameters:
  - category: 商品分类
  - type: 商品类型

# 获取商品详情
GET /api/shop/products/:id

# 创建购买订单
POST /api/shop/purchase
Body:
{
  "product_id": "商品ID",
  "discount_rate": "折扣率（可选）"
}

# 支付回调处理
POST /api/shop/payment_callback
Body:
{
  "purchase_id": "购买ID",
  "transaction_id": "交易ID",
  "status": "completed|failed"
}

# 获取购买历史
GET /api/shop/purchases
Query Parameters:
  - page: 页码
  - limit: 每页数量
```

### 响应格式

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "商品ID",
        "name": "商品名称",
        "product_type": "商品类型",
        "price": "价格（分）",
        "price_display": "价格显示",
        "currency": "货币类型",
        "status": "商品状态",
        "product_config": {},
        "display_config": {}
      }
    ]
  }
}
```

## 商品发放逻辑

### 月卡/周卡发放
```elixir
# 创建用户月卡记录
UserMonthlyCard.create(%{
  user_id: user_id,
  card_type: :monthly_card,
  daily_reward: daily_reward_amount,
  total_days: 30
})
```

### 金币礼包发放
```elixir
# 直接增加用户游戏币
UserAccount.add_coins(user_id, coin_amount)
```

### 次卡发放
```elixir
# 增加用户游戏次数（需要实现次数管理系统）
```

### 充值奖励包发放
```elixir
# 创建待领取奖励
RewardService.create_pending_reward(
  user_id,
  :coins,
  :recharge_bonus,
  bonus_amount,
  %{expires_at: expiry_time}
)
```

## 使用示例

### 1. 创建月卡商品

```elixir
# 在后台管理中创建，或通过代码：
Product.create(%{
  name: "豪华月卡",
  product_type: :monthly_card,
  price: Decimal.new("2999"),  # 29.99 INR
  currency: :inr,
  product_config: %{
    "daily_reward" => 1000,
    "total_days" => 30
  }
})
```

### 2. 用户购买流程

```elixir
# 1. 创建购买订单
{:ok, purchase_info} = ShopSystem.create_purchase_order(user_id, product_id)

# 2. 用户完成支付（通过支付网关）

# 3. 支付回调处理
{:ok, result} = ShopSystem.deliver_purchased_product(purchase_id)
```

### 3. 查看购买统计

```elixir
# 获取商品销售统计
{:ok, stats} = ShopSystem.get_product_sales_stats(product_id)
# => %{total_sales: 100, total_revenue: Decimal.new("299900"), ...}
```

## 初始化系统

运行初始化脚本：

```bash
# 生成数据库迁移并创建示例数据
mix run scripts/setup_shop_system.exs
```

这将：
1. 生成数据库迁移文件
2. 运行数据库迁移
3. 创建默认商品模板
4. 创建示例商品

## 集成现有支付系统

商品系统已经与现有的支付系统集成：

1. **购买订单创建** - 生成支付订单
2. **支付回调处理** - 自动发放商品
3. **状态同步** - 支付状态与发放状态联动

### 支付流程

```
用户选择商品 → 创建购买订单 → 跳转支付 → 支付完成 → 自动发放商品 → 完成购买
```

## 扩展功能

### 添加新商品类型

1. 在 `Product` 资源中添加新的 `product_type`
2. 在 `ShopSystem.deliver_product_by_type/1` 中添加发放逻辑
3. 创建对应的商品模板

### 自定义发放逻辑

在 `ShopSystem` 模块中实现特定商品类型的发放函数：

```elixir
defp deliver_custom_product(purchase) do
  # 自定义发放逻辑
  {:ok, %{type: :custom_product, result: "发放成功"}}
end
```

## 注意事项

1. **价格单位** - 所有价格以分为单位存储
2. **商品配置** - 使用 JSON 格式，保持灵活性
3. **状态管理** - 支付状态和发放状态分离管理
4. **错误处理** - 发放失败时保持购买记录，支持重新发放
5. **安全性** - 支付回调需要验证签名（根据支付网关要求）

现在你的游戏拥有了完整的商品销售系统，可以销售各种类型的商品，而不仅仅是按比例充值金币！
