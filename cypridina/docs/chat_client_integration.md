# 聊天客户端集成指南

本文档展示如何集成新的消息状态系统功能。

## 1. 连接到聊天频道

```javascript
import { Socket } from "phoenix"

// 创建Socket连接
const socket = new Socket("/socket", {
  params: { token: userToken }
})

socket.connect()

// 加入聊天会话频道
const channel = socket.channel(`chat_session:${sessionId}`, {})

channel.join()
  .receive("ok", resp => {
    console.log("加入聊天会话成功", resp)
    // resp 包含:
    // - session: 会话信息
    // - unread_count: 未读消息数
  })
  .receive("error", resp => {
    console.log("加入聊天会话失败", resp)
  })
```

## 2. 消息状态管理

### 发送消息
```javascript
// 发送消息
channel.push("send_message", {
  content: "Hello, World!",
  message_type: "text", // text, image, file, audio, video
  reply_to_id: null, // 可选，回复消息ID
  attachments: [] // 可选，附件列表
})
.receive("ok", resp => {
  console.log("消息发送成功", resp.message)
  // 消息会包含初始状态 status: "sent"
})
.receive("error", resp => {
  console.log("消息发送失败", resp.reason)
})
```

### 标记消息为已送达
```javascript
// 当用户收到新消息时，标记为已送达
channel.on("new_message", (payload) => {
  const message = payload.message
  
  // 如果不是自己发送的消息，标记为已送达
  if (message.sender_id !== currentUserId) {
    channel.push("mark_message_delivered", {
      message_id: message.id
    })
    
    // 显示消息
    displayMessage(message)
  }
})
```

### 标记消息为已读
```javascript
// 当用户查看消息时，标记为已读
function markMessageAsRead(messageId) {
  channel.push("mark_message_read", {
    message_id: messageId
  })
}

// 批量标记会话消息为已读
function markSessionAsRead(upToMessageId) {
  channel.push("mark_session_read", {
    up_to_message_id: upToMessageId
  })
}
```

## 3. 监听消息状态更新

```javascript
// 监听消息送达状态
channel.on("message_delivered", (payload) => {
  // payload: { message_id, user_id, delivered_at }
  updateMessageStatus(payload.message_id, 'delivered')
})

// 监听消息已读状态
channel.on("message_read", (payload) => {
  // payload: { message_id, user_id, read_at }
  updateMessageStatus(payload.message_id, 'read')
})

// 监听会话送达状态
channel.on("session_delivered", (payload) => {
  // payload: { session_id, user_id, up_to_message_id, delivered_at }
  updateMessagesStatus(payload.up_to_message_id, 'delivered')
})

// 监听会话已读状态
channel.on("session_read", (payload) => {
  // payload: { session_id, user_id, up_to_message_id, read_at }
  updateMessagesStatus(payload.up_to_message_id, 'read')
})
```

## 4. 消息状态显示

```javascript
// 根据消息的回执信息显示状态图标
function getMessageStatusIcon(message) {
  const participantCount = 2 // 私聊为2，群聊需要获取实际参与者数量
  
  // 计算除发送者外的其他参与者数量
  const otherParticipants = participantCount - 1
  
  if (message.status === 'recalled' || message.status === 'deleted') {
    return '🚫' // 已撤回或删除
  }
  
  // 检查已读回执
  if (message.read_receipts && message.read_receipts.length >= otherParticipants) {
    return '✓✓' // 蓝色双勾 - 所有人已读
  }
  
  // 检查送达回执
  if (message.delivery_receipts && message.delivery_receipts.length >= otherParticipants) {
    return '✓✓' // 灰色双勾 - 所有人已送达
  }
  
  return '✓' // 单勾 - 已发送
}
```

## 5. 获取未读消息数

```javascript
// 获取当前会话的未读消息数
channel.push("get_unread_count", {})
  .receive("ok", resp => {
    console.log("未读消息数:", resp.unread_count)
    updateUnreadBadge(resp.unread_count)
  })
```

## 6. 用户状态管理

```javascript
// 开始输入
function startTyping() {
  channel.push("typing_start", {})
}

// 停止输入
function stopTyping() {
  channel.push("typing_stop", {})
}

// 监听其他用户输入状态
channel.on("user_typing", (payload) => {
  // payload: { user_id, session_id, typing: true/false }
  if (payload.typing) {
    showTypingIndicator(payload.user_id)
  } else {
    hideTypingIndicator(payload.user_id)
  }
})

// 监听用户上线/下线
channel.on("user_joined", (payload) => {
  updateUserStatus(payload.user_id, 'online')
})

channel.on("user_left", (payload) => {
  updateUserStatus(payload.user_id, 'offline')
})
```

## 7. 完整示例：消息组件

```javascript
class ChatMessage {
  constructor(message) {
    this.message = message
  }
  
  render() {
    const statusIcon = this.getStatusIcon()
    const isOwn = this.message.sender_id === currentUserId
    
    return `
      <div class="message ${isOwn ? 'own' : 'other'}">
        <div class="message-content">${this.message.content}</div>
        <div class="message-meta">
          <span class="time">${formatTime(this.message.inserted_at)}</span>
          ${isOwn ? `<span class="status">${statusIcon}</span>` : ''}
        </div>
      </div>
    `
  }
  
  getStatusIcon() {
    if (this.message.read_receipts?.length > 0) {
      return '<span style="color: blue">✓✓</span>' // 已读
    } else if (this.message.delivery_receipts?.length > 0) {
      return '<span style="color: gray">✓✓</span>' // 已送达
    } else {
      return '<span style="color: gray">✓</span>' // 已发送
    }
  }
}
```

## 8. 性能优化建议

1. **批量操作**：使用批量标记功能减少网络请求
2. **防抖处理**：对输入状态更新进行防抖处理
3. **懒加载**：只在用户滚动到消息时才标记为已读
4. **缓存管理**：缓存消息状态，避免重复请求

## 9. 错误处理

```javascript
// 统一错误处理
channel.onError(() => {
  console.log("频道连接出错")
  attemptReconnect()
})

channel.onClose(() => {
  console.log("频道连接关闭")
  attemptReconnect()
})

function attemptReconnect() {
  setTimeout(() => {
    channel.rejoin()
      .receive("ok", resp => console.log("重连成功"))
      .receive("error", resp => console.log("重连失败"))
  }, 5000)
}
```