# IndiaGameServer VIP系统技术文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. VIP等级配置](#2-vip等级配置)
- [3. 数据库表结构](#3-数据库表结构)
- [4. 经验值系统](#4-经验值系统)
- [5. VIP权益实现](#5-vip权益实现)
- [6. API接口定义](#6-api接口定义)
- [7. 业务逻辑实现](#7-业务逻辑实现)
- [8. 配置管理](#8-配置管理)

## 1. 系统概述

VIP系统是IndiaGameServer的核心用户等级管理模块，通过用户的充值行为和游戏活跃度来提升VIP等级，为不同等级的用户提供差异化的权益和服务。

### 1.1 技术架构
- **框架**: Elixir + Phoenix + Ash Framework
- **数据库**: PostgreSQL
- **事件系统**: Phoenix.PubSub
- **计算引擎**: Decimal（高精度数值计算）

### 1.2 核心组件位置
```
lib/teen/vip_system/                # VIP业务逻辑
└── vip_service.ex                  # VIP核心服务

lib/teen/resources/vip_system/      # VIP数据资源
├── vip_level.ex                    # VIP等级配置资源
├── user_vip_info.ex               # 用户VIP信息资源
└── vip_system.ex                  # VIP系统域定义

lib/teen/protocol/websocket_handler.ex  # VIP相关协议处理
priv/repo/migrations/               # VIP相关数据库迁移
```

### 1.3 系统特性
- **等级自动升级**: 基于充值金额和经验值自动升级VIP等级
- **权益差异化**: 不同VIP等级享受不同的游戏权益
- **统计管理**: 完整的VIP数据统计和重置机制
- **事件驱动**: VIP升级时自动发布事件通知其他系统

## 2. VIP等级配置

### 2.1 等级划分标准

IndiaGameServer设计了11个VIP等级（0-10），每个等级都有明确的充值要求和权益配置：

| VIP等级 | 等级名称 | 充值要求(元) | 每日奖励(金币) | 兑换率加成(%) | 充值奖励(%) |
|--------|----------|-------------|--------------|-------------|------------|
| 0 | 普通用户 | 0 | 0 | 0 | 0 |
| 1 | 青铜VIP | 100 | 10 | 0.5 | 0.5 |
| 2 | 白银VIP | 500 | 20 | 1.0 | 1.0 |
| 3 | 黄金VIP | 1,000 | 50 | 1.5 | 1.5 |
| 4 | 白金VIP | 2,000 | 100 | 2.0 | 2.0 |
| 5 | 钻石VIP | 5,000 | 200 | 2.5 | 2.5 |
| 6 | 皇冠VIP | 10,000 | 500 | 3.0 | 3.0 |
| 7 | 至尊VIP | 20,000 | 1,000 | 3.5 | 3.5 |
| 8 | 传说VIP | 50,000 | 2,000 | 4.0 | 4.0 |
| 9 | 神话VIP | 100,000 | 5,000 | 4.5 | 4.5 |
| 10 | 超凡VIP | 200,000 | 10,000 | 5.0 | 5.0 |

### 2.2 VIP权益详细配置

除了基础权益外，每个VIP等级还享有以下特殊权益：

#### 2.2.1 提现权益配置
```elixir
# VIP等级提现权益 (在VipService中定义)
withdrawal_benefits = %{
  0 => %{limit: 10_000, times: 3},        # 每日限额1万，3次
  1 => %{limit: 20_000, times: 5},        # 每日限额2万，5次
  2 => %{limit: 50_000, times: 8},        # 每日限额5万，8次
  3 => %{limit: 100_000, times: 10},      # 每日限额10万，10次
  4 => %{limit: 200_000, times: 15},      # 每日限额20万，15次
  5 => %{limit: 500_000, times: 20},      # 每日限额50万，20次
  6 => %{limit: 1_000_000, times: 30},    # 每日限额100万，30次
  7 => %{limit: 2_000_000, times: 50},    # 每日限额200万，50次
  8 => %{limit: 5_000_000, times: 100},   # 每日限额500万，100次
  9 => %{limit: 10_000_000, times: 200},  # 每日限额1000万，200次
  10 => %{limit: 99_999_999, times: 999}  # 每日限额9999万，999次
}
```

#### 2.2.2 游戏奖励加成配置
```elixir
# VIP等级游戏奖励倍率
game_bonus_rates = %{
  0 => 0.0,    # 无加成
  1 => 0.1,    # 0.1%加成
  2 => 0.2,    # 0.2%加成
  3 => 0.3,    # 0.3%加成
  4 => 0.5,    # 0.5%加成
  5 => 0.8,    # 0.8%加成
  6 => 1.0,    # 1.0%加成
  7 => 1.5,    # 1.5%加成
  8 => 2.0,    # 2.0%加成
  9 => 2.5,    # 2.5%加成
  10 => 3.0    # 3.0%加成
}
```

#### 2.2.3 损失返水比例配置
```elixir
# VIP等级返水比例
loss_rebate_rates = %{
  0 => 0.0,    # 无返水
  1 => 0.1,    # 0.1%返水
  2 => 0.2,    # 0.2%返水
  3 => 0.3,    # 0.3%返水
  4 => 0.4,    # 0.4%返水
  5 => 0.5,    # 0.5%返水
  6 => 0.6,    # 0.6%返水
  7 => 0.7,    # 0.7%返水
  8 => 0.8,    # 0.8%返水
  9 => 0.9,    # 0.9%返水
  10 => 1.0    # 1.0%返水
}
```

## 3. 数据库表结构

### 3.1 VIP等级配置表 (vip_levels)

```sql
CREATE TABLE vip_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level INTEGER NOT NULL CHECK (level >= 0 AND level <= 10),
    level_name TEXT NOT NULL,
    recharge_requirement DECIMAL NOT NULL DEFAULT 0 CHECK (recharge_requirement >= 0),
    daily_bonus DECIMAL NOT NULL DEFAULT 0,
    exchange_rate_bonus DECIMAL NOT NULL DEFAULT 0,
    recharge_bonus DECIMAL NOT NULL DEFAULT 0,
    icon_url TEXT,
    description TEXT,
    privileges TEXT[] DEFAULT '{}',
    status INTEGER NOT NULL DEFAULT 1 CHECK (status >= 0 AND status <= 2),
    inserted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (now() AT TIME ZONE 'utc'),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (now() AT TIME ZONE 'utc')
);

-- 唯一索引
CREATE UNIQUE INDEX vip_levels_unique_level_index ON vip_levels (level);

-- 插入基础VIP等级数据
INSERT INTO vip_levels (level, level_name, recharge_requirement, daily_bonus, exchange_rate_bonus, recharge_bonus, description, privileges) VALUES
(0, '普通用户', 0, 0, 0, 0, '新注册用户默认等级', '{}'),
(1, '青铜VIP', 100, 10, 0.5, 0.5, '充值100元即可升级', '{"专属标识", "优先客服"}'),
(2, '白银VIP', 500, 20, 1.0, 1.0, '白银级别用户享受更多特权', '{"专属标识", "优先客服", "生日礼品"}'),
(3, '黄金VIP', 1000, 50, 1.5, 1.5, '黄金级别用户享受丰厚奖励', '{"专属标识", "优先客服", "生日礼品", "专属活动"}'),
(4, '白金VIP', 2000, 100, 2.0, 2.0, '白金级别用户享受顶级服务', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速"}'),
(5, '钻石VIP', 5000, 200, 2.5, 2.5, '钻石级别用户享受至尊体验', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速", "专属房间"}'),
(6, '皇冠VIP', 10000, 500, 3.0, 3.0, '皇冠级别用户享受皇室待遇', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速", "专属房间", "月度礼品"}'),
(7, '至尊VIP', 20000, 1000, 3.5, 3.5, '至尊级别用户享受无上荣耀', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速", "专属房间", "月度礼品", "专属经理"}'),
(8, '传说VIP', 50000, 2000, 4.0, 4.0, '传说级别用户享受传奇体验', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速", "专属房间", "月度礼品", "专属经理", "定制服务"}'),
(9, '神话VIP', 100000, 5000, 4.5, 4.5, '神话级别用户享受神级待遇', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速", "专属房间", "月度礼品", "专属经理", "定制服务", "专属顾问"}'),
(10, '超凡VIP', 200000, 10000, 5.0, 5.0, '超凡级别用户享受无极体验', '{"专属标识", "VIP客服", "生日礼品", "专属活动", "提现加速", "专属房间", "月度礼品", "专属经理", "定制服务", "专属顾问", "无限特权"}');
```

### 3.2 用户VIP信息表 (user_vip_infos)

```sql
CREATE TABLE user_vip_infos (
    id BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    user_id TEXT NOT NULL,
    vip_level INTEGER NOT NULL DEFAULT 0 CHECK (vip_level >= 0 AND vip_level <= 10),
    experience DECIMAL NOT NULL DEFAULT 0,
    total_experience DECIMAL NOT NULL DEFAULT 0,
    level_up_time TIMESTAMP WITH TIME ZONE,
    daily_bonus_claimed BOOLEAN NOT NULL DEFAULT false,
    last_bonus_claim_time TIMESTAMP WITH TIME ZONE,
    monthly_recharge DECIMAL NOT NULL DEFAULT 0,
    monthly_withdrawal DECIMAL NOT NULL DEFAULT 0,
    today_withdrawal_times INTEGER NOT NULL DEFAULT 0,
    today_withdrawal_amount DECIMAL NOT NULL DEFAULT 0,
    statistics JSONB DEFAULT '{}',
    inserted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (now() AT TIME ZONE 'utc'),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (now() AT TIME ZONE 'utc')
);

-- 唯一索引
CREATE UNIQUE INDEX user_vip_infos_unique_user_index ON user_vip_infos (user_id);

-- 普通索引
CREATE INDEX user_vip_infos_vip_level_index ON user_vip_infos (vip_level);
CREATE INDEX user_vip_infos_total_experience_index ON user_vip_infos (total_experience);
```

### 3.3 VIP相关迁移文件

```sql
-- 创建用户VIP信息ID生成序列
CREATE SEQUENCE user_vip_infos_id_seq;

-- 确保序列正确关联到表
ALTER TABLE user_vip_infos ALTER COLUMN id SET DEFAULT nextval('user_vip_infos_id_seq');
ALTER SEQUENCE user_vip_infos_id_seq OWNED BY user_vip_infos.id;

-- 修正VIP等级表添加缺失字段
ALTER TABLE vip_levels ADD COLUMN IF NOT EXISTS experience_required DECIMAL DEFAULT 0;
ALTER TABLE vip_levels ADD COLUMN IF NOT EXISTS withdrawal_limit DECIMAL DEFAULT 10000;
ALTER TABLE vip_levels ADD COLUMN IF NOT EXISTS withdrawal_times INTEGER DEFAULT 3;
ALTER TABLE vip_levels ADD COLUMN IF NOT EXISTS game_bonus_rate DECIMAL DEFAULT 0;
ALTER TABLE vip_levels ADD COLUMN IF NOT EXISTS loss_rebate_rate DECIMAL DEFAULT 0;
```

## 4. 经验值系统

### 4.1 经验值获取规则

VIP系统通过多种途径为用户提供经验值，具体规则如下：

```elixir
# 经验值来源配置 (lib/teen/vip_system/vip_service.ex:18-24)
@experience_sources %{
  recharge: 1.0,        # 充值1元 = 1经验值
  game_bet: 0.01,       # 下注100元 = 1经验值  
  daily_login: 10,      # 每日登录 = 10经验值
  complete_task: 20,    # 完成任务 = 20经验值
  invite_friend: 50     # 邀请好友 = 50经验值
}
```

### 4.2 经验值计算函数

#### 4.2.1 核心计算逻辑

**文件位置**: `lib/teen/vip_system/vip_service.ex:406-421`

```elixir
# 数值类型经验值计算
defp calculate_experience(source, value, multiplier) when is_number(value) do
  value_decimal = if is_float(value), do: Decimal.from_float(value), else: Decimal.new(value)
  multiplier_decimal = if is_float(multiplier), do: Decimal.from_float(multiplier), else: Decimal.new(multiplier)

  value_decimal
  |> Decimal.mult(multiplier_decimal)
  |> Decimal.round(0)
end

# Decimal类型经验值计算
defp calculate_experience(source, value, multiplier) do
  multiplier_decimal = if is_float(multiplier), do: Decimal.from_float(multiplier), else: Decimal.new(multiplier)

  value
  |> Decimal.mult(multiplier_decimal)
  |> Decimal.round(0)
end
```

#### 4.2.2 经验值添加接口

**文件位置**: `lib/teen/vip_system/vip_service.ex:68-89`

```elixir
def add_experience(user_id, amount, source \\ :other) do
  with {:ok, info} <- get_or_create_user_vip_info(user_id),
       {:ok, [updated_info | _]} <- UserVipInfo.add_experience(user_id, amount),
       {:ok, final_info} <- check_and_level_up(updated_info) do

    Logger.info("VIP经验增加: 用户=#{user_id}, 数量=#{amount}, 来源=#{source}")

    # 发布经验值变化事件
    PubSub.broadcast(
      Cypridina.PubSub,
      "vip:experience:#{user_id}",
      {:experience_added, %{user_id: user_id, amount: amount, source: source}}
    )

    {:ok, final_info}
  else
    {:error, reason} ->
      Logger.error("添加VIP经验失败: #{inspect(reason)}")
      {:error, reason}
  end
end
```

#### 4.2.3 基于来源的经验值添加

**文件位置**: `lib/teen/vip_system/vip_service.ex:94-103`

```elixir
def add_experience_from_source(user_id, source, value) do
  multiplier = Map.get(@experience_sources, source, 0)
  experience = calculate_experience(source, value, multiplier)

  if experience > 0 do
    add_experience(user_id, experience, source)
  else
    {:ok, nil}
  end
end
```

### 4.3 升级机制

#### 4.3.1 自动升级检查

**文件位置**: `lib/teen/vip_system/vip_service.ex:355-404`

```elixir
defp check_and_level_up(vip_info) do
  current_level = vip_info.vip_level

  # 获取所有等级配置
  case VipLevel.list_active() do
    {:ok, levels} ->
      # 找到用户应该在的等级
      new_level =
        levels
        |> Enum.reverse()
        |> Enum.find(fn level ->
          Decimal.compare(vip_info.total_experience, level.experience_required) != :lt
        end)

      if new_level && new_level.level > current_level do
        # 升级处理
        case UserVipInfo.update(vip_info, %{
          vip_level: new_level.level,
          level_up_time: DateTime.utc_now()
        }) do
          {:ok, updated_info} ->
            Logger.info("VIP升级: 用户=#{vip_info.user_id}, #{current_level} -> #{new_level.level}")

            # 发布升级事件
            PubSub.broadcast(
              Cypridina.PubSub,
              "vip:level_up:#{vip_info.user_id}",
              {:level_up, %{
                user_id: vip_info.user_id,
                old_level: current_level,
                new_level: new_level.level
              }}
            )

            # 发送升级奖励
            send_level_up_rewards(vip_info.user_id, new_level.level)

            {:ok, updated_info}

          {:error, reason} ->
            {:error, reason}
        end
      else
        {:ok, vip_info}
      end

    _ ->
      {:ok, vip_info}
  end
end
```

### 4.4 升级示例

以下是一个用户VIP升级的完整示例：

```elixir
# 用户初始状态：VIP0，经验值0
user_id = "user123"

# 用户充值1000元，获得1000经验值
Teen.VipSystem.VipService.add_experience_from_source(user_id, :recharge, 1000)

# 系统自动检查升级条件：
# - VIP1需要100经验值 ✓ (1000 >= 100)
# - VIP2需要500经验值 ✓ (1000 >= 500) 
# - VIP3需要1500经验值 ✗ (1000 < 1500)
# 结果：用户升级到VIP2

# 用户继续游戏，下注50000元，获得500经验值
Teen.VipSystem.VipService.add_experience_from_source(user_id, :game_bet, 50000)

# 总经验值：1000 + 500 = 1500
# VIP3需要1500经验值 ✓ (1500 >= 1500)
# 结果：用户升级到VIP3
```

## 5. VIP权益实现

### 5.1 权益获取接口

**文件位置**: `lib/teen/vip_system/vip_service.ex:134-155`

```elixir
def get_vip_benefits(user_id) do
  with {:ok, info} <- get_user_vip_info(user_id),
       {:ok, level_config} <- get_vip_level_config(info.vip_level) do

    benefits = %{
      level: info.vip_level,
      daily_bonus: level_config.daily_bonus,
      withdrawal_limit: level_config.withdrawal_limit,
      withdrawal_times: level_config.withdrawal_times,
      recharge_bonus_rate: level_config.recharge_bonus_rate,
      game_bonus_rate: level_config.game_bonus_rate,
      loss_rebate_rate: level_config.loss_rebate_rate,
      other_benefits: level_config.benefits || %{}
    }

    {:ok, benefits}
  else
    {:error, reason} ->
      Logger.error("获取VIP权益失败: #{inspect(reason)}")
      {:error, reason}
  end
end
```

### 5.2 每日奖励领取

**文件位置**: `lib/teen/vip_system/vip_service.ex:108-129`

```elixir
def claim_daily_bonus(user_id) do
  with {:ok, info} <- get_user_vip_info(user_id),
       :ok <- validate_daily_bonus_claim(info),
       {:ok, level_config} <- get_vip_level_config(info.vip_level),
       {:ok, [_updated | _]} <- UserVipInfo.claim_daily_bonus(user_id),
       {:ok, _} <- distribute_daily_bonus(user_id, level_config) do

    Logger.info("VIP每日奖励领取成功: 用户=#{user_id}, 等级=#{info.vip_level}")

    {:ok, %{
      bonus_amount: level_config.daily_bonus,
      vip_level: info.vip_level
    }}
  else
    {:error, :already_claimed} ->
      {:error, :already_claimed_today}

    {:error, reason} ->
      Logger.error("领取VIP奖励失败: #{inspect(reason)}")
      {:error, reason}
  end
end
```

### 5.3 提现权限检查

**文件位置**: `lib/teen/vip_system/vip_service.ex:160-183`

```elixir
def check_withdrawal_permission(user_id, amount) do
  with {:ok, info} <- get_user_vip_info(user_id),
       {:ok, level_config} <- get_vip_level_config(info.vip_level) do

    # 检查次数限制
    cond do
      info.today_withdrawal_times >= level_config.withdrawal_times ->
        {:error, :withdrawal_times_exceeded}

      # 检查金额限制
      Decimal.compare(
        Decimal.add(info.today_withdrawal_amount, amount),
        level_config.withdrawal_limit
      ) == :gt ->
        {:error, :withdrawal_limit_exceeded}

      true ->
        {:ok, :allowed}
    end
  else
    {:error, reason} ->
      {:error, reason}
  end
end
```

### 5.4 VIP礼包系统

VIP礼包按照时间周期分为三种类型：

#### 5.4.1 礼包奖励计算

**文件位置**: `lib/teen/protocol/websocket_handler.ex` (handle_get_vip_gift)

```elixir
# VIP礼包奖励计算公式
def calculate_vip_gift_rewards(vip_level) do
  %{
    daily_gift: %{
      amount: vip_level * 10,
      claimed: false,
      claim_time: nil
    },
    weekly_gift: %{
      amount: vip_level * 50,
      claimed: false,
      claim_time: nil
    },
    monthly_gift: %{
      amount: vip_level * 200,
      claimed: false,
      claim_time: nil
    }
  }
end

# 礼包领取示例
# VIP3用户礼包奖励：
# - 日礼包: 3 * 10 = 30金币
# - 周礼包: 3 * 50 = 150金币
# - 月礼包: 3 * 200 = 600金币

# VIP10用户礼包奖励：
# - 日礼包: 10 * 10 = 100金币
# - 周礼包: 10 * 50 = 500金币
# - 月礼包: 10 * 200 = 2000金币
```

#### 5.4.2 礼包领取时间限制

```elixir
# 礼包重置时间配置
gift_reset_schedule = %{
  daily: "00:00:00",    # 每日0点重置
  weekly: "Monday",     # 每周一重置
  monthly: 1            # 每月1号重置
}

# 礼包领取冷却检查
def can_claim_gift?(gift_type, last_claim_time) do
  now = DateTime.utc_now()
  
  case gift_type do
    :daily ->
      last_claim_time == nil or 
      Date.compare(DateTime.to_date(last_claim_time), Date.utc_today()) == :lt
      
    :weekly ->
      last_claim_time == nil or
      DateTime.diff(now, last_claim_time, :day) >= 7
      
    :monthly ->
      last_claim_time == nil or
      DateTime.to_date(last_claim_time).month != Date.utc_today().month
  end
end
```

## 6. API接口定义

### 6.1 WebSocket协议接口

VIP系统主要通过WebSocket协议提供服务，协议号为101:26-101:30。

#### 6.1.1 获取VIP礼包信息

```elixir
# 协议：[101, 26]
# 请求：{}
# 响应：
{
  "daily_gift": {
    "amount": 50,
    "claimed": false,
    "can_claim": true
  },
  "weekly_gift": {
    "amount": 250,
    "claimed": true,
    "can_claim": false,
    "claim_time": "2025-01-08T10:30:00Z"
  },
  "monthly_gift": {
    "amount": 1000,
    "claimed": false,
    "can_claim": true
  },
  "vip_level": 5,
  "next_level_requirement": 10000,
  "current_experience": 5000,
  "experience_to_next": 5000
}
```

#### 6.1.2 领取VIP礼包

```elixir
# 协议：[101, 28]
# 请求：{"gift_type": "daily"}
# 成功响应：
{
  "success": true,
  "gift_type": "daily",
  "amount": 50,
  "new_balance": 12550,
  "message": "礼包领取成功"
}

# 失败响应：
{
  "success": false,
  "error_code": 2001,
  "message": "今日礼包已领取"
}
```

#### 6.1.3 获取VIP信息

```elixir
# 协议：[101, 5] (通过用户金币信息接口返回VIP信息)
# 请求：{}
# 响应：
{
  "code": 0,
  "msg": "获取成功",
  "user": {
    "vip_level": 5,
    "vip_name": "钻石VIP",
    "total_experience": 5000,
    "experience_to_next": 5000,
    "daily_bonus": 200,
    "monthly_recharge": 5000,
    "monthly_withdrawal": 1000,
    "withdrawal_limit": 500000,
    "withdrawal_times_left": 18,
    "privileges": [
      "专属标识", "VIP客服", "生日礼品", 
      "专属活动", "提现加速", "专属房间"
    ]
  }
}
```

### 6.2 Ash Resource接口

VIP系统资源提供完整的CRUD接口：

#### 6.2.1 VIP等级管理

```elixir
# 读取所有活跃的VIP等级
{:ok, levels} = Teen.VipSystem.VipLevel.list_active()

# 根据等级获取配置
{:ok, level_config} = Teen.VipSystem.VipLevel.by_level(5)

# 根据充值金额获取VIP等级
{:ok, level} = Teen.VipSystem.VipLevel.get_level_by_recharge_amount(Decimal.new("5000"))

# 创建新的VIP等级
{:ok, new_level} = Teen.VipSystem.VipLevel.create(%{
  level: 11,
  level_name: "传奇VIP",
  recharge_requirement: Decimal.new("500000"),
  daily_bonus: Decimal.new("20000"),
  exchange_rate_bonus: Decimal.new("5.5"),
  recharge_bonus: Decimal.new("5.5"),
  description: "传奇级别用户享受无与伦比的体验",
  privileges: ["所有基础权益", "专属传奇标识", "无限特权"],
  status: 1
})
```

#### 6.2.2 用户VIP信息管理

```elixir
# 获取用户VIP信息
{:ok, [vip_info]} = Teen.VipSystem.UserVipInfo.by_user_id("user123")

# 添加经验值
{:ok, [updated_info]} = Teen.VipSystem.UserVipInfo.add_experience("user123", Decimal.new("100"))

# 领取每日奖励
{:ok, [updated_info]} = Teen.VipSystem.UserVipInfo.claim_daily_bonus("user123")

# 重置每日统计
{:ok, results} = Teen.VipSystem.UserVipInfo.reset_daily_stats()
```

## 7. 业务逻辑实现

### 7.1 统计管理

#### 7.1.1 每日统计重置

**文件位置**: `lib/teen/vip_system/vip_service.ex:224-241`

```elixir
def reset_daily_stats do
  case UserVipInfo.read() do
    {:ok, infos} ->
      Enum.each(infos, fn info ->
        UserVipInfo.update(info, %{
          daily_bonus_claimed: false,
          today_withdrawal_times: 0,
          today_withdrawal_amount: 0
        })
      end)
      Logger.info("VIP每日统计重置完成")
      {:ok, :reset_completed}

    {:error, reason} ->
      Logger.error("重置VIP每日统计失败: #{inspect(reason)}")
      {:error, reason}
  end
end
```

#### 7.1.2 每月统计重置

**文件位置**: `lib/teen/vip_system/vip_service.ex:246-262`

```elixir
def reset_monthly_stats do
  case UserVipInfo.read() do
    {:ok, infos} ->
      Enum.each(infos, fn info ->
        UserVipInfo.update(info, %{
          monthly_recharge: 0,
          monthly_withdrawal: 0
        })
      end)
      Logger.info("VIP每月统计重置完成")
      {:ok, :reset_completed}

    {:error, reason} ->
      Logger.error("重置VIP每月统计失败: #{inspect(reason)}")
      {:error, reason}
  end
end
```

### 7.2 充值和提现记录更新

#### 7.2.1 充值记录更新

**文件位置**: `lib/teen/vip_system/vip_service.ex:207-219`

```elixir
def update_recharge_record(user_id, amount) do
  with {:ok, info} <- get_user_vip_info(user_id) do
    UserVipInfo.update(info, %{
      monthly_recharge: Decimal.add(info.monthly_recharge, amount)
    })

    # 充值增加经验
    add_experience_from_source(user_id, :recharge, amount)
  else
    {:error, reason} ->
      {:error, reason}
  end
end
```

#### 7.2.2 提现记录更新

**文件位置**: `lib/teen/vip_system/vip_service.ex:188-202`

```elixir
def update_withdrawal_record(user_id, amount) do
  with {:ok, info} <- get_user_vip_info(user_id) do
    new_times = info.today_withdrawal_times + 1
    new_amount = Decimal.add(info.today_withdrawal_amount, amount)

    UserVipInfo.update(info, %{
      today_withdrawal_times: new_times,
      today_withdrawal_amount: new_amount,
      monthly_withdrawal: Decimal.add(info.monthly_withdrawal, amount)
    })
  else
    {:error, reason} ->
      {:error, reason}
  end
end
```

### 7.3 VIP信息丰富化

**文件位置**: `lib/teen/vip_system/vip_service.ex:298-322`

```elixir
defp enrich_vip_info(info) do
  # 获取当前等级配置
  {:ok, current_level} = get_vip_level_config(info.vip_level)

  # 获取下一等级配置
  next_level_config =
    if info.vip_level < 10 do
      case get_vip_level_config(info.vip_level + 1) do
        {:ok, config} -> config
        _ -> nil
      end
    else
      nil
    end

  # 计算进度
  progress = calculate_level_progress(info, current_level, next_level_config)

  Map.merge(info, %{
    current_level_config: current_level,
    next_level_config: next_level_config,
    experience_progress: progress[:percentage],
    experience_needed: progress[:needed]
  })
end
```

### 7.4 等级进度计算

**文件位置**: `lib/teen/vip_system/vip_service.ex:329-353`

```elixir
defp calculate_level_progress(info, current_level, next_level) do
  current_required = current_level.experience_required
  next_required = next_level.experience_required

  if Decimal.compare(next_required, current_required) == :gt do
    level_diff = Decimal.sub(next_required, current_required)
    user_progress = Decimal.sub(info.total_experience, current_required)

    percentage =
      Decimal.div(user_progress, level_diff)
      |> Decimal.mult(100)
      |> Decimal.round(2)
      |> Decimal.to_float()
      |> min(100.0)
      |> max(0.0)

    needed =
      Decimal.sub(next_required, info.total_experience)
      |> Decimal.max(0)

    %{percentage: percentage, needed: needed}
  else
    %{percentage: 100, needed: 0}
  end
end
```

## 8. 配置管理

### 8.1 初始化VIP等级配置

**文件位置**: `lib/teen/vip_system/vip_service.ex:466-500`

```elixir
def init_vip_levels do
  levels = [
    %{level: 0, experience_required: 0, daily_bonus: 0, withdrawal_limit: 10000, withdrawal_times: 3,
      recharge_bonus_rate: 0, game_bonus_rate: 0, loss_rebate_rate: 0},
    %{level: 1, experience_required: 100, daily_bonus: 10, withdrawal_limit: 20000, withdrawal_times: 5,
      recharge_bonus_rate: 0.5, game_bonus_rate: 0.1, loss_rebate_rate: 0.1},
    %{level: 2, experience_required: 500, daily_bonus: 20, withdrawal_limit: 50000, withdrawal_times: 8,
      recharge_bonus_rate: 1.0, game_bonus_rate: 0.2, loss_rebate_rate: 0.2},
    %{level: 3, experience_required: 1500, daily_bonus: 50, withdrawal_limit: 100000, withdrawal_times: 10,
      recharge_bonus_rate: 1.5, game_bonus_rate: 0.3, loss_rebate_rate: 0.3},
    %{level: 4, experience_required: 3000, daily_bonus: 100, withdrawal_limit: 200000, withdrawal_times: 15,
      recharge_bonus_rate: 2.0, game_bonus_rate: 0.5, loss_rebate_rate: 0.4},
    %{level: 5, experience_required: 5000, daily_bonus: 200, withdrawal_limit: 500000, withdrawal_times: 20,
      recharge_bonus_rate: 2.5, game_bonus_rate: 0.8, loss_rebate_rate: 0.5},
    %{level: 6, experience_required: 10000, daily_bonus: 500, withdrawal_limit: 1000000, withdrawal_times: 30,
      recharge_bonus_rate: 3.0, game_bonus_rate: 1.0, loss_rebate_rate: 0.6},
    %{level: 7, experience_required: 20000, daily_bonus: 1000, withdrawal_limit: 2000000, withdrawal_times: 50,
      recharge_bonus_rate: 3.5, game_bonus_rate: 1.5, loss_rebate_rate: 0.7},
    %{level: 8, experience_required: 50000, daily_bonus: 2000, withdrawal_limit: 5000000, withdrawal_times: 100,
      recharge_bonus_rate: 4.0, game_bonus_rate: 2.0, loss_rebate_rate: 0.8},
    %{level: 9, experience_required: 100000, daily_bonus: 5000, withdrawal_limit: 10000000, withdrawal_times: 200,
      recharge_bonus_rate: 4.5, game_bonus_rate: 2.5, loss_rebate_rate: 0.9},
    %{level: 10, experience_required: 200000, daily_bonus: 10000, withdrawal_limit: 99999999, withdrawal_times: 999,
      recharge_bonus_rate: 5.0, game_bonus_rate: 3.0, loss_rebate_rate: 1.0}
  ]

  Enum.each(levels, fn level_data ->
    case VipLevel.create(level_data) do
      {:ok, _} -> Logger.info("创建VIP等级 #{level_data.level} 成功")
      {:error, reason} -> Logger.error("创建VIP等级 #{level_data.level} 失败: #{inspect(reason)}")
    end
  end)

  {:ok, :initialized}
end
```

### 8.2 使用示例

#### 8.2.1 创建用户VIP信息

```elixir
# 在IEx中创建用户VIP信息
iex> user_id = "test_user_123"
iex> {:ok, vip_info} = Teen.VipSystem.VipService.get_or_create_user_vip_info(user_id)

# 查看用户VIP信息
iex> Teen.VipSystem.VipService.get_user_vip_info(user_id)
{:ok, %{
  user_id: "test_user_123",
  vip_level: 0,
  experience: 0,
  total_experience: 0,
  current_level_config: %{level: 0, daily_bonus: 0, ...},
  next_level_config: %{level: 1, experience_required: 100, ...},
  experience_progress: 0.0,
  experience_needed: 100
}}
```

#### 8.2.2 模拟用户充值升级

```elixir
# 用户充值1000元
iex> Teen.VipSystem.VipService.add_experience_from_source(user_id, :recharge, 1000)
{:ok, updated_info}

# 查看升级后的等级
iex> Teen.VipSystem.VipService.get_user_vip_level(user_id)
2  # 用户升级到VIP2

# 查看VIP权益
iex> Teen.VipSystem.VipService.get_vip_benefits(user_id)
{:ok, %{
  level: 2,
  daily_bonus: 20,
  withdrawal_limit: 50000,
  withdrawal_times: 8,
  recharge_bonus_rate: 1.0,
  game_bonus_rate: 0.2,
  loss_rebate_rate: 0.2
}}
```

#### 8.2.3 领取每日VIP奖励

```elixir
# 领取每日奖励
iex> Teen.VipSystem.VipService.claim_daily_bonus(user_id)
{:ok, %{bonus_amount: 20, vip_level: 2}}

# 再次尝试领取（应该失败）
iex> Teen.VipSystem.VipService.claim_daily_bonus(user_id)
{:error, :already_claimed_today}
```

#### 8.2.4 检查提现权限

```elixir
# 检查是否可以提现30000元
iex> Teen.VipSystem.VipService.check_withdrawal_permission(user_id, 30000)
{:ok, :allowed}

# 检查是否可以提现60000元（超过VIP2的50000限额）
iex> Teen.VipSystem.VipService.check_withdrawal_permission(user_id, 60000)
{:error, :withdrawal_limit_exceeded}
```

---

## 附录：集成说明

### A.1 与充值系统集成

VIP系统与充值系统的集成通过事件驱动方式实现：

```elixir
# 在充值完成后自动增加VIP经验
# 文件：lib/teen/workers/vip_update_worker.ex
def perform(%Oban.Job{args: %{"event_type" => "recharge_completed", "user_id" => user_id, "amount" => amount}}) do
  # 增加VIP经验
  VipService.add_experience_from_source(user_id, :recharge, amount)
  
  # 更新充值记录
  VipService.update_recharge_record(user_id, amount)
  
  :ok
end
```

### A.2 与游戏系统集成

游戏系统通过事件发布下注信息，VIP系统监听并累积经验：

```elixir
# 监听游戏下注事件
Phoenix.PubSub.subscribe(Cypridina.PubSub, "game:bet_placed")

# 处理下注事件
def handle_info({:bet_placed, %{user_id: user_id, amount: amount}}, state) do
  VipService.add_experience_from_source(user_id, :game_bet, amount)
  {:noreply, state}
end
```

### A.3 定时任务配置

VIP系统需要配置定时任务进行数据重置：

```elixir
# config/config.exs
config :cypridina, Oban,
  queues: [
    default: 10,
    vip_updates: 5,
    scheduled: 2
  ],
  crontab: [
    # 每日0点重置VIP每日统计
    {"0 0 * * *", Teen.Workers.VipDailyResetWorker},
    # 每月1号重置VIP月度统计
    {"0 0 1 * *", Teen.Workers.VipMonthlyResetWorker}
  ]
```

该文档提供了IndiaGameServer VIP系统的完整技术参考，包括数据库设计、业务逻辑实现、API接口和配置管理等所有方面。