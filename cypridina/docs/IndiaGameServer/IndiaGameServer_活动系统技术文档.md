# IndiaGameServer 活动系统技术文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 活动类型详解](#2-活动类型详解)
- [3. 协议定义](#3-协议定义)
- [4. 数据库存储过程](#4-数据库存储过程)
- [5. 核心文件分析](#5-核心文件分析)
- [6. 配置参数](#6-配置参数)

## 1. 系统概述

IndiaGameServer的活动系统是基于C++/JavaScript的混合架构实现的经典游戏营销活动管理系统。与新版Elixir服务器相比，这是原始的实现版本，包含了核心的活动逻辑。

### 1.1 技术架构
- **后端**: C++ Game Server + Node.js DBServer
- **数据库**: Microsoft SQL Server
- **协议**: 自定义二进制协议
- **存储过程**: SQL Server存储过程 (PrPs_* 命名规范)

### 1.2 系统特点
- **多活动并行**: 支持多种活动类型同时运行
- **实时数据**: 通过存储过程保证数据一致性
- **协议驱动**: 基于消息ID的协议处理
- **模块化设计**: 每种活动独立的处理逻辑

## 2. 活动类型详解

### 2.1 七日签到活动 (Seven Days)

**功能描述**: 连续7天签到活动，每日奖励递增

**核心文件**:
- 协议定义: `Code/Game/Public/F_DayLogin.h`
- 服务实现: `DBServer/service/com/getSevenDays.js`
- 存储过程: `PrPs_GetSevenDays`

**协议流程**:
```cpp
// 协议定义
CS_DAYLOGIN_INFO_P    // 客户端请求签到信息
SC_DAYLOGIN_INFO_P    // 服务端返回签到信息
CS_DAYLOGIN_P         // 客户端执行签到
SC_DAYLOGIN_RESULT_P  // 服务端返回签到结果
SC_DAYLOGIN_REWARD_P  // 服务端推送签到奖励
```

**数据结构**:
```javascript
// getSevenDays.js 返回的数据结构
{
  second: 0,           // 领取倒计时剩余秒数，0表示可领取
  currentday: 3,       // 当前可领取的天数
  sevendata: {
    1: {
      fetchid: 1,        // 领取ID
      daynumber: 1,      // 天数
      chargeamount: 0,   // 充值金额要求(0=无限制)
      currentcharge: 500,// 已充值金额
      awardcash: 100,    // 奖励金额
      fetchaward: 0,     // 已领取金额(0=未领取)
      fetchday: null     // 领取时间
    },
    // ... 其他天数
  }
}
```

### 2.2 游戏任务系统 (Game Task)

**功能描述**: 基于游戏行为的任务奖励系统

**核心文件**:
- 协议定义: `Code/Game/Public/F_TaskManager.h`
- 服务实现: `DBServer/service/com/getGameTask.js`
- 存储过程: `PrPs_GetGameTask`

**任务类型枚举**:
```cpp
enum emTaskType {
    emTaskType_NULL,
    emTaskType_1_GameNum,      // 游戏局数
    emTaskType_2_WinNum,       // 胜利次数
    emTaskType_3_FailNum,      // 失败局数
    emTaskType_4_WinMoneyNum,  // 总共赢得多少钱
    emTaskType_5_FailMoneyNum  // 总共输掉多少钱
};
```

**配置参数**:
```cpp
#define Def_Task_GetReward_Num        5  // 每日可领取任务奖励次数
#define Def_Task_GetReward_Vip_AddNum 3  // VIP可多领取次数
#define Def_Task6_TotalNum           16  // 总任务数量
```

**数据结构**:
```javascript
{
  taskdata: {
    1: {
      fetchid: 1,        // 领取ID
      tasktype: 0,       // 任务类型(0=对局, 1=连胜)
      gameid: 23,        // 游戏ID
      totalAward: 1000,  // 总奖励
      limitnumber: 10,   // 目标数量
      currentnumber: 5,  // 当前进度
      fetchstatus: 0     // 状态(0=未完成, 1=已完成, 2=已领取)
    }
  }
}
```

### 2.3 破产救济系统 (Relief System)

**功能描述**: 为资金不足的用户提供救济金

**核心文件**:
- 协议定义: `Code/Game/Public/F_Relief.h`
- 服务实现: `DBServer/service/com/getRelief.js`

**配置参数**:
```cpp
#define Def_Relief_Limit    3500  // 小于多少分放置救济金
#define Def_Relief_DatNum   3     // 每日发放多少次救济金
#define Def_Relief_Money    3500  // 一次多少救济金
```

**救济结果枚举**:
```cpp
enum emGetRelief {
    emGetRelief_Success,   // 成功
    emGetRelief_NoNum,     // 救济名额已经用完了
    emGetRelief_Time       // 距未到达一次领取救济金时间
};
```

### 2.4 VIP礼包系统

**功能描述**: 根据VIP等级提供日、周、月礼包

**核心文件**:
- 服务实现: `DBServer/service/com/getVipGift.js`
- 存储过程: `PrPs_GetVipGift`

**数据结构**:
```javascript
{
  usertask: {
    0: {
      viplevel: 5,           // 用户当前VIP等级
      dayaward: 50,          // 日奖金池
      weekaward: 250,        // 周奖金池
      monthaward: 1000,      // 月奖金池
      limitdaysocond: 0,     // 日领取倒计时
      limitweeksocond: 0,    // 周领取倒计时
      limitmonthsocond: 0,   // 月领取倒计时
      currentcharge: 5000    // 当前充值总额度
    }
  },
  viptask: {
    // VIP等级配置数据
    0: {
      viplevel: 1,      // VIP等级
      dayaward: 10,     // 每日奖励金额
      weekaward: 50,    // 每周奖励金额
      monthaward: 200,  // 每月奖励金额
      minamount: 100,   // 等级最小充值额度
      maxamount: 499    // 等级最大充值额度
    }
  }
}
```

### 2.5 充值任务系统

**功能描述**: 基于充值金额的任务奖励

**核心文件**:
- 服务实现: `DBServer/service/com/getChargeTask.js`
- 存储过程: `PrPs_GetChargeTask`

### 2.6 月卡/周卡系统

**功能描述**: 付费购买的长期收益卡片

**核心文件**:
- 服务实现: `DBServer/service/com/getCardTask.js`
- 存储过程: `PrPs_GetCardTask`

### 2.7 CDKEY兑换系统

**功能描述**: 兑换码奖励系统

**核心文件**:
- 服务实现: `DBServer/service/com/fetchCdkeyAward.js`
- 存储过程: `PrPs_FetchCdkeyAward`

## 3. 协议定义

### 3.1 大厅活动协议 (HallActivity)

所有活动都使用统一的协议范围，主协议ID为 `emFunction_HallActivity`，子协议ID区分不同功能：

```cpp
// 登录活动
CS_LOGINCASH_INFO_P = 0
SC_LOGINCASH_INFO_P = 1
CS_FETCH_LOGINCASH_AWARD_P = 2
SC_FETCH_LOGINCASH_AWARD_P = 3

// 用户资金
CS_GET_USER_MONEY_P = 4
SC_GET_USER_MONEY_P = 5

// 损失返水
CS_FETCH_USER_BONUS_P = 6
SC_FETCH_USER_BONUS_P = 7

// 七日签到
CS_GET_SEVEN_DAYS_P = 8
SC_GET_SEVEN_DAYS_P = 9
CS_FETCH_SEVEN_DAYS_AWARD_P = 10
SC_FETCH_SEVEN_DAYS_AWARD_P = 11

// 30次刮卡 (注意：原服务器未实现刮卡，这是新版本功能)
CS_GET_THIRTY_CARD_P = 12
SC_GET_THIRTY_CARD_P = 13
CS_FETCH_THIRTY_CARD_P = 14
SC_FETCH_THIRTY_CARD_P = 15

// 游戏任务
CS_GAME_TASK_P = 16
SC_GAME_TASK_P = 17
CS_FETCH_GAME_TASK_AWARD_P = 18
SC_FETCH_GAME_TASK_AWARD_P = 19

// 充值礼包
CS_GET_GIFT_CHARGE_P = 20
SC_GET_GIFT_CHARGE_P = 21

// 卡类任务
CS_GET_CARD_TASK_P = 22
SC_GET_CARD_TASK_P = 23
CS_FETCH_CARD_TASK_P = 24
SC_FETCH_CARD_TASK_P = 25

// VIP礼包
CS_GET_VIP_GIFT_P = 26
SC_GET_VIP_GIFT_P = 27
CS_FETCH_VIP_GIFT_P = 28
SC_FETCH_VIP_GIFT_P = 29

// 其他功能...
```

### 3.2 任务管理协议

```cpp
// F_TaskManager.h
CS_REQUEST_TASK_LIST_P,     // 请求任务列表
SC_REQUEST_TASK_LIST_P,
SC_TASK_COMPLETE_P,         // 任务完成
CS_GET_TASK_REWARD_P,       // 获取任务奖励
SC_GET_TASK_REWARD_RESULT_P,
SC_UPDATE_TASK_NUM_P,       // 更新可领取任务次数
CS_WEIXIN_P,                // 微信分享
CS_REQUEST_TASK_VIP_P,      // 请求VIP任务
SC_REQUEST_TASK_VIP_P
```

## 4. 数据库存储过程

### 4.1 存储过程命名规范

所有活动相关的存储过程都使用 `PrPs_` 前缀：

- `PrPs_GetSevenDays` - 获取七日签到信息
- `PrPs_FetchSevenDaysAward` - 领取七日签到奖励
- `PrPs_GetVipGift` - 获取VIP礼包信息
- `PrPs_FetchVipGiftAward` - 领取VIP礼包奖励
- `PrPs_GetGameTask` - 获取游戏任务信息
- `PrPs_FetchGameTaskAward` - 领取游戏任务奖励
- `PrPs_GetRelief` - 获取救济金
- `PrPs_GetCardTask` - 获取卡类任务
- `PrPs_GetChargeTask` - 获取充值任务
- `PrPs_FetchCdkeyAward` - 兑换CDKEY奖励

### 4.2 存储过程参数模式

**输入参数**:
- `@intUserID` - 用户ID (所有存储过程必需)
- `@intGameID` - 游戏ID (游戏相关任务)
- `@intFetchID` - 领取ID (领取奖励时)

**输出参数**:
- `@lngCurrentCharge` - 当前充值金额
- `@intLimitSecond` - 限制秒数
- `@intFetchMaxDay` - 最大领取天数

## 5. 核心文件分析

### 5.1 文件结构

```
IndiaGameServer/
├── Code/Game/Public/          # 协议头文件
│   ├── F_TaskManager.h        # 任务管理协议
│   ├── F_DayLogin.h          # 签到协议
│   ├── F_Relief.h            # 救济金协议
│   ├── F_ChengJiuManager.h   # 成就管理
│   └── F_LevelExp.h          # 等级经验
├── DBServer/service/com/      # 服务实现
│   ├── getSevenDays.js       # 七日签到
│   ├── fetchSevenDaysAward.js # 领取签到奖励
│   ├── getVipGift.js         # VIP礼包
│   ├── fetchVipGiftAward.js  # 领取VIP礼包
│   ├── getGameTask.js        # 游戏任务
│   ├── fetchGameTaskAward.js # 领取任务奖励
│   ├── getRelief.js          # 救济金
│   └── ...其他活动处理
└── Config/Lua/               # 配置文件
    └── ServerConfig_E.lua    # 服务器配置
```

### 5.2 消息处理流程

1. **协议识别**: 通过 `ishook` 方法匹配协议ID
2. **参数验证**: 提取用户ID和其他必需参数
3. **数据库调用**: 执行相应的存储过程
4. **结果处理**: 格式化返回数据
5. **错误处理**: 统一的错误日志记录

### 5.3 JavaScript服务模块模式

```javascript
function handler() {
    this.dbname = "CenterDB";  // 指定数据库
}

handler.prototype.ishook = function (cid, sid) {
    // 协议匹配逻辑
    return cid == config.msgCode.emFunction_HallActivity && 
           sid == config.msgCode.CS_GET_SEVEN_DAYS_P;
}

handler.prototype.exec = function (msg, conn, callback) {
    // 业务逻辑处理
    var request = new sql.Request(conn);
    request.input("intUserID", sql.Int, userid);
    request.execute("PrPs_GetSevenDays", function (err, recordsets, returnValue) {
        // 结果处理和回调
        callback(retdata);
    });
}
```

## 6. 配置参数

### 6.1 任务系统配置

```cpp
// F_TaskManager.h
#define Def_Task_GetReward_Num        5   // 每日可领取任务奖励次数
#define Def_Task_GetReward_Vip_AddNum 3   // VIP额外领取次数
#define Def_Task6_TotalNum           16   // 总任务数量
```

### 6.2 救济金配置

```cpp
// F_Relief.h
#define Def_Relief_Limit    3500  // 触发救济金的资金阈值
#define Def_Relief_DatNum   3     // 每日可领取次数
#define Def_Relief_Money    3500  // 每次救济金额
```

### 6.3 消息代码配置

所有协议ID都在 `config/sysconfig.js` 中的 `msgCode` 对象中定义，遵循统一的命名规范。

## 7. 与新版本的差异

### 7.1 架构差异

| 项目 | IndiaGameServer (旧版) | Cypridina (新版) |
|------|----------------------|-----------------|
| 语言 | C++ + JavaScript | Elixir |
| 数据库 | SQL Server | PostgreSQL |
| 协议 | 自定义二进制 | WebSocket + JSON |
| 状态管理 | 存储过程 | Ash Resources |
| 事件系统 | 无 | Phoenix.PubSub |

### 7.2 新增功能

1. **刮刮卡活动**: 原版本未实现，新版本新增
2. **事件驱动**: 新版本使用事件系统解耦
3. **LiveView管理**: 新版本提供Web管理界面
4. **类型安全**: Elixir提供更好的类型安全

### 7.3 迁移考虑

- **数据迁移**: 需要将SQL Server数据迁移到PostgreSQL
- **协议转换**: 将二进制协议转换为WebSocket协议
- **逻辑保持**: 核心业务逻辑需要在新系统中保持一致
- **配置迁移**: 硬编码配置需要迁移到数据库配置

---

## 总结

IndiaGameServer的活动系统是一个成熟的游戏营销活动管理系统，通过C++和JavaScript的混合架构实现了多种活动类型。其设计模式为新版Elixir系统的实现提供了重要参考，特别是在业务逻辑设计和数据结构定义方面。新版本在保持核心功能的基础上，通过现代化的技术栈提供了更好的可维护性和扩展性。