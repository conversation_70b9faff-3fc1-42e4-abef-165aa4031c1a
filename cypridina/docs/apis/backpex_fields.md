# Backpex 字段详解

## 什么是字段？

Backpex 字段是资源的构建块，定义在资源视图中显示和操作的数据。每种字段类型都有自己的配置选项和行为。通常，你需要为要在资源中显示的每种数据类型配置一个字段。

## 内置字段类型

### 文本类字段

#### Text 字段
基本的文本输入字段：

```elixir
username: %{
  module: Backpex.Fields.Text,
  label: "用户名",
  placeholder: "请输入用户名",
  searchable: true,
  orderable: true
}
```

#### Textarea 字段
多行文本输入：

```elixir
description: %{
  module: Backpex.Fields.Textarea,
  label: "描述",
  rows: 5,
  placeholder: "请输入描述信息"
}
```

#### URL 字段
URL 输入字段：

```elixir
website: %{
  module: Backpex.Fields.URL,
  label: "网站",
  placeholder: "https://example.com"
}
```

### 数值类字段

#### Number 字段
数字输入字段：

```elixir
price: %{
  module: Backpex.Fields.Number,
  label: "价格",
  min: 0,
  max: 10000,
  step: 0.01,
  placeholder: "0.00"
}
```

#### Currency 字段
货币字段：

```elixir
amount: %{
  module: Backpex.Fields.Currency,
  label: "金额",
  currency: :USD
}
```

### 选择类字段

#### Boolean 字段
布尔值字段：

```elixir
is_active: %{
  module: Backpex.Fields.Boolean,
  label: "是否激活"
}
```

#### Select 字段
单选下拉框：

```elixir
status: %{
  module: Backpex.Fields.Select,
  label: "状态",
  options: [
    {"启用", 1},
    {"禁用", 0}
  ],
  prompt: "请选择状态"
}
```

⚠️ **重要**: 选项必须使用元组格式 `{"显示文本", 值}`，不能使用 Map 格式。

#### MultiSelect 字段
多选字段：

```elixir
tags: %{
  module: Backpex.Fields.MultiSelect,
  label: "标签",
  options: [
    {"技术", "tech"},
    {"生活", "life"},
    {"工作", "work"}
  ]
}
```

### 日期时间字段

#### Date 字段
日期字段：

```elixir
birth_date: %{
  module: Backpex.Fields.Date,
  label: "出生日期"
}
```

#### DateTime 字段
日期时间字段：

```elixir
created_at: %{
  module: Backpex.Fields.DateTime,
  label: "创建时间",
  readonly: true
}
```

### 文件上传字段

#### Upload 字段
文件上传字段：

```elixir
avatar: %{
  module: Backpex.Fields.Upload,
  label: "头像",
  accept: ~w(.jpg .jpeg .png),
  max_entries: 1,
  max_file_size: 5_000_000  # 5MB
}
```

### 关联字段

#### BelongsTo 字段
多对一关联：

```elixir
category: %{
  module: Backpex.Fields.BelongsTo,
  label: "分类",
  display_field: :name,
  options_query: fn query, _assigns ->
    Ecto.Query.where(query, [c], c.active == true)
  end
}
```

#### HasMany 字段
一对多关联：

```elixir
comments: %{
  module: Backpex.Fields.HasMany,
  label: "评论",
  display_field: :content,
  child_fields: [
    content: %{
      module: Backpex.Fields.Textarea,
      label: "内容"
    },
    author: %{
      module: Backpex.Fields.Text,
      label: "作者"
    }
  ]
}
```

#### HasManyThrough 字段
多对多关联：

```elixir
tags: %{
  module: Backpex.Fields.HasManyThrough,
  label: "标签",
  display_field: :name,
  through: :post_tags
}
```

### 特殊字段

#### InlineCRUD 字段
内联 CRUD 操作：

```elixir
items: %{
  module: Backpex.Fields.InlineCRUD,
  label: "项目",
  child_fields: [
    name: %{
      module: Backpex.Fields.Text,
      label: "名称"
    },
    quantity: %{
      module: Backpex.Fields.Number,
      label: "数量"
    }
  ]
}
```

## 字段配置选项

### 通用选项

所有字段类型都支持以下选项：

```elixir
field_name: %{
  module: Backpex.Fields.Text,
  label: "字段标签",                    # 必需：显示标签
  searchable: true,                   # 是否可搜索
  orderable: true,                    # 是否可排序
  readonly: false,                    # 是否只读
  only: [:index, :show],              # 只在指定视图显示
  except: [:new],                     # 在指定视图隐藏
  help_text: "这是帮助文本",            # 帮助文本
  placeholder: "请输入...",            # 占位符文本
  default: fn _assigns -> "默认值" end  # 默认值函数
}
```

### 视图控制

#### only 选项
指定字段只在特定视图中显示：

```elixir
id: %{
  module: Backpex.Fields.Text,
  label: "ID",
  only: [:index, :show]  # 只在列表和详情页显示
}
```

#### except 选项
指定字段在特定视图中隐藏：

```elixir
password: %{
  module: Backpex.Fields.Text,
  label: "密码",
  except: [:index, :show]  # 在列表和详情页隐藏
}
```

可用的视图类型：
- `:index` - 列表页
- `:show` - 详情页
- `:new` - 新建页
- `:edit` - 编辑页

### 自定义渲染

#### render 函数
自定义字段在列表页的显示方式：

```elixir
status: %{
  module: Backpex.Fields.Select,
  label: "状态",
  options: [
    {"启用", 1},
    {"禁用", 0}
  ],
  render: fn assigns ->
    status_class = if assigns.value == 1, do: "badge-success", else: "badge-error"
    status_text = if assigns.value == 1, do: "启用", else: "禁用"
    
    assigns = assigns
      |> assign(:status_class, status_class)
      |> assign(:status_text, status_text)

    ~H"""
    <span class={"badge #{@status_class}"}><%= @status_text %></span>
    """
  end
}
```

#### 复杂渲染示例

```elixir
balance: %{
  module: Backpex.Fields.Number,
  label: "余额",
  render: fn assigns ->
    balance_display = format_balance(assigns.value)
    assigns = assign(assigns, :balance_display, balance_display)
    
    ~H"""
    <div class="text-right">
      <span class="font-mono"><%= @balance_display %></span>
    </div>
    """
  end
}
```

## 项目中的实际应用

### 用户管理字段

```elixir
# lib/teen/live/user_live.ex
@impl Backpex.LiveResource
def fields do
  %{
    numeric_id: %{
      module: Backpex.Fields.Number,
      label: "玩家ID",
      only: [:index, :show]
    },
    username: %{
      module: Backpex.Fields.Text,
      label: "用户名",
      searchable: true
    },
    permission_level: %{
      module: Backpex.Fields.Select,
      label: "权限级别",
      options: [
        {"普通用户", 0},
        {"管理员", 1},
        {"超级管理员", 2}
      ]
    },
    gender: %{
      module: Backpex.Fields.Select,
      label: "性别",
      options: [
        {"未知", 0},
        {"男", 1},
        {"女", 2}
      ],
      default: fn _assigns -> 0 end
    }
  }
end
```

### 商品管理字段

```elixir
# lib/teen/live/product_live.ex
@impl Backpex.LiveResource
def fields do
  %{
    name: %{
      module: Backpex.Fields.Text,
      label: "商品名称",
      searchable: true,
      orderable: true
    },
    product_type: %{
      module: Backpex.Fields.Select,
      label: "商品类型",
      options: [
        {"月卡", :monthly_card},
        {"周卡", :weekly_card},
        {"金币礼包", :coin_package}
      ],
      searchable: true
    },
    price: %{
      module: Backpex.Fields.Number,
      label: "价格（分）",
      orderable: true
    },
    status: %{
      module: Backpex.Fields.Select,
      label: "状态",
      options: [
        {"上架", :active},
        {"下架", :inactive}
      ],
      render: fn assigns ->
        status_class = case assigns.value do
          :active -> "badge-success"
          :inactive -> "badge-error"
          _ -> "badge-ghost"
        end
        status_text = case assigns.value do
          :active -> "上架"
          :inactive -> "下架"
          _ -> "未知"
        end
        
        assigns = assigns
          |> assign(:status_class, status_class)
          |> assign(:status_text, status_text)

        ~H"""
        <span class={"badge #{@status_class}"}><%= @status_text %></span>
        """
      end
    }
  }
end
```

## 常见问题和解决方案

### 1. 选项格式错误

❌ **错误的格式**:
```elixir
options: [
  %{label: "启用", value: 1},
  %{label: "禁用", value: 0}
]
```

✅ **正确的格式**:
```elixir
options: [
  {"启用", 1},
  {"禁用", 0}
]
```

### 2. 变量访问错误

❌ **错误的方式**:
```elixir
render: fn assigns ->
  formatted_value = format_value(assigns.value)
  ~H"""
  <span><%= formatted_value %></span>
  """
end
```

✅ **正确的方式**:
```elixir
render: fn assigns ->
  formatted_value = format_value(assigns.value)
  assigns = assign(assigns, :formatted_value, formatted_value)
  ~H"""
  <span><%= @formatted_value %></span>
  """
end
```

### 3. 关联字段配置

```elixir
# 正确配置 BelongsTo 字段
category: %{
  module: Backpex.Fields.BelongsTo,
  label: "分类",
  display_field: :name,  # 显示字段
  options_query: fn query, _assigns ->
    # 可以添加查询条件
    Ecto.Query.where(query, [c], c.active == true)
  end
}
```

## 最佳实践

1. **字段命名**: 使用清晰、描述性的字段名
2. **标签文本**: 提供有意义的中文标签
3. **帮助文本**: 为复杂字段提供帮助说明
4. **只读字段**: 对系统生成的字段设置 `readonly: true`
5. **搜索优化**: 只对需要搜索的字段设置 `searchable: true`
6. **视图控制**: 合理使用 `only` 和 `except` 控制字段显示
7. **自定义渲染**: 为状态类字段提供视觉化的渲染效果
