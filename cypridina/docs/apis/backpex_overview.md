# Backpex 文档概览

## 简介

Backpex 是一个高度可定制的 Phoenix LiveView 应用程序管理面板。通过可配置的 LiveResources 快速为现有数据创建美观的 CRUD 视图。Backpex 与现有的 Phoenix 应用程序无缝集成，提供了一种简单的资源管理方式。

![Backpex Logo](https://github.com/naymspace/backpex/blob/develop/priv/static/images/logo.svg?raw=true)

## 核心特性

- 📊 **LiveResources**: 快速为数据库表创建 LiveResource 模块，具有完全可定制的 CRUD 视图
- 🔍 **搜索和过滤器**: 定义可搜索字段并添加自定义过滤器，获得即时的 LiveView 驱动结果
- ⚡ **资源操作**: 实现全局自定义操作，如用户邀请或导出，支持额外的表单字段
- 🔒 **授权**: 通过简单的模式匹配处理 CRUD 和自定义操作授权
- 🧩 **字段类型**: 开箱即用支持文本、数字、日期、上传等字段类型，易于扩展自定义字段类型
- 🔗 **关联**: 轻松处理 HasOne、BelongsTo 和 HasMany(Through) 关联，配置最少
- 📈 **指标**: 添加值指标如总和或平均值，快速获得数据洞察

## 版本信息

- **当前版本**: v0.13.0
- **许可证**: MIT License
- **文档**: https://hexdocs.pm/backpex
- **演示**: https://backpex.live/

## 依赖要求

### 必需依赖

1. **Phoenix LiveView**: Backpex 基于 Phoenix LiveView 构建
2. **Tailwind CSS**: 用于样式设计（需要版本 4）
3. **daisyUI**: 用于 UI 组件（需要版本 5）
4. **Ecto**: 作为数据库层（需要单一主键字段）

### 支持的主键类型

- UUID (binary_id)
- Integer (bigserial)
- String

⚠️ **注意**: 不支持复合键

## 快速开始

### 1. 添加依赖

在 `mix.exs` 中添加：

```elixir
defp deps do
  [
    {:backpex, "~> 0.13.0"}
  ]
end
```

### 2. 配置 PubSub

在 `config.exs` 中设置：

```elixir
config :backpex, :pubsub_server, MyApp.PubSub
```

### 3. 添加 JavaScript Hooks

在 `app.js` 中：

```javascript
import { Hooks as BackpexHooks } from 'backpex';

const Hooks = [] // 你的应用程序 hooks（可选）

const liveSocket = new LiveSocket('/live', Socket, {
  params: { _csrf_token: csrfToken },
  hooks: {...Hooks, ...BackpexHooks }
})
```

### 4. 配置 Tailwind CSS

在样式表中添加：

```css
@source "../../deps/backpex/**/*.*ex";
@source '../../deps/backpex/assets/js/**/*.*js'
```

### 5. 设置格式化器

在 `.formatter.exs` 中：

```elixir
[
  import_deps: [:backpex]
]
```

## 核心概念

### LiveResource

LiveResource 是包含资源配置的模块，负责定义：
- 资源的模式（schema）
- 可执行的操作
- 要渲染的字段

### 字段（Fields）

字段是资源的构建块，定义在资源视图中显示和操作的数据。每种字段类型都有自己的配置选项和行为。

### 内置字段类型

- `Backpex.Fields.Text` - 文本字段
- `Backpex.Fields.Number` - 数字字段
- `Backpex.Fields.Boolean` - 布尔字段
- `Backpex.Fields.DateTime` - 日期时间字段
- `Backpex.Fields.Date` - 日期字段
- `Backpex.Fields.Select` - 选择字段
- `Backpex.Fields.MultiSelect` - 多选字段
- `Backpex.Fields.Textarea` - 文本区域字段
- `Backpex.Fields.Upload` - 上传字段
- `Backpex.Fields.URL` - URL 字段
- `Backpex.Fields.Currency` - 货币字段
- `Backpex.Fields.BelongsTo` - 关联字段
- `Backpex.Fields.HasMany` - 一对多关联
- `Backpex.Fields.HasManyThrough` - 多对多关联
- `Backpex.Fields.InlineCRUD` - 内联 CRUD

## 项目中的使用

在我们的项目中，Backpex 被用于创建管理后台界面，包括：

- 用户管理 (`Teen.Live.UserLive`)
- 商品管理 (`Teen.Live.ProductLive`)
- 支付订单管理 (`Teen.Live.PaymentOrderLive`)
- 渠道管理 (`Teen.Live.ChannelLive`)
- 等等...

## 相关文档

- [安装指南](backpex_installation.md) - 详细的安装和配置步骤
- [LiveResource 详解](backpex_live_resource.md) - LiveResource 的核心概念和配置
- [字段配置](backpex_fields.md) - 各种字段类型的使用和配置
- [过滤器和搜索](backpex_filters.md) - 搜索和过滤功能的实现
- [操作和权限](backpex_actions.md) - 项目操作和权限控制

## 官方资源

- **GitHub**: https://github.com/naymspace/backpex
- **Hex.pm**: https://hex.pm/packages/backpex
- **文档**: https://hexdocs.pm/backpex
- **演示站点**: https://backpex.live/
