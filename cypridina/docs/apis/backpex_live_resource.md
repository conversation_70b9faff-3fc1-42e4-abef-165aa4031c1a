# Backpex LiveResource 详解

## 什么是 LiveResource？

在 Backpex 中，LiveResource 是一个包含资源配置的模块。这个模块负责定义：

- 资源的数据库模式（schema）
- 可以对资源执行的操作
- 要渲染的字段

LiveResource 模块实现了 `Backpex.LiveResource` 行为，是 Backpex 的核心配置文件。

## 基本结构

```elixir
defmodule MyAppWeb.Live.PostLive do
  use Backpex.LiveResource,
    adapter_config: [
      schema: MyApp.Blog.Post,
      repo: MyApp.Repo,
      update_changeset: &MyApp.Blog.Post.update_changeset/3,
      create_changeset: &MyApp.Blog.Post.create_changeset/3
    ],
    layout: {MyAppWeb.Layouts, :admin}

  # 必需的回调函数
  @impl Backpex.LiveResource
  def singular_name, do: "Post"

  @impl Backpex.LiveResource
  def plural_name, do: "Posts"

  @impl Backpex.LiveResource
  def fields do
    [
      title: %{
        module: Backpex.Fields.Text,
        label: "Title"
      }
    ]
  end
end
```

## 配置选项

### 必需配置

#### adapter_config

- `schema`: 资源对应的 Ecto 模式
- `repo`: 使用的 Ecto 仓库
- `update_changeset`: 更新时使用的 changeset 函数
- `create_changeset`: 创建时使用的 changeset 函数

#### layout

指定 LiveResource 使用的布局模板：

```elixir
layout: {MyAppWeb.Layouts, :admin}
```

#### primary_key（可选）

如果主键不是 "id"，需要指定：

```elixir
use Backpex.LiveResource,
  adapter_config: [...],
  primary_key: :code
```

### 可选配置

#### pubsub

配置 PubSub 选项：

```elixir
use Backpex.LiveResource,
  adapter_config: [...],
  pubsub: [
    name: MyApp.PubSub,
    topic: "posts"
  ]
```

## 必需的回调函数

### singular_name/0

返回资源的单数名称：

```elixir
@impl Backpex.LiveResource
def singular_name, do: "Post"
```

### plural_name/0

返回资源的复数名称：

```elixir
@impl Backpex.LiveResource
def plural_name, do: "Posts"
```

### fields/0

返回要显示的字段列表：

```elixir
@impl Backpex.LiveResource
def fields do
  [
    title: %{
      module: Backpex.Fields.Text,
      label: "Title",
      searchable: true
    },
    content: %{
      module: Backpex.Fields.Textarea,
      label: "Content"
    },
    published: %{
      module: Backpex.Fields.Boolean,
      label: "Published"
    }
  ]
end
```

## 可选的回调函数

### can?/3

控制用户权限：

```elixir
@impl Backpex.LiveResource
def can?(assigns, action, item) do
  case action do
    :index -> true
    :show -> true
    :new -> assigns.current_user.role == :admin
    :edit -> assigns.current_user.role == :admin
    :delete -> assigns.current_user.role == :admin
    _ -> false
  end
end
```

### filters/0

定义过滤器：

```elixir
@impl Backpex.LiveResource
def filters do
  [
    status: %{
      module: MyApp.Filters.StatusSelect
    },
    category: %{
      module: MyApp.Filters.CategorySelect
    }
  ]
end
```

### item_actions/1

定义项目操作：

```elixir
@impl Backpex.LiveResource
def item_actions(_) do
  [
    show: %{
      module: Backpex.ItemActions.Show,
      only: [:row]
    },
    edit: %{
      module: Backpex.ItemActions.Edit,
      only: [:row, :show]
    },
    delete: %{
      module: Backpex.ItemActions.Delete,
      only: [:row, :index, :show]
    }
  ]
end
```

### resource_actions/0

定义资源级别的操作：

```elixir
@impl Backpex.LiveResource
def resource_actions do
  [
    export: %{
      module: MyApp.Actions.ExportPosts,
      label: "Export Posts",
      icon: "hero-document-arrow-down"
    }
  ]
end
```

### metrics/0

定义指标：

```elixir
@impl Backpex.LiveResource
def metrics do
  [
    total_posts: %{
      module: Backpex.Metrics.Simple,
      label: "Total Posts",
      value: fn _socket ->
        MyApp.Repo.aggregate(MyApp.Blog.Post, :count, :id)
      end
    }
  ]
end
```

### panels/0

定义面板：

```elixir
@impl Backpex.LiveResource
def panels do
  [
    main: [
      %{
        label: "Basic Information",
        fields: [:title, :content]
      },
      %{
        label: "Settings",
        fields: [:published, :featured]
      }
    ]
  ]
end
```

## 字段配置

### 基本字段配置

每个字段必须包含：

- `module`: 实现字段行为的模块
- `label`: 字段显示标签

```elixir
title: %{
  module: Backpex.Fields.Text,
  label: "Title"
}
```

### 字段选项

#### 通用选项

- `searchable`: 是否可搜索
- `orderable`: 是否可排序
- `readonly`: 是否只读
- `only`: 只在指定视图中显示 `[:index, :show, :new, :edit]`
- `except`: 在指定视图中隐藏

```elixir
title: %{
  module: Backpex.Fields.Text,
  label: "Title",
  searchable: true,
  orderable: true,
  only: [:index, :show]
}
```

#### 字段特定选项

不同字段类型有特定的配置选项：

```elixir
# Text 字段
username: %{
  module: Backpex.Fields.Text,
  label: "Username",
  placeholder: "Enter username"
}

# Select 字段
status: %{
  module: Backpex.Fields.Select,
  label: "Status",
  options: [
    {"Active", "active"},
    {"Inactive", "inactive"}
  ]
}

# Number 字段
price: %{
  module: Backpex.Fields.Number,
  label: "Price",
  min: 0,
  step: 0.01
}
```

## 自定义渲染

### 自定义字段渲染

```elixir
status: %{
  module: Backpex.Fields.Select,
  label: "Status",
  options: [
    {"Active", "active"},
    {"Inactive", "inactive"}
  ],
  render: fn assigns ->
    status_class = if assigns.value == "active", do: "badge-success", else: "badge-error"
    status_text = if assigns.value == "active", do: "Active", else: "Inactive"
    
    assigns = assigns
      |> assign(:status_class, status_class)
      |> assign(:status_text, status_text)

    ~H"""
    <span class={"badge #{@status_class}"}><%= @status_text %></span>
    """
  end
}
```

## 关联字段

### BelongsTo 关联

```elixir
category: %{
  module: Backpex.Fields.BelongsTo,
  label: "Category",
  display_field: :name,
  options_query: fn query, _assigns ->
    Ecto.Query.where(query, [c], c.active == true)
  end
}
```

### HasMany 关联

```elixir
comments: %{
  module: Backpex.Fields.HasMany,
  label: "Comments",
  display_field: :content,
  child_fields: [
    content: %{
      module: Backpex.Fields.Textarea,
      label: "Content"
    },
    author: %{
      module: Backpex.Fields.Text,
      label: "Author"
    }
  ]
}
```

## 项目中的实际应用

在我们的项目中，LiveResource 被广泛使用：

### 用户管理

```elixir
defmodule Teen.Live.UserLive do
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Cypridina.Accounts.User,
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "用户"

  @impl Backpex.LiveResource
  def plural_name, do: "用户列表"

  @impl Backpex.LiveResource
  def fields do
    %{
      username: %{
        module: Backpex.Fields.Text,
        label: "用户名",
        searchable: true
      },
      permission_level: %{
        module: Backpex.Fields.Select,
        label: "权限级别",
        options: [
          {"普通用户", 0},
          {"管理员", 1},
          {"超级管理员", 2}
        ]
      }
    }
  end
end
```

### 商品管理

```elixir
defmodule Teen.Live.ProductLive do
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ShopSystem.Product,
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def filters do
    [
      product_type: %{
        module: Teen.Filters.ProductTypeSelect
      },
      status: %{
        module: Teen.Filters.ProductStatusSelect
      }
    ]
  end
end
```

## 最佳实践

1. **模块命名**: 使用 `*Live` 后缀命名 LiveResource 模块
2. **文件位置**: 放在 `lib/myapp_web/live/` 目录下
3. **权限控制**: 始终实现 `can?/3` 回调进行权限控制
4. **字段组织**: 使用 panels 组织复杂的字段布局
5. **性能优化**: 对大数据集使用适当的查询优化
6. **用户体验**: 提供有意义的标签和帮助文本
