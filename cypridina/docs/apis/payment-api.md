# 支付系统API文档
## 概述

本文档描述了基于MasterPay88支付网关的充值和提现API接口。系统支持代收（充值）和代付（提现）功能，并提供完整的回调处理机制。

## 📚 官方文档链接

以下是MasterPay88官方提供的详细API文档链接（需要访问密码）：

1. **[通用规则页](http://***********:4999/web/#/*********/211908046)** - 接口通用规则和规范
2. **[签名代码示例](http://***********:4999/web/#/*********/211908048)** - 签名算法的代码实现示例
3. **[接口调用常见问题](http://***********:4999/web/#/*********/211908049)** - 常见问题解答和故障排除
4. **[代付下单接口](http://***********:4999/web/#/*********/211908038)** - 提现订单创建接口详解
5. **[代付订单查询接口](http://***********:4999/web/#/*********/211908039)** - 提现订单状态查询
6. **[代付结果通知(回调)](http://***********:4999/web/#/*********/211908040)** - 提现结果回调处理
7. **[代收下单接口](http://***********:4999/web/#/*********/211908057)** - 充值订单创建接口详解
8. **[代收订单查询接口](http://***********:4999/web/#/*********/*********)** - 充值订单状态查询
9. **[代收结果通知(回调)](http://***********:4999/web/#/*********/211908041)** - 充值结果回调处理

> **注意**: 上述官方文档链接需要访问密码. 4sXxcWpJ ，本文档基于现有代码实现和API规范整理了完整的接口说明。

# 支付API接口文档

## 目录
1. [快速参考](#快速参考)
2. [通用规则](#通用规则)
3. [签名代码示例](#签名代码示例)
4. [接口调用常见问题](#接口调用常见问题)
5. [代付接口](#代付接口)
   - [代付下单接口](#代付下单接口)
   - [代付订单查询接口](#代付订单查询接口)
   - [代付结果通知(回调)](#代付结果通知回调)
6. [代收接口](#代收接口)
   - [代收下单接口](#代收下单接口)
   - [代收订单查询接口](#代收订单查询接口)
   - [代收结果通知(回调)](#代收结果通知回调)
7. [Elixir集成示例](#elixir集成示例)
8. [附录](#附录)

---

## 快速参考

### 基本配置
```
网关地址: https://api.masterpay88.in/app-api
商户ID: 10236
密钥: ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM
代收通道ID: 3021
代付通道ID: 3020
```

### 核心接口
| 功能 | 接口路径 | 方法 | 说明 |
|------|----------|------|------|
| 创建订单 | `/v1.0/api/order/create` | POST JSON | 代收/代付统一接口 |
| 查询订单 | `/v1.0/api/order/query` | POST JSON | 订单状态查询 |
| 回调通知 | 商户提供的notifyUrl | GET | 支付结果通知 |

### 签名算法要点
1. 非空参数按key字典序排序
2. 拼接格式：`key1=value1&key2=value2&secretKey=密钥`
3. MD5加密后转大写
4. sign参数不参与签名

### 状态码速查
| 代收状态 | 代付状态 | 说明 |
|----------|----------|------|
| 0 | 0 | 支付中 |
| 1 | 1 | 已完成 |
| 3 | 3 | 已超时 |
| - | 5 | 驳回中 |
| - | 7 | 已驳回 |
| - | 9 | 冲正 |

### 关键参数
| 参数 | 代收 | 代付 | 说明 |
|------|------|------|------|
| productId | 3021 | 3020 | 通道区分 |
| userName | - | ✓ | 收款人姓名 |
| cardNumber | - | ✓ | 银行卡号 |
| ifscCode | - | ✓ | IFSC代码 |
| returnUrl | ✓ | - | 前端跳转 |

---

## 通用规则

### 基本信息
- **文档来源**: [通用规则页](http://***********:4999/web/#/*********/211908046)
- **更新时间**: 2025-07-09

### 协议规则
- **传输方式**: 采用HTTPS传输
- **提交方式**: 采用POST/GET方式提交
- **字符编码**: UTF-8
- **签名算法**: MD5（所有涉及到MD5加密，都需要将加密后的字符串所有字符转换为大写）

### 参数规范
- **交易金额**: 默认为卢比交易，单位为分，参数值不能带小数

### 签名算法
1. **对所有发送或接收的数据集合M中的非空参数，按照参数名ASCII码从小到大排序（字典序）**
2. 使用URL键值对格式（key1=value1&key2=value2…）拼接成字符串stringA
3. 拼接加密后的接口密钥secretKey到stringA末尾，得到stringSignTemp字符串
4. 对stringSignTemp进行MD5加密，将结果转换为大写字符，得到sign值

### 注意事项
- 按参数名ASCII码从小到大排序（字典序）
- 值为空的参数不参与签名，参数名区分大小写
- sign参数不参与签名校验，验证签名时需支持支付中心接口增加的扩展字段
- 最好直接遍历整个参数集合，不要使用参数名来单独获取各个参数进行计算签名，不然在参数变动的情况下需要修改对接

### 特别说明
**代收和代付使用同一个下单接口，通过productId来区分是代收还是代付**

---

## 签名代码示例

### 文档信息
- **文档来源**: [签名代码示例](http://***********:4999/web/#/*********/211908048)

### Java签名代码示例
```java
import org.springframework.util.DigestUtils;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

class SignUtil {
    public static String sign(Map<String, Object> params, String secretKey) {
        StringBuilder paramStr = new StringBuilder();
        Map<String, Object> sortParams = new TreeMap<>(params);
        for (Map.Entry<String, Object> entry : sortParams.entrySet()) {
            if (entry.getValue() == null || entry.getValue().equals("null") ||
                entry.getValue().toString().isEmpty() || entry.getKey().equals("sign"))
                continue;
            paramStr.append(entry.getKey())
                    .append("=")
                    .append(entry.getValue())
                    .append("&");
        }
        paramStr.append("secretKey=").append(secretKey.toUpperCase());
        String sign = DigestUtils.md5DigestAsHex(paramStr.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return sign;
    }
}
```

### PHP签名代码示例
```php
<?php
function sign($map, $secretKey) {
    $list = array();
    foreach ($map as $k => $v) {
        if ($v !== '') {
            $list[] = $k . '=' . $v . '&';
        }
    }
    sort($list, SORT_STRING | SORT_FLAG_CASE);
    $result = implode('', $list);
    $result .= 'secretKey=' . $secretKey;
    $result = strtoupper(md5($result));
    return $result;
}

function MD5($s) {
    return md5($s);
}

// Example usage:
$map = array(
    'mchId' => '10000',
    'amount' => '100',
    'orderId' => 'ORDER123456',
    'notifyUrl' => 'http://example.com/notify'
);
$secretKey = 'your_secret_key';
$signature = sign($map, $secretKey);
echo "Signature: " . $signature;
?>
```

### C#签名代码示例
```csharp
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

public class SignatureGenerator {
    /// <summary>
    /// 签名参数
    /// </summary>
    /// <param name="parameters"></param>
    /// <param name="secretsecretKey"></param>
    /// <returns></returns>
    private static string Sign(Dictionary<string, string> parameters, string secretsecretKey) {
        var sortedParams = parameters
            .Where(kvp => kvp.Value != null && !string.IsNullOrWhiteSpace(kvp.Value))
            .OrderBy(kvp => kvp.Key, StringComparer.Ordinal);

        string orderString = string.Join("&", sortedParams.Select(k => $"{k.Key}={k.Value.Trim()}"));
        string stringSignTemp = orderString + $"&secretKey={secretsecretKey}";
        string sign = MD5Encrypt(stringSignTemp);
        return sign;
    }

    /// <summary>
    /// 对字符串进行 MD5 生成哈希
    /// </summary>
    /// <param name="input">待加密的明文字符串</param>
    /// <returns>生成的哈希值</returns>
    private static string MD5Encrypt(string input) {
        byte[] hashBytes = MD5.HashData(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(hashBytes).ToUpper();
    }
}
```

### Python签名代码示例
```python
import hashlib

def sign(map_data, secretKey):
    # Create a list of non-empty key-value pairs
    data_list = []
    for k, v in map_data.items():
        if v != "":
            data_list.append(f"{k}={v}&")

    # Sort the list alphabetically (case-insensitive)
    data_list.sort(key=lambda x: x.lower())

    # Concatenate all elements
    result = ''.join(data_list)

    # Append the secretKey
    result += f"secretKey={secretKey}"

    # Generate MD5 hash and convert to uppercase
    result = md5(result).upper()
    return result

def md5(s):
    return hashlib.md5(s.encode('utf-8')).hexdigest()

# Example usage
if __name__ == "__main__":
    map_data = {
        "mchId": "10000",
        "amount": "100",
        "orderId": "ORDER123456",
        "notifyUrl": "http://example.com/notify"
    }
    secretKey = "your_secret_key"
    signature = sign(map_data, secretKey)
    print(f"Signature: {signature}")
```

---

## 接口调用常见问题

### 文档信息
- **文档来源**: [接口调用常见问题](http://***********:4999/web/#/*********/211908049)

### 常见问题

#### 1. 调用接口返回 {"code":500,"msg":"Internal Server Error","data":null}
**解决方案：**
1. 打印出请求体，检查字段名称拼写是否有错误
2. 检查必传字段是否没有传输，类似于mchId，clientIp（clientIp如果没有则传输0.0.0.0)
3. 检查http请求方式是否与文档中的一致，请求路径是否正确
4. **如果是订单查询接口，请确保请求方式为post json或get form**

#### 2. 创建订单返回资源不足错误
**错误示例：**
```json
{
  "retCode": "FAIL",
  "retMsg": "添加订单时出错: 资源不足",
  "payOrderId": null,
  "payUrl": null,
  "payParams": null,
  "sign": null,
  "orderId": 0,
  "cardInfo": null
}
```
**解决方案：** 这是后台没有支持接单的卡，请检查码商或卡的设置，是否打开接单

#### 3. 签名错误
**解决方案：**
- 请仔细核对通用规则中对于签名算法的描述
- **所有非空非sign字段都要参与签名**
- 最后拼接的密钥名称为**secretKey**，请核对是否正确
- 如果无法找到问题所在，请提供签名原始字符串，联系客服核对

---

## 代付接口

### 代付下单接口

#### 文档信息
- **文档来源**: [代付下单接口](http://***********:4999/web/#/*********/211908038)
- **接口用途**: 提现订单创建
- **简要描述**: 通过该接口，业务系统可发起代付下单请求。创建代付订单时需要加白IP。

#### 接口详情
- **请求URL**: `https://{网关地址}/v1.0/api/order/create`
- **请求方式**: POST JSON

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mchId | string | 是 | 商户ID |
| productId | string | 是 | 通道ID，用于区分代收和代付 |
| mchOrderNo | string | 是 | 商户订单号 |
| amount | integer | 是 | 订单金额，单位分 |
| clientIp | string | 是 | 会员IP，可固定填：0.0.0.0 |
| notifyUrl | string | 是 | 支付结果后台回调URL |
| sign | string | 是 | 签名值 |
| userName | string | 是 | 收款人姓名 |
| cardNumber | string | 是 | 收款人银行卡号 |
| ifscCode | string | 是 | 印度银行卡代付，IFSC代码（11位字母数字，第5位为0） |
| bankName | string | 是 | 收款人开户行名称（可为空值，但参数必须传递） |
| param1 | string | 否 | 扩展参数1，回调时原样返回 |
| param2 | string | 否 | 扩展参数2，回调时原样返回 |

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| retCode | string | 返回状态码，成功返回"SUCCESS"，失败返回"FAIL" |
| retMsg | string | 返回信息，错误原因（如有） |
| sign | string | 仅在retCode为"SUCCESS"时返回，签名 |
| payOrderId | string | 仅在retCode为"SUCCESS"时返回，支付订单号 |
| status | string | 0-支付中，1-已完成，3-已超时，5-驳回中，7-已驳回。状态3，5，7都可以认为代付订单已经失败 |

#### 成功返回示例
```json
{
  "retCode": "SUCCESS",
  "retMsg": "",
  "sign": "C380BEC2BFD727A4B6845133519F3AD6",
  "payOrderId": "123123",
  "status": 0
}
```

### 代付订单查询接口

#### 文档信息
- **文档来源**: [代付订单查询接口](http://***********:4999/web/#/*********/211908039)
- **接口用途**: 提现订单状态查询
- **简要描述**: 获取最新的代付订单状态，用于处理业务逻辑

#### 接口详情
- **请求URL**: `https://{网关地址}/v1.0/api/order/query`
- **请求方式**: POST JSON

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mchId | string | 否 | 商户ID |
| mchOrderNo | string | 否 | 商户订单号 |
| sign | string | 否 | 签名 |

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| retCode | string | 返回状态码，成功返回："SUCCESS"，失败返回："FAIL" |
| retMsg | string | 返回信息，错误原因 |
| mchId | string | 商户ID |
| productId | string | 通道ID |
| payOrderId | string | 平台订单号 |
| mchOrderNo | string | 商户订单号 |
| amount | integer | 订单金额，单位分 |
| realAmount | integer | 实际要求付款金额，单位分 |
| status | integer | 0-支付中，1-已完成，3-已超时，5-驳回中，7-已驳回。状态3，5，7都可以认为代付订单已经失败 |
| utr | string | utr |
| paySuccessTime | number | 支付成功时间，精确到毫秒 |

#### 成功返回示例
```json
{
  "retCode": "string",
  "retMsg": "string",
  "mchId": "string",
  "productId": "string",
  "payOrderId": "string",
  "mchOrderNo": "string",
  "amount": "integer",
  "realAmount": "integer",
  "status": "integer",
  "utr": "string",
  "paySuccessTime": "number"
}
```

### 代付结果通知(回调)

#### 文档信息
- **文档来源**: [代付结果通知(回调)](http://***********:4999/web/#/*********/211908040)
- **接口用途**: 提现结果回调处理
- **简要描述**: 通过统一下单接口设置的 notifyUrl，默认采用GET请求发送，如果需要更换为POST JSON，请联系客服更换回调形式

#### 回调详情
- **请求URL**: `https://{网关地址}/商户传递的代付回调地址`
- **请求方式**: GET

#### 回调参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payOrderId | string | 是 | 平台订单号 |
| mchId | string | 是 | 商户ID |
| productId | string | 是 | 通道ID |
| mchOrderNo | string | 是 | 商户订单号 |
| amount | string | 是 | 订单金额，单位分 |
| realAmount | string | 否 | 实际要求付款金额（浮动金额），单位分 |
| income | string | 否 | 入账金额，单位分，商户入账金额，浮动后扣完手续费 |
| status | string | 是 | 支付状态，1-成功，3-已超时，5-驳回中，7-已驳回，9-冲正。5和7都认为代付订单已经失败。在处理驳回状态的回调时，也需要返回SUCCESS表示回调已处理 |
| paySuccessTime | string | 否 | 支付成功时间，精确到毫秒 |
| sign | string | 是 | 签名 |
| rejectReason | string | 否 | 代付订单驳回通知时的驳回理由 |
| param1 | string | 否 | 扩展参数1，支付中心回调时原样返回 |
| param2 | string | 否 | 扩展参数2，支付中心回调时原样返回 |
| utr | string | 否 | 银行交易参考号（UTR），仅在支付成功时返回 |

#### 成功返回示例
```
SUCCESS
```

---

## 代收接口

### 代收下单接口

#### 文档信息
- **文档来源**: [代收下单接口](http://***********:4999/web/#/*********/211908057)
- **接口用途**: 充值订单创建
- **简要描述**: 通过该接口，业务系统可发起代收下单请求。创建代收订单时需要加白IP

#### 接口详情
- **请求URL**: `https://{网关地址}/v1.0/api/order/create`
- **请求方式**: POST JSON

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mchId | string | 是 | 商户ID |
| productId | string | 是 | 通道ID，用于区分代收和代付 |
| mchOrderNo | string | 是 | 商户订单号 |
| amount | integer | 是 | 订单金额，单位分。不支持小数点 |
| clientIp | string | 是 | 会员IP，可固定填：0.0.0.0 |
| notifyUrl | string | 是 | 支付结果后台回调URL |
| sign | string | 是 | 签名值 |
| returnUrl | string | 否 | 支付结果前端跳转URL |
| subject | string | 否 | 商品主题 |
| body | string | 否 | 商品描述信息 |
| param1 | string | 否 | 扩展参数1，回调时原样返回 |
| param2 | string | 否 | 扩展参数2，回调时原样返回 |
| validateUserName | string | 否 | 会员姓名，实名制模式需填 |
| requestCardInfo | boolean | 否 | 是否需要提供分配的卡信息，默认为false |

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| retCode | string | 返回状态码，成功返回"SUCCESS"，失败返回"FAIL" |
| retMsg | string | 返回信息，错误原因（如有） |
| sign | string | 仅在retCode为"SUCCESS"时返回，签名 |
| payOrderId | string | 仅在retCode为"SUCCESS"时返回，支付订单号 |
| payUrl | string | 仅在retCode为"SUCCESS"时返回，支付页面URL |
| payParams | object | 仅在retCode为"SUCCESS"时返回，支付参数，为JSON格式数据 |
| CardInfo | object | 仅在retCode为"SUCCESS"且requestCardInfo为true时返回，包括银行卡号、姓名、开户行 |

#### 成功返回示例
```json
{
  "retCode": "SUCCESS",
  "retMsg": "",
  "sign": "C380BEC2BFD727A4B6845133519F3AD6",
  "payOrderId": "123123",
  "payUrl": "https://test.com/pay",
  "payParams": {},
  "cardInfo": {
    "bankCard": "*************",
    "userName": "asdf aaaa",
    "bankName": null,
    "validateUserName": null,
    "amount": 10000,
    "timeoutMinutes": 15,
    "link": "upi://pay?ver=01&orgid=159052&mode=01&pa=1234567@kbl&pn=KUMMARI%20CHINNA%20NARASIMHULU&mc=5199&cu=INR&tn=&am="
  }
}
```

### 代收订单查询接口

#### 文档信息
- **文档来源**: [代收订单查询接口](http://***********:4999/web/#/*********/*********)
- **接口用途**: 充值订单状态查询
- **简要描述**: 获取最新的代收订单状态，用于处理业务逻辑

#### 接口详情
- **请求URL**: `https://{网关地址}/v1.0/api/order/query`
- **请求方式**: POST JSON

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mchId | string | 否 | 商户ID |
| mchOrderNo | string | 否 | 商户订单号 |
| sign | string | 否 | 签名 |

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| retCode | string | 返回状态码，成功返回："SUCCESS"，失败返回："FAIL" |
| retMsg | string | 返回信息，错误原因 |
| mchId | string | 商户ID |
| productId | string | 通道ID |
| payOrderId | string | 平台订单号 |
| mchOrderNo | string | 商户订单号 |
| amount | integer | 订单金额，单位分 |
| realAmount | integer | 实际要求付款金额，单位分 |
| status | integer | 订单状态 |
| utr | string | utr |
| paySuccessTime | number | 支付成功时间，精确到毫秒 |

### 代收结果通知(回调)

#### 文档信息
- **文档来源**: [代收结果通知(回调)](http://***********:4999/web/#/*********/211908041)
- **接口用途**: 充值结果回调处理
- **简要描述**: 通过统一下单接口设置的 notifyUrl，默认采用GET请求发送，如果需要更换为POST JSON，请联系客服更换回调形式。amount为发起的订单金额，realAmount为用户实际支付金额，这个金额会有浮动损失，具体按照哪个金额进行回调上分请根据业务需求自行选择

#### 回调详情
- **请求URL**: `https://{网关地址}/商户传递的代收回调地址`
- **请求方式**: GET

#### 回调参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payOrderId | string | 是 | 平台订单号 |
| mchId | string | 是 | 商户ID |
| productId | string | 是 | 通道ID |
| mchOrderNo | string | 是 | 商户订单号 |
| amount | integer | 是 | 订单金额，单位分 |
| realAmount | integer | 是 | 实际要求付款金额（浮动金额），单位分 |
| income | integer | 是 | 入账金额，单位分，商户入账金额，浮动后扣完手续费 |
| status | integer | 是 | 支付状态，0-支付中，1-已完成，3-已超时。超时状态的订单还是可能会变为支付成功状态。超时状态不会触发回调请求，只有订单完成才会触发代收成功回调 |
| paySuccessTime | number | 是 | 支付成功时间，精确到毫秒 |
| sign | string | 是 | 签名 |
| param1 | string | 是 | 扩展参数1，支付中心回调时原样返回 |
| param2 | string | 是 | 扩展参数2，支付中心回调时原样返回 |
| utr | string | 否 | 银行交易参考号（UTR），仅在支付成功时返回 |

#### 成功返回示例
```
SUCCESS
```

---

## 附录

### 状态码说明

#### 代付订单状态
- **0**: 支付中
- **1**: 已完成
- **3**: 已超时
- **5**: 驳回中
- **7**: 已驳回
- **9**: 冲正

**注意**: 状态3、5、7都可以认为代付订单已经失败

#### 代收订单状态
- **0**: 支付中
- **1**: 已完成
- **3**: 已超时

**注意**: 超时状态的订单还是可能会变为支付成功状态。超时状态不会触发回调请求，只有订单完成才会触发代收成功回调

#### 通用返回码
- **SUCCESS**: 操作成功
- **FAIL**: 操作失败

### 重要提醒

1. **IP白名单**: 创建订单时需要加白IP
2. **签名验证**: 所有非空非sign字段都要参与签名
3. **密钥名称**: 最后拼接的密钥名称为`secretKey`
4. **MD5加密**: 所有涉及到MD5加密，都需要将加密后的字符串所有字符转换为大写
5. **金额单位**: 默认为卢比交易，单位为分，参数值不能带小数
6. **回调处理**: 回调成功需要返回`SUCCESS`字符串
7. **接口区分**: 代收和代付使用同一个下单接口，通过productId来区分

### 技术支持

如果在接口对接过程中遇到问题：
1. 请先检查签名算法是否正确
2. 确认所有必填参数是否已传递
3. 验证请求方式和URL是否正确
4. 如果无法解决，请提供签名原始字符串联系客服核对

### Elixir集成示例

基于现有代码库的Elixir实现示例：

```elixir
defmodule MasterPay88.SignatureUtil do
  @moduledoc """
  MasterPay88签名工具模块
  """

  @doc """
  生成签名
  """
  def generate_signature(params, secret_key) do
    params
    |> Enum.reject(fn {_key, value} -> is_nil(value) or value == "" end)
    |> Enum.reject(fn {key, _value} -> key == "sign" end)
    |> Enum.sort_by(fn {key, _value} -> key end)
    |> Enum.map(fn {key, value} -> "#{key}=#{value}" end)
    |> Enum.join("&")
    |> Kernel.<>("&secretKey=#{secret_key}")
    |> then(&:crypto.hash(:md5, &1))
    |> Base.encode16()
  end

  @doc """
  验证签名
  """
  def verify_signature(params, secret_key, received_sign) do
    expected_sign = generate_signature(params, secret_key)
    expected_sign == received_sign
  end
end

defmodule MasterPay88.ApiClient do
  @moduledoc """
  MasterPay88 API客户端
  """

  require Logger

  @base_url "https://api.masterpay88.in/app-api"
  @merchant_id "10236"
  @secret_key "ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM"

  def create_recharge_order(params) do
    request_params = %{
      "mchId" => @merchant_id,
      "productId" => "3021",  # 代收通道
      "mchOrderNo" => params.order_no,
      "amount" => params.amount,
      "clientIp" => params.client_ip || "0.0.0.0",
      "notifyUrl" => params.notify_url,
      "returnUrl" => params.return_url,
      "subject" => params.subject,
      "body" => params.body
    }

    signature = MasterPay88.SignatureUtil.generate_signature(request_params, @secret_key)
    request_params = Map.put(request_params, "sign", signature)

    send_request("/v1.0/api/order/create", request_params)
  end

  def create_withdrawal_order(params) do
    request_params = %{
      "mchId" => @merchant_id,
      "productId" => "3020",  # 代付通道
      "mchOrderNo" => params.order_no,
      "amount" => params.amount,
      "clientIp" => params.client_ip || "0.0.0.0",
      "notifyUrl" => params.notify_url,
      "userName" => params.user_name,
      "cardNumber" => params.card_number,
      "ifscCode" => params.ifsc_code,
      "bankName" => params.bank_name || ""
    }

    signature = MasterPay88.SignatureUtil.generate_signature(request_params, @secret_key)
    request_params = Map.put(request_params, "sign", signature)

    send_request("/v1.0/api/order/create", request_params)
  end

  def query_order(order_no) do
    request_params = %{
      "mchId" => @merchant_id,
      "mchOrderNo" => order_no
    }

    signature = MasterPay88.SignatureUtil.generate_signature(request_params, @secret_key)
    request_params = Map.put(request_params, "sign", signature)

    send_request("/v1.0/api/order/query", request_params)
  end

  defp send_request(path, params) do
    url = @base_url <> path
    headers = [{"Content-Type", "application/json"}]
    body = Jason.encode!(params)

    Logger.info("发送请求到MasterPay88: #{url}")
    Logger.debug("请求参数: #{inspect(params)}")

    case HTTPoison.post(url, body, headers, timeout: 30_000, recv_timeout: 30_000) do
      {:ok, %{status_code: 200, body: response_body}} ->
        case Jason.decode(response_body) do
          {:ok, %{"retCode" => "SUCCESS"} = response} ->
            {:ok, response}
          {:ok, %{"retCode" => "FAIL", "retMsg" => msg}} ->
            {:error, msg}
          {:ok, response} ->
            {:error, "Unknown response: #{inspect(response)}"}
          {:error, reason} ->
            {:error, "JSON decode error: #{inspect(reason)}"}
        end

      {:ok, %{status_code: status_code}} ->
        {:error, "HTTP error: #{status_code}"}

      {:error, reason} ->
        {:error, "Request error: #{inspect(reason)}"}
    end
  end
end
```

### 回调处理示例

```elixir
defmodule MasterPay88.CallbackHandler do
  @moduledoc """
  MasterPay88回调处理器
  """

  require Logger

  @secret_key "ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM"

  def handle_callback(params) do
    with :ok <- verify_signature(params),
         {:ok, result} <- process_callback(params) do
      Logger.info("回调处理成功: #{inspect(result)}")
      {:ok, "SUCCESS"}
    else
      {:error, reason} ->
        Logger.error("回调处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp verify_signature(params) do
    received_sign = Map.get(params, "sign")

    if MasterPay88.SignatureUtil.verify_signature(params, @secret_key, received_sign) do
      :ok
    else
      {:error, "Invalid signature"}
    end
  end

  defp process_callback(%{"status" => "1"} = params) do
    # 处理成功回调
    order_no = params["mchOrderNo"]
    amount = String.to_integer(params["realAmount"] || params["amount"])

    case update_order_status(order_no, :success, amount) do
      {:ok, _} -> {:ok, :processed}
      error -> error
    end
  end

  defp process_callback(%{"status" => status} = params) when status in ["3", "5", "7"] do
    # 处理失败回调
    order_no = params["mchOrderNo"]
    reason = params["rejectReason"] || "Payment failed"

    case update_order_status(order_no, :failed, 0, reason) do
      {:ok, _} -> {:ok, :processed}
      error -> error
    end
  end

  defp process_callback(params) do
    Logger.warning("未处理的回调状态: #{inspect(params)}")
    {:ok, :ignored}
  end

  defp update_order_status(order_no, status, amount, reason \\ nil) do
    # 这里应该调用您的业务逻辑来更新订单状态
    # 例如：PaymentOrder.update_status(order_no, status, amount, reason)
    Logger.info("更新订单状态: #{order_no} -> #{status}, 金额: #{amount}")
    {:ok, :updated}
  end
end
```

### 更新记录

- **2025-07-09**: 初始文档整理完成，包含所有主要接口信息
- **2025-07-09**: 添加Elixir集成示例和回调处理代码

