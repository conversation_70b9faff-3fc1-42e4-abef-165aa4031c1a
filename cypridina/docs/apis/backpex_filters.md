# Backpex 过滤器和搜索

## 概述

Backpex 提供了强大的过滤和搜索功能，允许用户快速找到所需的数据。过滤器可以是下拉选择、文本输入或自定义组件。

## 搜索功能

### 配置可搜索字段

在字段配置中设置 `searchable: true` 来启用搜索：

```elixir
@impl Backpex.LiveResource
def fields do
  [
    username: %{
      module: Backpex.Fields.Text,
      label: "用户名",
      searchable: true  # 启用搜索
    },
    email: %{
      module: Backpex.Fields.Text,
      label: "邮箱",
      searchable: true
    },
    created_at: %{
      module: Backpex.Fields.DateTime,
      label: "创建时间",
      searchable: false  # 不可搜索（默认值）
    }
  ]
end
```

### 全文搜索

配置全文搜索列：

```elixir
use Backpex.LiveResource,
  adapter_config: [...],
  full_text_search: :search_vector  # 指定全文搜索列
```

## 过滤器

### 基本过滤器配置

在 LiveResource 中实现 `filters/0` 回调：

```elixir
@impl Backpex.LiveResource
def filters do
  [
    status: %{
      module: MyApp.Filters.StatusSelect
    },
    category: %{
      module: MyApp.Filters.CategorySelect
    },
    date_range: %{
      module: MyApp.Filters.DateRangeFilter
    }
  ]
end
```

### 动态过滤器

使用 `filters/1` 回调根据当前用户或其他条件动态配置过滤器：

```elixir
@impl Backpex.LiveResource
def filters(assigns) do
  base_filters = [
    status: %{
      module: MyApp.Filters.StatusSelect
    }
  ]

  # 只有管理员才能看到高级过滤器
  if assigns.current_user.role == :admin do
    base_filters ++ [
      advanced_status: %{
        module: MyApp.Filters.AdvancedStatusSelect
      }
    ]
  else
    base_filters
  end
end
```

## 内置过滤器类型

### Select 过滤器

最常用的过滤器类型，提供下拉选择：

```elixir
defmodule MyApp.Filters.StatusSelect do
  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"启用", 1},
      {"禁用", 0}
    ]
  end
end
```

### 动态选项过滤器

从数据库动态获取选项：

```elixir
defmodule MyApp.Filters.CategorySelect do
  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "分类"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择分类..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    case MyApp.Blog.Category.list_active() do
      {:ok, categories} ->
        category_options = 
          categories
          |> Enum.map(fn category -> {category.name, category.id} end)
        
        [{"全部", nil}] ++ category_options
      
      _ ->
        [{"全部", nil}]
    end
  end
end
```

## 自定义过滤器

### 基本自定义过滤器

```elixir
defmodule MyApp.Filters.CustomFilter do
  use Backpex.Filter

  @impl Backpex.Filter
  def label, do: "自定义过滤器"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div class="form-control">
      <label class="label">
        <span class="label-text"><%= label() %></span>
      </label>
      <.input
        field={@form[:value]}
        type="text"
        placeholder="输入搜索条件"
      />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, _filter_config, value, _live_resource) do
    case value do
      nil -> query
      "" -> query
      search_term ->
        import Ecto.Query
        where(query, [item], ilike(item.name, ^"%#{search_term}%"))
    end
  end
end
```

### 日期范围过滤器

```elixir
defmodule MyApp.Filters.DateRangeFilter do
  use Backpex.Filter

  @impl Backpex.Filter
  def label, do: "日期范围"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div class="form-control">
      <label class="label">
        <span class="label-text">开始日期</span>
      </label>
      <.input
        field={@form[:start_date]}
        type="date"
      />
    </div>
    <div class="form-control">
      <label class="label">
        <span class="label-text">结束日期</span>
      </label>
      <.input
        field={@form[:end_date]}
        type="date"
      />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, _filter_config, %{start_date: start_date, end_date: end_date}, _live_resource) do
    import Ecto.Query
    
    query
    |> maybe_filter_start_date(start_date)
    |> maybe_filter_end_date(end_date)
  end

  defp maybe_filter_start_date(query, nil), do: query
  defp maybe_filter_start_date(query, ""), do: query
  defp maybe_filter_start_date(query, start_date) do
    where(query, [item], item.inserted_at >= ^start_date)
  end

  defp maybe_filter_end_date(query, nil), do: query
  defp maybe_filter_end_date(query, ""), do: query
  defp maybe_filter_end_date(query, end_date) do
    where(query, [item], item.inserted_at <= ^end_date)
  end
end
```

## 项目中的实际应用

### 用户过滤器

```elixir
# lib/teen/filters/user_status_select.ex
defmodule Teen.Filters.UserStatusSelect do
  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "用户状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择用户状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"正常", :active},
      {"禁用", :disabled},
      {"待验证", :pending}
    ]
  end
end
```

### 商品类型过滤器

```elixir
# lib/teen/filters/product_type_select.ex
defmodule Teen.Filters.ProductTypeSelect do
  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "商品类型"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择商品类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"月卡", :monthly_card},
      {"周卡", :weekly_card},
      {"次卡", :play_card},
      {"金币礼包", :coin_package},
      {"VIP礼包", :vip_package},
      {"特殊道具", :special_item},
      {"充值奖励包", :recharge_bonus}
    ]
  end
end
```

### 支付状态过滤器

```elixir
# lib/teen/filters/payment_status_select.ex
defmodule Teen.Filters.PaymentStatusSelect do
  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "订单状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择订单状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待支付", "pending"},
      {"支付中", "processing"},
      {"支付成功", "success"},
      {"支付失败", "failed"},
      {"已取消", "cancelled"},
      {"已退款", "refunded"}
    ]
  end
end
```

## 过滤器权限控制

### 基于用户角色的过滤器

```elixir
@impl Backpex.Filter
def can?(assigns) do
  # 只有管理员可以使用此过滤器
  assigns.current_user.permission_level >= 1
end
```

### 动态权限控制

```elixir
@impl Backpex.Filter
def can?(assigns) do
  case assigns.current_user.role do
    :admin -> true
    :manager -> true
    :user -> false
    _ -> false
  end
end
```

## 过滤器查询优化

### 使用索引

确保过滤字段有适当的数据库索引：

```sql
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_products_type ON products(product_type);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

### 复合过滤器

```elixir
@impl Backpex.Filter
def query(query, _filter_config, %{status: status, category: category}, _live_resource) do
  import Ecto.Query
  
  query
  |> maybe_filter_status(status)
  |> maybe_filter_category(category)
end

defp maybe_filter_status(query, nil), do: query
defp maybe_filter_status(query, status) do
  where(query, [item], item.status == ^status)
end

defp maybe_filter_category(query, nil), do: query
defp maybe_filter_category(query, category) do
  where(query, [item], item.category_id == ^category)
end
```

## 最佳实践

1. **性能优化**: 为过滤字段创建数据库索引
2. **用户体验**: 提供清晰的过滤器标签和提示文本
3. **权限控制**: 根据用户角色显示不同的过滤器
4. **默认值**: 为常用过滤器设置合理的默认值
5. **组合过滤**: 支持多个过滤器同时使用
6. **清空过滤**: 提供清空所有过滤器的功能
7. **状态保持**: 在页面刷新后保持过滤器状态

## 常见问题

### 1. 过滤器选项格式

确保使用正确的元组格式：

```elixir
# ✅ 正确
options: [{"显示文本", 值}]

# ❌ 错误
options: [%{label: "显示文本", value: 值}]
```

### 2. 查询性能

对于大数据集，使用适当的查询优化：

```elixir
@impl Backpex.Filter
def query(query, _filter_config, value, _live_resource) do
  # 使用索引字段进行过滤
  import Ecto.Query
  where(query, [item], item.indexed_field == ^value)
end
```

### 3. 动态选项缓存

对于动态选项，考虑使用缓存：

```elixir
@impl Backpex.Filters.Select
def options(_assigns) do
  case :ets.lookup(:filter_cache, :categories) do
    [{:categories, options}] -> options
    [] ->
      options = fetch_categories_from_db()
      :ets.insert(:filter_cache, {:categories, options})
      options
  end
end
```
