# TPMasterClient 活动模块实现文档

## 目录
1. [概述](#概述)
2. [活动系统架构](#活动系统架构)
3. [活动类型详解](#活动类型详解)
4. [通信协议](#通信协议)
5. [数据模型](#数据模型)
6. [UI架构](#ui架构)
7. [实现细节](#实现细节)

## 概述

TPMasterClient的活动系统是游戏的核心功能之一，通过多样化的活动类型提升用户留存和付费转化。系统采用模块化设计，每个活动都是独立的模块，同时共享统一的基础架构。

## 活动系统架构

### 基础架构

所有活动模块都继承自 `PopupLayer` 基类，遵循统一的生命周期和事件管理模式：

```typescript
export default class HallActivityXXX extends PopupLayer {
    // 生命周期方法
    protected onLoad(): void    // 初始化组件
    start()                     // 组件启动
    onDisable()                 // 组件禁用时清理
    protected update(dt: number) // 每帧更新（如倒计时）
    
    // 事件管理
    addLayerEventLister()       // 注册事件监听
    removeLayerEventLister()    // 移除事件监听
    
    // UI管理
    insitUI() / insistLayer()   // 初始化UI
    updateUI()                  // 更新UI显示
}
```

### 文件组织结构

```
TPMasterClient/
├── assets/
│   ├── script/hall/popup/          # 活动弹窗脚本
│   │   ├── HallActivityPopup.ts    # 活动主弹窗
│   │   ├── HallActivityFreePopup.ts # 免费奖励任务
│   │   ├── HallScratchcardPopup.ts # 刮刮卡活动
│   │   ├── HallSignDayPopup.ts     # 7天签到
│   │   ├── HallVipGiftPopup.ts     # VIP礼包
│   │   └── ...
│   └── resources/hall/
│       ├── images/                  # 活动图片资源
│       │   ├── activity/
│       │   ├── scratchcard/
│       │   └── signDay/
│       ├── animations/              # 活动动画资源
│       └── prefabs/popup/           # 活动预制体
```

## 活动类型详解

### 1. 刮刮卡活动（HallScratchcardPopup）

**功能特点**：
- 30天刮刮卡活动，每天可领取刮卡机会
- 通过充值达到条件解锁刮卡次数
- 使用触摸绘制实现刮卡效果
- 本地保存刮卡进度

**核心实现**：
```typescript
// 刮卡触摸处理
_onTouchMoved(touch: cc.Event.EventTouch) {
    let point = this.node.convertToNodeSpaceAR(touch.getLocation());
    let ctx = this.mMask.getComponent(cc.Graphics);
    ctx.circle(point.x, point.y, 40);
    ctx.fill();
    
    // 碰撞检测
    for (let index = 0; index < this.mRewards.length; index++) {
        let rewardData = this.mRewards[index];
        if (this.collisionDetection(point, rewardData.position)) {
            // 记录已刮开的位置
            this.mScratchPosTab[index] = true;
        }
    }
}
```

### 2. 7天签到活动（HallSignDayPopup）

**功能特点**：
- 连续7天签到领取递增奖励
- 每天需要达到充值条件才能领取
- 显示倒计时和签到进度
- 首次打开显示引导动画

**签到数据结构**：
```typescript
{
    daynumber: number,      // 天数（1-7）
    awardcash: number,      // 奖励金额
    chargeamount: number,   // 充值要求
    currentcharge: number,  // 当前充值金额
    fetchaward: number,     // 是否已领取（0/1）
    fetchid: string        // 领取ID
}
```

### 3. 免费奖励任务（HallActivityFreePopup）

**功能特点**：
- 包含三种任务类型：邀请任务、游戏赢分任务、提现任务
- 动态生成任务列表
- 任务进度实时更新
- 完成后跳转对应功能

**任务类型定义**：
```typescript
enum FreeBonusType {
    TypeInvite = 1,      // 邀请任务
    TypeWinTp = 2,       // 游戏赢分任务  
    TypewithDrawal = 3   // 提现任务
}
```

### 4. VIP礼包活动（HallVipGiftPopup）

**功能特点**：
- 展示VIP等级对应的礼包
- 实时更新VIP进度
- 一键领取所有可领礼包
- 显示礼包内容详情

### 5. 邀请好友活动（HallInviteFriendSPopup）

**功能特点**：
- 生成专属邀请码
- 跟踪邀请进度和奖励
- 支持分享到社交平台
- 显示邀请排行榜

## 通信协议

### 协议定义位置
所有活动协议定义在 `HallActivity` 命名空间下：

```typescript
// 免费奖励任务
HallActivity.CS_GET_FREE_BONUS_P = 10      // 请求任务信息
HallActivity.SC_GET_FREE_BONUS_P = 11      // 返回任务信息

// 刮刮卡
HallActivity.CS_GET_THIRTY_CARD_P = 6      // 请求刮刮卡信息
HallActivity.SC_GET_THIRTY_CARD_P = 7      // 返回刮刮卡信息
HallActivity.CS_FETCH_THIRTY_CARD_P = 8    // 领取刮刮卡
HallActivity.SC_FETCH_THIRTY_CARD_P = 9    // 返回领取结果

// 7天签到
HallActivity.CS_SEVEN_DAY_LOGIN_P = 2      // 请求签到信息
HallActivity.SC_SEVEN_DAY_LOGIN_P = 3      // 返回签到信息
HallActivity.CS_FETCH_SEVEN_DAYAWARD_P = 4 // 领取签到奖励
HallActivity.SC_FETCH_SEVEN_DAYAWARD_P = 5 // 返回领取结果
```

### 通信流程

1. **发送请求**：
```typescript
// 通过HallManager发送请求
HallManager.instance.sendHallFeatureReq(
    MainProto.HallActivity,     // 主协议
    HallActivity.CS_XXX_P,      // 子协议
    {}                          // 请求参数
);
```

2. **接收响应**：
```typescript
// 通过EventManager监听响应
EventManager.instance.on(
    GameEvent.HALL_ACTIVITY_XXX, 
    this.onActivityUpdate, 
    this
);
```

## 数据模型

### 1. 免费奖励任务数据模型

```typescript
interface BonusTaskInfo {
    awardbonus: number;      // 奖励总金额
    sharecount: number;      // 需要分享次数
    currentshare: number;    // 当前分享次数
    exchangecount: number;   // 需要提现次数
    currentexchange: number; // 当前提现次数
    gameid: number;         // 游戏ID
    gamewinning: number;    // 需要赢分金额
    currentwinning: number; // 当前赢分金额
}
```

### 2. 刮刮卡数据模型

```typescript
interface ScratchCardInfo {
    currentlevel: number;    // 当前等级（天数）
    maxcards: number;        // 最大可领取卡片数
    currentcard: number;     // 已领取卡片数
    limitcharge: number;     // 充值条件
    currentcharge: number;   // 当前充值金额
    isfetch: number;        // 今日是否已领取
    status: number;         // 活动状态 0:未激活 1:已激活
    second: number;         // 下次领取倒计时
    // 三个奖励位置
    oneaward: string;       // 奖励1金额
    onetype: number;        // 奖励1类型
    twoaward: string;       // 奖励2金额
    twotype: string;        // 奖励2类型
    threeaward: string;     // 奖励3金额
    threetype: number;      // 奖励3类型
}
```

### 3. 签到数据模型

```typescript
interface SignDayInfo {
    signlist: SignDayItem[];  // 7天签到列表
}

interface SignDayItem {
    daynumber: number;       // 天数（1-7）
    awardcash: number;       // 奖励金额
    chargeamount: number;    // 充值要求
    currentcharge: number;   // 当前充值
    fetchaward: number;      // 是否已领取
    fetchid: string;        // 领取ID
}
```

## UI架构

### 1. UI加载机制

使用 `LoadAllLayerObject` 统一加载UI元素：

```typescript
protected onLoad(): void {
    // 加载所有UI元素到LayerItems
    LoadAllLayerObject(this.node);
    
    // 通过LayerItems访问UI元素
    this.LayerItems.btnClose.on('click', this.onBtnClose, this);
}
```

### 2. 动态内容生成

```typescript
// 创建任务项
let item = cc.instantiate(this.LayerItems.itemLayout);
this.LayerItems.content.addChild(item);

// 设置任务内容
let lblTitle = item.getChildByName("lblTitle");
lblTitle.getComponent(cc.Label).string = "邀请3位好友";
```

### 3. 动画系统

```typescript
// 加载并播放动画
this.loadAsset("hall/animations/guide/GuideFinger2", cc.Prefab, (assets) => {
    let node = cc.instantiate(assets);
    this.LayerItems.guide_anim.addChild(node);
    
    // 播放动画
    let anim = node.getComponent(cc.Animation);
    anim.play("guide_finger");
});
```

## 实现细节

### 1. 状态管理

活动通常包含多种状态，需要妥善管理：

```typescript
// 刮刮卡状态管理
private updateCardStatus() {
    // 未激活状态
    if (this.mData.status == 0) {
        this.LayerItems.btnTake.active = false;
        this.LayerItems.lblNotActive.active = true;
    }
    // 已领取状态
    else if (this.mData.isfetch == 1) {
        this.LayerItems.btnTake.active = false;
        this.LayerItems.lblAlreadyTake.active = true;
    }
    // 可领取状态
    else {
        this.LayerItems.btnTake.active = true;
    }
}
```

### 2. 倒计时处理

```typescript
protected update(dt: number): void {
    if (this.mCountDown > 0) {
        this.mCountDown--;
        let h = Math.floor(this.mCountDown / 3600);
        let m = Math.floor((this.mCountDown % 3600) / 60);
        let s = this.mCountDown % 60;
        
        this.LayerItems.lblTime.getComponent(cc.Label).string = 
            `${StringUtil.formatNumber(h)}:${StringUtil.formatNumber(m)}:${StringUtil.formatNumber(s)}`;
    }
}
```

### 3. 本地存储

```typescript
// 保存刮卡进度
private saveProgress() {
    cc.sys.localStorage.setItem(
        this.mStorageKey, 
        JSON.stringify(this.mScratchPosTab)
    );
}

// 读取刮卡进度
private loadProgress() {
    let data = cc.sys.localStorage.getItem(this.mStorageKey);
    if (data) {
        this.mScratchPosTab = JSON.parse(data);
    }
}
```

### 4. 错误处理

```typescript
// 网络请求错误处理
private onRequestError(event) {
    let { code, message } = event.data;
    
    if (code === ErrorCode.INSUFFICIENT_BALANCE) {
        ToastManager.instance.show("余额不足");
    } else {
        ToastManager.instance.show(message || "请求失败");
    }
}
```

### 5. 性能优化

1. **资源按需加载**：
```typescript
// 只在需要时加载资源
private loadRewardAssets() {
    this.loadAsset("hall/images/activity/reward", cc.SpriteFrame, (asset) => {
        this.LayerItems.imgReward.getComponent(cc.Sprite).spriteFrame = asset;
    });
}
```

2. **事件及时清理**：
```typescript
onDisable() {
    // 移除所有事件监听，防止内存泄漏
    this.removeLayerEventLister();
    
    // 停止所有计时器
    this.unscheduleAllCallbacks();
}
```

3. **UI复用**：
```typescript
// 复用任务项节点
private taskItemPool: cc.NodePool = new cc.NodePool();

private getTaskItem(): cc.Node {
    return this.taskItemPool.get() || cc.instantiate(this.taskItemPrefab);
}

private putTaskItem(item: cc.Node) {
    this.taskItemPool.put(item);
}
```

## 最佳实践

1. **统一的错误提示**：使用 ToastManager 显示错误信息
2. **加载提示**：网络请求时显示 loading 动画
3. **防重复点击**：使用标志位防止重复请求
4. **数据缓存**：通过 DataManager 缓存活动数据，减少请求
5. **引导提示**：首次使用时显示操作引导
6. **动画反馈**：操作成功时播放奖励动画增强体验

## 扩展开发指南

创建新活动时，建议遵循以下步骤：

1. 继承 `PopupLayer` 基类
2. 实现基础生命周期方法
3. 定义活动数据模型
4. 实现网络通信协议
5. 设计UI布局和动画
6. 添加必要的本地存储
7. 实现错误处理和提示
8. 添加数据上报和统计

通过遵循这套架构，可以快速开发新的活动模块，并保持代码的一致性和可维护性。