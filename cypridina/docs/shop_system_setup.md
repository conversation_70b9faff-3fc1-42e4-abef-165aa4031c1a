# 商店系统初始化指南

## 快速开始

### 方法一：一键初始化（推荐）

```bash
# 运行完整初始化脚本
./scripts/setup_shop_system_complete.sh
```

这个脚本会自动完成：
1. 生成数据库迁移文件
2. 运行数据库迁移
3. 创建商品模板和示例商品
4. 编译项目

### 方法二：手动初始化

```bash
# 1. 生成数据库迁移
mix ash.codegen --name create_shop_system_tables

# 2. 运行数据库迁移
mix ecto.migrate

# 3. 运行种子文件
mix run priv/repo/seeds/shop_system_seeds.exs

# 4. 编译项目
mix compile
```

### 方法三：仅运行种子文件

如果数据库已经迁移，只需要创建示例数据：

```bash
mix run priv/repo/seeds/shop_system_seeds.exs
```

## 初始化后的数据

### 商品模板（7个）
- 标准月卡模板
- 标准周卡模板
- 标准次卡模板
- 标准金币礼包模板
- 标准VIP礼包模板
- 标准特殊道具模板
- 标准充值奖励包模板

### 示例商品（20个）

#### 月卡系列（3个）
- **新手月卡** - ₹19.99 - 30天每日800游戏币
- **豪华月卡** - ₹49.99 - 30天每日1500游戏币
- **至尊月卡** - ₹99.99 - 30天每日2500游戏币

#### 周卡系列（2个）
- **精品周卡** - ₹9.99 - 7天每日500游戏币
- **豪华周卡** - ₹15.99 - 7天每日800游戏币

#### 次卡系列（3个）
- **10次游戏卡** - ₹7.99 - 10次游戏，1.1倍奖励
- **30次游戏卡** - ₹19.99 - 30次游戏，1.2倍奖励
- **100次游戏卡** - ₹59.99 - 100次游戏，1.5倍奖励

#### 金币礼包系列（4个）
- **5000金币礼包** - ₹24.99 - 5000币+10%奖励
- **10000金币礼包** - ₹49.99 - 10000币+20%奖励
- **50000金币礼包** - ₹199.99 - 50000币+30%奖励
- **100000金币礼包** - ₹399.99 - 100000币+50%奖励

#### VIP礼包系列（3个）
- **VIP1礼包** - ₹99.99 - VIP1等级+5000币+10次游戏
- **VIP2礼包** - ₹249.99 - VIP2等级+15000币+30次游戏
- **VIP3礼包** - ₹499.99 - VIP3等级+50000币+100次游戏

#### 特殊道具系列（3个）
- **幸运符** - ₹14.99 - 7天幸运值+20%
- **经验加速器** - ₹9.99 - 3天经验+50%
- **金币倍增器** - ₹19.99 - 5天金币+100%

#### 充值奖励包系列（2个）
- **首充奖励包** - ₹9.99 - 首充专享2000币
- **每日充值奖励包** - ₹4.99 - 每日专享1000币

## 后台管理

初始化完成后，可以通过以下地址访问后台管理：

- **商品管理**: http://localhost:4000/admin/products
- **商品模板管理**: http://localhost:4000/admin/product-templates  
- **购买记录管理**: http://localhost:4000/admin/user-purchases

## API 接口

### 获取商品列表
```http
GET /api/shop/products
```

可选参数：
- `category` - 商品分类筛选
- `type` - 商品类型筛选

### 获取商品详情
```http
GET /api/shop/products/:id
```

### 创建购买订单
```http
POST /api/shop/purchase
Content-Type: application/json

{
  "product_id": "商品ID",
  "discount_rate": "0.1"  // 可选，折扣率
}
```

### 支付回调
```http
POST /api/shop/payment_callback
Content-Type: application/json

{
  "purchase_id": "购买ID",
  "transaction_id": "交易ID",
  "status": "completed"
}
```

### 获取购买历史
```http
GET /api/shop/purchases?page=1&limit=20
```

## 商品配置说明

每个商品都有 `product_config` 字段，包含商品特定的配置：

### 月卡/周卡配置
```json
{
  "daily_reward": 1000,        // 每日奖励金额
  "total_days": 30,            // 总天数
  "card_benefits": [           // 卡片特权列表
    "每日登录获得游戏币",
    "VIP特权加成"
  ]
}
```

### 次卡配置
```json
{
  "play_count": 30,            // 游戏次数
  "bonus_multiplier": 1.2,     // 奖励倍率
  "valid_days": 30             // 有效期天数
}
```

### 金币礼包配置
```json
{
  "coin_amount": 10000,        // 游戏币数量
  "bonus_percentage": 20,      // 额外奖励百分比
  "instant_delivery": true     // 是否立即发放
}
```

### VIP礼包配置
```json
{
  "vip_level": 1,              // VIP等级
  "vip_duration": 30,          // VIP时长（天）
  "included_items": [          // 包含的物品
    {"type": "coins", "amount": 5000},
    {"type": "play_times", "amount": 10}
  ]
}
```

## 自定义商品

### 通过后台管理创建
1. 访问 `/admin/products`
2. 点击"新建"按钮
3. 填写商品信息和配置
4. 保存商品

### 通过代码创建
```elixir
Product.create(%{
  name: "自定义商品",
  product_type: :monthly_card,
  price: Decimal.new("2999"),
  currency: :inr,
  product_config: %{
    "daily_reward" => 1000,
    "total_days" => 30
  }
})
```

## 故障排除

### 数据库迁移失败
```bash
# 重置数据库
mix ecto.reset

# 重新运行迁移
mix ecto.migrate
```

### 种子文件执行失败
```bash
# 检查数据库连接
mix ecto.create

# 重新运行种子文件
mix run priv/repo/seeds/shop_system_seeds.exs
```

### 编译错误
```bash
# 清理编译缓存
mix clean

# 重新编译
mix compile
```

## 下一步

1. **启动开发服务器**: `mix phx.server`
2. **访问后台管理**: http://localhost:4000/admin/products
3. **测试API接口**: 使用 Postman 或 curl 测试
4. **自定义商品**: 根据游戏需求添加新商品
5. **集成支付**: 将商品购买流程与支付网关集成

现在你的游戏拥有了完整的商品销售系统！🎉
