# 30次刮卡活动协议实现

## 概述

本文档描述了在websocket_handler中添加的30次刮卡活动相关协议的实现，包括：
- 101,12协议（请求30次卡活动信息）
- 101,14协议（领取30次卡活动奖励）

## 协议详情

### 1. 获取活动信息协议

#### 请求协议
- **主协议ID**: 101 (HallActivity)
- **子协议ID**: 12 (CS_GET_THIRTY_CARD_P)
- **请求数据**: 空对象 `{}`

#### 响应协议
- **主协议ID**: 101 (HallActivity)
- **子协议ID**: 13 (SC_GET_THIRTY_CARD_P)
- **响应数据**: 详细的30次刮卡活动信息

### 2. 领取奖励协议

#### 请求协议
- **主协议ID**: 101 (HallActivity)
- **子协议ID**: 14 (CS_FETCH_THIRTY_CARD_P)
- **请求数据**:
  ```json
  {
    "cardindex": 5,    // 卡片索引 (0-29)
    "level": 1         // 等级 (1-3)
  }
  ```

#### 响应协议
- **主协议ID**: 101 (HallActivity)
- **子协议ID**: 15 (SC_FETCH_THIRTY_CARD_P)
- **响应数据**: 领取结果信息
  ```json
  {
    "code": 0,           // 状态码：0=成功，1001=等级无效，1002=卡片索引无效
    "msg": "领取成功",    // 状态消息
    "cardindex": 5,      // 卡片索引
    "level": 1,          // 等级
    "awardtype": 0,      // 奖励类型：0=积分，1=金币
    "awardnum": 3500     // 奖励数量
  }
  ```

## 实现细节

### 1. 协议架构

30次刮刮卡活动使用WebSocket协议进行通信，通过以下架构处理：

```
WebSocket消息 → websocket_handler.ex → ProtocolRouter → HallActivityProtocol → ScratchCardService
```

### 2. 获取活动信息实现

通过 `Teen.Protocol.HallActivityProtocol.handle_protocol/3` 函数处理协议12：

- 获取用户VIP信息和累计充值金额
- 根据充值金额计算用户当前等级
- 返回活动配置和用户当前状态
- 支持断线重连，恢复待领取状态

### 3. 领取奖励实现

通过协议14处理，支持两种模式：

#### 两步领取模式（推荐）
- **fetchtype=0**: 生成3张随机奖励卡片，保存待领取状态
- **fetchtype=1**: 确认领取并发放所有奖励

#### 单步领取模式（兼容旧版本）
- 通过cardindex和level参数直接领取指定卡片

#### 业务逻辑
- **等级判定**: 基于VIP累计充值金额
- **奖励生成**: 根据等级配置随机生成奖励
- **防重复**: 检查已领取记录避免重复领取
- **实时发放**: 通过Ledger系统实际发放积分/金币

### 4. 关键特性

1. **VIP系统集成**
   - 根据用户VIP累计充值确定刮卡等级
   - 不同等级有不同的奖励范围

2. **数据持久化**
   - 使用Ash框架管理所有数据操作
   - 完整的参与记录和奖励发放历史

3. **断线重连支持**
   - 保存待领取状态到数据库
   - 用户重连后可恢复待确认的奖励

4. **两步领取机制**
   - 增强用户体验的"开箱"效果
   - 防止网络问题导致的奖励丢失

## 测试

测试文件位于 `test/teen/protocol/thirty_card_test.exs`，包含：

- 获取活动信息的完整测试
- 成功领取奖励的测试
- 参数验证错误的测试
- 不同等级奖励类型的测试

运行测试：
```bash
mix test test/teen/protocol/thirty_card_test.exs
```

## 实现位置

### 1. websocket_handler.ex 修改

在 `lib/teen/protocol/websocket_handler.ex` 文件中：

1. **协议路由添加** (第499-509行):
   ```elixir
   def handle_message(%{main_id: @main_proto_hall_activity, sub_id: sub_id} = message, state)
       when sub_id in [0, 2, 4, 6, 8, 10, 12, 14, 20, 22, 24, 26, 28, 30, 32, 34, 36, 40, 44, 46, 48, 50] do
     # 使用新的协议路由器处理
     case ProtocolRouter.route_protocol(@main_proto_hall_activity, sub_id, message.data || %{}, context) do
       {:ok, response} -> {:reply, response, state}
       {:error, reason} -> # 错误处理
     end
   end
   ```

2. **协议处理器实现** - 在 `lib/teen/protocol/hall_activity_protocol.ex` 中：
   ```elixir
   # 通过统一的 handle_protocol/3 接口处理
   def handle_protocol(12, data, context) do
     # 处理获取30次刮卡活动信息
   end
   
   def handle_protocol(14, data, context) do  
     # 处理领取30次刮卡奖励
   end
   ```

## 响应数据结构

```json
{
  "mainId": 101,
  "subId": 13,
  "data": {
    "code": 0,
    "msg": "获取成功",
    "user": {
      "currentlevel": 1,        // 当前等级
      "maxcards": 30,           // 最大可领取卡片
      "currentcard": 0,         // 已领取卡片
      "limitcharge": 10000,     // 达成条件（分）
      "currentcharge": 0,       // 已充值金额（分）
      "isfetch": 0,             // 今日是否已领取 0 否 1是
      "second": 0,              // 可领取时间 倒计时秒
      "status": 1,              // 状态 0 未激活 1 激活
      "lastfetchstatus": 0      // 上次领取状态 0 未获取 1 待领取 2 已领取
    },
    "leveldata": {
      "1": {
        "levelnumber": 1,
        "limitcharge": 10000,   // 达成条件（分）
        "levelaward": [
          {
            "awardtype": 0,     // 奖励类型 0 积分 1 金币
            "minaward": 1000,   // 最小金额
            "maxaward": 5000    // 最大金额
          }
        ]
      },
      "2": { /* 第二级配置 */ },
      "3": { /* 第三级配置 */ }
    }
  }
}
```

## 测试

创建了测试文件 `test/teen/protocol/thirty_card_test.exs` 来验证协议实现：

- 测试协议路由正确性
- 验证响应数据结构
- 确保所有必要字段存在且类型正确

## 使用方式

客户端可以通过发送以下消息来请求30次刮卡活动信息：

```json
{
  "main_id": 101,
  "sub_id": 12,
  "data": {}
}
```

服务器将返回包含完整活动信息的响应，包括用户当前状态和各等级配置。

## 注意事项

1. 当前实现返回的是静态数据，实际应用中需要根据用户ID查询数据库获取真实的用户活动状态
2. 奖励配置和等级设置可以根据实际需求进行调整
3. 所有金额单位为分（1元 = 100分）
