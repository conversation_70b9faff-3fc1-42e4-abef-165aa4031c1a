充值、提现、短信验证、手机验证码登录和绑定
1. 请参考sms.md文档接入短信服务，借鉴https://hexdocs.pm/ash/wrap-external-apis.html将其包装为ash资源，只供服务器内部调用，只需要实现发送接口，并使用hammer做速率限制
2. 请参考如下信息接入充值，也包装成ash资源，并使用ash reactor：

网关地址: https://api.masterpay88.in/app-api
下单地址: /v1.0/api/order/create
查询地址: /v1.0/api/order/query
回调ip: *************

在线对接文档: 链接:  http://***********:4999/web/#/646544147/211908040. 4sXxcWpJ
**详细API文档已整理到: [payment-api.md](./payment-api.md)**

通道ID:
代收综合通道: 3021
代付综合通道: 3020
 
3. 使用第一个步骤提供的短信发送接口，用AshAuthentication实现手机验证码注册登录和游客账户绑定手机

用户系统
    1. 封号
    2. 渠道（多租户）

统计分析

游戏记录
客服系统
游戏管理
活动管理
推广系统/代理系统

后台权限分级

1. 6月16~21：
    1. 5款子游戏（剩余TeenPatti、TeenPattiAK47, PotBlind未全部完成）
    2. 金币体系
    3. 游戏接入金币系统、流水记录
2. 6月23~28：
    1. 积分系统
        1. 充值、提现
        2. 流水洗码
    2. 用户系统
        1. 手机验证码登录和绑定
        2. 用户封号、设备封号、IP封号
        3. 推广系统
        4. 用户系统接入渠道
        5. 渠道包
    3. 游戏
        5. TeenPatti、TeenPattiAK47, PotBlind完成
        7. 测试问题修复
        6. 接入控分
    4. 测试内容：
        1. 6款游戏
        2. 用户金币流水
        3. 封号功能
3. 6月30~7月5日：
    1. 游戏记录
    2. 游戏管理
    3. 活动管理
    6. 控分优化
    7. 测试内容：
        1. 手机登录、充值提现
        2. 渠道
        3. 封号

 6月23~28：
    1. 充值、手机验证码登录和绑定
    2. 用户封号、设备封号、IP封号
    3. 用户系统接入渠道
    4. TeenPatti、PotBlind完成
    5. 接入控制线算法
    6. 5款游戏配合测试修改
    7. 管理后台系统设置、公告等界面
6月30~7月5日：
    1. 游戏管理
    3. 控制线优化
    4. 控制线算法对应的后台设置功能
    
    2. 活动管理
    3. 充值、提现

    5. 测试内容：
        1. 手机注册登录、绑定手机
        2. 用户头像、昵称修改
        3. 顶号
        4. 龙虎接入控制线 
        5. 充值
        6. 封号
        7. TeenPatti
        8. PotBlind

4. 7月7号~7月12日：
    1. 客服系统
    2. 后台权限分级
    3. 优化游戏监控
    4. 统计分析
    4. 控分优化
    5. 真实机器人
5. 7月14号~7月19日
    2. 部署流程
    3. 分布式蓝绿部署

龙虎斗
    1. 筹码显示错误
    2. 获胜8%抽水
    3. 去掉区域下注限额

    4. 在线人数同步
    5. 重连
    6. 庄家算分错误

    7. 筹码刷新
    8. 钱包金额变动通知

—— 游戏钱包 完成

玩家信息
-- 修改头像
-- 修改昵称
-- 绑定邮箱
-- 充值、赢钱、奖励金钱的统计（流水洗码）

多设备登入
-- 渠道系统
-- 绑定手机号
-- 封号
-- 手机号注册登录
    

推广系统

接入充值


### 游戏活动配置总表

| 活动类型             | 活动名称                         | 详细配置 |
|----------------------|----------------------------------|----------|
| **游戏任务**         | 游戏任务                         | **每日游戏任务**<br>• 搜索项：游戏、任务类型<br>• 数据项：编号、任务名称、游戏ID、游戏名称、任务类型（游戏局数\|游戏赢的局数）、局数、领取次数、奖励金额、修改时间、状态（启用、禁用）<br>• 操作项：新增\|编辑任务、删除任务、启用\|禁用 |
| **周卡任务（Crads）**| 周卡任务                         | **周卡配置**<br>• 示例：<br>  - 充值500：初始奖励10，每日领取50（共7天）<br>  - 充值1000：初始奖励30，每日领取80<br>  - 充值5000：初始奖励180，每日领取150<br>• 数据项：编号、标题、充值金额、奖励金额（自动计算）、初始奖励、领取天数、每日领取、修改时间、状态（启用\|禁用）<br>• 操作项：新增\|编辑任务、删除任务、启用\|禁用<br>• 编辑项：标题、充值金额、初始奖励、领取天数、每日领取 |
| **七次任务（7-days）**| 七次任务                        | **连续登录任务**<br>• 规则：连续登录7天可循环，中断则重置<br>• 数据项：编号、领取次数（第几天）、奖励金币、是否循环、修改时间、状态（启用\|禁用）<br>• 操作项：新增\|编辑任务、启用\|禁用<br>• 编辑项：领取次数、奖励金币、是否循环 |
| **VIP礼包（VIP Gift）**| VIP礼包                         | **VIP等级奖励**<br>• 数据项：VIP等级、每日奖励、每周奖励、每月奖励、状态（启用\|禁用）<br>• 操作项：编辑、启用\|禁用<br>• 编辑项：VIP等级、每日奖励、每周奖励、每月奖励 |
| **充值任务**         | 充值任务（充值页面加提示设计）   | **充值奖励**<br>• 规则：充值X金额奖励Y金币<br>• 数据项：编号、充值金额、奖励金币、修改时间、状态（启用\|禁用）<br>• 操作项：新增、编辑任务、删除、启用\|禁用<br>• 编辑项：充值金额、奖励金币 |
| **充值转盘**         | 充值转盘                         | **转盘奖金池系统**<br>• 数据项：序号、累计充值金币、转盘次数、修改时间、状态（启用\|禁用）<br>• 操作项：新增\|编辑任务、删除任务、启用\|禁用、中奖配置<br>• 编辑项：充值金币（累计）、转盘次数<br><br>**中奖配置**<br>• 数据项：序号、中奖类型（金币/现金/奖金池百分比）、中奖类型值、概率、修改时间<br>• 操作项：新增\|修改、删除<br>• 特殊规则：奖金池百分比奖励最后触发 |
| **30刮刮卡任务（30 Cards）**| 任务奖励                  | **多级任务系统**<br>**主配置**<br>• 数据项：活动编号、活动标题、可领取数量（30）、奖励概率、修改时间、状态（启用\|禁用）<br>• 操作项：新增\|编辑任务、删除任务、启用\|禁用<br><br>**子配置**<br>1. 任务轮次配置：轮次、充值金额、修改时间（操作：新增\|编辑、删除）<br>2. 任务等级配置：编号、任务等级、充值金额、修改时间（操作：新增\|编辑、删除）<br>3. 等级奖励配置：任务等级、奖励类型（金币/现金）、最小奖励、最大奖励（展示）、实际最大奖励（计算）、概率、排序号、修改时间（操作：新增\|编辑、删除） |
| **首充礼包（Sale）** | 首充礼包                         | **新手福利**<br>• 数据项：编号、标题、限制天数（注册天数）、获得金币、修改时间、状态（启用\|禁用）<br>• 操作项：编辑、删除、启用\|禁用<br>• 编辑项：标题、限制天数、获得金币 |
| **输钱返利金罐子**   | 输钱返利金罐子                   | **输钱返利**<br>• 规则：每日领取前一天损失10%<br>• 数据项：编号、标题、领取次数、用户输钱金币值、输钱奖励（百分比）、奖励上限、状态（启用\|禁用）<br>• 操作项：编辑、启用\|禁用<br>• 编辑项：标题、领取次数、用户输钱值、输钱奖励、奖励上限 |
| **拼多多邀请提现（Free Cash）**| 拼多多邀请提现（Free Cash） | **邀请活动**<br>• 数据项：编号、标题、奖励总金额、初始最小值、初始最大值、修改时间、状态（启用\|禁用）<br>• 操作项：编辑、启用\|禁用、奖励配置<br><br>**奖励配置**<br>• 数据项：编号、轮次、奖励类型（邀请新朋友/邀请充值）、奖励最小值、奖励最大值<br>• 操作项：新增、编辑、删除 |
| **手机绑定**         | 手机、邮箱绑定                   | **绑定奖励**<br>• 数据项：编号、标题、奖励金额、修改时间、状态（启用\|禁用）<br>• 操作项：修改、启用\|禁用<br>• 修改项：标题、奖励金额 |
| **免费任务（Free bonus）**| 免费任务（Free bonus）      | **分享任务**<br>• 数据项：编号、标题、分享次数、游戏、需要赢金币、提现次数、修改时间、状态（启用\|禁用）<br>• 操作项：修改、启用\|禁用<br>• 修改项：标题、分享次数、游戏、需要赢金币、提现次数 |
| **CDKey活动**        | CDKey活动                        | **兑换码系统**<br>• 搜索项：创建时间区间、奖励类型<br>• 数据项：编号、领取KEY、奖励类型、总奖励金币、总奖励数量、已领取数量、已领取金币、有效天数、可领取用户VIP等级、创建时间、状态（启用\|禁用）<br>• 操作项：新增\|编辑、删除、启用\|禁用、领取用户列表<br>• 编辑项：奖励类型、总奖励金币、总奖励数量、有效天数、可领取用户<br><br>**领取记录**<br>• 数据项：序号、用户ID、用户账户、领取金币、领取时间 |


1. 日报信息
    新增设备	当日新增注册账号的手机设备数量
    新增注册	当日新增注册账号的数量
    有效新增	当日新增的下注用户数
    手机注册绑定	绑定手机号的人数
    活跃用户	登录用户数量
    有效活跃	登录用户中有下注行为的用户数量
    活跃手机绑定	活跃用户中有绑定手机号的人数
    充值人数	每日充值人数
    新增充值人数	新增注册用户中的充值用户人数
    首付人数	每人首充的用户人数
    新增充值额	每日新增注册玩家的充值总额
    退出总额	提现金额
    退出人数	提现人数
    退充比	退充比=提现金额÷充值金额（看充值金额中提现金额的占比）
    付费率	付费率=总充值人数÷总注册人数（此数据看充值人数在总注册人数中的占比，也叫充值率）
    新增付费率	新增付费率=新增充值人数÷新增注册人数（此数据看新增用户中，新增充值用户的占比）
    有效付费率	有效付费率=充值人数÷有效活跃数（此数据反馈有效活跃用户中，充值人数的占比）
    有效新增付费率	有效新增付费率=新增充值人数÷有效新增人数（看新增有下注行为的人数中，充值人数的占比）
    ARPU	ARPU=充值总额÷活跃用户人数（注册用户的平均消费贡献金额）
    ARPPU	ARPPU=充值总额÷充值人数（充值用户的平均消费贡献金额）
    有效ARPU	有效ARPU=充值总额÷有效活跃人数（活跃下注用户的平均消费贡献金额）
    次留	注册用户第二天复充的人数占比
    3留	注册用户连续3天充值的人数占比
    7留	注册用户连续7天充值的人数占比
    14留	注册用户连续14天充值的人数占比
    30留	注册用户连续30天充值的人数占比

2. 流水洗码规则
    Deposit：充值获得的本金（可以直接玩游戏），设定默认流水倍数1倍（配置项）

    Bonus：充值额外赠送的金币（可以直接玩游戏），不同的赠送类型可以设定不同的流水倍数

    举例
    1、用户充值100真钱获得 奖励：100本金（流水倍数1倍）；20 bonus额外赠送（流水倍数5倍），则 写入用户流水值=100*1+20*5=200，用户需要完成200的有效投注才可以降当前的余额全部转换成可提现

    2、当用户再次充值100真钱获得 奖励100本金（流水倍数1倍）；20 bonus额外赠送（流水倍数5倍），则将流水值100*1+20*5=200增加到流水目标值中，此时流水要求=400，以此类推

    流水值清0

    当用户当前的余额<=5时，初始化流水值为0


    流水值的显示
    大厅点击余额需要展示 用户当前剩余需要的流水值（例：流水目标=400，用户已经完成100的有效投注，则显示剩余所需300）

    提现界面需要展示用户当前剩余需要的流水值


    类型	流水倍数
    存钱罐领取	5
    Cash back流水返利	5
    VIP奖励	5
    每日任务奖励	5
    注册赠送	5
    绑定手机赠送	5
    Lucky spin转盘 奖励	5
    7天登录奖励	5
    月卡额外奖励	5
    周首充额外奖励	5
    破产充值额外奖励	5
    Bonus code 兑换码	5


3. 扣款逻辑
    1、优先扣除deposit，扣除为deposit时，赢取也是deposit
    2、没有deposit，则扣除winnings，扣除为winnings时，赢取也是winnings
    3、如果同时扣除了deposit和winnings，则赢取为deposit

    或者可以设定为：如果目前流水目标>0,则赢取统一为deposit

4. 幸运值系统


你的任务是分析需要创建哪些字段来满足给定的业务需求。请仔细阅读以下各项信息：
<日报信息>
{{DAILY_REPORT_INFO}}
</日报信息>
<流水洗码规则>
{{WASH_CODE_RULES}}
</流水洗码规则>
<扣款逻辑>
{{DEDUCTION_LOGIC}}
</扣款逻辑>
<幸运值系统信息>
{{LUCKY_VALUE_SYSTEM_INFO}}
</幸运值系统信息>
在分析时，请考虑各项业务规则和数据统计的要求，梳理出满足这些需求所必须创建的字段。
首先，在<思考>标签中详细阐述你对每个业务模块所需字段的分析过程，思考每个业务规则如何转化为具体的数据字段。
然后，在<回答>标签中列出你认为需要创建的字段，确保涵盖所有业务需求且字段之间互不重叠。
<思考>
[在此详细分析需要创建的字段]
</思考>
<回答>
[在此列出需要创建的字段]
</回答>
请确保你的分析全面、准确，能够满足所有业务需求。





你的任务是为使用Elixir + Phoenix + Ash Framework开发的金币游戏，提供一种不通过充值和游戏逻辑来更新任务进度的更好实现方式。请仔细阅读以下游戏开发的相关信息：
<游戏开发信息>
{{GAME_DEVELOPMENT_INFO}}
</游戏开发信息>
为了找到合适的实现方式，请按照以下步骤进行分析：
1. 全面理解游戏中各个系统（活动系统、充值系统、任务系统等）的需求和数据依赖关系。
2. 思考监控事件流的可行性，分析事件流可能包含的关键事件。
3. 考虑其他可能的替代方案，如使用消息队列、定时任务等。
4. 评估每个方案的优缺点，包括实现难度、性能影响、可维护性等。
5. 形成初步的建议。
6. 再次检查，确保没有遗漏重要细节。

在<思考>标签中详细分析每个可能的实现方式，包括其原理、优缺点和适用场景。然后在<回答>标签中给出最终的建议，并详细解释选择该建议的理由。请确保你的回答丰富、全面，能充分说明每个方案的特点和选择依据。
<思考>
[在此分析各种可能的实现方式]
</思考>
<回答>
[在此给出最终的建议和详细解释]
</回答>



1. 游戏里有些奖励不是马上发送给玩家的，比如月卡、任务奖励、 储钱罐，而是需要手动领取的，这些奖励我如何为之设计数据资源
2. 月卡、任务之类的活动是每日刷新的？我如何利用ash_oban来编写每日刷新逻辑？


