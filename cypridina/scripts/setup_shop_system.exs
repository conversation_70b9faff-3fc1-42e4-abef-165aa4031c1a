#!/usr/bin/env elixir

# 商品系统初始化脚本
# 使用方法: mix run scripts/setup_shop_system.exs

IO.puts("🚀 开始初始化商品系统...")

# 1. 生成数据库迁移
IO.puts("📋 生成数据库迁移文件...")

# 使用 mix ash.codegen 生成迁移
System.cmd("mix", ["ash.codegen", "--name", "create_shop_system_tables"])

IO.puts("✅ 数据库迁移文件已生成")

# 2. 运行迁移
IO.puts("🔄 运行数据库迁移...")
System.cmd("mix", ["ecto.migrate"])
IO.puts("✅ 数据库迁移完成")

# 3. 创建默认商品模板
IO.puts("📦 创建默认商品模板...")

# 启动应用
Application.ensure_all_started(:cypridina)

# 创建模板
Teen.ShopSystem.ProductTemplates.create_default_templates()

IO.puts("✅ 默认商品模板创建完成")

# 4. 创建示例商品
IO.puts("🛍️ 创建示例商品...")

alias Teen.ShopSystem.Product

# 月卡商品
monthly_card_data = %{
  name: "豪华月卡",
  description: "购买后30天内每日可领取1000游戏币",
  product_type: :monthly_card,
  category: "卡类商品",
  sku: "MONTHLY_CARD_LUXURY",
  price: Decimal.new("2999"),  # 29.99 INR
  currency: :inr,
  product_config: %{
    "daily_reward" => 1000,
    "total_days" => 30,
    "card_benefits" => [
      "每日登录获得1000游戏币",
      "VIP特权加成",
      "专属客服服务"
    ]
  },
  display_config: %{
    "icon" => "monthly_card_icon.png",
    "color" => "#FFD700",
    "badge" => "热门"
  },
  sort_order: 1
}

case Product.create(monthly_card_data) do
  {:ok, product} ->
    IO.puts("✅ 创建月卡商品成功: #{product.name}")
  {:error, reason} ->
    IO.puts("❌ 创建月卡商品失败: #{inspect(reason)}")
end

# 周卡商品
weekly_card_data = %{
  name: "精品周卡",
  description: "购买后7天内每日可领取500游戏币",
  product_type: :weekly_card,
  category: "卡类商品",
  sku: "WEEKLY_CARD_PREMIUM",
  price: Decimal.new("999"),   # 9.99 INR
  currency: :inr,
  product_config: %{
    "daily_reward" => 500,
    "total_days" => 7,
    "card_benefits" => [
      "每日登录获得500游戏币",
      "周卡专属活动"
    ]
  },
  display_config: %{
    "icon" => "weekly_card_icon.png",
    "color" => "#87CEEB",
    "badge" => "推荐"
  },
  sort_order: 2
}

case Product.create(weekly_card_data) do
  {:ok, product} ->
    IO.puts("✅ 创建周卡商品成功: #{product.name}")
  {:error, reason} ->
    IO.puts("❌ 创建周卡商品失败: #{inspect(reason)}")
end

# 30次卡商品
play_card_data = %{
  name: "30次游戏卡",
  description: "获得30次游戏机会，奖励倍率1.2倍",
  product_type: :play_card,
  category: "卡类商品",
  sku: "PLAY_CARD_30",
  price: Decimal.new("1999"),  # 19.99 INR
  currency: :inr,
  product_config: %{
    "play_count" => 30,
    "bonus_multiplier" => 1.2,
    "valid_days" => 30
  },
  display_config: %{
    "icon" => "play_card_icon.png",
    "color" => "#98FB98"
  },
  sort_order: 3
}

case Product.create(play_card_data) do
  {:ok, product} ->
    IO.puts("✅ 创建30次卡商品成功: #{product.name}")
  {:error, reason} ->
    IO.puts("❌ 创建30次卡商品失败: #{inspect(reason)}")
end

# 金币礼包
coin_package_data = %{
  name: "10000金币礼包",
  description: "立即获得10000游戏币+20%额外奖励",
  product_type: :coin_package,
  category: "金币商品",
  sku: "COIN_PACKAGE_10K",
  price: Decimal.new("4999"),  # 49.99 INR
  currency: :inr,
  product_config: %{
    "coin_amount" => 10000,
    "bonus_percentage" => 20,
    "instant_delivery" => true
  },
  display_config: %{
    "icon" => "coin_package_icon.png",
    "color" => "#FFD700",
    "badge" => "超值"
  },
  sort_order: 4
}

case Product.create(coin_package_data) do
  {:ok, product} ->
    IO.puts("✅ 创建金币礼包商品成功: #{product.name}")
  {:error, reason} ->
    IO.puts("❌ 创建金币礼包商品失败: #{inspect(reason)}")
end

# VIP礼包
vip_package_data = %{
  name: "VIP1礼包",
  description: "升级到VIP1，享受30天特权+5000游戏币+10次游戏",
  product_type: :vip_package,
  category: "VIP商品",
  sku: "VIP_PACKAGE_1",
  price: Decimal.new("9999"),  # 99.99 INR
  currency: :inr,
  product_config: %{
    "vip_level" => 1,
    "vip_duration" => 30,
    "included_items" => [
      %{"type" => "coins", "amount" => 5000},
      %{"type" => "play_times", "amount" => 10}
    ]
  },
  display_config: %{
    "icon" => "vip_package_icon.png",
    "color" => "#FF6347",
    "badge" => "限时"
  },
  sort_order: 5
}

case Product.create(vip_package_data) do
  {:ok, product} ->
    IO.puts("✅ 创建VIP礼包商品成功: #{product.name}")
  {:error, reason} ->
    IO.puts("❌ 创建VIP礼包商品失败: #{inspect(reason)}")
end

IO.puts("🎉 商品系统初始化完成！")
IO.puts("")
IO.puts("📋 系统功能:")
IO.puts("  • 商品管理后台: /admin/products")
IO.puts("  • 商品模板管理: /admin/product-templates")
IO.puts("  • 购买记录管理: /admin/user-purchases")
IO.puts("")
IO.puts("🔗 API接口:")
IO.puts("  • 获取商品列表: GET /api/shop/products")
IO.puts("  • 获取商品详情: GET /api/shop/products/:id")
IO.puts("  • 创建购买订单: POST /api/shop/purchase")
IO.puts("  • 支付回调: POST /api/shop/payment_callback")
IO.puts("  • 购买历史: GET /api/shop/purchases")
IO.puts("")
IO.puts("✨ 现在你的支付系统可以销售具体的商品，而不只是按比例充值金币了！")
