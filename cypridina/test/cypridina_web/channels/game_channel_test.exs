defmodule CypridinaWeb.GameChannelTest do
  use CypridinaWeb.ChannelCase

  setup do
    {:ok, _, socket} =
      CypridinaWeb.PhoenixSocket
      |> socket("user_id", %{session_id: "test_session", user_id: nil, authenticate: false})
      |> subscribe_and_join(CypridinaWeb.GameChannel, "game:lobby")

    %{socket: socket}
  end

  test "handle message with mainId and subId", %{socket: socket} do
    # 测试登录消息
    message = %{
      "mainId" => 0,
      "subId" => 2,
      "data" => %{
        "username" => "test_user",
        "password" => "test_pass"
      }
    }

    ref = push(socket, "message", message)

    assert_reply ref, :ok, response

    assert response["mainId"] == 0
    assert response["subId"] == 3
    assert response["data"]["status"] == 0
    assert response["data"]["username"] == "test_user"
    assert is_binary(response["data"]["userid"])
  end

  test "handle heartbeat message", %{socket: socket} do
    # 测试心跳消息
    message = %{
      "mainId" => 0,
      "subId" => 19,
      "data" => %{}
    }

    ref = push(socket, "message", message)

    assert_reply ref, :ok, response

    assert response["mainId"] == 0
    assert response["subId"] == 20
    assert is_integer(response["data"]["server_time"])
  end

  test "handle heartbeat event", %{socket: socket} do
    ref = push(socket, "heartbeat", %{})

    assert_reply ref, :ok, response

    assert response["mainId"] == 0
    assert response["subId"] == 19
    assert is_integer(response["data"]["server_time"])
  end
end
