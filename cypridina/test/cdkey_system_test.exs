defmodule CdkeySystemTest do
  @moduledoc """
  CDKEY系统基础功能测试
  """
  
  alias Teen.ActivitySystem.{CdkeyService, CdkeyManagementService}
  
  @doc """
  测试CDKEY系统基础功能
  """
  def test_basic_functionality do
    IO.puts("🧪 开始测试CDKEY系统基础功能...")
    
    # 测试1: 生成兑换码
    IO.puts("📝 测试1: 生成兑换码")
    reward_config = %{
      type: :coins,
      amount: 1000,
      items: %{},
      max_uses: 1,
      valid_from: DateTime.utc_now(),
      valid_to: DateTime.add(DateTime.utc_now(), 7 * 24 * 60 * 60, :second),
      creator: "系统测试",
      purpose: "测试生成"
    }
    
    case CdkeyService.generate_codes("test_batch_#{System.unique_integer()}", 5, reward_config) do
      results when is_list(results) ->
        successful = Enum.filter(results, fn {status, _} -> status == :ok end)
        IO.puts("   ✅ 成功生成 #{length(successful)} 个兑换码")
        
        # 获取第一个成功生成的兑换码进行后续测试
        if length(successful) > 0 do
          {:ok, test_cdkey} = hd(successful)
          test_code_validation(test_cdkey.code)
        else
          IO.puts("   ❌ 没有成功生成兑换码")
        end
        
      error ->
        IO.puts("   ❌ 生成兑换码失败: #{inspect(error)}")
    end
    
    IO.puts("🎉 CDKEY系统测试完成!")
  end
  
  defp test_code_validation(code) do
    IO.puts("📝 测试2: 验证兑换码")
    
    case CdkeyService.validate_code(code) do
      {:ok, cdkey} ->
        IO.puts("   ✅ 兑换码验证成功: #{cdkey.code}")
        IO.puts("   📊 奖励信息: #{cdkey.reward_type} - #{cdkey.reward_amount}")
        
        test_code_status(code)
        
      {:error, reason} ->
        IO.puts("   ❌ 兑换码验证失败: #{inspect(reason)}")
    end
  end
  
  defp test_code_status(code) do
    IO.puts("📝 测试3: 查询兑换码状态")
    
    case CdkeyService.get_code_status(code) do
      {:ok, status_info} ->
        IO.puts("   ✅ 状态查询成功")
        IO.puts("   📋 状态: #{status_info.status}")
        IO.puts("   🔢 剩余使用次数: #{status_info.remaining_uses}")
        
      {:error, reason} ->
        IO.puts("   ❌ 状态查询失败: #{inspect(reason)}")
    end
  end
end

# 运行测试
IO.puts("=" <> String.duplicate("=", 50))
IO.puts("CDKEY兑换配置系统测试")
IO.puts("=" <> String.duplicate("=", 50))

try do
  CdkeySystemTest.test_basic_functionality()
rescue
  error ->
    IO.puts("❌ 测试过程中发生错误: #{inspect(error)}")
    IO.puts("📝 这可能是因为数据库表还未创建，请先运行数据库迁移")
end