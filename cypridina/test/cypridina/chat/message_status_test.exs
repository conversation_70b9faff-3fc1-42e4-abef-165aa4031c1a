defmodule Cypridina.Chat.MessageStatusTest do
  use Cypridina.DataCase
  
  alias Cypridina.Chat.{ChatService, ChatSession, ChatMessage, MessageReadReceipt, MessageDeliveryReceipt}
  alias Cypridina.Accounts.User
  
  describe "message delivery status" do
    setup do
      # 创建测试用户
      {:ok, user1} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user1_#{System.unique_integer()}",
          email: "user1_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
        
      {:ok, user2} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user2_#{System.unique_integer()}",
          email: "user2_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
      
      # 创建聊天会话
      {:ok, session} = ChatService.create_or_get_private_session(user1.id, user2.id)
      
      %{user1: user1, user2: user2, session: session}
    end
    
    test "mark message as delivered", %{user1: user1, user2: user2, session: session} do
      # user1 发送消息
      {:ok, message} = ChatService.send_message(session.id, user1.id, "Hello!")
      
      # user2 标记消息为已送达
      {:ok, receipt} = ChatService.mark_message_as_delivered(message.id, user2.id)
      
      assert receipt.message_id == message.id
      assert receipt.user_id == user2.id
      assert receipt.delivered_at != nil
      
      # 验证不能标记自己发送的消息
      {:error, :cannot_mark_own_message} = 
        ChatService.mark_message_as_delivered(message.id, user1.id)
    end
    
    test "mark session messages as delivered", %{user1: user1, user2: user2, session: session} do
      # user1 发送多条消息
      {:ok, msg1} = ChatService.send_message(session.id, user1.id, "Message 1")
      {:ok, msg2} = ChatService.send_message(session.id, user1.id, "Message 2")
      {:ok, msg3} = ChatService.send_message(session.id, user1.id, "Message 3")
      
      # user2 批量标记消息为已送达
      {:ok, _} = ChatService.mark_session_messages_as_delivered(session.id, user2.id)
      
      # 验证所有消息都已送达
      query = 
        MessageDeliveryReceipt
        |> Ash.Query.filter(expr(user_id == ^user2.id))
        
      {:ok, receipts} = Ash.read(query)
      assert length(receipts) == 3
      
      message_ids = Enum.map(receipts, & &1.message_id) |> Enum.sort()
      expected_ids = [msg1.id, msg2.id, msg3.id] |> Enum.sort()
      assert message_ids == expected_ids
    end
    
    test "message status calculation", %{user1: user1, user2: user2, session: session} do
      # 发送消息
      {:ok, message} = ChatService.send_message(session.id, user1.id, "Test message")
      
      # 初始状态应该是 sent
      message = message |> Ash.load!([:delivery_receipts, :read_receipts])
      assert message.status == :sent
      assert message.delivery_receipts == []
      assert message.read_receipts == []
      
      # 标记为已送达
      {:ok, _} = ChatService.mark_message_as_delivered(message.id, user2.id)
      
      message = 
        ChatMessage 
        |> Ash.get!(message.id)
        |> Ash.load!([:delivery_receipts, :read_receipts])
        
      assert length(message.delivery_receipts) == 1
      assert hd(message.delivery_receipts).user_id == user2.id
      
      # 标记为已读
      {:ok, _} = ChatService.mark_message_as_read(message.id, user2.id)
      
      message = 
        ChatMessage 
        |> Ash.get!(message.id)
        |> Ash.load!([:delivery_receipts, :read_receipts])
        
      assert length(message.read_receipts) == 1
      assert hd(message.read_receipts).user_id == user2.id
    end
  end
  
  describe "unread message count" do
    setup do
      # 创建测试用户
      {:ok, user1} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user1_#{System.unique_integer()}",
          email: "user1_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
        
      {:ok, user2} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user2_#{System.unique_integer()}",
          email: "user2_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
      
      # 创建聊天会话
      {:ok, session} = ChatService.create_or_get_private_session(user1.id, user2.id)
      
      %{user1: user1, user2: user2, session: session}
    end
    
    test "get unread count for session", %{user1: user1, user2: user2, session: session} do
      # 初始未读数应该为0
      {:ok, count} = ChatService.get_unread_count(session.id, user2.id)
      assert count == 0
      
      # user1 发送消息
      {:ok, _msg1} = ChatService.send_message(session.id, user1.id, "Message 1")
      {:ok, _msg2} = ChatService.send_message(session.id, user1.id, "Message 2")
      
      # user2 的未读数应该为2
      {:ok, count} = ChatService.get_unread_count(session.id, user2.id)
      assert count == 2
      
      # user1 的未读数应该为0（自己发送的消息不计入）
      {:ok, count} = ChatService.get_unread_count(session.id, user1.id)
      assert count == 0
    end
    
    test "unread count updates after marking as read", %{user1: user1, user2: user2, session: session} do
      # user1 发送消息
      {:ok, msg1} = ChatService.send_message(session.id, user1.id, "Message 1")
      {:ok, msg2} = ChatService.send_message(session.id, user1.id, "Message 2")
      {:ok, _msg3} = ChatService.send_message(session.id, user1.id, "Message 3")
      
      # 初始未读数为3
      {:ok, count} = ChatService.get_unread_count(session.id, user2.id)
      assert count == 3
      
      # 标记第一条消息为已读
      {:ok, _} = ChatService.mark_message_as_read(msg1.id, user2.id)
      
      {:ok, count} = ChatService.get_unread_count(session.id, user2.id)
      assert count == 2
      
      # 批量标记到第二条消息为已读
      {:ok, _} = ChatService.mark_session_messages_as_read(session.id, user2.id, msg2.id)
      
      {:ok, count} = ChatService.get_unread_count(session.id, user2.id)
      assert count == 1
    end
    
    test "get all unread counts", %{user1: user1, user2: user2} do
      # 创建多个会话
      {:ok, session1} = ChatService.create_or_get_private_session(user1.id, user2.id)
      
      # 创建第三个用户
      {:ok, user3} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user3_#{System.unique_integer()}",
          email: "user3_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
        
      {:ok, session2} = ChatService.create_or_get_private_session(user1.id, user3.id)
      
      # 在不同会话中发送消息
      {:ok, _} = ChatService.send_message(session1.id, user2.id, "From user2")
      {:ok, _} = ChatService.send_message(session1.id, user2.id, "From user2 again")
      {:ok, _} = ChatService.send_message(session2.id, user3.id, "From user3")
      
      # 获取 user1 的所有未读消息统计
      {:ok, counts} = ChatService.get_all_unread_counts(user1.id)
      
      assert counts[session1.id] == 2
      assert counts[session2.id] == 1
    end
  end
  
  describe "session calculations" do
    setup do
      # 创建测试用户
      {:ok, user1} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user1_#{System.unique_integer()}",
          email: "user1_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
        
      {:ok, user2} = 
        User
        |> Ash.Changeset.for_create(:create, %{
          username: "user2_#{System.unique_integer()}",
          email: "user2_#{System.unique_integer()}@test.com",
          password: "password123"
        })
        |> Ash.create()
      
      # 创建聊天会话
      {:ok, session} = ChatService.create_or_get_private_session(user1.id, user2.id)
      
      %{user1: user1, user2: user2, session: session}
    end
    
    test "session unread_count calculation", %{user1: user1, user2: user2, session: session} do
      # 发送一些消息
      {:ok, _} = ChatService.send_message(session.id, user1.id, "Message 1")
      {:ok, _} = ChatService.send_message(session.id, user1.id, "Message 2")
      
      # 加载带有未读计数的会话
      session = 
        ChatSession
        |> Ash.get!(session.id)
        |> Ash.load!(unread_count: %{user_id: user2.id})
        
      assert session.unread_count == 2
    end
    
    test "last_unread_message_id calculation", %{user1: user1, user2: user2, session: session} do
      # 发送消息
      {:ok, msg1} = ChatService.send_message(session.id, user1.id, "Message 1")
      {:ok, msg2} = ChatService.send_message(session.id, user1.id, "Message 2")
      {:ok, msg3} = ChatService.send_message(session.id, user1.id, "Message 3")
      
      # 标记前两条为已读
      {:ok, _} = ChatService.mark_message_as_read(msg1.id, user2.id)
      {:ok, _} = ChatService.mark_message_as_read(msg2.id, user2.id)
      
      # 加载会话
      session = 
        ChatSession
        |> Ash.get!(session.id)
        |> Ash.load!(last_unread_message_id: %{user_id: user2.id})
        
      # 最后一条未读消息应该是 msg3
      assert session.last_unread_message_id == msg3.id
    end
  end
end