defmodule Cypridina.Chat.MessageHandlerTest do
  @moduledoc """
  测试优化的消息处理模块
  """

  use Cypridina.DataCase, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Chat.{MessageHandler, ChatSession, ChatMessage, ChatParticipant}
  alias Cypridina.Accounts.User

  describe "send_messages_batch/1" do
    setup do
      user1 = insert(:user)
      user2 = insert(:user)
      session = insert(:chat_session, creator_id: user1.id)

      insert(:chat_participant, session_id: session.id, user_id: user1.id)
      insert(:chat_participant, session_id: session.id, user_id: user2.id)

      %{user1: user1, user2: user2, session: session}
    end

    test "成功批量发送消息", %{user1: user1, session: session} do
      messages_data = [
        %{
          session_id: session.id,
          sender_id: user1.id,
          content: "第一条消息",
          message_type: :text
        },
        %{
          session_id: session.id,
          sender_id: user1.id,
          content: "第二条消息",
          message_type: :text
        }
      ]

      assert {:ok, messages} = MessageHandler.send_messages_batch(messages_data)
      assert length(messages) == 2
      assert Enum.all?(messages, &(&1.sender_id == user1.id))
    end

    test "验证失败时返回错误", %{session: session} do
      messages_data = [
        %{
          session_id: session.id,
          # 缺少 sender_id
          content: "无效消息",
          message_type: :text
        }
      ]

      assert {:error, :missing_required_fields} =
               MessageHandler.send_messages_batch(messages_data)
    end
  end

  describe "get_session_messages/3" do
    setup do
      user1 = insert(:user)
      user2 = insert(:user)
      session = insert(:chat_session, creator_id: user1.id)

      insert(:chat_participant, session_id: session.id, user_id: user1.id)
      insert(:chat_participant, session_id: session.id, user_id: user2.id)

      # 创建一些测试消息
      messages =
        for i <- 1..10 do
          insert(:chat_message,
            session_id: session.id,
            sender_id: user1.id,
            content: "消息 #{i}",
            inserted_at: DateTime.add(DateTime.utc_now(), i, :second)
          )
        end

      %{user1: user1, user2: user2, session: session, messages: messages}
    end

    test "成功获取会话消息", %{user1: user1, session: session} do
      assert {:ok, messages} = MessageHandler.get_session_messages(session.id, user1.id)
      # 默认限制
      assert length(messages) <= 50
      assert Enum.all?(messages, &(&1.session_id == session.id))
    end

    test "支持分页参数", %{user1: user1, session: session} do
      assert {:ok, messages} =
               MessageHandler.get_session_messages(session.id, user1.id,
                 limit: 5,
                 offset: 0
               )

      assert length(messages) == 5
    end

    test "非参与者无法获取消息", %{session: session} do
      other_user = insert(:user)

      assert {:error, :not_participant} =
               MessageHandler.get_session_messages(session.id, other_user.id)
    end
  end

  describe "mark_messages_read_batch/3" do
    setup do
      user1 = insert(:user)
      user2 = insert(:user)
      session = insert(:chat_session, creator_id: user1.id)

      insert(:chat_participant, session_id: session.id, user_id: user1.id)
      insert(:chat_participant, session_id: session.id, user_id: user2.id)

      messages =
        for _i <- 1..3 do
          insert(:chat_message, session_id: session.id, sender_id: user1.id)
        end

      %{user1: user1, user2: user2, session: session, messages: messages}
    end

    test "成功批量标记消息为已读", %{user2: user2, session: session, messages: messages} do
      message_ids = Enum.map(messages, & &1.id)

      assert {:ok, receipts} =
               MessageHandler.mark_messages_read_batch(session.id, user2.id, message_ids)

      assert length(receipts) == length(message_ids)
    end

    test "非参与者无法标记消息", %{session: session, messages: messages} do
      other_user = insert(:user)
      message_ids = Enum.map(messages, & &1.id)

      assert {:error, :not_participant} =
               MessageHandler.mark_messages_read_batch(session.id, other_user.id, message_ids)
    end
  end

  describe "get_unread_count/2" do
    setup do
      user1 = insert(:user)
      user2 = insert(:user)
      session = insert(:chat_session, creator_id: user1.id)

      insert(:chat_participant, session_id: session.id, user_id: user1.id)
      insert(:chat_participant, session_id: session.id, user_id: user2.id)

      # 创建一些未读消息
      messages =
        for _i <- 1..5 do
          insert(:chat_message, session_id: session.id, sender_id: user1.id)
        end

      %{user1: user1, user2: user2, session: session, messages: messages}
    end

    test "正确计算未读消息数量", %{user2: user2, session: session} do
      assert {:ok, count} = MessageHandler.get_unread_count(session.id, user2.id)
      # user2 没有读过任何消息
      assert count == 5
    end

    test "已读消息不计入未读数量", %{user2: user2, session: session, messages: messages} do
      # 标记前3条消息为已读
      message_ids = messages |> Enum.take(3) |> Enum.map(& &1.id)
      {:ok, _} = MessageHandler.mark_messages_read_batch(session.id, user2.id, message_ids)

      assert {:ok, count} = MessageHandler.get_unread_count(session.id, user2.id)
      # 还有2条未读
      assert count == 2
    end

    test "自己发送的消息不计入未读", %{user1: user1, session: session} do
      assert {:ok, count} = MessageHandler.get_unread_count(session.id, user1.id)
      # 自己发送的消息不算未读
      assert count == 0
    end
  end

  describe "handle_message_sent_async/1" do
    setup do
      user = insert(:user)
      session = insert(:chat_session, creator_id: user.id)
      message = insert(:chat_message, session_id: session.id, sender_id: user.id)

      %{user: user, session: session, message: message}
    end

    test "异步处理消息发送后任务", %{message: message} do
      # 这个函数是异步的，我们主要测试它不会崩溃
      assert :ok = MessageHandler.handle_message_sent_async(message)
    end
  end

  # 性能测试
  describe "performance tests" do
    setup do
      user1 = insert(:user)
      user2 = insert(:user)
      session = insert(:chat_session, creator_id: user1.id)

      insert(:chat_participant, session_id: session.id, user_id: user1.id)
      insert(:chat_participant, session_id: session.id, user_id: user2.id)

      %{user1: user1, user2: user2, session: session}
    end

    test "批量发送大量消息的性能", %{user1: user1, session: session} do
      messages_data =
        for i <- 1..100 do
          %{
            session_id: session.id,
            sender_id: user1.id,
            content: "性能测试消息 #{i}",
            message_type: :text
          }
        end

      {time_microseconds, {:ok, messages}} =
        :timer.tc(fn ->
          MessageHandler.send_messages_batch(messages_data)
        end)

      assert length(messages) == 100
      # 确保批量操作在合理时间内完成（小于1秒）
      assert time_microseconds < 1_000_000

      # 记录性能指标
      IO.puts("批量发送100条消息耗时: #{time_microseconds / 1000}ms")
    end

    test "大量消息的分页查询性能", %{user1: user1, user2: user2, session: session} do
      # 创建大量消息
      for i <- 1..1000 do
        insert(:chat_message,
          session_id: session.id,
          sender_id: user1.id,
          content: "大量消息测试 #{i}"
        )
      end

      {time_microseconds, {:ok, messages}} =
        :timer.tc(fn ->
          MessageHandler.get_session_messages(session.id, user2.id, limit: 50)
        end)

      assert length(messages) == 50
      # 确保查询在合理时间内完成（小于100ms）
      assert time_microseconds < 100_000

      # 记录性能指标
      IO.puts("查询50条消息（从1000条中）耗时: #{time_microseconds / 1000}ms")
    end

    test "并发消息发送性能", %{user1: user1, user2: user2, session: session} do
      # 测试并发发送消息的性能
      tasks =
        for i <- 1..20 do
          Task.async(fn ->
            MessageHandler.send_messages_batch([
              %{
                session_id: session.id,
                sender_id: if(rem(i, 2) == 0, do: user1.id, else: user2.id),
                content: "并发消息 #{i}",
                message_type: :text
              }
            ])
          end)
        end

      {time_microseconds, results} =
        :timer.tc(fn ->
          Task.await_many(tasks, 5000)
        end)

      # 验证所有任务都成功完成
      assert Enum.all?(results, &match?({:ok, _}, &1))

      # 确保并发操作在合理时间内完成
      # 2秒
      assert time_microseconds < 2_000_000

      IO.puts("并发发送20条消息耗时: #{time_microseconds / 1000}ms")
    end

    test "批量标记已读性能", %{user1: user1, user2: user2, session: session} do
      # 创建大量消息
      messages =
        for i <- 1..500 do
          insert(:chat_message,
            session_id: session.id,
            sender_id: user1.id,
            content: "已读测试消息 #{i}"
          )
        end

      message_ids = Enum.map(messages, & &1.id)

      {time_microseconds, {:ok, _receipts}} =
        :timer.tc(fn ->
          MessageHandler.mark_messages_read_batch(session.id, user2.id, message_ids)
        end)

      # 确保批量标记已读在合理时间内完成
      # 500ms
      assert time_microseconds < 500_000

      IO.puts("批量标记500条消息已读耗时: #{time_microseconds / 1000}ms")
    end
  end

  # 辅助函数
  defp insert(factory, attrs \\ %{}) do
    # 这里需要根据实际的factory实现
    # 或者使用ExMachina等测试工厂库
    case factory do
      :user ->
        %User{
          id: Ecto.UUID.generate(),
          nickname: "test_user_#{System.unique_integer()}",
          email: "test#{System.unique_integer()}@example.com"
        }
        |> Map.merge(attrs)

      :chat_session ->
        %ChatSession{
          id: Ecto.UUID.generate(),
          session_type: :private,
          status: :active,
          participant_count: 0,
          message_count: 0
        }
        |> Map.merge(attrs)

      :chat_participant ->
        %ChatParticipant{
          id: Ecto.UUID.generate(),
          role: :member,
          status: :active,
          joined_at: DateTime.utc_now()
        }
        |> Map.merge(attrs)

      :chat_message ->
        %ChatMessage{
          id: Ecto.UUID.generate(),
          content: "测试消息",
          message_type: :text,
          status: :sent,
          inserted_at: DateTime.utc_now()
        }
        |> Map.merge(attrs)
    end
  end
end
