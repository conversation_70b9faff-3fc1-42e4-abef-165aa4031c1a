defmodule Cypridina.Chat.ErrorHandlerTest do
  @moduledoc """
  测试聊天系统错误处理模块
  """

  use Cypridina.DataCase, async: true

  alias Cypridina.Chat.ErrorHandler

  describe "handle_session_error/2" do
    test "处理会话不存在错误" do
      error = {:error, :not_found}
      context = %{session_id: "test-session-id", user_id: "test-user-id"}

      assert {:error, %{type: :session_not_found, message: message}} =
               ErrorHandler.handle_session_error(error, context)

      assert message == "聊天会话不存在"
    end

    test "处理权限拒绝错误" do
      error = {:error, :permission_denied}
      context = %{session_id: "test-session-id", user_id: "test-user-id"}

      assert {:error, %{type: :session_permission_denied, message: message}} =
               ErrorHandler.handle_session_error(error, context)

      assert message == "无权限访问此会话"
    end

    test "处理会话已满错误" do
      error = {:error, :session_full}
      context = %{session_id: "test-session-id"}

      assert {:error, %{type: :session_full, message: message}} =
               ErrorHandler.handle_session_error(error, context)

      assert message == "会话参与者已满"
    end

    test "处理未知错误" do
      error = {:error, :some_unknown_error}
      context = %{session_id: "test-session-id"}

      assert {:error, %{type: :session_unknown_error, message: message}} =
               ErrorHandler.handle_session_error(error, context)

      assert String.contains?(message, "会话操作失败")
    end
  end

  describe "handle_message_error/2" do
    test "处理消息不存在错误" do
      error = {:error, :message_not_found}
      context = %{message_id: "test-message-id"}

      assert {:error, %{type: :message_not_found, message: message}} =
               ErrorHandler.handle_message_error(error, context)

      assert message == "消息不存在"
    end

    test "处理消息内容过长错误" do
      error = {:error, :content_too_long}
      context = %{session_id: "test-session-id"}

      assert {:error, %{type: :content_too_long, message: message}} =
               ErrorHandler.handle_message_error(error, context)

      assert message == "消息内容过长"
    end

    test "处理无效消息类型错误" do
      error = {:error, :invalid_message_type}
      context = %{message_type: "invalid"}

      assert {:error, %{type: :invalid_message_type, message: message}} =
               ErrorHandler.handle_message_error(error, context)

      assert message == "无效的消息类型"
    end
  end

  describe "handle_permission_error/2" do
    test "处理非参与者错误" do
      error = {:error, :not_participant}
      context = %{session_id: "test-session-id", user_id: "test-user-id"}

      assert {:error, %{type: :not_participant, message: message}} =
               ErrorHandler.handle_permission_error(error, context)

      assert message == "用户不是会话参与者"
    end

    test "处理权限不足错误" do
      error = {:error, :insufficient_permissions}
      context = %{user_id: "test-user-id", action: "delete_message"}

      assert {:error, %{type: :insufficient_permissions, message: message}} =
               ErrorHandler.handle_permission_error(error, context)

      assert message == "权限不足"
    end

    test "处理用户被禁言错误" do
      error = {:error, :user_banned}
      context = %{user_id: "test-user-id"}

      assert {:error, %{type: :user_banned, message: message}} =
               ErrorHandler.handle_permission_error(error, context)

      assert message == "用户已被禁言"
    end
  end

  describe "handle_file_error/2" do
    test "处理文件过大错误" do
      error = {:error, :file_too_large}
      context = %{file_size: 10_000_000, max_size: 5_000_000}

      assert {:error, %{type: :file_too_large, message: message}} =
               ErrorHandler.handle_file_error(error, context)

      assert message == "文件大小超出限制"
    end

    test "处理无效文件格式错误" do
      error = {:error, :invalid_file_format}
      context = %{file_type: "exe", allowed_types: ["jpg", "png", "pdf"]}

      assert {:error, %{type: :invalid_file_format, message: message}} =
               ErrorHandler.handle_file_error(error, context)

      assert message == "无效的文件格式"
    end

    test "处理上传失败错误" do
      error = {:error, :upload_failed}
      context = %{file_name: "test.jpg"}

      assert {:error, %{type: :upload_failed, message: message}} =
               ErrorHandler.handle_file_error(error, context)

      assert message == "文件上传失败"
    end

    test "处理无效Base64数据错误" do
      error = {:error, :invalid_base64}
      context = %{data_length: 1000}

      assert {:error, %{type: :invalid_base64, message: message}} =
               ErrorHandler.handle_file_error(error, context)

      assert message == "无效的Base64数据"
    end
  end

  describe "handle_database_error/2" do
    test "处理数据库超时错误" do
      error = {:error, :timeout}
      context = %{operation: "insert_message"}

      assert {:error, %{type: :database_timeout, message: message}} =
               ErrorHandler.handle_database_error(error, context)

      assert message == "数据库操作超时"
    end

    test "处理连接失败错误" do
      error = {:error, :connection_failed}
      context = %{database: "chat_db"}

      assert {:error, %{type: :database_connection_failed, message: message}} =
               ErrorHandler.handle_database_error(error, context)

      assert message == "数据库连接失败"
    end

    test "处理约束违反错误" do
      error = {:error, :constraint_violation}
      context = %{constraint: "unique_session_user"}

      assert {:error, %{type: :database_constraint_violation, message: message}} =
               ErrorHandler.handle_database_error(error, context)

      assert message == "数据约束违反"
    end
  end

  describe "handle_network_error/2" do
    test "处理网络超时错误" do
      error = {:error, :timeout}
      context = %{url: "https://api.example.com", timeout: 5000}

      assert {:error, %{type: :network_timeout, message: message}} =
               ErrorHandler.handle_network_error(error, context)

      assert message == "网络请求超时"
    end

    test "处理连接被拒绝错误" do
      error = {:error, :connection_refused}
      context = %{host: "api.example.com", port: 443}

      assert {:error, %{type: :network_connection_refused, message: message}} =
               ErrorHandler.handle_network_error(error, context)

      assert message == "网络连接被拒绝"
    end
  end

  describe "safe_execute/2" do
    test "成功执行操作" do
      operation = fn -> {:ok, "success"} end

      assert {:ok, "success"} = ErrorHandler.safe_execute(operation)
    end

    test "处理操作失败" do
      operation = fn -> {:error, :some_error} end

      assert {:error, %{type: :generic_error}} = ErrorHandler.safe_execute(operation)
    end

    test "使用自定义错误处理器" do
      operation = fn -> {:error, :custom_error} end

      custom_handler = fn reason, context ->
        {:error, %{type: :custom_handled, reason: reason, context: context}}
      end

      assert {:error, %{type: :custom_handled, reason: :custom_error}} =
               ErrorHandler.safe_execute(operation, custom_handler)
    end

    test "处理非错误返回值" do
      operation = fn -> "direct_result" end

      assert {:ok, "direct_result"} = ErrorHandler.safe_execute(operation)
    end
  end

  describe "handle_batch_results/2" do
    test "处理全部成功的批量结果" do
      results = [
        {:ok, "result1"},
        {:ok, "result2"},
        {:ok, "result3"}
      ]

      batch_result = ErrorHandler.handle_batch_results(results)

      assert batch_result.success_count == 3
      assert batch_result.error_count == 0
      assert batch_result.successes == ["result1", "result2", "result3"]
      assert batch_result.errors == []
    end

    test "处理部分失败的批量结果" do
      results = [
        {:ok, "result1"},
        {:error, :error1},
        {:ok, "result2"},
        {:error, :error2}
      ]

      batch_result = ErrorHandler.handle_batch_results(results)

      assert batch_result.success_count == 2
      assert batch_result.error_count == 2
      assert batch_result.successes == ["result1", "result2"]
      assert batch_result.errors == [error: :error1, error: :error2]
    end

    test "处理全部失败的批量结果" do
      results = [
        {:error, :error1},
        {:error, :error2}
      ]

      batch_result = ErrorHandler.handle_batch_results(results)

      assert batch_result.success_count == 0
      assert batch_result.error_count == 2
      assert batch_result.successes == []
      assert batch_result.errors == [error: :error1, error: :error2]
    end
  end

  describe "validate_and_sanitize_input/2" do
    test "验证和清理有效输入" do
      input = %{content: "Hello world", type: "text"}
      validation_rules = [:content_required, :type_valid]

      assert {:ok, sanitized} = ErrorHandler.validate_and_sanitize_input(input, validation_rules)
      assert is_map(sanitized)
    end

    test "处理无效输入" do
      input = %{content: "", type: "invalid"}
      validation_rules = [:content_required, :type_valid]

      assert {:error, %{type: :input_validation_failed}} =
               ErrorHandler.validate_and_sanitize_input(input, validation_rules)
    end
  end

  # 集成测试
  describe "error handling integration" do
    test "错误处理链式调用" do
      # 模拟一个复杂的错误处理场景
      session_error = {:error, :not_found}
      context = %{session_id: "test-session", user_id: "test-user", game_type: :teen_patti}

      # 处理会话错误
      assert {:error, session_result} = ErrorHandler.handle_session_error(session_error, context)
      assert session_result.type == :session_not_found
      assert session_result.context[:game_type] == :teen_patti

      # 验证错误信息包含上下文
      assert session_result.context[:session_id] == "test-session"
      assert session_result.context[:user_id] == "test-user"
    end

    test "批量操作错误处理" do
      # 模拟批量操作中的混合结果
      operations = [
        fn -> {:ok, "success1"} end,
        fn -> {:error, :database_timeout} end,
        fn -> {:ok, "success2"} end,
        fn -> {:error, :permission_denied} end
      ]

      results = Enum.map(operations, &ErrorHandler.safe_execute/1)
      batch_result = ErrorHandler.handle_batch_results(results)

      assert batch_result.success_count == 2
      assert batch_result.error_count == 2
      assert length(batch_result.successes) == 2
      assert length(batch_result.errors) == 2
    end
  end
end
