defmodule Cypridina.Utils.AESTest do
  use ExUnit.Case, async: true
  doctest Cypridina.Utils.AES

  # 直接导入AES模块以方便测试
  alias Cypridina.Utils.AES

  describe "encrypt/3" do
    test "加密文本并使用默认Base64编码" do
      plain_text = "Hello World"
      key = "1234567890123456"
      encrypted = AES.encrypt(plain_text, key)

      assert is_binary(encrypted)
      assert {:ok, _} = Base.decode64(encrypted)
    end

    test "加密文本不使用Base64编码" do
      plain_text = "Hello World"
      key = "1234567890123456"
      encrypted = AES.encrypt(plain_text, key, encode: false)

      assert is_binary(encrypted)
      # AES-128块大小
      assert byte_size(encrypted) == 16
    end

    test "加密文本使用不同的填充方式" do
      plain_text = "Hello World"
      key = "1234567890123456"

      # 使用PKCS7填充
      encrypted_pkcs7 = AES.encrypt(plain_text, key, padding: :pkcs7)
      assert is_binary(encrypted_pkcs7)

      # 无填充 - 必须是16字节的倍数才能不使用填充
      even_plain = String.duplicate("A", 16)
      encrypted_none = AES.encrypt(even_plain, key, padding: :none)
      assert is_binary(encrypted_none)
    end

    test "处理不同长度的密钥" do
      plain_text = "Hello World"

      # 短密钥应该被填充
      short_key = "short"
      encrypted_short = AES.encrypt(plain_text, short_key)
      assert is_binary(encrypted_short)

      # 长密钥应该被截断
      long_key = "thisisaverylongkeythatwillbetruncated"
      encrypted_long = AES.encrypt(plain_text, long_key)
      assert is_binary(encrypted_long)

      # 刚好16字节的密钥
      exact_key = "exactsixteenbytes"
      encrypted_exact = AES.encrypt(plain_text, exact_key)
      assert is_binary(encrypted_exact)
    end
  end

  describe "decrypt/3" do
    test "解密使用默认选项加密的文本" do
      plain_text = "Hello World"
      key = "1234567890123456"
      encrypted = AES.encrypt(plain_text, key)

      decrypted = AES.decrypt(encrypted, key)
      assert decrypted == plain_text
    end

    test "解密非Base64编码的加密文本" do
      plain_text = "Hello World"
      key = "1234567890123456"
      encrypted = AES.encrypt(plain_text, key, encode: false)

      decrypted = AES.decrypt(encrypted, key, decode: false)
      assert decrypted == plain_text
    end

    test "使用不同填充方式解密" do
      key = "1234567890123456"

      # 使用PKCS7填充
      plain_text = "Hello World"
      encrypted_pkcs7 = AES.encrypt(plain_text, key, padding: :pkcs7)
      decrypted_pkcs7 = AES.decrypt(encrypted_pkcs7, key, padding: :pkcs7)
      assert decrypted_pkcs7 == plain_text

      # 无填充 - 必须是16字节的倍数
      even_plain = String.duplicate("A", 16)
      encrypted_none = AES.encrypt(even_plain, key, padding: :none)
      decrypted_none = AES.decrypt(encrypted_none, key, padding: :none, decode: false)
      assert decrypted_none == even_plain
    end

    test "处理不同长度的密钥进行解密" do
      plain_text = "Hello World"

      # 短密钥应该被填充
      short_key = "short"
      encrypted_short = AES.encrypt(plain_text, short_key)
      decrypted_short = AES.decrypt(encrypted_short, short_key)
      assert decrypted_short == plain_text

      # 长密钥应该被截断
      long_key = "thisisaverylongkeythatwillbetruncated"
      encrypted_long = AES.encrypt(plain_text, long_key)
      decrypted_long = AES.decrypt(encrypted_long, long_key)
      assert decrypted_long == plain_text
    end
  end

  describe "加密和解密的一致性" do
    test "加密后解密应该得到原始文本" do
      plain_texts = [
        "Hello World",
        "测试中文字符",
        "Special chars: !@#$%^&*()",
        # 测试长文本
        String.duplicate("A", 32),
        # 测试短文本
        "Short"
      ]

      keys = [
        # 正好16字节
        "1234567890123456",
        # 短于16字节
        "short",
        # 长于16字节
        "verylongkeymorethan16bytes"
      ]

      for plain_text <- plain_texts, key <- keys do
        # 默认选项
        encrypted = AES.encrypt(plain_text, key)
        decrypted = AES.decrypt(encrypted, key)
        assert decrypted == plain_text

        # 不使用Base64编码
        encrypted_no_base64 = AES.encrypt(plain_text, key, encode: false)
        decrypted_no_base64 = AES.decrypt(encrypted_no_base64, key, decode: false)
        assert decrypted_no_base64 == plain_text
      end
    end
  end
end
