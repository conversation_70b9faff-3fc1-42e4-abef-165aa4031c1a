defmodule Cypridina.Types.TransactionTypeTest do
  @moduledoc """
  交易类型枚举测试
  """
  use ExUnit.Case, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Types.TransactionType

  describe "description/1" do
    test "returns correct Chinese descriptions for all transaction types" do
      assert TransactionType.description(:transfer) == "用户间转账"
      assert TransactionType.description(:game_bet) == "游戏投注"
      assert TransactionType.description(:game_win) == "游戏获奖"
      assert TransactionType.description(:buy_stock) == "买入股票"
      assert TransactionType.description(:sell_stock) == "卖出股票"
      assert TransactionType.description(:refund) == "退款"
      assert TransactionType.description(:commission) == "佣金"
      assert TransactionType.description(:system_adjust) == "系统调整"
      assert TransactionType.description(:admin_operation) == "管理员操作"
      assert TransactionType.description(:deposit) == "充值"
      assert TransactionType.description(:withdrawal) == "提现"
      assert TransactionType.description(:withdrawal_request) == "提现请求"
      assert TransactionType.description(:force_liquidation) == "强制平仓"
      assert TransactionType.description(:system_adjust) == "系统调整"
    end

    test "returns unknown type message for invalid types" do
      assert TransactionType.description(:invalid_type) == "未知交易类型"
      assert TransactionType.description(:not_exist) == "未知交易类型"
    end
  end

  describe "all_types/0" do
    test "returns all transaction types" do
      types = TransactionType.all_types()

      assert is_list(types)
      assert length(types) > 0
      assert :transfer in types
      assert :game_bet in types
      assert :buy_stock in types
    end
  end

  describe "base_types/0" do
    test "returns base transaction types" do
      types = TransactionType.base_types()

      assert :transfer in types
      assert :refund in types
      assert :commission in types
      assert :system_adjust in types
      assert :deposit in types
      assert :withdrawal in types
      assert :withdrawal_request in types

      # 管理员操作不应该在基础类型中
      refute :admin_operation in types
      # 游戏类型不应该在基础类型中
      refute :game_bet in types
      refute :buy_stock in types
    end
  end

  describe "game_types/0" do
    test "returns game-related transaction types" do
      types = TransactionType.game_types()

      assert :game_bet in types
      assert :game_win in types

      # 非游戏类型不应该在游戏类型中
      refute :transfer in types
      refute :buy_stock in types
    end
  end

  describe "stock_types/0" do
    test "returns stock-related transaction types" do
      types = TransactionType.stock_types()

      assert :buy_stock in types
      assert :sell_stock in types
      assert :force_liquidation in types

      # 非股票类型不应该在股票类型中
      refute :game_bet in types
      refute :transfer in types
    end
  end

  describe "admin_types/0" do
    test "returns admin-related transaction types" do
      types = TransactionType.admin_types()

      assert :admin_operation in types

      # 非管理员类型不应该在管理员类型中
      refute :game_bet in types
      refute :transfer in types
    end
  end

  describe "types_for_mode/1" do
    test "returns correct types for teen mode" do
      types = TransactionType.types_for_mode(:teen)

      # Teen模式应该包含基础、游戏、管理员类型
      assert :transfer in types
      assert :game_bet in types
      assert :game_win in types
      assert :admin_operation in types

      # 但不应该包含股票类型
      refute :buy_stock in types
      refute :sell_stock in types
    end

    test "returns correct types for race mode" do
      types = TransactionType.types_for_mode(:race)

      # Race模式应该包含基础、股票、管理员类型
      assert :transfer in types
      assert :buy_stock in types
      assert :sell_stock in types
      assert :admin_operation in types

      # 但不应该包含游戏类型
      refute :game_bet in types
      refute :game_win in types
    end

    test "returns correct types for self mode" do
      types = TransactionType.types_for_mode(:self)

      # 自用模式应该只包含基础类型
      assert :transfer in types
      assert :refund in types
      assert :system_adjust in types

      # 不应该包含游戏和股票类型
      refute :game_bet in types
      refute :buy_stock in types
    end

    test "returns all types for unknown mode" do
      types = TransactionType.types_for_mode(:unknown)

      # 未知模式应该返回所有类型
      all_types = TransactionType.all_types()
      assert types == all_types
    end
  end

  describe "available_in_mode?/2" do
    test "correctly checks type availability in different modes" do
      assert TransactionType.available_in_mode?(:buy_stock, :race)
      refute TransactionType.available_in_mode?(:buy_stock, :teen)
      refute TransactionType.available_in_mode?(:buy_stock, :self)

      assert TransactionType.available_in_mode?(:game_bet, :teen)
      refute TransactionType.available_in_mode?(:game_bet, :race)
      refute TransactionType.available_in_mode?(:game_bet, :self)

      assert TransactionType.available_in_mode?(:transfer, :teen)
      assert TransactionType.available_in_mode?(:transfer, :race)
      assert TransactionType.available_in_mode?(:transfer, :self)
    end
  end

  describe "category/1" do
    test "correctly categorizes transaction types" do
      assert TransactionType.category(:transfer) == :base
      assert TransactionType.category(:game_bet) == :game
      assert TransactionType.category(:buy_stock) == :stock
      assert TransactionType.category(:admin_operation) == :admin

      assert TransactionType.category(:unknown_type) == :unknown
    end
  end

  describe "type_descriptions/0" do
    test "returns map of all types and descriptions" do
      descriptions = TransactionType.type_descriptions()

      assert is_map(descriptions)
      assert descriptions[:transfer] == "用户间转账"
      assert descriptions[:game_bet] == "游戏投注"
      assert descriptions[:buy_stock] == "买入股票"

      # 确保所有类型都有描述
      all_types = TransactionType.all_types()
      assert Map.keys(descriptions) |> Enum.sort() == all_types |> Enum.sort()
    end
  end
end
