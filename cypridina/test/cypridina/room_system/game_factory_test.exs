defmodule Cypridina.RoomSystem.GameFactoryTest do
  use ExUnit.Case, async: false
  alias <PERSON><PERSON><PERSON><PERSON>.RoomSystem.{GameFactory, GameRegistry}

  # 测试游戏模块
  defmodule TestGame do
    @behaviour GameFactory

    def game_type, do: :test_game
    def game_name, do: "测试游戏"
    def room_module, do: Cypridina.RoomSystem.GameFactoryTest.TestGameRoom
    def default_config, do: %{max_players: 4}
    def is_lobby_game?, do: false
    def supported_game_ids, do: [999]
  end

  defmodule TestGameRoom do
    def start_link(_), do: {:ok, self()}
  end

  describe "GameFactory" do
    test "can register and retrieve games" do
      # 注册测试游戏
      assert :ok = GameRegistry.register_game(TestGame)

      # 验证游戏类型映射
      assert :test_game = GameFactory.get_game_type(999)

      # 验证游戏配置
      assert %{max_players: 4} = GameFactory.get_game_config(:test_game)

      # 验证房间模块
      assert Cy<PERSON><PERSON><PERSON>.RoomSystem.GameFactoryTest.TestGameRoom ==
               GameFactory.get_room_module(:test_game)

      # 验证不是百人场游戏
      assert false == GameFactory.is_lobby_game?(:test_game)
    end

    test "validates game modules" do
      # 测试有效的游戏模块
      assert :ok = GameRegistry.validate_game_module(TestGame)

      # 测试无效的游戏模块（缺少回调）
      defmodule InvalidGame do
        def game_type, do: :invalid
        # 缺少其他必需的回调
      end

      assert {:error, {:missing_callback, _}} = GameRegistry.validate_game_module(InvalidGame)
    end

    test "provides game statistics" do
      stats = GameRegistry.get_game_stats()

      assert is_integer(stats.total_games)
      assert is_integer(stats.lobby_games)
      assert is_integer(stats.table_games)
      assert is_list(stats.supported_game_ids)
    end

    test "lists all games" do
      games = GameRegistry.list_games()

      assert is_list(games)
      assert length(games) > 0

      # 检查游戏信息结构
      game = hd(games)
      assert Map.has_key?(game, :game_type)
      assert Map.has_key?(game, :game_name)
      assert Map.has_key?(game, :room_module)
      assert Map.has_key?(game, :is_lobby_game)
      assert Map.has_key?(game, :supported_game_ids)
    end

    test "checks game support" do
      # 内置游戏应该被支持
      # 龙虎斗
      assert true == GameRegistry.game_supported?(201)
      # 龙虎斗 (客户端ID)
      assert true == GameRegistry.game_supported?(22)
      # Teen Patti
      assert true == GameRegistry.game_supported?(1)

      # 不存在的游戏ID应该不被支持
      assert false == GameRegistry.game_supported?(99999)
    end
  end
end
