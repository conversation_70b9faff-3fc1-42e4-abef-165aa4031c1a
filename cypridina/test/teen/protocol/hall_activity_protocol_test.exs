defmodule Teen.Protocol.HallActivityProtocolTest do
  use Cy<PERSON><PERSON>ina.DataCase, async: false

  alias Teen.Protocol.HallActivityProtocol

  @protocol_id 101

  describe "协议基本信息" do
    test "返回正确的协议ID" do
      assert HallActivityProtocol.protocol_id() == @protocol_id
    end

    test "返回协议信息" do
      info = HallActivityProtocol.protocol_info()
      
      assert %{name: name, description: description} = info
      assert is_binary(name)
      assert is_binary(description)
    end
  end

  describe "协议处理" do
    setup do
      user_id = Ecto.UUID.generate()
      
      context = %{
        user_id: user_id,
        connection_id: "test_conn",
        ip_address: "127.0.0.1"
      }
      
      %{user_id: user_id, context: context}
    end

    test "处理登录现金奖励信息请求", %{context: context} do
      data = %{}
      sub_protocol = 0  # CS_LOGINCASH_INFO_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, data, context)
      
      assert {:ok, response_protocol, response_data} = result
      assert response_protocol == 1  # SC_LOGINCASH_INFO_P
      assert Map.has_key?(response_data, "code")
    end

    test "处理登录现金奖励领取请求", %{context: context} do
      data = %{"fetchtype" => 0}  # 登录奖励
      sub_protocol = 2  # CS_FETCH_LOGINCASH_AWARD_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, data, context)
      
      assert {:ok, response_protocol, response_data} = result
      assert response_protocol == 3  # SC_FETCH_LOGINCASH_AWARD_P
      assert Map.has_key?(response_data, "code")
    end

    test "处理破产补助请求", %{context: context} do
      data = %{"isfetch" => 0}  # 获取状态
      sub_protocol = 40  # CS_FETCH_BROKE_AWARD_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, data, context)
      
      assert {:ok, response_protocol, response_data} = result
      assert response_protocol == 41  # SC_FETCH_BROKE_AWARD_P
      assert Map.has_key?(response_data, "fetchcount")
    end

    test "处理免费提现信息请求", %{context: context} do
      data = %{}
      sub_protocol = 34  # CS_GET_FREE_CASH_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, data, context)
      
      assert {:ok, response_protocol, response_data} = result
      assert response_protocol == 35  # SC_GET_FREE_CASH_P
      assert Map.has_key?(response_data, "code")
    end

    test "处理免费提现领取请求", %{context: context} do
      data = %{}
      sub_protocol = 36  # CS_FETCH_FREE_CASH_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, data, context)
      
      assert {:ok, response_protocol, response_data} = result
      assert response_protocol == 37  # SC_FETCH_FREE_CASH_P
      assert Map.has_key?(response_data, "code")
    end

    test "处理未知子协议", %{context: context} do
      data = %{}
      sub_protocol = 999  # 未知协议
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, data, context)
      
      assert {:error, :unknown_sub_protocol} = result
    end
  end

  describe "参数验证" do
    setup do
      user_id = Ecto.UUID.generate()
      
      context = %{
        user_id: user_id,
        connection_id: "test_conn",
        ip_address: "127.0.0.1"
      }
      
      %{user_id: user_id, context: context}
    end

    test "CDKEY参数验证", %{context: context} do
      # 测试有效的CDKEY
      valid_data = %{"cdkey" => "TESTKEY123"}
      sub_protocol = 48  # CS_FETCH_CDKEY_AWARD_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, valid_data, context)
      assert {:ok, _, _} = result
      
      # 测试无效的CDKEY（空字符串）
      invalid_data = %{"cdkey" => ""}
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, invalid_data, context)
      assert {:error, :invalid_params} = result
    end

    test "邮箱绑定参数验证", %{context: context} do
      # 测试有效参数
      valid_data = %{"email" => "<EMAIL>", "code" => "123456"}
      sub_protocol = 46  # CS_BIND_MAIL_USER_P
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, valid_data, context)
      assert {:ok, _, _} = result
      
      # 测试缺少必要参数
      invalid_data = %{"email" => "<EMAIL>"}
      
      result = HallActivityProtocol.handle_protocol(sub_protocol, invalid_data, context)
      assert {:error, :invalid_params} = result
    end
  end
end