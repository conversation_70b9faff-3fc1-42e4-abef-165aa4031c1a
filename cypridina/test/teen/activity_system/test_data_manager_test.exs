defmodule Teen.ActivitySystem.TestDataManagerTest do
  use ExUnit.Case, async: false
  
  alias Teen.ActivitySystem.{TestDataManager, TestDataAuth}
  alias Teen.ActivitySystem.{UserActivityParticipation, UserActivityRecord, RewardClaimRecord}
  alias Cypridina.Accounts.User
  
  @moduletag :integration

  setup do
    # 设置测试环境
    Application.put_env(:cypridina, :environment, :test)
    
    # 创建测试用户
    {:ok, user} = User.create(%{
      email: "test_user_#{System.unique_integer([:positive])}@example.com",
      nickname: "test_user",
      hashed_password: Ash.CiString.sigil_c("password123", [])
    })
    
    # 创建测试操作员
    operator = %{
      id: Ash.UUID.generate(),
      username: "test_admin",
      ip_address: "127.0.0.1",
      user_agent: "TestSuite"
    }
    
    on_exit(fn ->
      # 清理测试数据
      cleanup_test_data(user.id)
    end)
    
    %{user: user, operator: operator}
  end

  describe "environment validation" do
    test "validates environment correctly" do
      assert :ok = TestDataManager.validate_environment!()
    end
    
    test "raises error in production environment" do
      Application.put_env(:cypridina, :environment, :prod)
      
      assert_raise RuntimeError, ~r/测试数据管理功能仅在开发和测试环境中可用/, fn ->
        TestDataManager.validate_environment!()
      end
      
      # 恢复测试环境
      Application.put_env(:cypridina, :environment, :test)
    end
  end

  describe "preview_clear_data/2" do
    test "returns correct data counts for empty user", %{user: user} do
      {:ok, preview} = TestDataManager.preview_clear_data(user.id, [])
      
      assert preview.user_id == user.id
      assert preview.activity_types == []
      assert preview.data_counts.participations == 0
      assert preview.data_counts.records == 0
      assert preview.data_counts.rewards == 0
      assert preview.total_records == 0
    end
    
    test "returns correct data counts with test data", %{user: user} do
      # 创建测试数据
      create_test_activity_data(user.id)
      
      {:ok, preview} = TestDataManager.preview_clear_data(user.id, [:sign_in_activity])
      
      assert preview.user_id == user.id
      assert preview.data_counts.participations >= 1
      assert preview.total_records >= 1
    end
  end

  describe "clear_user_activity_data/4" do
    test "clears user activity data successfully", %{user: user, operator: operator} do
      # 创建测试数据
      create_test_activity_data(user.id)
      
      # 执行清除操作
      {:ok, result} = TestDataManager.clear_user_activity_data(
        user.id, 
        [:sign_in_activity], 
        operator,
        false  # 不是预览模式
      )
      
      assert result.user_id == user.id
      assert result.activity_types == [:sign_in_activity]
      assert result.total_deleted >= 0
      
      # 验证数据确实被清除
      {:ok, preview_after} = TestDataManager.preview_clear_data(user.id, [:sign_in_activity])
      assert preview_after.total_records == 0
    end
    
    test "preview mode does not delete data", %{user: user, operator: operator} do
      # 创建测试数据
      create_test_activity_data(user.id)
      
      # 获取清除前的数据
      {:ok, preview_before} = TestDataManager.preview_clear_data(user.id, [:sign_in_activity])
      
      # 执行预览模式
      {:ok, result} = TestDataManager.clear_user_activity_data(
        user.id, 
        [:sign_in_activity], 
        operator,
        true  # 预览模式
      )
      
      # 验证数据没有被删除
      {:ok, preview_after} = TestDataManager.preview_clear_data(user.id, [:sign_in_activity])
      assert preview_after.total_records == preview_before.total_records
    end
  end

  describe "simulate_time_advance/3" do
    test "updates participation data with simulated time", %{user: user, operator: operator} do
      # 创建测试数据
      create_test_activity_data(user.id)
      
      # 执行时间模拟
      {:ok, result} = TestDataManager.simulate_time_advance(user.id, 7, operator)
      
      assert result.user_id == user.id
      assert result.days_advanced == 7
      assert %DateTime{} = result.simulated_time
      assert result.affected_records >= 0
      
      # 验证数据被更新
      participations = UserActivityParticipation
      |> Ash.Query.filter(user_id == ^user.id)
      |> Ash.read!()
      
      # 检查是否有记录包含模拟时间数据
      updated_participation = Enum.find(participations, fn p ->
        p.participation_data && p.participation_data["simulated_time"]
      end)
      
      if updated_participation do
        assert updated_participation.participation_data["time_advance_days"] == 7
      end
    end
  end

  describe "authorization" do
    test "validates user permissions" do
      # 测试有效用户ID
      case TestDataAuth.authorize_user("valid_admin_id") do
        {:ok, _} -> :ok
        {:error, _} -> :ok  # 预期可能失败，因为没有真实的管理员用户
      end
    end
    
    test "generates confirmation tokens" do
      admin_user_id = Ash.UUID.generate()
      operation_type = "clear_user_activity_data"
      params = %{user_id: Ash.UUID.generate(), activity_types: [:sign_in_activity]}
      
      token = TestDataAuth.generate_confirmation_token(admin_user_id, operation_type, params)
      
      assert is_binary(token)
      assert String.length(token) > 0
      
      # 验证令牌
      assert :ok = TestDataAuth.verify_confirmation_token(admin_user_id, operation_type, token, params)
      
      # 验证错误令牌
      assert {:error, _} = TestDataAuth.verify_confirmation_token(admin_user_id, operation_type, "invalid_token", params)
    end
  end

  # 辅助函数

  defp create_test_activity_data(user_id) do
    # 创建测试活动参与记录
    UserActivityParticipation.create!(%{
      user_id: user_id,
      activity_type: :sign_in_activity,
      activity_id: Ash.UUID.generate(),
      progress: 5,
      status: :active,
      participation_data: %{
        "last_sign_in" => DateTime.utc_now() |> DateTime.to_iso8601(),
        "consecutive_days" => 5
      }
    })
    
    # 创建测试活动记录
    UserActivityRecord.create!(%{
      user_id: user_id,
      activity_type: "sign_in_activity",
      activity_id: Ash.UUID.generate(),
      participation_data: %{
        "completed_tasks" => 3
      },
      reward_claimed: false
    })
    
    # 创建测试奖励记录
    RewardClaimRecord.create!(%{
      user_id: user_id,
      activity_type: :sign_in_activity,
      activity_id: Ash.UUID.generate(),
      reward_type: :coins,
      reward_amount: Decimal.new("1000"),
      reward_data: %{
        "reward_source" => "daily_sign_in"
      }
    })
  end

  defp cleanup_test_data(user_id) do
    # 清理测试数据
    try do
      UserActivityParticipation
      |> Ash.Query.filter(user_id == ^user_id)
      |> Ash.read!()
      |> Enum.each(&Ash.destroy!/1)
      
      UserActivityRecord
      |> Ash.Query.filter(user_id == ^user_id)
      |> Ash.read!()
      |> Enum.each(&Ash.destroy!/1)
      
      RewardClaimRecord
      |> Ash.Query.filter(user_id == ^user_id)
      |> Ash.read!()
      |> Enum.each(&Ash.destroy!/1)
    rescue
      _ -> :ok  # 忽略清理错误
    end
  end
end