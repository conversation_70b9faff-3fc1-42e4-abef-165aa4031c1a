defmodule Teen.ActivitySystem.BrokeAwardActivityTest do
  use Cypridina.DataCase, async: false

  alias Teen.ActivitySystem.BrokeAwardActivity

  @valid_attrs %{
    activity_name: "测试破产补助",
    description: "测试破产补助活动",
    min_balance_threshold: Decimal.new("1000"),
    award_amount: Decimal.new("500"),
    max_claims_per_day: 3,
    max_claims_total: 10,
    cooldown_hours: 8,
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 30, :day)
  }

  describe "基本CRUD操作" do
    test "创建破产补助活动" do
      assert {:ok, activity} = BrokeAwardActivity.create(@valid_attrs)
      assert activity.activity_name == "测试破产补助"
      assert Decimal.equal?(activity.min_balance_threshold, Decimal.new("1000"))
      assert activity.status == :active
    end

    test "读取活动信息" do
      {:ok, activity} = BrokeAwardActivity.create(@valid_attrs)
      
      assert {:ok, found_activity} = BrokeAwardActivity.read(activity.id)
      assert found_activity.id == activity.id
      assert found_activity.activity_name == @valid_attrs.activity_name
    end

    test "更新活动信息" do
      {:ok, activity} = BrokeAwardActivity.create(@valid_attrs)
      
      update_attrs = %{award_amount: Decimal.new("800")}
      assert {:ok, updated_activity} = BrokeAwardActivity.update(activity, update_attrs)
      assert Decimal.equal?(updated_activity.award_amount, Decimal.new("800"))
    end
  end

  describe "活动查询" do
    test "获取活跃活动列表" do
      {:ok, _active_activity} = BrokeAwardActivity.create(@valid_attrs)
      
      assert {:ok, active_activities} = BrokeAwardActivity.list_active_activities()
      assert length(active_activities) >= 1
      assert Enum.all?(active_activities, fn activity -> activity.status == :active end)
    end
  end

  describe "业务逻辑" do
    test "获取破产补助数据 - 获取状态" do
      user_id = Ecto.UUID.generate()
      
      {:ok, data} = BrokeAwardActivity.get_broke_award_data(user_id, 0)
      
      assert Map.has_key?(data, :fetchcount)
      assert Map.has_key?(data, :awardcash)
      assert Map.has_key?(data, :canfetch)
    end

    test "处理破产补助请求" do
      user_id = Ecto.UUID.generate()
      request_data = %{"isfetch" => 0}
      
      {:ok, result} = BrokeAwardActivity.process_broke_award_request(user_id, request_data)
      
      assert Map.has_key?(result, :fetchcount)
      assert Map.has_key?(result, :awardcash)
    end
  end

  describe "数据验证" do
    test "活动名称不能为空" do
      invalid_attrs = Map.put(@valid_attrs, :activity_name, nil)
      
      assert {:error, %Ash.Error.Invalid{}} = BrokeAwardActivity.create(invalid_attrs)
    end

    test "补助金额必须大于等于0" do
      invalid_attrs = Map.put(@valid_attrs, :award_amount, Decimal.new("-100"))
      
      assert {:error, %Ash.Error.Invalid{}} = BrokeAwardActivity.create(invalid_attrs)
    end

    test "每日最大领取次数必须大于0" do
      invalid_attrs = Map.put(@valid_attrs, :max_claims_per_day, 0)
      
      assert {:error, %Ash.Error.Invalid{}} = BrokeAwardActivity.create(invalid_attrs)
    end
  end
end