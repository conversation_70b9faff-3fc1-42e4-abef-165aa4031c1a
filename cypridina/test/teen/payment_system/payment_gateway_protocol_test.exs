defmodule Teen.PaymentSystem.PaymentGatewayProtocolTest do
  use ExUnit.Case, async: true

  alias Teen.PaymentSystem.PaymentGateway

  describe "Phoenix.HTML.Safe protocol" do
    test "renders gateway name correctly" do
      gateway = %PaymentGateway{
        id: "test-id",
        name: "Test Gateway",
        gateway_type: "recharge"
      }

      result = Phoenix.HTML.Safe.to_iodata(gateway)
      assert IO.iodata_to_binary(result) == "Test Gateway"
    end

    test "handles nil name gracefully" do
      gateway = %PaymentGateway{
        id: "test-id",
        name: nil,
        gateway_type: "recharge"
      }

      result = Phoenix.HTML.Safe.to_iodata(gateway)
      assert IO.iodata_to_binary(result) == "未知网关"
    end
  end

  describe "String.Chars protocol" do
    test "converts gateway to string correctly" do
      gateway = %PaymentGateway{
        id: "test-id",
        name: "Test Gateway",
        gateway_type: "recharge"
      }

      result = to_string(gateway)
      assert result == "Test Gateway"
    end

    test "handles nil name gracefully" do
      gateway = %PaymentGateway{
        id: "test-id",
        name: nil,
        gateway_type: "recharge"
      }

      result = to_string(gateway)
      assert result == "未知网关"
    end
  end
end