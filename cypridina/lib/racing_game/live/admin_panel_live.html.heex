<div class="flex min-h-screen bg-base-200 font-sans relative">
  <!-- 移动端顶部栏 -->
  <div class="navbar bg-primary text-primary-content fixed top-0 left-0 right-0 z-[1001] lg:hidden shadow-lg">
    <div class="navbar-start">
      <button phx-click="toggle_sidebar" class="btn btn-ghost btn-circle">
        <i class="fas fa-bars text-xl"></i>
      </button>
    </div>
    <div class="navbar-center">
      <h1 class="text-lg font-semibold">{get_page_title(@current_page)}</h1>
    </div>
    <div class="navbar-end">
      <div class="avatar placeholder">
        <div class="bg-neutral-focus text-neutral-content rounded-full w-9">
          <span class="text-sm font-bold">
            {String.first(to_string(@current_user.username || "U"))}
          </span>
        </div>
      </div>
    </div>
  </div>
  
<!-- 侧边栏遮罩 -->
  <%= if @sidebar_open do %>
    <div class="fixed inset-0 bg-black bg-opacity-50 z-[999] lg:hidden" phx-click="toggle_sidebar">
    </div>
  <% end %>
  
<!-- 左侧导航栏 -->
  <div class={["sidebar", if(@sidebar_open, do: "sidebar-open", else: "")]}>
    <!-- 用户信息区域 -->
    <div class="user-info">
      <div class="user-avatar">
        {String.first(to_string(@current_user.username || "U"))}
      </div>
      <div class="user-details">
        <div class="username">{to_string(@current_user.username)}</div>
        <div class="user-role">
          {CypridinaWeb.AuthHelper.get_permission_level_name(@current_user)}
        </div>
      </div>
    </div>
    
<!-- 导航菜单 -->
    <nav class="nav-menu">
      <%= for menu_item <- get_navigation_menu(@current_user) do %>
        <%= if menu_item.type == :single do %>
          <!-- 单级菜单项 -->
          <.link
            navigate={~p"/admin_panel/#{menu_item.page}"}
            class={["nav-item", if(@current_page == menu_item.page, do: "active", else: "")]}
          >
            <i class={menu_item.icon}></i>
            <span>{menu_item.title}</span>
          </.link>
        <% else %>
          <!-- 分类菜单项 -->
          <div class="nav-category">
            <button
              phx-click="toggle_category"
              phx-value-category={menu_item.id}
              class={[
                "nav-category-header",
                if(category_expanded?(@expanded_categories, menu_item.id),
                  do: "expanded",
                  else: ""
                )
              ]}
            >
              <div class="nav-category-title">
                <i class={menu_item.icon}></i>
                <span>{menu_item.title}</span>
              </div>
              <i class="fas fa-chevron-down nav-category-arrow"></i>
            </button>

            <%= if category_expanded?(@expanded_categories, menu_item.id) do %>
              <div class="nav-category-children">
                <%= for child <- menu_item.children do %>
                  <.link
                    navigate={~p"/admin_panel/#{child.page}"}
                    class={[
                      "nav-child-item",
                      if(@current_page == child.page, do: "active", else: "")
                    ]}
                  >
                    <i class={child.icon}></i>
                    <span>{child.title}</span>
                    <%= if Map.get(child, :badge_count, 0) > 0 do %>
                      <span class="nav-badge">
                        {child.badge_count}
                      </span>
                    <% end %>
                  </.link>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </nav>
    
<!-- 底部操作 -->
    <div class="sidebar-footer">
      <a href="/" class="back-button">
        <i class="fas fa-arrow-left"></i>
        <span>返回游戏</span>
      </a>
    </div>
  </div>
  
<!-- 主内容区域 -->
  <div class="flex-1 flex flex-col bg-base-100 lg:pt-0 pt-16">
    <!-- 页面标题 -->
    <div class="bg-base-100 border-b border-base-300 shadow-sm hidden lg:block">
      <div class="p-6">
        <h1 class="text-3xl font-bold text-base-content">{get_page_title(@current_page)}</h1>
      </div>
    </div>
    
<!-- Flash 消息 -->
    <%= if live_flash(@flash, :info) do %>
      <div class="alert alert-success mx-4 mt-4 shadow-lg">
        <div>
          <i class="fas fa-check-circle text-lg"></i>
          <span>{live_flash(@flash, :info)}</span>
        </div>
        <div class="flex-none">
          <button
            phx-click="lv:clear-flash"
            phx-value-key="info"
            class="btn btn-sm btn-ghost btn-circle"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    <% end %>

    <%= if live_flash(@flash, :error) do %>
      <div class="alert alert-error mx-4 mt-4 shadow-lg">
        <div>
          <i class="fas fa-exclamation-circle text-lg"></i>
          <span>{live_flash(@flash, :error)}</span>
        </div>
        <div class="flex-none">
          <button
            phx-click="lv:clear-flash"
            phx-value-key="error"
            class="btn btn-sm btn-ghost btn-circle"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    <% end %>
    
<!-- 内容区域 -->
    <div class="flex-1 p-4 lg:p-6">
      <div class="bg-base-100 rounded-lg shadow-lg border border-base-300 p-6">
        <%= case @current_page do %>
          <% "profile" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.ProfileComponent}
              id="profile-component"
              current_user={@current_user}
            />
          <% "users" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.UserManagementComponent}
              id="user-management-component"
              current_user={@current_user}
            />
          <% "subordinates" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.SubordinateManagementComponent}
              id="subordinate-management-component"
              current_user={@current_user}
            />
          <% "stocks" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.StockHoldingsComponent}
              id="stock-holdings-component"
              current_user={@current_user}
            />
          <% "bet_records" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.BetRecordsComponent}
              id="bet-records-component"
              current_user={@current_user}
            />
          <% _ -> %>
            <div class="flex justify-center items-center h-48">
              <div class="loading loading-spinner loading-lg text-primary"></div>
              <span class="ml-4 text-base-content/70">页面正在加载中...</span>
            </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- 导航菜单栏样式 CSS -->
<style>
  /* 左侧导航栏 */
  .sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.3s ease;
  }

  /* 用户信息区域 */
  .user-info {
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .user-details {
    flex: 1;
  }

  .username {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .user-role {
    font-size: 14px;
    opacity: 0.8;
    color: #e0e7ff;
  }

  /* 导航菜单 */
  .nav-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
  }

  /* 单级菜单项 */
  .nav-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 4px solid transparent;
    text-decoration: none;
  }

  .nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.5);
  }

  .nav-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #ffd700;
    font-weight: 600;
  }

  .nav-item i {
    width: 20px;
    text-align: center;
    font-size: 18px;
  }

  /* 分类菜单 */
  .nav-category {
    margin-bottom: 8px;
  }

  .nav-category-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 25px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 4px solid transparent;
  }

  .nav-category-header:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.3);
  }

  .nav-category-header.expanded {
    background: rgba(255, 255, 255, 0.15);
    border-left-color: rgba(255, 255, 255, 0.6);
  }

  .nav-category-title {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .nav-category-title i {
    width: 20px;
    text-align: center;
    font-size: 18px;
  }

  .nav-category-arrow {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .nav-category-header.expanded .nav-category-arrow {
    transform: rotate(180deg);
  }

  /* 子菜单项 */
  .nav-category-children {
    background: rgba(0, 0, 0, 0.1);
    border-left: 2px solid rgba(255, 255, 255, 0.2);
    margin-left: 25px;
    animation: slideDown 0.3s ease-out;
  }

  .nav-child-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 25px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 3px solid transparent;
    text-decoration: none;
  }

  .nav-child-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.4);
    color: white;
  }

  .nav-child-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #ffd700;
    font-weight: 600;
    color: white;
  }

  .nav-child-item i {
    width: 18px;
    text-align: center;
    font-size: 16px;
  }

  /* Badge 样式 */
  .nav-badge {
    background: #ef4444;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  /* 动画效果 */
  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      max-height: 500px;
      transform: translateY(0);
    }
  }

  /* 底部操作 */
  .sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  /* 响应式设计 */
  @media (max-width: 1023px) {
    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 280px;
      z-index: 1000;
      transform: translateX(-100%);
    }

    .sidebar-open {
      transform: translateX(0);
    }

    .user-info {
      padding: 20px 15px;
    }

    .nav-item, .nav-category-header {
      padding: 12px 20px;
      font-size: 15px;
    }

    .nav-child-item {
      padding: 10px 20px;
      font-size: 14px;
    }

    .nav-category-children {
      margin-left: 15px;
    }
  }

  /* 平板设备 */
  @media (max-width: 1024px) and (min-width: 769px) {
    .sidebar {
      width: 260px;
    }

    .nav-item, .nav-category-header {
      padding: 13px 22px;
    }

    .nav-child-item {
      padding: 11px 22px;
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1200px) {
    .sidebar {
      width: 300px;
    }

    .nav-item, .nav-category-header {
      padding: 16px 28px;
      font-size: 17px;
    }

    .nav-child-item {
      padding: 13px 28px;
      font-size: 16px;
    }
  }
</style>
