defmodule RacingGame.Live.AdminPanel.ProfileComponent do
  @moduledoc """
  个人信息管理组件
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias RacingGame.Bet
  alias <PERSON>pridina.Accounts.{User, AgentRelationship, AccountIdentifier}
  alias <PERSON><PERSON>ridina.Utils.TimeHelper
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:show_change_password_modal, false)
      |> assign(:show_refund_modal, false)
      |> assign(:change_password_form, %{})
      |> assign(:refund_form, %{})
      |> load_profile_data()

    {:ok, socket}
  end

  def handle_event("refresh", _params, socket) do
    socket = load_profile_data(socket)
    {:noreply, socket}
  end

  def handle_event("show_change_password_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_change_password_modal, true)
      |> assign(:change_password_form, %{
        "current_password" => "",
        "password" => "",
        "password_confirmation" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_change_password_modal", _params, socket) do
    socket = assign(socket, :show_change_password_modal, false)
    {:noreply, socket}
  end

  def handle_event("show_refund_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_refund_modal, true)
      |> assign(:refund_form, %{
        "amount" => "",
        "reason" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_refund_modal", _params, socket) do
    socket = assign(socket, :show_refund_modal, false)
    {:noreply, socket}
  end

  def handle_event("change_password", %{"password" => password_params}, socket) do
    user = socket.assigns.current_user

    Logger.info("change_password: #{inspect(password_params)}")

    case user
         |> Ash.Changeset.for_update(:change_password, password_params)
         |> Ash.update() do
      {:ok, _updated_user} ->
        send(self(), {:flash, :info, "密码修改成功"})

        socket =
          socket
          |> assign(:show_change_password_modal, false)
          |> assign(:change_password_form, %{})

        {:noreply, socket}

      {:error, error} ->
        error_message =
          case error do
            %Ash.Error.Invalid{} -> "密码修改失败，请检查输入信息"
            _ -> "密码修改失败，请稍后重试"
          end

        send(self(), {:flash, :error, error_message})
        {:noreply, socket}
    end
  end

  def handle_event("request_refund", %{"refund" => refund_params}, socket) do
    user = socket.assigns.current_user
    amount = String.to_integer(refund_params["amount"] || "0")
    reason = refund_params["reason"] || ""

    # 检查用户积分是否足够
    current_points = Cypridina.Accounts.get_user_points(user.id)

    if current_points >= amount do
      case create_refund_request_with_deduction(user, amount, reason) do
        {:ok, _transaction} ->
          send(self(), {:flash, :info, "退费申请提交成功，积分已暂时扣除，请等待处理"})

          socket =
            socket
            |> assign(:show_refund_modal, false)
            |> assign(:refund_form, %{})
            |> load_profile_data()

          {:noreply, socket}

        {:error, error} ->
          error_message =
            case error do
              %Ash.Error.Invalid{} ->
                Logger.error("退费申请失败: 用户#{user.id}, 原因: #{inspect(error)}")
                "退费申请失败，请检查输入信息"

              _ ->
                "退费申请失败，请稍后重试"
            end

          send(self(), {:flash, :error, error_message})
          {:noreply, socket}
      end
    else
      send(self(), {:flash, :error, "积分不足，无法申请退费"})
      {:noreply, socket}
    end
  end

  defp load_profile_data(socket) do
    user = socket.assigns.current_user

    profile_data = %{
      user: user,
      role: AuthHelper.get_permission_level_name(user),
      permissions: get_user_permissions(user),
      stats: get_user_stats(user),
      agent_info: get_agent_info(user.id)
    }

    assign(socket, :profile_data, profile_data)
  end

  defp get_user_permissions(user) do
    [
      %{name: "管理员权限", has: AuthHelper.has_permission?(user, :admin)},
      %{name: "超级管理员", has: AuthHelper.has_permission?(user, :super_admin)}
    ]
  end

  defp get_user_stats(user) do
    %{
      total_bets: get_user_bet_count(user.id),
      total_stock_transactions: get_user_stock_transaction_count(user.id),
      current_points: get_user_current_points(user.id),
      subordinates_count: get_subordinates_count(user.id)
    }
  end

  defp get_user_bet_count(user_id) do
    Bet
    |> Ash.Query.filter(user_id == ^user_id)
    |> Ash.read!()
    |> length()
  end

  defp get_user_stock_transaction_count(user_id) do
    # 使用Ledger系统查询股票交易记录
    case Cypridina.Ledger.get_user_transfer_history(user_id) do
      {:ok, transfers} ->
        # 过滤出股票相关的交易
        transfers
        |> Enum.filter(&(&1.transaction_type in [:buy_stock, :sell_stock]))
        |> length()

      {:error, _} ->
        0
    end
  end

  defp get_user_current_points(user_id) do
    Cypridina.Accounts.get_user_points(user_id)
  end

  defp get_subordinates_count(user_id) do
    get_agent_subordinates(user_id) |> length()
  end

  defp get_agent_subordinates(agent_id) do
    case AgentRelationship
         |> Ash.Query.filter(agent_id == ^agent_id and status == 1)
         |> Ash.read() do
      {:ok, relationships} -> relationships
      _ -> []
    end
  end

  # 获取代理信息（包括上级代理）
  defp get_agent_info(user_id) do
    # 查找用户的上级代理
    case Cypridina.Accounts.AgentRelationship
         |> Ash.Query.filter(subordinate_id == ^user_id and status == 1)
         |> Ash.Query.load([:agent])
         |> Ash.read() do
      {:ok, [relationship | _]} ->
        %{
          has_agent: true,
          agent: relationship.agent,
          commission_rate: relationship.commission_rate,
          level: relationship.level
        }

      {:ok, []} ->
        %{has_agent: false, agent: nil, commission_rate: nil, level: nil}

      _ ->
        %{has_agent: false, agent: nil, commission_rate: nil, level: nil}
    end
  end

  # 创建退费申请 - 直接将积分转给上线代理，状态设为pending
  defp create_refund_request_with_deduction(user, amount, reason) do
    user_id = user.id
    username = user.username
    # 获取用户的上线代理
    case Cypridina.Accounts.get_user_agent_relationship(user_id) do
      {:ok, agent_relationship} ->
        agent_id = agent_relationship.agent_id

        # 同步更新BalanceCache - 申请退费时积分从用户转给代理
        user_identifier = AccountIdentifier.user(user_id, :XAA)
        agent_identifier = AccountIdentifier.user(agent_id, :XAA)

        # 获取用户和代理的账户
        with {:ok, user_account} <- Cypridina.Ledger.Account.get_by_identifier(user_identifier),
             {:ok, agent_account} <- Cypridina.Ledger.Account.get_by_identifier(agent_identifier) do
          # 创建pending状态的转账记录，直接将积分转给代理
          case Cypridina.Ledger.Transfer
               |> Ash.Changeset.for_create(:transfer, %{
                 amount: Money.new(:XAA, amount),
                 from_account_id: user_account.id,
                 to_account_id: agent_account.id,
                 transaction_type: :refund,
                 status: :pending,
                 description: "玩家#{username}申请退费: #{reason}",
                 metadata: %{
                   refund_reason: reason,
                   requested_at: DateTime.utc_now(),
                   request_type: "refund",
                   requester_id: user_id,
                   agent_id: agent_id,
                   agent_username: agent_relationship.agent.username,
                   requester_username: get_username(user_id)
                 }
               })
               |> Ash.create() do
            {:ok, transfer} ->
              Logger.info("退费申请创建成功: 用户#{user_id} 向代理#{agent_id} 申请退费#{amount}积分")

              Cypridina.Ledger.BalanceCache.subtract_balance(user_identifier, amount)
              Cypridina.Ledger.BalanceCache.add_balance(agent_identifier, amount)

              {:ok, transfer}

            {:error, error} ->
              Logger.error("创建退费申请失败: #{inspect(error)}")
              {:error, error}
          end
        else
          {:error, reason} ->
            Logger.error("获取账户信息失败: #{inspect(reason)}")
            {:error, "获取账户信息失败"}
        end

      {:error, :no_agent} ->
        {:error, "您没有上线代理，无法申请退费"}

      {:error, reason} ->
        Logger.error("获取代理关系失败: #{inspect(reason)}")
        {:error, "获取代理关系失败"}
    end
  end

  # 获取用户名的辅助函数
  defp get_username(user_id) do
    Cypridina.Accounts.get_username(user_id)
  end

  def render(assigns) do
    ~H"""
    <div class="profile-wrapper">
      <style>
        .profile-wrapper .profile-container {
          padding: 1.5rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .profile-wrapper .profile-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.5rem;
        }

        .profile-wrapper .profile-card {
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
          overflow: hidden;
        }

        .profile-wrapper .stats-card {
          grid-column: 1 / -1;
        }

        .profile-wrapper .card-header {
          padding: 1.25rem 1.5rem;
          border-bottom: 1px solid #e5e7eb;
          background: #f9fafb;
        }

        .profile-wrapper .card-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #374151;
          margin: 0;
        }

        .profile-wrapper .card-content {
          padding: 1.5rem;
        }

        .profile-wrapper .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid #f3f4f6;
        }

        .profile-wrapper .info-item:last-child {
          border-bottom: none;
        }

        .profile-wrapper .info-label {
          color: #6b7280;
          font-size: 0.875rem;
        }

        .profile-wrapper .info-value {
          font-weight: 500;
          color: #374151;
        }

        .profile-wrapper .role-badge {
          background: #3b82f6;
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .profile-wrapper .permission-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
        }

        .profile-wrapper .permission-name {
          color: #6b7280;
          font-size: 0.875rem;
        }

        .profile-wrapper .permission-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .profile-wrapper .permission-granted {
          background: #10b981;
          color: white;
        }

        .profile-wrapper .permission-denied {
          background: #e5e7eb;
          color: #6b7280;
        }

        .profile-wrapper .stats-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 1rem;
          padding: 1.5rem;
        }

        .profile-wrapper .stat-item {
          background: #f8fafc;
          padding: 1.5rem;
          border-radius: 8px;
          text-align: center;
          border: 1px solid #e2e8f0;
        }

        .profile-wrapper .stat-label {
          font-size: 0.875rem;
          color: #64748b;
          margin-bottom: 0.5rem;
        }

        .profile-wrapper .stat-value {
          font-size: 1.875rem;
          font-weight: 700;
          line-height: 1;
        }

        .profile-wrapper .stat-primary .stat-value { color: #3b82f6; }
        .profile-wrapper .stat-secondary .stat-value { color: #8b5cf6; }
        .profile-wrapper .stat-accent .stat-value { color: #06b6d4; }
        .profile-wrapper .stat-info .stat-value { color: #10b981; }

        .profile-wrapper .loading-container {
          text-align: center;
          padding: 3rem;
        }

        .profile-wrapper .loading-text {
          color: #6b7280;
          font-size: 1rem;
        }

        /* 移动端响应式 */
        @media (max-width: 768px) {
          .profile-wrapper .profile-container {
            padding: 1rem;
          }

          .profile-wrapper .profile-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .profile-wrapper .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            padding: 1rem;
          }

          .profile-wrapper .stat-item {
            padding: 1rem;
          }

          .profile-wrapper .stat-value {
            font-size: 1.5rem;
          }

          .profile-wrapper .card-header {
            padding: 1rem 1.25rem;
          }

          .profile-wrapper .card-content {
            padding: 1.25rem;
          }

          .profile-wrapper .info-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
          }

          .profile-wrapper .permission-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
          }
        }

        /* 小屏幕设备 */
        @media (max-width: 480px) {
          .profile-wrapper .stats-grid {
            grid-template-columns: 1fr;
          }

          .profile-wrapper .stat-value {
            font-size: 1.25rem;
          }
        }
      </style>

      <div class="profile-container">
        <%= if @profile_data do %>
          <div class="profile-grid">
            <!-- 基本信息卡片 -->
            <div class="profile-card">
              <div class="card-header">
                <h2 class="card-title">基本信息</h2>
                <div class="flex gap-2">
                  <.live_component
                    module={CypridinaWeb.Components.PointsHistoryComponent}
                    id="profile-points-history"
                    user_id={@profile_data.user.id}
                    user_info={
                      %{
                        username: @profile_data.user.username,
                        numeric_id: @profile_data.user.numeric_id,
                        current_points: @profile_data.stats.current_points
                      }
                    }
                  />
                  <button
                    phx-click="show_change_password_modal"
                    phx-target={@myself}
                    class="btn btn-sm btn-outline btn-primary"
                  >
                    修改密码
                  </button>
                </div>
              </div>
              <div class="card-content">
                <div class="info-item">
                  <span class="info-label">用户名:</span>
                  <span class="info-value">{to_string(@profile_data.user.username)}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">数字ID:</span>
                  <span class="info-value">{@profile_data.user.numeric_id}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">角色:</span>
                  <span class="role-badge">{@profile_data.role}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">注册时间:</span>
                  <span class="info-value">
                    {TimeHelper.format_local_datetime(@profile_data.user.inserted_at)}
                  </span>
                </div>
              </div>
            </div>
            
    <!-- 权限信息卡片 -->
            <div class="profile-card">
              <div class="card-header">
                <h2 class="card-title">权限信息</h2>
              </div>
              <div class="card-content">
                <%= for permission <- @profile_data.permissions do %>
                  <div class="permission-item">
                    <span class="permission-name">{permission.name}</span>
                    <%= if permission.has do %>
                      <span class="permission-badge permission-granted">已授权</span>
                    <% else %>
                      <span class="permission-badge permission-denied">未授权</span>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
            
    <!-- 身份信息卡片 -->
            <div class="profile-card">
              <div class="card-header">
                <h2 class="card-title">身份信息</h2>
              </div>
              <div class="card-content">
                <div class="info-item">
                  <span class="info-label">用户身份:</span>
                  <span class="info-value">
                    <%= if @profile_data.user.agent_level >= 0 do %>
                      <span class="badge badge-info">代理L{@profile_data.user.agent_level}</span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </span>
                </div>
                <%= if @profile_data.user.agent_level >= 0 do %>
                  <div class="info-item">
                    <span class="info-label">代理类型:</span>
                    <span class="info-value">
                      <%= case @profile_data.user.agent_level do %>
                        <% 0 -> %>
                          <span class="badge badge-warning">根代理</span>
                        <% level when level > 0 -> %>
                          <span class="badge badge-info">{level}级代理</span>
                      <% end %>
                    </span>
                  </div>
                <% end %>
              </div>
            </div>
            
    <!-- 上级信息卡片 -->
            <div class="profile-card">
              <div class="card-header">
                <h2 class="card-title">上级代理信息</h2>
                <%= if @profile_data.agent_info.has_agent do %>
                  <button
                    phx-click="show_refund_modal"
                    phx-target={@myself}
                    class="btn btn-sm btn-outline btn-warning"
                  >
                    申请退费
                  </button>
                <% end %>
              </div>
              <div class="card-content">
                <%= if @profile_data.agent_info.has_agent do %>
                  <div class="info-item">
                    <span class="info-label">上级代理:</span>
                    <span class="info-value font-semibold text-blue-600">
                      {@profile_data.agent_info.agent.username}
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">代理等级:</span>
                    <span class="info-value">
                      <span class="badge badge-info">L{@profile_data.agent_info.level}</span>
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">抽水比例:</span>
                    <span class="info-value text-green-600">
                      {@profile_data.agent_info.commission_rate}
                    </span>
                  </div>
                <% else %>
                  <div class="text-center py-8 text-gray-500">
                    <div class="text-lg mb-2">🏢</div>
                    <p>您暂无上级代理</p>
                    <p class="text-sm">如需加入代理体系，请联系管理员</p>
                  </div>
                <% end %>
              </div>
            </div>
            <!-- 统计信息卡片 -->
            <div class="profile-card stats-card">
              <div class="card-header">
                <h2 class="card-title">统计信息</h2>
              </div>
              <div class="stats-grid">
                <div class="stat-item stat-primary">
                  <div class="stat-label">当前积分</div>
                  <div class="stat-value">
                    <span>{@profile_data.stats.current_points}</span>
                  </div>
                </div>
                <div class="stat-item stat-secondary">
                  <div class="stat-label">下注次数</div>
                  <div class="stat-value">{@profile_data.stats.total_bets}</div>
                </div>
                <div class="stat-item stat-accent">
                  <div class="stat-label">股票交易</div>
                  <div class="stat-value">{@profile_data.stats.total_stock_transactions}</div>
                </div>
                <div class="stat-item stat-info">
                  <div class="stat-label">下线数量</div>
                  <div class="stat-value">{@profile_data.stats.subordinates_count}</div>
                </div>
              </div>
            </div>
          </div>
          
    <!-- 修改密码模态框 -->
          <%= if @show_change_password_modal do %>
            <div class="modal modal-open">
              <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">修改密码</h3>
                <form phx-submit="change_password" phx-target={@myself}>
                  <div class="form-control mb-4">
                    <label class="label">
                      <span class="label-text">当前密码</span>
                    </label>
                    <input
                      type="password"
                      name="password[current_password]"
                      class="input input-bordered"
                      required
                      placeholder="请输入当前密码"
                    />
                  </div>
                  <div class="form-control mb-4">
                    <label class="label">
                      <span class="label-text">新密码</span>
                    </label>
                    <input
                      type="password"
                      name="password[password]"
                      class="input input-bordered"
                      required
                      placeholder="请输入新密码"
                      minlength="6"
                    />
                  </div>
                  <div class="form-control mb-6">
                    <label class="label">
                      <span class="label-text">确认新密码</span>
                    </label>
                    <input
                      type="password"
                      name="password[password_confirmation]"
                      class="input input-bordered"
                      required
                      placeholder="请再次输入新密码"
                      minlength="6"
                    />
                  </div>
                  <div class="modal-action">
                    <button type="submit" class="btn btn-primary">确认修改</button>
                    <button
                      type="button"
                      phx-click="hide_change_password_modal"
                      phx-target={@myself}
                      class="btn btn-ghost"
                    >
                      取消
                    </button>
                  </div>
                </form>
              </div>
            </div>
          <% end %>
          
    <!-- 退费申请模态框 -->
          <%= if @show_refund_modal do %>
            <div class="modal modal-open">
              <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">申请退费</h3>
                
    <!-- 当前积分余额显示 -->
                <div class="alert alert-info mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="stroke-current shrink-0 h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    >
                    </path>
                  </svg>
                  <span>当前积分余额: <strong>{@profile_data.stats.current_points}</strong> 积分</span>
                </div>

                <div class="alert alert-warning mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="stroke-current shrink-0 h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                  <div>
                    <p><strong>重要提示：</strong></p>
                    <p>• 提交申请后，积分将立即扣除</p>
                    <p>• 申请通过后，积分将转为现金退还</p>
                    <p>• 申请被拒绝后，积分将自动返还到您的账户</p>
                  </div>
                </div>

                <form phx-submit="request_refund" phx-target={@myself}>
                  <div class="form-control mb-4">
                    <label class="label">
                      <span class="label-text">退费金额</span>
                    </label>
                    <input
                      type="number"
                      name="refund[amount]"
                      class="input input-bordered"
                      required
                      min="1"
                      max={@profile_data.stats.current_points}
                      placeholder="请输入退费金额"
                    />
                    <label class="label">
                      <span class="label-text-alt">
                        最大可退费: {@profile_data.stats.current_points} 积分
                      </span>
                    </label>
                  </div>
                  <div class="form-control mb-6">
                    <label class="label">
                      <span class="label-text">退费原因</span>
                    </label>
                    <textarea
                      name="refund[reason]"
                      class="textarea textarea-bordered"
                      required
                      placeholder="请详细说明退费原因"
                      rows="3"
                    >
                    </textarea>
                  </div>
                  <div class="modal-action">
                    <button type="submit" class="btn btn-warning">提交申请</button>
                    <button
                      type="button"
                      phx-click="hide_refund_modal"
                      phx-target={@myself}
                      class="btn btn-ghost"
                    >
                      取消
                    </button>
                  </div>
                </form>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="loading-container">
            <p class="loading-text">正在加载个人信息...</p>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
