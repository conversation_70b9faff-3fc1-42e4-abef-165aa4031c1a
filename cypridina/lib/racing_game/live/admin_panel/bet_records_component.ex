defmodule RacingGame.Live.AdminPanel.BetRecordsComponent do
  @moduledoc """
  下注记录组件
  """
  use CypridinaWeb, :live_component

  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  alias RacingGame.Bet
  require Ash.Query

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_bet_records_data()

    {:ok, socket}
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
  end

  def handle_event("refresh", _params, socket) do
    socket = load_bet_records_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_bet_records_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:page, 1)
      |> load_bet_records_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)

    socket =
      socket
      |> assign(:page, page)
      |> load_bet_records_data()

    {:noreply, socket}
  end

  defp load_bet_records_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 构建基础查询
    query =
      Bet
      |> Ash.Query.new()
      |> Ash.Query.load([:user])

    # 应用权限过滤
    query = PermissionFilter.apply_user_filter(query, user, :user_id)

    # 添加搜索条件
    query =
      if search_query != "" do
        # 对于 Bet，我们需要通过关联的用户名进行搜索
        # 先获取匹配的用户ID，然后过滤下注记录
        import Ash.Query

        matching_user_ids =
          case Cypridina.Accounts.User
               |> Ash.Query.new()
               |> filter(contains(username, ^search_query))
               |> select([:id])
               |> Ash.read() do
            {:ok, users} -> Enum.map(users, & &1.id)
            _ -> []
          end

        if length(matching_user_ids) > 0 do
          filter(query, user_id in ^matching_user_ids)
        else
          # 如果没有匹配的用户，返回空结果
          filter(query, user_id == "no-match")
        end
      else
        query
      end

    # 使用 Ash.Query.page 进行分页查询并按更新时间倒序排列
    case query
         |> Ash.Query.sort(updated_at: :desc)
         |> Ash.Query.page(count: true, limit: per_page, offset: (page - 1) * per_page)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: bets, count: total_count}} ->
        socket
        |> assign(:bet_records_data, bets)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

      {:error, error} ->
        require Logger
        Logger.error("Failed to load bet records data: #{inspect(error)}")

        socket
        |> assign(:bet_records_data, [])
        |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">下注记录</h2>
        <div class="text-sm text-base-content/60">
          <%= if @page_info do %>
            共 {@page_info.total_count} 条记录，第 {@page_info.page} 页
          <% else %>
            加载中...
          <% end %>
        </div>
      </div>
      
    <!-- 搜索框 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="按用户名搜索..."
            class="input input-bordered input-sm w-64"
          />
          <button type="submit" class="btn btn-outline btn-sm">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" /> 搜索
          </button>
        </form>
        <%= if @search_query != "" do %>
          <button phx-click="clear_search" phx-target={@myself} class="btn btn-ghost btn-sm">
            <.icon name="hero-x-mark" class="w-4 h-4" /> 清除搜索
          </button>
        <% end %>
      </div>

      <%= if @bet_records_data && length(@bet_records_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>用户名</th>
                <th>用户ID</th>
                <th>期号</th>
                <th>选择</th>
                <th>下注金额</th>
                <th>状态</th>
                <th>奖金</th>
                <th>下注时间</th>
              </tr>
            </thead>
            <tbody>
              <%= for bet <- @bet_records_data do %>
                <tr>
                  <td class="font-medium">
                    <%= if bet.user do %>
                      {to_string(bet.user.username)}
                    <% else %>
                      未知用户
                    <% end %>
                  </td>
                  <td class="font-mono text-sm">{String.slice(bet.user_id, 0, 8)}...</td>
                  <td class="font-mono">{bet.race_issue}</td>
                  <td>
                    <span class="badge badge-primary">{bet.selection}</span>
                  </td>
                  <td class="font-medium">{bet.amount}</td>
                  <td>
                    <%= case bet.status do %>
                      <% 0 -> %>
                        <span class="badge badge-warning">待开奖</span>
                      <% 1 -> %>
                        <span class="badge badge-success">中奖</span>
                      <% 2 -> %>
                        <span class="badge badge-error">未中奖</span>
                      <% _ -> %>
                        <span class="badge badge-ghost">未知</span>
                    <% end %>
                  </td>
                  <td class="font-medium">
                    <%= if bet.payout > 0 do %>
                      <span class="text-success">{bet.payout}</span>
                    <% else %>
                      <span class="text-base-content/40">-</span>
                    <% end %>
                  </td>
                  <td>{TimeHelper.format_local_short_time(bet.inserted_at)}</td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
    <!-- 分页组件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  {page_num}
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-ticket" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">暂无下注记录</p>
        </div>
      <% end %>
    </div>
    """
  end
end
