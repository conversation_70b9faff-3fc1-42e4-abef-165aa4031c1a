defmodule RacingGame.Live.AdminPanel.PermissionFilter do
  @moduledoc """
  管理后台权限过滤器模块

  提供基于用户身份的数据过滤功能：
  - 普通玩家：仅能看到自己的记录
  - 代理：能看到自己和下线的记录
  - 管理员：可以查看所有记录
  """

  alias Cypridina.Accounts.AgentRelationship
  alias Cypridina.Accounts
  require Ash.Query
  import Ash.Expr
  require Logger

  @doc """
  根据用户权限过滤用户ID列表

  ## 参数
  - `user`: 当前用户

  ## 返回值
  - `{:all}`: 管理员可以查看所有记录
  - `{:user_ids, [user_id]}`: 限制查看的用户ID列表
  """
  def get_accessible_user_ids(user) do
    try do
      cond do
        # 管理员可以查看所有记录
        Accounts.is_admin?(user) ->
          {:all}

        # 代理可以查看自己和下线的记录
        user.agent_level >= 0 ->
          subordinate_ids = get_agent_subordinate_ids(user.id)
          {:user_ids, [user.id | subordinate_ids]}

        # 普通用户只能查看自己的记录
        true ->
          {:user_ids, [user.id]}
      end
    rescue
      error ->
        Logger.error(
          "Error in get_accessible_user_ids: #{inspect(error)}, user: #{inspect(user)}"
        )

        # 安全降级：只允许查看自己的记录
        {:user_ids, [user.id]}
    end
  end

  @doc """
  为Ash查询添加用户权限过滤器

  ## 参数
  - `query`: Ash查询对象
  - `user`: 当前用户
  - `user_field`: 用户ID字段名，默认为 :user_id

  ## 返回值
  - 添加了权限过滤的查询对象
  """
  def apply_user_filter(query, user, user_field \\ :user_id) do
    require Ash.Query
    import Ash.Expr

    try do
      case get_accessible_user_ids(user) do
        {:all} ->
          # 管理员不需要过滤
          query

        {:user_ids, user_ids} ->
          # 添加用户ID过滤
          case user_field do
            :user_id -> Ash.Query.filter(query, expr(user_id in ^user_ids))
            :id -> Ash.Query.filter(query, expr(id in ^user_ids))
            field -> Ash.Query.filter(query, expr(^ref(field) in ^user_ids))
          end
      end
    rescue
      error ->
        Logger.error(
          "Error in apply_user_filter: #{inspect(error)}, user: #{inspect(user)}, user_field: #{inspect(user_field)}"
        )

        # 安全降级：只允许查看自己的记录
        case user_field do
          :user_id -> Ash.Query.filter(query, expr(user_id == ^user.id))
          :id -> Ash.Query.filter(query, expr(id == ^user.id))
          field -> Ash.Query.filter(query, expr(^ref(field) == ^user.id))
        end
    end
  end

  @doc """
  为已加载的数据列表应用权限过滤

  ## 参数
  - `data_list`: 数据列表
  - `user`: 当前用户
  - `user_field`: 用户ID字段名，默认为 :user_id

  ## 返回值
  - 过滤后的数据列表
  """
  def filter_data_by_permission(data_list, user, user_field \\ :user_id) do
    case get_accessible_user_ids(user) do
      {:all} ->
        # 管理员可以查看所有数据
        data_list

      {:user_ids, user_ids} ->
        # 过滤数据
        Enum.filter(data_list, fn item ->
          user_id = get_field_value(item, user_field)
          user_id in user_ids
        end)
    end
  end

  @doc """
  检查用户是否可以访问特定用户的数据

  ## 参数
  - `current_user`: 当前用户
  - `target_user_id`: 目标用户ID

  ## 返回值
  - `true`: 可以访问
  - `false`: 不能访问
  """
  def can_access_user_data?(current_user, target_user_id) do
    case get_accessible_user_ids(current_user) do
      {:all} ->
        true

      {:user_ids, user_ids} ->
        target_user_id in user_ids
    end
  end

  @doc """
  获取代理的所有下线用户ID

  ## 参数
  - `agent_id`: 代理用户ID

  ## 返回值
  - 下线用户ID列表
  """
  def get_agent_subordinate_ids(agent_id) do
    try do
      case AgentRelationship
           |> Ash.Query.filter(agent_id == ^agent_id and status == 1)
           |> Ash.read() do
        {:ok, relationships} ->
          Enum.map(relationships, & &1.subordinate_id)

        {:error, error} ->
          Logger.error(
            "Failed to get agent subordinate IDs for agent #{agent_id}: #{inspect(error)}"
          )

          []
      end
    rescue
      error ->
        Logger.error(
          "Exception in get_agent_subordinate_ids for agent #{agent_id}: #{inspect(error)}"
        )

        []
    end
  end

  @doc """
  获取代理的所有下线用户ID（包括多级下线）

  ## 参数
  - `agent_id`: 代理用户ID

  ## 返回值
  - 所有下线用户ID列表（包括多级）
  """
  def get_all_subordinate_ids(agent_id) do
    # 递归获取所有下线
    get_subordinate_ids_recursive(agent_id, [])
  rescue
    _ -> []
  end

  @doc """
  为搜索查询添加权限过滤

  ## 参数
  - `query`: Ash查询对象
  - `user`: 当前用户
  - `search_query`: 搜索关键词
  - `search_field`: 搜索字段名，默认为 :username
  - `user_field`: 用户ID字段名，默认为 :user_id

  ## 返回值
  - 添加了权限过滤和搜索过滤的查询对象
  """
  def apply_search_with_permission(
        query,
        user,
        search_query,
        search_field \\ :username,
        user_field \\ :user_id
      ) do
    # 先应用权限过滤
    query = apply_user_filter(query, user, user_field)

    # 再应用搜索过滤
    if search_query != "" do
      case search_field do
        :username -> Ash.Query.filter(query, expr(contains(username, ^search_query)))
        field -> Ash.Query.filter(query, expr(contains(^ref(field), ^search_query)))
      end
    else
      query
    end
  end

  @doc """
  获取用户权限范围描述

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 权限范围描述字符串
  """
  def get_permission_scope_description(user) do
    cond do
      Accounts.is_super_admin?(user) ->
        "超级管理员 - 可查看所有用户数据"

      Accounts.is_admin?(user) ->
        "管理员 - 可查看所有用户数据"

      user.agent_level >= 0 ->
        subordinate_count = length(get_agent_subordinate_ids(user.id))
        "代理 - 可查看自己和 #{subordinate_count} 个下线的数据"

      true ->
        "普通用户 - 仅可查看自己的数据"
    end
  end

  # 私有函数

  defp get_subordinate_ids_recursive(agent_id, visited) do
    if agent_id in visited do
      # 防止循环引用
      []
    else
      direct_subordinates = get_agent_subordinate_ids(agent_id)
      new_visited = [agent_id | visited]

      # 获取直接下线的下线
      indirect_subordinates =
        direct_subordinates
        |> Enum.flat_map(&get_subordinate_ids_recursive(&1, new_visited))

      # 合并直接下线和间接下线
      (direct_subordinates ++ indirect_subordinates)
      |> Enum.uniq()
    end
  end

  defp get_field_value(item, field) when is_map(item) do
    case field do
      :user_id -> Map.get(item, :user_id)
      :id -> Map.get(item, :id)
      _ -> Map.get(item, field)
    end
  end

  defp get_field_value(item, field) when is_struct(item) do
    Map.get(item, field)
  end

  defp get_field_value(_, _), do: nil
end
