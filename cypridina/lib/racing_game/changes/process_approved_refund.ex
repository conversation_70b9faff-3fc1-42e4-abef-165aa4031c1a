defmodule RacingGame.Changes.ProcessApprovedRefund do
  @moduledoc """
  当退费申请被批准时，给上级代理增加积分
  """
  use Ash.Resource.Change

  alias Cypridina.Accounts.AgentRelationship
  alias Cypridina.Ledger
  require Logger

  @impl true
  def change(changeset, _opts, _context) do
    # 只在状态变为 approved 时执行
    if Ash.Changeset.get_attribute(changeset, :status) == :approved do
      handle_refund_approval(changeset)
    else
      changeset
    end
  end

  defp handle_refund_approval(changeset) do
    user_id = Ash.Changeset.get_attribute(changeset, :user_id)
    amount = Ash.Changeset.get_attribute(changeset, :amount)
    extra_data = Ash.Changeset.get_attribute(changeset, :extra_data) || %{}

    # 检查是否是退费申请
    if is_refund_request?(extra_data) do
      refund_amount = abs(Decimal.to_integer(amount))

      case add_points_to_agent(user_id, refund_amount, extra_data) do
        {:ok, agent_id} ->
          # 更新 extra_data 标记积分已给代理
          new_extra_data =
            Map.merge(extra_data, %{
              "points_added_to_agent" => true,
              "agent_id" => agent_id,
              "agent_points_added_at" => DateTime.utc_now()
            })

          Ash.Changeset.change_attribute(changeset, :extra_data, new_extra_data)

        {:error, :no_agent} ->
          # 用户没有代理，记录日志但不报错
          Logger.info(
            "用户 #{user_id} 没有代理，退费申请 #{Ash.Changeset.get_attribute(changeset, :id)} 无需给代理增加积分"
          )

          new_extra_data = Map.put(extra_data, "no_agent_found", true)
          Ash.Changeset.change_attribute(changeset, :extra_data, new_extra_data)

        {:error, reason} ->
          Logger.error("给代理增加退费积分失败: #{inspect(reason)}")
          Ash.Changeset.add_error(changeset, :base, "给代理增加积分失败: #{reason}")
      end
    else
      changeset
    end
  end

  defp is_refund_request?(extra_data) do
    extra_data["request_type"] == "refund"
  end

  defp add_points_to_agent(user_id, amount, extra_data) do
    # 获取用户的代理关系
    case get_user_agent_relationship(user_id) do
      {:ok, agent_relationship} ->
        agent_id = agent_relationship.agent_id

        # 使用 Accounts 模块的 add_points 函数增加积分（已包含Ledger记录）
        case Cypridina.Accounts.transfer_points(user_id, agent_id, amount,
               transaction_type: :refund_income,
               description: "收到下线退费: #{extra_data["refund_reason"] || "无原因"}",
               metadata: %{
                 "refund_reason" => extra_data["refund_reason"],
                 "subordinate_id" => user_id,
                 "subordinate_username" => get_username(user_id),
                 "original_request_type" => "refund",
                 "income_source" => "subordinate_refund"
               }
             ) do
          {:ok, transfer} ->
            Logger.info("成功给代理 #{agent_id} 增加退费积分 #{amount}")
            {:ok, agent_id}

          {:error, reason} ->
            Logger.error("给代理增加退费积分失败: #{inspect(reason)}")
            {:error, "增加积分失败"}
        end

      {:error, :no_agent} ->
        {:error, :no_agent}

      {:error, reason} ->
        Logger.error("获取代理关系失败: #{inspect(reason)}")
        {:error, "获取代理关系失败"}
    end
  end

  defp get_user_agent_relationship(user_id) do
    # 使用 Ash.Query 来查询代理关系
    case Cypridina.Accounts.AgentRelationship
         |> Ash.Query.filter(subordinate_id == ^user_id)
         |> Ash.Query.load(:agent)
         |> Ash.read() do
      {:ok, [relationship | _]} -> {:ok, relationship}
      {:ok, []} -> {:error, :no_agent}
      error -> error
    end
  end

  defp get_username(user_id) do
    case Cypridina.Accounts.User |> Ash.get(user_id) do
      {:ok, user} -> user.username || "未知用户"
      _ -> "未知用户"
    end
  end
end
