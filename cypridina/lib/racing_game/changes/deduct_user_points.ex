defmodule RacingGame.Changes.DeductUserPoints do
  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    user_id = Ash.Changeset.get_attribute(changeset, :user_id)
    amount = Ash.Changeset.get_attribute(changeset, :amount)

    # 使用新的账户系统扣除积分
    case Cypridina.Ledger.game_bet(0, user_id, amount,
           description: "下注扣除积分",
           metadata: %{racing_game: true}
         ) do
      {:ok, _new_balance} ->
        changeset

      {:error, reason} ->
        Ash.Changeset.add_error(changeset, :amount, reason)
    end
  end
end

# filepath: /app/cypridina/lib/cypridina/racing_game/changes/add_payout_to_user.ex
# defmodule RacingGame.Changes.AddPayoutToUser do
#   use Ash.Resource.Change

#   def change(changeset, _opts, _context) do
#     bet = Ash.Changeset.data(changeset)
#     status = Ash.Changeset.get_attribute(changeset, :status)

#     if status == :win do
#       payout_amount = Ash.Changeset.get_attribute(changeset, :payout_amount)

#       case Cypridina.Accounts.User.get(bet.user_id) do
#         {:ok, user} ->
#           # 使用事务增加用户积分
#           case Cypridina.Accounts.User.update(user, %{points: user.points + payout_amount}) do
#             {:ok, _} -> changeset
#             {:error, error} -> Ash.Changeset.add_error(changeset, field: :payout_amount, message: "增加积分失败: #{inspect(error)}")
#           end
#         {:error, error} ->
#           Ash.Changeset.add_error(changeset, field: :user_id, message: "找不到用户: #{inspect(error)}")
#       end
#     else
#       changeset
#     end
#   end
# end
