defmodule RacingGame.Race do
  use Ash.Resource,
    otp_app: :cypridina,
    domain: RacingGame,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "races"
    repo Cypridina.Repo
  end

  code_interface do
    define :get_current, action: :current_race
    define :get_latest, action: :latest_race
    define :get_recent, action: :recent_races
    define :create, action: :create
    define :update, action: :update
    define :set_result, action: :set_race_result
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :issue,
        :issue_id,
        :status,
        :start_time,
        :end_time,
        :issue_end_time,
        :order_end_time,
        :bet_amount_map
      ]
    end

    update :update do
      primary? true

      accept [
        :issue,
        :issue_id,
        :status,
        :start_time,
        :end_time,
        :issue_end_time,
        :order_end_time
      ]
    end

    read :current_race do
      filter expr(status < 2 and end_time > ^DateTime.utc_now())
      prepare build(sort: [start_time: :desc], limit: 1)
    end

    read :latest_race do
      filter expr(status == 2)
      prepare build(sort: [end_time: :desc], limit: 1)
    end

    read :recent_races do
      filter expr(status == 2)
      prepare build(sort: [end_time: :desc], limit: 100)
    end

    update :set_race_result do
      accept [:positions, :speeds, :end_times, :status]
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :issue, :string do
      allow_nil? false
      public? true
    end

    attribute :issue_id, :string do
      # 修改：在上面定义了 allow_nil? false，这里不需要重复
      allow_nil? true
      public? true
    end

    # 修改：根据接口示例，positions 应该是 string 数组，不是 integer 数组
    attribute :positions, {:array, :string} do
      # 添加默认值
      default []
      public? true
    end

    attribute :speeds, {:array, :string} do
      # 添加默认值
      default []
      public? true
    end

    attribute :end_times, {:array, :string} do
      # 添加默认值
      default []
      public? true
    end

    attribute :status, :integer do
      # 添加默认值
      default 0
      public? true
    end

    attribute :start_time, :utc_datetime_usec do
      public? true
    end

    attribute :end_time, :utc_datetime_usec do
      public? true
    end

    # 添加缺少的字段
    attribute :issue_end_time, :utc_datetime_usec do
      public? true
    end

    attribute :order_end_time, :utc_datetime_usec do
      public? true
    end

    attribute :bet_amount_map, :map do
      public? true
    end

    timestamps do
      public? true
    end
  end
end
