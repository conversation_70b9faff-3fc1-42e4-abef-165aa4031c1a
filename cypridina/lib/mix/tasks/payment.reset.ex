defmodule Mix.Tasks.Payment.Reset do
  @moduledoc """
  重置支付系统配置数据

  使用方法:
    mix payment.reset                    # 重置所有支付配置
    mix payment.reset --keep-orders      # 保留订单记录
    mix payment.reset --dry-run          # 仅显示将要删除的数据，不执行
    mix payment.reset --backup           # 重置前创建备份
  """

  use Mix.Task
  require Logger

  alias Teen.PaymentSystem.{
    PaymentGateway,
    PaymentGatewayConfig,
    PaymentConfig,
    ExchangeConfig,
    WithdrawalConfig,
    PaymentOrder,
    PaymentCallback,
    RechargeRecord,
    WithdrawalRecord
  }

  @shortdoc "重置支付系统配置数据"

  def run(args) do
    Mix.Task.run("app.start")

    {opts, _args, _} = OptionParser.parse(args,
      switches: [
        keep_orders: :boolean,
        dry_run: :boolean,
        backup: :boolean,
        force: :boolean
      ],
      aliases: [
        k: :keep_orders,
        d: :dry_run,
        b: :backup,
        f: :force
      ]
    )

    unless opts[:force] do
      confirm_reset()
    end

    if opts[:backup] do
      create_backup()
    end

    if opts[:dry_run] do
      show_reset_plan(opts)
    else
      perform_reset(opts)
    end
  end

  defp confirm_reset do
    Mix.shell().info("⚠️  即将重置支付系统配置，这将删除以下数据：")
    Mix.shell().info("   • 支付网关配置")
    Mix.shell().info("   • 支付渠道配置")
    Mix.shell().info("   • 兑换配置")
    Mix.shell().info("   • 提现配置")
    Mix.shell().info("   • 支付记录（可选保留）")
    Mix.shell().info("")

    unless Mix.shell().yes?("确认继续？") do
      Mix.shell().info("操作已取消")
      System.halt(0)
    end
  end

  defp create_backup do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic)
    backup_dir = "priv/payment_backups"
    backup_file = "#{backup_dir}/payment_backup_#{timestamp}.sql"

    File.mkdir_p!(backup_dir)

    Mix.shell().info("🗄️  创建数据备份: #{backup_file}")

    backup_sql = """
    -- 支付系统数据备份 - #{DateTime.utc_now()}
    
    -- 支付网关备份
    COPY (SELECT * FROM payment_gateways) TO STDOUT WITH CSV HEADER;
    
    -- 支付网关配置备份
    COPY (SELECT * FROM payment_gateway_configs) TO STDOUT WITH CSV HEADER;
    
    -- 支付配置备份
    COPY (SELECT * FROM payment_configs) TO STDOUT WITH CSV HEADER;
    
    -- 兑换配置备份
    COPY (SELECT * FROM exchange_configs) TO STDOUT WITH CSV HEADER;
    
    -- 提现配置备份
    COPY (SELECT * FROM withdrawal_configs) TO STDOUT WITH CSV HEADER;
    """

    File.write!(backup_file, backup_sql)

    # 使用pg_dump创建真实备份
    System.cmd("pg_dump", [
      "--host", Application.get_env(:cypridina, Cypridina.Repo)[:hostname],
      "--port", to_string(Application.get_env(:cypridina, Cypridina.Repo)[:port]),
      "--username", Application.get_env(:cypridina, Cypridina.Repo)[:username],
      "--dbname", Application.get_env(:cypridina, Cypridina.Repo)[:database],
      "--table", "payment_gateways",
      "--table", "payment_gateway_configs", 
      "--table", "payment_configs",
      "--table", "exchange_configs",
      "--table", "withdrawal_configs",
      "--file", backup_file <> ".dump"
    ])

    Mix.shell().info("✅ 备份完成")
  end

  defp show_reset_plan(opts) do
    Mix.shell().info("🔍 重置计划预览：")
    Mix.shell().info("")

    # 统计当前数据
    stats = get_current_stats()

    Mix.shell().info("将要删除的数据:")
    Mix.shell().info("  • 支付网关: #{stats.gateways} 条记录")
    Mix.shell().info("  • 网关配置: #{stats.gateway_configs} 条记录")
    Mix.shell().info("  • 支付配置: #{stats.payment_configs} 条记录")
    Mix.shell().info("  • 兑换配置: #{stats.exchange_configs} 条记录")
    Mix.shell().info("  • 提现配置: #{stats.withdrawal_configs} 条记录")

    unless opts[:keep_orders] do
      Mix.shell().info("  • 支付订单: #{stats.payment_orders} 条记录")
      Mix.shell().info("  • 支付回调: #{stats.payment_callbacks} 条记录")
      Mix.shell().info("  • 充值记录: #{stats.recharge_records} 条记录")
      Mix.shell().info("  • 提现记录: #{stats.withdrawal_records} 条记录")
    else
      Mix.shell().info("  ✅ 保留支付订单和相关记录")
    end

    Mix.shell().info("")
    Mix.shell().info("📝 使用 'mix payment.reset --force' 执行重置")
  end

  defp perform_reset(opts) do
    Mix.shell().info("🔄 开始重置支付系统...")

    try do
      Cypridina.Repo.transaction(fn ->
        # 删除配置数据
        reset_configs()

        # 可选删除订单数据
        unless opts[:keep_orders] do
          reset_orders()
        end

        Mix.shell().info("✅ 数据重置完成")
      end)

      # 重新运行种子文件
      if Mix.shell().yes?("是否立即运行新的种子文件重新初始化？") do
        Mix.shell().info("🌱 运行支付系统种子文件...")
        Mix.Task.run("run", ["priv/repo/seeds/payment_system_seeds.exs"])
      end

    rescue
      error ->
        Mix.shell().error("❌ 重置失败: #{inspect(error)}")
        Mix.shell().info("请检查数据库连接和权限")
        System.halt(1)
    end
  end

  defp reset_configs do
    Mix.shell().info("🗑️  删除配置数据...")

    # 按依赖顺序删除
    delete_all_records(PaymentConfig, "支付配置")
    delete_all_records(ExchangeConfig, "兑换配置")
    delete_all_records(WithdrawalConfig, "提现配置")
    delete_all_records(PaymentGatewayConfig, "网关配置")
    delete_all_records(PaymentGateway, "支付网关")
  end

  defp reset_orders do
    Mix.shell().info("🗑️  删除订单数据...")

    delete_all_records(PaymentCallback, "支付回调")
    delete_all_records(RechargeRecord, "充值记录")
    delete_all_records(WithdrawalRecord, "提现记录")
    delete_all_records(PaymentOrder, "支付订单")
  end

  defp delete_all_records(module, name) do
    case apply(module, :read, []) do
      {:ok, records} ->
        count = length(records)
        if count > 0 do
          Enum.each(records, fn record ->
            apply(module, :destroy, [record])
          end)
          Mix.shell().info("  ✅ 删除#{name}: #{count} 条")
        else
          Mix.shell().info("  ➖ #{name}: 无数据")
        end
      {:error, reason} ->
        Mix.shell().error("  ❌ 删除#{name}失败: #{inspect(reason)}")
    end
  end

  defp get_current_stats do
    %{
      gateways: get_record_count(PaymentGateway),
      gateway_configs: get_record_count(PaymentGatewayConfig),
      payment_configs: get_record_count(PaymentConfig),
      exchange_configs: get_record_count(ExchangeConfig),
      withdrawal_configs: get_record_count(WithdrawalConfig),
      payment_orders: get_record_count(PaymentOrder),
      payment_callbacks: get_record_count(PaymentCallback),
      recharge_records: get_record_count(RechargeRecord),
      withdrawal_records: get_record_count(WithdrawalRecord)
    }
  end

  defp get_record_count(module) do
    case apply(module, :read, []) do
      {:ok, records} -> length(records)
      _ -> 0
    end
  end
end