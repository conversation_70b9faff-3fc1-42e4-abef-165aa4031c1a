defmodule Mix.Tasks.SetupTestDataManagement do
  @moduledoc """
  设置测试数据管理系统

  ## 使用方法

  ```bash
  mix setup_test_data_management
  ```

  此任务将：
  1. 检查环境配置
  2. 创建必要的目录
  3. 设置权限
  4. 运行基本验证
  """

  use Mix.Task

  @shortdoc "设置测试数据管理系统"

  def run(_args) do
    Mix.Task.run("app.start")
    
    IO.puts("""
    
    🚀 设置测试数据管理系统
    ===========================
    """)

    check_environment()
    create_directories()
    setup_permissions()
    verify_setup()
    show_usage_info()

    IO.puts("""
    
    ✅ 测试数据管理系统设置完成！
    """)
  end

  defp check_environment do
    IO.puts("1. 检查环境配置...")
    
    env = Application.get_env(:cypridina, :environment, :prod)
    
    case env do
      env when env in [:dev, :test] ->
        IO.puts("   ✅ 环境检查通过: #{env}")
      
      _ ->
        IO.puts("   ❌ 警告: 当前环境为 #{env}，测试数据管理功能将被禁用")
        IO.puts("   请在开发或测试环境中使用此功能")
    end
  end

  defp create_directories do
    IO.puts("2. 创建必要目录...")
    
    backup_dir = Application.get_env(:cypridina, :test_data_management, [])
                 |> Keyword.get(:backup_dir, "./tmp/test_data_backups")
    
    case File.mkdir_p(backup_dir) do
      :ok ->
        IO.puts("   ✅ 备份目录创建成功: #{backup_dir}")
      
      {:error, reason} ->
        IO.puts("   ❌ 备份目录创建失败: #{reason}")
    end

    # 创建日志目录
    log_dir = "./tmp/test_data_logs"
    case File.mkdir_p(log_dir) do
      :ok ->
        IO.puts("   ✅ 日志目录创建成功: #{log_dir}")
      
      {:error, reason} ->
        IO.puts("   ❌ 日志目录创建失败: #{reason}")
    end
  end

  defp setup_permissions do
    IO.puts("3. 检查权限设置...")
    
    # 这里可以检查数据库中是否存在必要的权限记录
    # 由于具体的权限系统实现可能不同，这里只做基本检查
    
    try do
      # 尝试加载权限相关的模块
      Teen.SystemSettings.Permission
      IO.puts("   ✅ 权限模块加载成功")
    rescue
      _ ->
        IO.puts("   ⚠️  权限模块可能未正确配置")
    end
  end

  defp verify_setup do
    IO.puts("4. 验证系统设置...")
    
    # 验证主要模块是否可用
    modules_to_check = [
      Teen.ActivitySystem.TestDataManager,
      Teen.ActivitySystem.TestDataAuth,
      Teen.ActivitySystem.TestDataBackup
    ]
    
    Enum.each(modules_to_check, fn module ->
      try do
        Code.ensure_loaded(module)
        IO.puts("   ✅ #{module} 模块加载成功")
      rescue
        e ->
          IO.puts("   ❌ #{module} 模块加载失败: #{inspect(e)}")
      end
    end)

    # 验证数据库连接
    try do
      Cypridina.Repo.__adapter__()
      IO.puts("   ✅ 数据库连接正常")
    rescue
      _ ->
        IO.puts("   ❌ 数据库连接失败，请检查配置")
    end
  end

  defp show_usage_info do
    IO.puts("""
    
    📖 使用说明
    ============
    
    1. 访问管理界面:
       http://localhost:4000/admin/test-data
    
    2. 编程接口:
       alias Teen.ActivitySystem.TestDataManager
       TestDataManager.clear_user_activity_data(user_id, [], operator)
    
    3. 备份管理:
       alias Teen.ActivitySystem.TestDataBackup
       TestDataBackup.list_backups()
    
    4. 权限要求:
       - 管理员账户登录
       - test_data_management 权限
       - 开发或测试环境
    
    5. 安全提示:
       - 所有操作都会记录日志
       - 危险操作需要二次确认
       - 自动创建数据备份
    
    详细文档: docs/test_data_management.md
    """)
  end
end