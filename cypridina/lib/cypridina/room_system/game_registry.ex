defmodule Cypridina.RoomSystem.GameRegistry do
  @moduledoc """
  游戏注册表 - 提供游戏的注册和配置管理功能

  功能：
  - 运行时注册新游戏
  - 游戏热更新
  - 游戏状态监控
  - 游戏配置管理
  """

  alias Cypridina.RoomSystem.GameFactory

  @doc """
  注册新游戏类型

  ## 示例

      # 注册百家乐游戏
      GameRegistry.register_game(Cypridina.RoomSystem.Games.BaccaratGame)

      # 注册自定义游戏
      GameRegistry.register_game(MyApp.Games.CustomGame)
  """
  def register_game(game_module) do
    GameFactory.register_game(game_module)
  end

  @doc """
  获取所有已注册的游戏信息
  """
  def list_games do
    GameFactory.list_games()
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    games = list_games()

    %{
      total_games: length(games),
      lobby_games: Enum.count(games, & &1.is_lobby_game),
      table_games: Enum.count(games, &(not &1.is_lobby_game)),
      supported_game_ids: games |> Enum.flat_map(& &1.supported_game_ids) |> Enum.sort()
    }
  end

  @doc """
  检查游戏ID是否被支持
  """
  def game_supported?(game_id) do
    GameFactory.get_game_type(game_id) != :unknown
  end

  @doc """
  获取游戏详细信息
  """
  def get_game_info(game_type) do
    with config when not is_nil(config) <- GameFactory.get_game_config(game_type),
         room_module when not is_nil(room_module) <- GameFactory.get_room_module(game_type) do
      games = list_games()
      game_info = Enum.find(games, &(&1.game_type == game_type))

      if game_info do
        %{
          game_type: game_type,
          game_name: game_info.game_name,
          room_module: room_module,
          config: config,
          is_lobby_game: game_info.is_lobby_game,
          supported_game_ids: game_info.supported_game_ids
        }
      else
        {:error, :game_not_found}
      end
    else
      _ -> {:error, :game_not_found}
    end
  end

  @doc """
  热注册游戏 - 在运行时添加新游戏

  这个功能允许在不重启应用的情况下添加新游戏类型
  """
  def hot_register_game(game_module) do
    case register_game(game_module) do
      :ok ->
        # 通知房间管理器刷新百人场房间（如果是百人场游戏）
        if game_module.is_lobby_game?() do
          # 这里可以添加逻辑来创建新的百人场房间
          :ok
        else
          :ok
        end

      error ->
        error
    end
  end

  @doc """
  验证游戏模块是否符合规范
  """
  def validate_game_module(game_module) do
    required_callbacks = [
      :game_type,
      :game_name,
      :room_module,
      :default_config,
      :is_lobby_game?,
      :supported_game_ids
    ]

    try do
      # 检查是否实现了所有必需的回调
      Enum.each(required_callbacks, fn callback ->
        if not function_exported?(game_module, callback, 0) do
          throw({:missing_callback, callback})
        end
      end)

      # 验证返回值类型
      game_type = game_module.game_type()
      game_name = game_module.game_name()
      room_module = game_module.room_module()
      default_config = game_module.default_config()
      is_lobby = game_module.is_lobby_game?()
      supported_ids = game_module.supported_game_ids()

      cond do
        not is_atom(game_type) ->
          {:error, :invalid_game_type}

        not is_binary(game_name) ->
          {:error, :invalid_game_name}

        not is_atom(room_module) ->
          {:error, :invalid_room_module}

        not is_map(default_config) ->
          {:error, :invalid_default_config}

        not is_boolean(is_lobby) ->
          {:error, :invalid_is_lobby_game}

        not is_list(supported_ids) or not Enum.all?(supported_ids, &is_integer/1) ->
          {:error, :invalid_supported_game_ids}

        true ->
          :ok
      end
    catch
      {:missing_callback, callback} ->
        {:error, {:missing_callback, callback}}
    rescue
      error ->
        {:error, {:validation_error, error}}
    end
  end
end
