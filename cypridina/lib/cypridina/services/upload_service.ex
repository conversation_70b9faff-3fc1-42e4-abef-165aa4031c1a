defmodule Cypridina.Services.UploadService do
  @moduledoc """
  基于Waffle的文件上传服务
  提供头像上传、文件上传等功能
  """

  require Logger
  alias <PERSON>pridina.Uploaders.Avatar
  alias Cypridina.Uploaders.File
  alias Cypridina.Accounts.UserProfile

  @doc """
  上传用户头像

  ## 参数
    - user_id: 用户ID
    - file_path: 本地文件路径或Plug.Upload结构
    - scope: 上传作用域（通常是用户信息）

  ## 返回
    - {:ok, urls} 成功时返回包含原图和缩略图URL的map
    - {:error, reason} 失败时返回错误原因
  """
  def upload_avatar(user_id, file_path, scope \\ nil) do
    scope = scope || %{user_id: user_id}

    try do
      case Avatar.store({file_path, scope}) do
        {:ok, filename} ->
          urls = %{
            original: Avatar.url({filename, scope}, :original),
            thumb: Avatar.url({filename, scope}, :thumb)
          }

          Logger.info("头像上传成功 - 用户ID: #{user_id}, 文件名: #{filename}")
          {:ok, urls}

        {:error, reason} ->
          Logger.error("头像上传失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("头像上传异常 - 用户ID: #{user_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  上传通用文件

  ## 参数
    - user_id: 用户ID
    - file_path: 本地文件路径或Plug.Upload结构
    - scope: 上传作用域（通常是用户信息）

  ## 返回
    - {:ok, url} 成功时返回文件URL
    - {:error, reason} 失败时返回错误原因
  """
  def upload_file(user_id, file_path, scope \\ nil) do
    scope = scope || %{user_id: user_id}

    try do
      case File.store({file_path, scope}) do
        {:ok, filename} ->
          url = File.url({filename, scope}, :original)

          Logger.info("文件上传成功 - 用户ID: #{user_id}, 文件名: #{filename}")
          {:ok, url}

        {:error, reason} ->
          Logger.error("文件上传失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("文件上传异常 - 用户ID: #{user_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  从base64数据上传头像

  ## 参数
    - user_id: 用户ID
    - base64_data: base64编码的图片数据
    - file_extension: 文件扩展名（如 "jpg", "png"）

  ## 返回
    - {:ok, urls} 成功时返回包含原图和缩略图URL的map
    - {:error, reason} 失败时返回错误原因
  """
  def upload_avatar_from_base64(user_id, base64_data, file_extension \\ "png") do
    try do
      # 处理data URL格式，提取纯base64数据
      clean_base64_data =
        case String.contains?(base64_data, ",") do
          true ->
            # 如果包含逗号，说明是data URL格式，提取逗号后的部分
            base64_data |> String.split(",", parts: 2) |> List.last()

          false ->
            # 否则认为是纯base64数据
            base64_data
        end

      # 解码base64数据
      binary_data = Base.decode64!(clean_base64_data)

      # 创建临时文件
      temp_file = create_temp_file(binary_data, file_extension)

      # 上传头像
      result = upload_avatar(user_id, temp_file)

      # 清理临时文件
      File.rm!(temp_file.path)

      result
    rescue
      error ->
        Logger.error("Base64头像上传异常 - 用户ID: #{user_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  删除用户头像

  ## 参数
    - user_id: 用户ID
    - filename: 文件名

  ## 返回
    - :ok 成功
    - {:error, reason} 失败时返回错误原因
  """
  def delete_avatar(user_id, filename) do
    scope = %{user_id: user_id}

    try do
      # 删除原图和缩略图
      Avatar.delete({filename, scope})
      Logger.info("头像删除成功 - 用户ID: #{user_id}, 文件名: #{filename}")
      :ok
    rescue
      error ->
        Logger.error("头像删除异常 - 用户ID: #{user_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  生成预签名上传URL（用于前端直接上传）

  ## 参数
    - user_id: 用户ID
    - file_extension: 文件扩展名
    - content_type: 文件MIME类型

  ## 返回
    - {:ok, upload_url, file_path} 成功时返回上传URL和文件路径
    - {:error, reason} 失败时返回错误原因
  """
  def generate_presigned_upload_url(user_id, file_extension, content_type) do
    try do
      # 生成文件路径
      timestamp = System.system_time(:millisecond)
      file_path = "avatars/#{user_id}/#{timestamp}.#{file_extension}"

      # 生成预签名URL
      config = ExAws.Config.new(:s3)
      bucket = get_bucket()

      case ExAws.S3.presigned_url(config, :put, bucket, file_path,
             expires_in: 3600,
             query_params: [{"Content-Type", content_type}]
           ) do
        {:ok, upload_url} ->
          Logger.info("预签名URL生成成功 - 用户ID: #{user_id}, 路径: #{file_path}")
          {:ok, upload_url, file_path}

        {:error, reason} ->
          Logger.error("预签名URL生成失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("预签名URL生成异常 - 用户ID: #{user_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  # 私有函数：创建临时文件
  defp create_temp_file(binary_data, file_extension) do
    temp_path = System.tmp_dir!()
    filename = "upload_#{System.unique_integer([:positive])}.#{file_extension}"
    file_path = Path.join(temp_path, filename)

    File.write!(file_path, binary_data)

    %Plug.Upload{
      path: file_path,
      filename: filename,
      content_type: MIME.from_path(filename)
    }
  end

  # 私有函数：获取bucket名称
  defp get_bucket do
    Application.get_env(:waffle, :bucket) ||
      Application.get_env(:ex_aws, :s3)[:bucket] ||
      "cypridina"
  end
end
