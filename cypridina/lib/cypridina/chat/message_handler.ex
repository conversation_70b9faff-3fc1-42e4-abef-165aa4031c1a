defmodule Cypridina.Chat.MessageHandler do
  @moduledoc """
  优化的聊天消息处理模块

  提供高性能的消息处理功能，包括：
  - 批量消息处理
  - 缓存优化
  - 减少数据库查询
  - 异步处理
  """

  require Logger
  require Ash.Query
  import Ash.Expr

  alias Cypridina.Chat.{ChatSession, ChatMessage, ChatParticipant, MessageReadReceipt}
  alias Phoenix.PubSub

  @pubsub Cypridina.PubSub

  @doc """
  批量发送消息
  """
  def send_messages_batch(messages_data) do
    with {:ok, validated_messages} <- validate_messages_batch(messages_data),
         {:ok, created_messages} <- create_messages_batch(validated_messages),
         :ok <- broadcast_messages_batch(created_messages) do
      {:ok, created_messages}
    else
      {:error, reason} ->
        Logger.error("💬 [MESSAGE_HANDLER] 批量发送消息失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取会话消息（带分页和缓存）
  """
  def get_session_messages(session_id, user_id, opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)
    offset = Keyword.get(opts, :offset, 0)
    before_message_id = Keyword.get(opts, :before_message_id)

    with {:ok, _participant} <- verify_user_in_session(session_id, user_id),
         {:ok, messages} <-
           fetch_messages_with_cache(session_id, limit, offset, before_message_id),
         {:ok, enriched_messages} <- enrich_messages_batch(messages) do
      {:ok, enriched_messages}
    else
      {:error, reason} ->
        Logger.error("💬 [MESSAGE_HANDLER] 获取会话消息失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量标记消息为已读
  """
  def mark_messages_read_batch(session_id, user_id, message_ids) do
    with {:ok, _participant} <- verify_user_in_session(session_id, user_id),
         {:ok, read_receipts} <- create_read_receipts_batch(message_ids, user_id),
         :ok <- broadcast_read_status_updates(session_id, user_id, message_ids) do
      {:ok, read_receipts}
    else
      {:error, reason} ->
        Logger.error("💬 [MESSAGE_HANDLER] 批量标记已读失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取未读消息统计
  """
  def get_unread_count(session_id, user_id) do
    with {:ok, _participant} <- verify_user_in_session(session_id, user_id),
         {:ok, count} <- calculate_unread_count(session_id, user_id) do
      {:ok, count}
    else
      {:error, reason} ->
        Logger.error("💬 [MESSAGE_HANDLER] 获取未读数量失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 私有函数

  defp validate_messages_batch(messages_data) do
    validated = Enum.map(messages_data, &validate_single_message/1)

    case Enum.find(validated, &match?({:error, _}, &1)) do
      nil -> {:ok, Enum.map(validated, fn {:ok, msg} -> msg end)}
      {:error, reason} -> {:error, reason}
    end
  end

  defp validate_single_message(message_data) do
    required_fields = [:session_id, :sender_id, :content, :message_type]

    case Enum.all?(required_fields, &Map.has_key?(message_data, &1)) do
      true -> {:ok, message_data}
      false -> {:error, :missing_required_fields}
    end
  end

  defp create_messages_batch(messages_data) do
    changesets =
      Enum.map(messages_data, fn message_data ->
        ChatMessage
        |> Ash.Changeset.for_create(:send_message, message_data)
      end)

    case Ash.bulk_create(changesets, ChatMessage, :send_message) do
      %{records: messages} when length(messages) > 0 ->
        {:ok, messages}

      %{errors: errors} ->
        {:error, errors}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp broadcast_messages_batch(messages) do
    Enum.each(messages, fn message ->
      session_topic = "chat_session:#{message.session_id}"

      PubSub.broadcast(@pubsub, session_topic, {:new_message, message})

      # 广播到全局聊天更新
      PubSub.broadcast(@pubsub, "chat_updates", {:new_message, message})
    end)

    :ok
  end

  defp verify_user_in_session(session_id, user_id) do
    ChatParticipant
    |> Ash.Query.filter(
      expr(session_id == ^session_id and user_id == ^user_id and status == :active)
    )
    |> Ash.read_one()
    |> case do
      {:ok, participant} when not is_nil(participant) -> {:ok, participant}
      {:ok, nil} -> {:error, :not_participant}
      {:error, reason} -> {:error, reason}
    end
  end

  defp fetch_messages_with_cache(session_id, limit, offset, before_message_id) do
    query =
      ChatMessage
      |> Ash.Query.filter(expr(session_id == ^session_id and status != :deleted))
      |> Ash.Query.sort(inserted_at: :desc)
      |> Ash.Query.limit(limit)
      |> Ash.Query.offset(offset)

    query =
      if before_message_id do
        # 获取指定消息之前的消息
        Ash.Query.filter(
          query,
          expr(
            inserted_at <
              subquery(
                from m in ChatMessage,
                  where: m.id == ^before_message_id,
                  select: m.inserted_at
              )
          )
        )
      else
        query
      end

    case Ash.read(query) do
      {:ok, messages} -> {:ok, messages}
      {:error, reason} -> {:error, reason}
    end
  end

  defp enrich_messages_batch(messages) do
    # 批量加载关联数据以减少数据库查询
    case Ash.load(messages, [:sender, :reply_to, :read_receipts]) do
      {:ok, enriched_messages} -> {:ok, enriched_messages}
      {:error, reason} -> {:error, reason}
    end
  end

  defp create_read_receipts_batch(message_ids, user_id) do
    read_receipts_data =
      Enum.map(message_ids, fn message_id ->
        %{message_id: message_id, user_id: user_id}
      end)

    changesets =
      Enum.map(read_receipts_data, fn data ->
        MessageReadReceipt
        |> Ash.Changeset.for_create(:mark_message_read, data)
      end)

    case Ash.bulk_create(changesets, MessageReadReceipt, :mark_message_read) do
      %{records: receipts} -> {:ok, receipts}
      %{errors: errors} -> {:error, errors}
      {:error, reason} -> {:error, reason}
    end
  end

  defp broadcast_read_status_updates(session_id, user_id, message_ids) do
    session_topic = "chat_session:#{session_id}"

    PubSub.broadcast(@pubsub, session_topic, {
      :messages_read,
      %{user_id: user_id, message_ids: message_ids}
    })

    :ok
  end

  defp calculate_unread_count(session_id, user_id) do
    # 使用子查询优化未读消息统计
    query =
      ChatMessage
      |> Ash.Query.filter(
        expr(
          session_id == ^session_id and
            sender_id != ^user_id and
            status != :deleted and
            not exists(
              from r in MessageReadReceipt,
                where: r.message_id == parent_as(:message).id and r.user_id == ^user_id
            )
        )
      )
      |> Ash.Query.select([:id])

    case Ash.count(query) do
      {:ok, count} -> {:ok, count}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  异步处理消息发送后的任务
  """
  def handle_message_sent_async(message) do
    Task.start(fn ->
      # 更新会话的最后消息时间
      update_session_last_message(message.session_id, message)

      # 发送推送通知给离线用户
      send_push_notifications_to_offline_users(message)

      # 更新参与者的未读计数缓存
      update_unread_count_cache(message)
    end)
  end

  defp update_session_last_message(session_id, message) do
    ChatSession
    |> Ash.Changeset.for_update(:update, %{
      last_message_at: message.inserted_at,
      last_message_content: truncate_content(message.content)
    })
    |> Ash.update()
  end

  defp truncate_content(content) when is_binary(content) do
    if String.length(content) > 100 do
      String.slice(content, 0, 100) <> "..."
    else
      content
    end
  end

  defp truncate_content(_), do: ""

  defp send_push_notifications_to_offline_users(message) do
    # 获取会话中的离线用户并发送推送通知
    # 这里可以集成推送通知服务
    Logger.info("💬 [MESSAGE_HANDLER] 发送推送通知 - 消息ID: #{message.id}")
  end

  defp update_unread_count_cache(message) do
    # 更新未读消息计数缓存
    # 这里可以使用Redis或ETS缓存
    Logger.debug("💬 [MESSAGE_HANDLER] 更新未读计数缓存 - 会话: #{message.session_id}")
  end
end
