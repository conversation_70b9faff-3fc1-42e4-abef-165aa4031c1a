defmodule Cypridina.Chat.ChatLogger do
  @moduledoc """
  聊天系统专用日志模块

  按照系统日志和游戏类型分类记录聊天相关事件，
  支持按日期排序和结构化日志记录。
  """

  require Logger

  @log_categories %{
    system: "system_chat",
    teen_patti: "teen_patti_chat",
    crash: "crash_chat",
    slotniu: "slotniu_chat",
    racing: "racing_chat"
  }

  @doc """
  记录聊天会话事件
  """
  def log_session_event(event_type, session_data, opts \\ []) do
    category = get_log_category(opts)
    level = Keyword.get(opts, :level, :info)

    metadata = build_session_metadata(session_data, opts)

    log_message = format_session_message(event_type, session_data)

    log_with_category(level, log_message, metadata, category)
  end

  @doc """
  记录消息事件
  """
  def log_message_event(event_type, message_data, opts \\ []) do
    category = get_log_category(opts)
    level = Keyword.get(opts, :level, :info)

    metadata = build_message_metadata(message_data, opts)

    log_message = format_message_event(event_type, message_data)

    log_with_category(level, log_message, metadata, category)
  end

  @doc """
  记录用户事件
  """
  def log_user_event(event_type, user_data, opts \\ []) do
    category = get_log_category(opts)
    level = Keyword.get(opts, :level, :info)

    metadata = build_user_metadata(user_data, opts)

    log_message = format_user_event(event_type, user_data)

    log_with_category(level, log_message, metadata, category)
  end

  @doc """
  记录文件上传事件
  """
  def log_file_event(event_type, file_data, opts \\ []) do
    category = get_log_category(opts)
    level = Keyword.get(opts, :level, :info)

    metadata = build_file_metadata(file_data, opts)

    log_message = format_file_event(event_type, file_data)

    log_with_category(level, log_message, metadata, category)
  end

  @doc """
  记录错误事件
  """
  def log_error(error_type, error_data, opts \\ []) do
    category = get_log_category(opts)

    metadata = build_error_metadata(error_data, opts)

    log_message = format_error_message(error_type, error_data)

    log_with_category(:error, log_message, metadata, category)
  end

  @doc """
  记录性能指标
  """
  def log_performance(metric_name, value, opts \\ []) do
    category = get_log_category(opts)

    metadata = build_performance_metadata(metric_name, value, opts)

    log_message = format_performance_message(metric_name, value)

    log_with_category(:info, log_message, metadata, category)
  end

  # 私有函数

  defp get_log_category(opts) do
    game_type = Keyword.get(opts, :game_type, :system)
    Map.get(@log_categories, game_type, @log_categories.system)
  end

  defp log_with_category(level, message, metadata, category) do
    enhanced_metadata =
      Map.merge(metadata, %{
        category: category,
        timestamp: DateTime.utc_now(),
        component: "chat_system"
      })

    Logger.log(level, message, enhanced_metadata)
  end

  defp build_session_metadata(session_data, opts) do
    base_metadata = %{
      session_id: Map.get(session_data, :id),
      session_type: Map.get(session_data, :session_type),
      participant_count: Map.get(session_data, :participant_count, 0)
    }

    add_optional_metadata(base_metadata, opts)
  end

  defp build_message_metadata(message_data, opts) do
    base_metadata = %{
      message_id: Map.get(message_data, :id),
      session_id: Map.get(message_data, :session_id),
      sender_id: Map.get(message_data, :sender_id),
      message_type: Map.get(message_data, :message_type)
    }

    add_optional_metadata(base_metadata, opts)
  end

  defp build_user_metadata(user_data, opts) do
    base_metadata = %{
      user_id: Map.get(user_data, :id),
      user_type: Map.get(user_data, :user_type, :regular)
    }

    add_optional_metadata(base_metadata, opts)
  end

  defp build_file_metadata(file_data, opts) do
    base_metadata = %{
      file_name: Map.get(file_data, :original_name),
      file_size: Map.get(file_data, :file_size),
      file_type: Map.get(file_data, :file_type),
      session_id: Map.get(file_data, :session_id),
      sender_id: Map.get(file_data, :sender_id)
    }

    add_optional_metadata(base_metadata, opts)
  end

  defp build_error_metadata(error_data, opts) do
    base_metadata = %{
      error_type: Map.get(error_data, :type),
      error_reason: Map.get(error_data, :reason),
      context: Map.get(error_data, :context, %{})
    }

    add_optional_metadata(base_metadata, opts)
  end

  defp build_performance_metadata(metric_name, value, opts) do
    base_metadata = %{
      metric: metric_name,
      value: value,
      unit: Keyword.get(opts, :unit, "ms")
    }

    add_optional_metadata(base_metadata, opts)
  end

  defp add_optional_metadata(base_metadata, opts) do
    optional_fields = [:request_id, :user_agent, :ip_address, :game_room_id]

    Enum.reduce(optional_fields, base_metadata, fn field, acc ->
      case Keyword.get(opts, field) do
        nil -> acc
        value -> Map.put(acc, field, value)
      end
    end)
  end

  defp format_session_message(:created, session_data) do
    "💬 [CHAT_SESSION] 会话创建 - ID: #{session_data.id}, 类型: #{session_data.session_type}"
  end

  defp format_session_message(:joined, session_data) do
    "💬 [CHAT_SESSION] 用户加入会话 - 会话: #{session_data.session_id}, 用户: #{session_data.user_id}"
  end

  defp format_session_message(:left, session_data) do
    "💬 [CHAT_SESSION] 用户离开会话 - 会话: #{session_data.session_id}, 用户: #{session_data.user_id}"
  end

  defp format_session_message(:closed, session_data) do
    "💬 [CHAT_SESSION] 会话关闭 - ID: #{session_data.id}"
  end

  defp format_message_event(:sent, message_data) do
    "💬 [CHAT_MESSAGE] 消息发送 - ID: #{message_data.id}, 会话: #{message_data.session_id}, 类型: #{message_data.message_type}"
  end

  defp format_message_event(:received, message_data) do
    "💬 [CHAT_MESSAGE] 消息接收 - ID: #{message_data.id}, 接收者: #{message_data.receiver_id}"
  end

  defp format_message_event(:read, message_data) do
    "💬 [CHAT_MESSAGE] 消息已读 - ID: #{message_data.id}, 读者: #{message_data.reader_id}"
  end

  defp format_message_event(:deleted, message_data) do
    "💬 [CHAT_MESSAGE] 消息删除 - ID: #{message_data.id}"
  end

  defp format_user_event(:online, user_data) do
    "💬 [CHAT_USER] 用户上线 - ID: #{user_data.id}"
  end

  defp format_user_event(:offline, user_data) do
    "💬 [CHAT_USER] 用户下线 - ID: #{user_data.id}"
  end

  defp format_user_event(:typing, user_data) do
    "💬 [CHAT_USER] 用户输入中 - ID: #{user_data.id}, 会话: #{user_data.session_id}"
  end

  defp format_file_event(:uploaded, file_data) do
    "💬 [CHAT_FILE] 文件上传 - 文件: #{file_data.original_name}, 大小: #{file_data.file_size}字节"
  end

  defp format_file_event(:download, file_data) do
    "💬 [CHAT_FILE] 文件下载 - 文件: #{file_data.original_name}, 用户: #{file_data.user_id}"
  end

  defp format_error_message(:validation_error, error_data) do
    "💬 [CHAT_ERROR] 验证错误 - #{error_data.reason}"
  end

  defp format_error_message(:permission_denied, error_data) do
    "💬 [CHAT_ERROR] 权限拒绝 - #{error_data.reason}"
  end

  defp format_error_message(:system_error, error_data) do
    "💬 [CHAT_ERROR] 系统错误 - #{error_data.reason}"
  end

  defp format_performance_message(metric_name, value) do
    "💬 [CHAT_PERF] 性能指标 - #{metric_name}: #{value}"
  end

  @doc """
  获取聊天日志统计信息
  """
  def get_log_stats(category, date_range \\ nil) do
    # 这里可以实现日志统计功能
    # 例如统计不同类型事件的数量、错误率等
    %{
      category: category,
      date_range: date_range,
      total_events: 0,
      error_count: 0,
      warning_count: 0
    }
  end

  @doc """
  清理过期日志
  """
  def cleanup_old_logs(days_to_keep \\ 30) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-days_to_keep, :day)

    Logger.info("💬 [CHAT_LOGGER] 开始清理 #{days_to_keep} 天前的聊天日志")

    # 这里可以实现日志清理逻辑
    # 例如删除过期的日志文件或数据库记录

    Logger.info("💬 [CHAT_LOGGER] 日志清理完成")
  end
end
