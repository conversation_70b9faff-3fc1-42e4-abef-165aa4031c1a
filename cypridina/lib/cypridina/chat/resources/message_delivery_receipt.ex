defmodule Cypridina.Chat.MessageDeliveryReceipt do
  @moduledoc """
  消息送达回执资源
  
  记录消息的送达状态，用于实现消息的双勾状态（已送达）。
  与已读回执（MessageReadReceipt）配合，实现完整的消息状态追踪。
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.Chat,
    extensions: [AshAdmin.Resource]

  require Ash.Query
  import Ash.Expr

  admin do
    table_columns [:id, :message_id, :user_id, :delivered_at]
  end

  postgres do
    table "message_delivery_receipts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :mark_message_delivered
    define :mark_session_messages_delivered
    define :get_delivery_status
  end

  actions do
    defaults [:create, :read]

    # 标记单条消息为已送达
    create :mark_message_delivered do
      argument :message_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false

      change set_attribute(:message_id, arg(:message_id))
      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:delivered_at, &DateTime.utc_now/0)

      # 避免重复标记
      upsert? true
      upsert_identity :unique_message_user_delivery
    end

    # 批量标记会话中的消息为已送达
    create :mark_session_messages_delivered do
      argument :session_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false
      argument :up_to_message_id, :uuid, allow_nil?: true

      change fn changeset, _context ->
        session_id = Ash.Changeset.get_argument(changeset, :session_id)
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        up_to_message_id = Ash.Changeset.get_argument(changeset, :up_to_message_id)

        # 获取需要标记为已送达的消息
        query =
          Cypridina.Chat.ChatMessage
          |> Ash.Query.filter(
            expr(
              session_id == ^session_id and
                sender_id != ^user_id and
                not exists(delivery_receipts, user_id == ^user_id)
            )
          )

        query =
          if up_to_message_id do
            case Cypridina.Chat.ChatMessage |> Ash.get(up_to_message_id) do
              {:ok, message} ->
                Ash.Query.filter(query, expr(inserted_at <= ^message.inserted_at))

              _ ->
                query
            end
          else
            query
          end

        case Ash.read(query) do
          {:ok, messages} ->
            # 批量创建送达回执
            Enum.each(messages, fn message ->
              __MODULE__
              |> Ash.Changeset.for_create(:mark_message_delivered, %{
                message_id: message.id,
                user_id: user_id
              })
              |> Ash.create!()
            end)

            changeset

          _ ->
            changeset
        end
      end
    end

    # 获取消息的送达状态
    read :get_delivery_status do
      argument :message_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: true

      prepare fn query, _context ->
        message_id = Ash.Query.get_argument(query, :message_id)
        user_id = Ash.Query.get_argument(query, :user_id)

        query = Ash.Query.filter(query, expr(message_id == ^message_id))

        if user_id do
          Ash.Query.filter(query, expr(user_id == ^user_id))
        else
          query
        end
      end

      prepare build(load: [:user])
      prepare build(sort: [delivered_at: :asc])
    end
  end

  validations do
    validate present([:message_id, :user_id, :delivered_at])

    # 验证用户不能标记自己发送的消息为已送达
    validate fn changeset, _context ->
      message_id = Ash.Changeset.get_attribute(changeset, :message_id)
      user_id = Ash.Changeset.get_attribute(changeset, :user_id)

      if message_id and user_id do
        case Cypridina.Chat.ChatMessage |> Ash.get(message_id) do
          {:ok, message} ->
            if message.sender_id == user_id do
              {:error, field: :user_id, message: "不能标记自己发送的消息为已送达"}
            else
              :ok
            end

          _ ->
            {:error, field: :message_id, message: "消息不存在"}
        end
      else
        :ok
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :message_id, :uuid do
      allow_nil? false
      public? true
      description "消息ID"
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "接收用户ID"
    end

    attribute :delivered_at, :utc_datetime do
      allow_nil? false
      public? true
      description "送达时间"
      default &DateTime.utc_now/0
    end

    timestamps()
  end

  relationships do
    belongs_to :message, Cypridina.Chat.ChatMessage do
      public? true
      source_attribute :message_id
      destination_attribute :id
    end

    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    # 确保用户对同一消息只能有一条送达记录
    identity :unique_message_user_delivery, [:message_id, :user_id]
  end
end