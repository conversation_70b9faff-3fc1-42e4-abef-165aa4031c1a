defmodule Cypridina.Chat.MessageReadReceipt do
  @moduledoc """
  消息已读回执资源

  管理消息的已读状态，记录用户对消息的阅读时间。
  用于实现消息已读功能和未读消息统计。
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.Chat,
    extensions: [AshAdmin.Resource]

  require Ash.Query
  import Ash.Expr

  admin do
    table_columns [:id, :message_id, :user_id, :read_at]
  end

  postgres do
    table "message_read_receipts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :mark_message_read
    define :mark_session_messages_read
    define :get_read_status
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    # 标记单条消息为已读
    create :mark_message_read do
      argument :message_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false

      change set_attribute(:message_id, arg(:message_id))
      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:read_at, &DateTime.utc_now/0)

      # 避免重复标记
      upsert? true
      upsert_identity :unique_message_user_read
    end

    # 批量标记会话中的消息为已读
    create :mark_session_messages_read do
      argument :session_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false
      argument :up_to_message_id, :uuid, allow_nil?: true

      change fn changeset, _context ->
        session_id = Ash.Changeset.get_argument(changeset, :session_id)
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        up_to_message_id = Ash.Changeset.get_argument(changeset, :up_to_message_id)

        # 获取需要标记为已读的消息
        query =
          Cypridina.Chat.ChatMessage
          |> Ash.Query.filter(
            expr(
              session_id == ^session_id and
                sender_id != ^user_id
            )
          )

        query =
          if up_to_message_id do
            # 获取指定消息的时间戳
            case Cypridina.Chat.ChatMessage
                 |> Ash.get(up_to_message_id) do
              {:ok, message} ->
                Ash.Query.filter(query, expr(inserted_at <= ^message.inserted_at))

              _ ->
                query
            end
          else
            query
          end

        case Ash.read(query) do
          {:ok, messages} ->
            # 批量创建已读回执
            read_receipts =
              Enum.map(messages, fn message ->
                %{
                  message_id: message.id,
                  user_id: user_id,
                  read_at: DateTime.utc_now()
                }
              end)

            # 这里需要批量插入，实际实现中可能需要使用 Repo.insert_all
            changeset

          _ ->
            changeset
        end
      end
    end

    # 获取消息的已读状态
    read :get_read_status do
      argument :message_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: true

      prepare fn query, _context ->
        message_id = Ash.Query.get_argument(query, :message_id)
        user_id = Ash.Query.get_argument(query, :user_id)

        query = Ash.Query.filter(query, expr(message_id == ^message_id))

        if user_id do
          Ash.Query.filter(query, expr(user_id == ^user_id))
        else
          query
        end
      end

      prepare build(load: [:user])
      prepare build(sort: [read_at: :asc])
    end

    # 获取用户在会话中的已读状态
    read :get_user_read_status_in_session do
      argument :session_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false

      prepare fn query, _context ->
        require Ash.Query
        import Ash.Expr

        session_id = Ash.Query.get_argument(query, :session_id)
        user_id = Ash.Query.get_argument(query, :user_id)

        Ash.Query.filter(
          query,
          expr(
            user_id == ^user_id and
              exists(message, session_id == ^session_id)
          )
        )
      end

      prepare build(load: [:message])
      prepare build(sort: [read_at: :desc])
    end
  end

  validations do
    validate present([:message_id, :user_id, :read_at])

    # 验证用户不能标记自己发送的消息为已读
    validate fn changeset, _context ->
      message_id = Ash.Changeset.get_attribute(changeset, :message_id)
      user_id = Ash.Changeset.get_attribute(changeset, :user_id)

      if message_id and user_id do
        case Cypridina.Chat.ChatMessage
             |> Ash.get(message_id) do
          {:ok, message} ->
            if message.sender_id == user_id do
              {:error, field: :user_id, message: "不能标记自己发送的消息为已读"}
            else
              :ok
            end

          _ ->
            {:error, field: :message_id, message: "消息不存在"}
        end
      else
        :ok
      end
    end

    # 验证用户是否在会话中
    validate fn changeset, _context ->
      message_id = Ash.Changeset.get_attribute(changeset, :message_id)
      user_id = Ash.Changeset.get_attribute(changeset, :user_id)

      if message_id and user_id do
        with {:ok, message} <- Cypridina.Chat.ChatMessage |> Ash.get(message_id),
             {:ok, participant} <-
               Cypridina.Chat.ChatParticipant
               |> Ash.Query.filter(
                 expr(
                   session_id == ^message.session_id and
                     user_id == ^user_id and
                     status == :active
                 )
               )
               |> Ash.read_one() do
          if participant do
            :ok
          else
            {:error, field: :user_id, message: "用户不在该会话中"}
          end
        else
          _ -> {:error, "验证用户会话权限时出错"}
        end
      else
        :ok
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :message_id, :uuid do
      allow_nil? false
      public? true
      description "消息ID"
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "阅读用户ID"
    end

    attribute :read_at, :utc_datetime do
      allow_nil? false
      public? true
      description "阅读时间"
      default &DateTime.utc_now/0
    end

    timestamps()
  end

  relationships do
    belongs_to :message, Cypridina.Chat.ChatMessage do
      public? true
      source_attribute :message_id
      destination_attribute :id
    end

    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    # 确保用户对同一消息只能有一条已读记录
    identity :unique_message_user_read, [:message_id, :user_id]
  end
end
