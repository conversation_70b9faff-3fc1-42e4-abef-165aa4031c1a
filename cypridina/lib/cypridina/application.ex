defmodule Cyprid<PERSON>.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    # :ok = Oban.Telemetry.attach_default_logger()

    # 基础服务 - 所有模式都需要
    base_children = [
      # 速率限制器 - 必须在使用速率限制的服务之前启动
      {Cypridina.RateLimiter, []},
      CypridinaWeb.Telemetry,
      Cypridina.Repo,
      {DNSCluster, query: Application.get_env(:cypridina, :dns_cluster_query) || :ignore},
      {Oban, Application.fetch_env!(:cypridina, Oban)},
      {Phoenix.PubSub, name: Cypridina.PubSub},
      {AshAuthentication.Supervisor, [otp_app: :cypridina]},
      # 自定义货币服务 - 必须在使用Money的服务之前启动
      Cldr.Currency,
      # Start to serve requests, typically the last entry
      CypridinaWeb.Endpoint,
      Cypridina.UserCache,
      Cypridina.Ledger.BalanceCache
    ]

    # 根据项目模式添加特定服务
    mode_specific_children =
      case Cypridina.ProjectMode.current() do
        :race ->
          [
            # 动物运动会 - 使用重启策略确保崩溃后自动重启
            {RacingGame.RaceController, []}
          ]

        :teen ->
          [
            # Teen项目
            Cypridina.Teen.GameSystem.RobotService,
            # 用户在线状态管理 - 基于Phoenix Presence，支持顶号机制
            Teen.UserPresence,
            # 游戏房间注册表
            {Registry, keys: :unique, name: :game_room_registry},
            # 游戏工厂 - 必须在房间管理器之前启动
            Cypridina.RoomSystem.GameFactory,
            Cypridina.RoomSystem.RoomManager,
            # 优化的统一事件发布器
            Teen.Events.EventPublisher,
            Teen.ActivitySystem.ActivityManager
          ]

        :self ->
          # 自用模式不启动游戏相关服务
          []
      end

    children = base_children ++ mode_specific_children

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Cypridina.Supervisor]

    case Supervisor.start_link(children, opts) do
      {:ok, pid} ->
        # 在监督树启动后定义自定义货币
        Cypridina.CustomCurrencies.define_currencies()
        {:ok, pid}

      error ->
        error
    end
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    CypridinaWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
