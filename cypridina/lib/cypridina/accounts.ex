defmodule Cypridina.Accounts do
  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [
      AshAdmin.Domain,
      AshPaperTrail.Domain
      # AshAuthentication
    ]

  require Logger

  alias <PERSON><PERSON>ridina.Ledger.AccountIdentifier

  admin do
    show? true
  end

  resources do
    resource Cypridina.Accounts.Token
    resource Cypridina.Accounts.User
    resource Cypridina.Accounts.User.Version
    resource Cypridina.Accounts.UserProfile
    resource Cypridina.Accounts.UserDevice
    resource Cypridina.Accounts.UserIdentity
    resource Cypridina.Accounts.ApiKey
    resource Cypridina.Accounts.AgentRelationship
    resource Cypridina.Accounts.Channel
  end

  alias Cypridina.Accounts.User

  @doc """
  获取用户积分余额
  使用BalanceCache缓存系统
  """
  def get_user_points(user_id) do
    identifier = AccountIdentifier.user(user_id, :XAA)
    {:ok, balance} = Cypridina.Ledger.BalanceCache.get_balance(identifier)

    balance
  end

  @doc """
  增加用户积分
  使用新的复式记账系统，并同步更新BalanceCache
  """
  def add_points(user_id, amount, opts) when amount > 0 do
    system_identifier = AccountIdentifier.system(:rewards, :XAA)
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    # 直接使用Ledger系统
    Cypridina.Ledger.transfer(system_identifier, user_identifier, amount, opts)
  end

  @doc """
  扣除用户积分
  使用新的复式记账系统，并同步更新BalanceCache
  """
  def subtract_points(user_id, amount, opts) when amount > 0 do
    system_identifier = AccountIdentifier.system(:fees, :XAA)
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    # 直接使用Ledger系统
    Cypridina.Ledger.transfer(user_identifier, system_identifier, amount, opts)
  end

  # ========== 游戏账户相关API ==========
  @doc """
  转移积分从一个用户到另一个用户

  ## 参数
  - from_user_id: 转出用户ID
  - to_user_id: 转入用户ID
  - amount: 转移数量
  - reason: 转移原因（可选）

  ## 返回值
  - {:ok, result} 转移成功
  - {:error, reason} 转移失败

  ## 示例
      iex> Cypridina.Accounts.transfer_points(1, 2, 100, "奖励")
      {:ok, %{from_asset: %{points: 900}, to_asset: %{points: 1100}}}

      iex> Cypridina.Accounts.transfer_points(1, 2, 1000000, "测试")
      {:error, "积分不足"}
  """
  def transfer_points(from_user_id, to_user_id, _amount, _reason)
      when from_user_id == to_user_id do
    {:error, "不能向自己转移积分"}
  end

  def transfer_points(_from_user_id, _to_user_id, amount, _reason) when amount <= 0 do
    {:error, "积分设置错误"}
  end

  def transfer_points(from_user_id, to_user_id, amount, reason) do
    # 获取用户信息用于记录
    from_user = Cypridina.UserCache.get_user!(from_user_id)
    to_user = Cypridina.UserCache.get_user!(to_user_id)

    from_username = from_user.username
    to_username = to_user.username

    # 构建转账选项
    opts = [
      description: "用户间转账: #{from_username} -> #{to_username}",
      metadata: %{
        from_username: from_username,
        to_username: to_username,
        reason: reason
      }
    ]

    # 同步更新BalanceCache
    from_identifier = AccountIdentifier.user(from_user_id, :XAA)
    to_identifier = AccountIdentifier.user(to_user_id, :XAA)
    # 直接使用Ledger系统
    Cypridina.Ledger.transfer(from_identifier, to_identifier, amount, opts)
  end

  @doc """
  获取用户信息
  """
  def get_user(user_id) do
    User |> Ash.get(user_id)
  end

  # 获取用户信息的辅助函数
  defp get_user_by_id(user_id) do
    User |> Ash.get(user_id)
  end

  # ========== 管理员权限管理 ==========

  # 权限级别常量
  @user_level 0
  @admin_level 1
  @super_admin_level 2

  @doc """
  检查用户是否为管理员（级别 >= 1）
  """
  def is_admin?(user) when is_struct(user, User) do
    user.permission_level >= @admin_level
  end

  def is_admin?(_) do
    false
  end

  @doc """
  检查用户是否为超级管理员（级别 = 2）
  """
  def is_super_admin?(user) when is_struct(user, User) do
    user.permission_level == @super_admin_level
  end

  def is_super_admin?(user_id) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  检查用户权限级别是否满足要求
  """
  def has_permission_level?(user, required_level) when is_struct(user, User) do
    user.permission_level >= required_level
  end

  def has_permission_level?(user_id, _required_level) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  兼容性函数：检查用户是否有特定权限
  现在基于权限级别进行简化判断
  """
  def has_permission?(user, permission) when is_struct(user, User) do
    case permission do
      "super_admin" -> is_super_admin?(user)
      # 其他权限只要是管理员即可
      _ -> is_admin?(user)
    end
  end

  def has_permission?(user_id, _permission) when is_binary(user_id) do
    # 暂时不支持通过ID查询，需要传入完整的用户结构体
    false
  end

  @doc """
  创建管理员用户
  """
  def create_admin(attrs) do
    admin_attrs =
      Map.merge(attrs, %{
        permission_level: Map.get(attrs, :permission_level, @admin_level)
      })

    # 使用Ash资源直接创建
    User
    |> Ash.Changeset.for_create(:register_with_username, admin_attrs)
    |> Ash.create()
  end

  @doc """
  更新用户的权限级别
  """
  def update_permission_level(user, permission_level) do
    user
    |> Ash.Changeset.for_update(:update_permission_level, %{permission_level: permission_level})
    |> Ash.update()
  end

  @doc """
  获取所有管理员用户
  """
  def list_admins do
    User
    |> Ash.Query.for_read(:get_admins)
    |> Ash.read()
  end

  @doc """
  获取所有超级管理员用户
  """
  def list_super_admins do
    User
    |> Ash.Query.for_read(:get_super_admins)
    |> Ash.read()
  end

  @doc """
  创建超级管理员（仅在系统初始化时使用）
  """
  def create_super_admin(attrs) do
    super_admin_attrs =
      Map.merge(attrs, %{
        permission_level: @super_admin_level
      })

    User
    |> Ash.Changeset.for_create(:register_with_username, super_admin_attrs)
    |> Ash.create()
  end

  @doc """
  检查是否已存在超级管理员
  """
  def super_admin_exists? do
    case User
         |> Ash.Query.for_read(:get_super_admins)
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, []} -> false
      {:ok, [_ | _]} -> true
      _ -> false
    end
  end

  @doc """
  权限级别检查装饰器函数
  """
  def require_permission_level(user, required_level, fun) when is_function(fun, 0) do
    if has_permission_level?(user, required_level) do
      fun.()
    else
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  权限级别检查装饰器函数（带参数）
  """
  def require_permission_level(user, required_level, fun, args) when is_function(fun, 1) do
    if has_permission_level?(user, required_level) do
      fun.(args)
    else
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  兼容性函数：权限检查装饰器
  """
  def require_permission(user, permission, fun) when is_function(fun, 0) do
    if has_permission?(user, permission) do
      fun.()
    else
      {:error, :insufficient_permissions}
    end
  end

  def require_permission(user, permission, fun, args) when is_function(fun, 1) do
    if has_permission?(user, permission) do
      fun.(args)
    else
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  获取权限级别名称
  """
  def permission_level_name(level) do
    case level do
      2 -> "超级管理员"
      1 -> "管理员"
      0 -> "普通用户"
      _ -> "未知"
    end
  end

  @doc """
  获取所有权限级别
  """
  def permission_levels do
    [
      {@user_level, "普通用户"},
      {@admin_level, "管理员"},
      {@super_admin_level, "超级管理员"}
    ]
  end

  @doc """
  权限级别常量
  """
  def user_level, do: @user_level
  def admin_level, do: @admin_level
  def super_admin_level, do: @super_admin_level

  @doc """
  管理员操作日志记录
  """
  def log_admin_action(admin_user, action, target \\ nil, details \\ %{}) do
    log_data = %{
      admin_id: admin_user.id,
      admin_username: admin_user.username,
      permission_level: admin_user.permission_level,
      action: action,
      target: target,
      details: details,
      timestamp: DateTime.utc_now()
    }

    # 这里可以集成到日志系统或审计系统
    Logger.info("Admin Action: #{inspect(log_data)}")

    {:ok, log_data}
  end

  # 兼容性函数别名
  def is_root_admin?(user), do: is_super_admin?(user)
  def create_root_admin(attrs), do: create_super_admin(attrs)
  def root_admin_exists?(), do: super_admin_exists?()

  # ========== 用户管理功能 ==========

  @doc """
  初始化超级管理员账号

  这个函数应该在数据库初始化时调用，用于创建第一个超级管理员账号
  """
  def initialize_super_admin(attrs \\ %{}) do
    require Ash.Query
    import Ash.Expr

    default_attrs = %{
      username: "super_admin",
      password: "admin123456",
      password_confirmation: "admin123456",
      permission_level: 2,
      # 超级管理员默认为根代理级别
      agent_level: 0
    }

    attrs = Map.merge(default_attrs, attrs)

    # 检查是否已存在超级管理员
    case User
         |> Ash.Query.filter(expr(permission_level == 2))
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, []} ->
        # 没有超级管理员，创建一个
        Logger.info("正在创建超级管理员账号: #{attrs.username}")

        User
        |> Ash.Changeset.for_create(:register_with_username, attrs)
        |> Ash.Changeset.set_tenant(nil)
        |> Ash.create()
        |> case do
          {:ok, user} ->
            Logger.info("超级管理员创建成功: #{user.username} (ID: #{user.numeric_id})")
            {:ok, user}

          {:error, error} ->
            Logger.error("超级管理员创建失败: #{inspect(error)}")
            {:error, error}
        end

      {:ok, [admin | _]} ->
        Logger.info("超级管理员已存在: #{admin.username} (ID: #{admin.numeric_id})")
        {:ok, admin}

      {:error, error} ->
        Logger.error("检查超级管理员时出错: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  超级管理员创建管理员账号
  """
  def create_admin_by_super_admin(super_admin_id, attrs) do
    with {:ok, super_admin} <- get_user_by_id(super_admin_id),
         :ok <- validate_super_admin_permission(super_admin),
         {:ok, admin} <- create_admin_user(attrs) do
      Logger.info("超级管理员 #{super_admin.username} 创建了管理员 #{admin.username}")
      {:ok, admin}
    end
  end

  @doc """
  管理员创建普通用户账号
  """
  def create_user_by_admin(admin_id, attrs) do
    with {:ok, admin} <- get_user_by_id(admin_id),
         :ok <- validate_admin_permission(admin),
         {:ok, user} <- create_regular_user(attrs) do
      Logger.info("管理员 #{admin.username} 创建了用户 #{user.username}")
      {:ok, user}
    end
  end

  @doc """
  管理员创建代理用户
  """
  def create_agent_by_admin(admin_id, attrs) do
    with {:ok, admin} <- get_user_by_id(admin_id),
         :ok <- validate_admin_permission(admin),
         {:ok, agent} <- create_agent_user(attrs) do
      Logger.info("管理员 #{admin.username} 创建了代理 #{agent.username}")
      {:ok, agent}
    end
  end

  @doc """
  代理创建下线用户
  """
  def create_user_by_agent(agent_id, attrs) do
    with {:ok, agent} <- get_user_by_id(agent_id),
         :ok <- validate_agent_permission(agent),
         {:ok, user} <- create_subordinate_user(agent, attrs) do
      Logger.info("代理 #{agent.username} 创建了下线用户 #{user.username}")
      {:ok, user}
    end
  end

  @doc """
  代理创建下级代理
  """
  def create_agent_by_agent(agent_id, attrs) do
    with {:ok, agent} <- get_user_by_id(agent_id),
         :ok <- validate_agent_permission(agent),
         {:ok, sub_agent} <- create_subordinate_agent(agent, attrs) do
      Logger.info("代理 #{agent.username} 创建了下级代理 #{sub_agent.username}")
      {:ok, sub_agent}
    end
  end

  @doc """
  普通用户注册（公开注册）
  """
  def register_regular_user(attrs) do
    # register_with_username action 只接受特定参数，不包括 agent_level
    # agent_level 有默认值 -1，所以不需要显式设置
    attrs = Map.merge(attrs, %{permission_level: 0})

    User
    |> Ash.Changeset.for_create(:register_with_username, attrs)
    |> Ash.create()
    |> case do
      {:ok, user} ->
        Logger.info("新用户注册成功: #{user.username} (ID: #{user.numeric_id})")
        {:ok, user}

      {:error, error} ->
        Logger.error("用户注册失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  查询所有用户
  """
  def list_all_users do
    User
    |> Ash.Query.sort(numeric_id: :asc)
    |> Ash.read!()
  end

  @doc """
  根据权限级别查询用户
  """
  def list_users_by_permission(permission_level) do
    require Ash.Query
    import Ash.Expr

    User
    |> Ash.Query.filter(expr(permission_level == ^permission_level))
    |> Ash.Query.sort(numeric_id: :asc)
    |> Ash.read!()
  end

  @doc """
  查询所有代理用户
  """
  def list_agents do
    require Ash.Query
    import Ash.Expr

    User
    |> Ash.Query.filter(expr(agent_level >= 0))
    |> Ash.Query.sort(agent_level: :asc, numeric_id: :asc)
    |> Ash.read!()
  end

  @doc """
  查询指定代理的下线用户
  """
  def list_subordinates(agent_id) do
    case get_user_subordinates(agent_id) do
      {:ok, subordinates} ->
        Enum.sort_by(subordinates, & &1.numeric_id)

      _ ->
        []
    end
  end

  @doc """
  批量创建测试用户
  """
  def create_test_users(count \\ 10) do
    Logger.info("开始创建 #{count} 个测试用户")

    results =
      1..count
      |> Enum.map(fn i ->
        attrs = %{
          username: "test_user_#{i}",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 0
          # agent_level 有默认值 -1，不需要显式设置
        }

        register_regular_user(attrs)
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("成功创建 #{success_count}/#{count} 个测试用户")

    results
  end

  # ========== 私有辅助函数 ==========

  defp validate_super_admin_permission(user) do
    if user.permission_level == 2 do
      :ok
    else
      {:error, "只有超级管理员才能执行此操作"}
    end
  end

  defp validate_admin_permission(user) do
    if user.permission_level >= 1 do
      :ok
    else
      {:error, "只有管理员或超级管理员才能执行此操作"}
    end
  end

  defp validate_agent_permission(user) do
    if user.agent_level >= 0 do
      :ok
    else
      {:error, "只有代理才能创建下线用户"}
    end
  end

  defp create_admin_user(attrs) do
    attrs = Map.merge(attrs, %{permission_level: 1})

    User
    |> Ash.Changeset.for_create(:register_with_username, attrs)
    |> Ash.create()
  end

  defp create_regular_user(attrs) do
    # 普通用户默认权限级别为0，代理级别为-1（不是代理）
    attrs = Map.merge(attrs, %{permission_level: 0, agent_level: -1})

    User
    |> Ash.Changeset.for_create(:register_with_username, attrs)
    |> Ash.create()
  end

  defp create_agent_user(attrs) do
    # 代理用户默认权限级别为0，代理级别从参数获取
    agent_level = Map.get(attrs, :agent_level, 0)
    attrs = Map.merge(attrs, %{permission_level: 0, agent_level: agent_level})

    User
    |> Ash.Changeset.for_create(:register_with_username, attrs)
    |> Ash.create()
  end

  defp create_subordinate_user(agent, attrs) do
    # 计算下线用户的代理级别
    subordinate_agent_level =
      case Map.get(attrs, :agent_level) do
        :auto ->
          # 自动计算：上线的 agent_level + 1
          agent.agent_level + 1

        level when is_integer(level) and level >= 0 ->
          # 如果明确指定为代理级别，确保不低于上线的级别+1
          max(agent.agent_level + 1, level)

        _ ->
          # 不是代理，设置为 -1
          -1
      end

    # 提取 commission_rate，因为 register_with_username action 不接受这个参数
    commission_rate = Map.get(attrs, :commission_rate, Decimal.new("0.05"))

    # 准备用户创建参数，移除 commission_rate
    user_attrs =
      attrs
      |> Map.delete(:commission_rate)
      |> Map.merge(%{
        permission_level: 0,
        agent_level: subordinate_agent_level
      })

    # 创建用户
    case User
         |> Ash.Changeset.for_create(:register_with_username, user_attrs)
         |> Ash.create() do
      {:ok, user} ->
        # 创建代理关系
        case create_agent_relationship(agent.id, user.id,
               level: 1,
               commission_rate: commission_rate
             ) do
          {:ok, _relationship} -> {:ok, user}
          {:error, error} -> {:error, error}
        end

      {:error, error} ->
        {:error, error}
    end
  end

  defp create_subordinate_agent(agent, attrs) do
    # 下级代理的代理级别比上级代理高1
    subordinate_agent_level = agent.agent_level + 1

    attrs =
      attrs
      |> Map.merge(%{
        permission_level: 0,
        agent_level: subordinate_agent_level
      })

    # 创建代理用户
    case User
         |> Ash.Changeset.for_create(:register_with_username, attrs)
         |> Ash.create() do
      {:ok, user} ->
        # 创建代理关系
        case create_agent_relationship(agent.id, user.id,
               level: 1,
               commission_rate: Map.get(attrs, :commission_rate, Decimal.new("0.05"))
             ) do
          {:ok, _relationship} -> {:ok, user}
          {:error, error} -> {:error, error}
        end

      {:error, error} ->
        {:error, error}
    end
  end

  # ========== 代理系统功能 ==========

  @doc """
  检查用户是否为代理
  """
  def is_agent?(user) when is_struct(user, User) do
    user.agent_level >= 0
  end

  def is_agent?(user_id) when is_binary(user_id) do
    case get_user_by_id(user_id) do
      {:ok, user} -> is_agent?(user)
      _ -> false
    end
  end

  @doc """
  检查用户是否为根代理
  """
  def is_root_agent?(user) when is_struct(user, User) do
    user.agent_level == 0
  end

  def is_root_agent?(user_id) when is_binary(user_id) do
    case get_user_by_id(user_id) do
      {:ok, user} -> is_root_agent?(user)
      _ -> false
    end
  end

  @doc """
  检查代理是否可以管理指定用户
  """
  def can_manage_user?(agent, target_user_id) when is_struct(agent, User) do
    # 如果是管理员或超级管理员，可以管理所有用户
    if is_admin?(agent) do
      true
    else
      # 检查是否为代理关系
      case get_user_agent(target_user_id) do
        {:ok, user_agent} when not is_nil(user_agent) ->
          user_agent.id == agent.id

        _ ->
          false
      end
    end
  end

  def can_manage_user?(agent_id, target_user_id) when is_binary(agent_id) do
    case get_user_by_id(agent_id) do
      {:ok, agent} -> can_manage_user?(agent, target_user_id)
      _ -> false
    end
  end

  @doc """
  获取用户的代理
  """
  def get_user_agent(user_id) do
    require Ash.Query
    import Ash.Expr

    Cypridina.Accounts.AgentRelationship
    |> Ash.Query.filter(expr(subordinate_id == ^user_id and status == 1))
    |> Ash.Query.load([:agent])
    |> Ash.read_one()
    |> case do
      {:ok, relationship} when not is_nil(relationship) ->
        {:ok, relationship.agent}

      {:ok, nil} ->
        {:error, :no_agent}

      error ->
        error
    end
  end

  @doc """
  获取用户的代理关系（包含抽水比例等信息）
  """
  def get_user_agent_relationship(user_id) do
    require Ash.Query
    import Ash.Expr

    Cypridina.Accounts.AgentRelationship
    |> Ash.Query.filter(expr(subordinate_id == ^user_id and status == 1))
    |> Ash.Query.load([:agent])
    |> Ash.read_one()
    |> case do
      {:ok, relationship} when not is_nil(relationship) ->
        {:ok, relationship}

      {:ok, nil} ->
        {:error, :no_agent}

      error ->
        error
    end
  end

  @doc """
  创建代理关系
  """
  def create_agent_relationship(agent_id, subordinate_id, opts \\ []) do
    level = Keyword.get(opts, :level, 1)
    commission_rate = Keyword.get(opts, :commission_rate, Decimal.new("0.05"))

    Cypridina.Accounts.AgentRelationship
    |> Ash.Changeset.for_create(:create_relationship, %{
      agent_id: agent_id,
      subordinate_id: subordinate_id,
      level: level,
      commission_rate: commission_rate
    })
    |> Ash.create()
  end

  @doc """
  获取代理关系
  """
  def get_agent_relationship(agent_id, subordinate_id) do
    require Ash.Query
    import Ash.Expr

    Cypridina.Accounts.AgentRelationship
    |> Ash.Query.filter(expr(agent_id == ^agent_id and subordinate_id == ^subordinate_id))
    |> Ash.read_one()
  end

  @doc """
  构建代理关系树 - 递归获取所有下线关系
  """
  def build_agent_tree(agent_id, opts \\ []) do
    max_depth = Keyword.get(opts, :max_depth, 10)
    include_inactive = Keyword.get(opts, :include_inactive, false)

    case Cypridina.Accounts.User |> Ash.get(agent_id) do
      {:ok, agent} ->
        tree_node = build_agent_tree_node(agent, 0, max_depth, include_inactive)
        {:ok, tree_node}

      error ->
        error
    end
  end

  @doc """
  获取代理的直接下线列表（带用户信息）
  """
  def get_agent_subordinates_with_info(agent_id, opts \\ []) do
    include_inactive = Keyword.get(opts, :include_inactive, false)

    require Ash.Query
    import Ash.Expr

    query =
      if include_inactive do
        Cypridina.Accounts.AgentRelationship
        |> Ash.Query.filter(expr(agent_id == ^agent_id))
      else
        Cypridina.Accounts.AgentRelationship
        |> Ash.Query.filter(expr(agent_id == ^agent_id and status == 1))
      end

    case query
         |> Ash.Query.load([:subordinate])
         |> Ash.Query.sort(level: :asc, inserted_at: :asc)
         |> Ash.read() do
      {:ok, relationships} ->
        subordinates_with_info =
          Enum.map(relationships, fn rel ->
            subordinate = rel.subordinate
            points = get_user_points(subordinate.id)

            # 获取下线数量
            subordinate_count = count_agent_subordinates(subordinate.id)

            %{
              user: subordinate,
              relationship: rel,
              points: points,
              subordinate_count: subordinate_count,
              level: rel.level,
              commission_rate: rel.commission_rate,
              status: rel.status
            }
          end)

        {:ok, subordinates_with_info}

      error ->
        error
    end
  end

  @doc """
  统计代理的下线数量
  """
  def count_agent_subordinates(agent_id, include_inactive \\ false) do
    require Ash.Query
    import Ash.Expr

    query =
      if include_inactive do
        Cypridina.Accounts.AgentRelationship
        |> Ash.Query.filter(expr(agent_id == ^agent_id))
      else
        Cypridina.Accounts.AgentRelationship
        |> Ash.Query.filter(expr(agent_id == ^agent_id and status == 1))
      end

    case query |> Ash.read() do
      {:ok, relationships} -> length(relationships)
      _ -> 0
    end
  end

  # 私有函数：构建树节点
  defp build_agent_tree_node(user, current_depth, max_depth, include_inactive) do
    subordinate_count = count_agent_subordinates(user.id, include_inactive)
    points = get_user_points(user.id)

    # 如果达到最大深度或没有下线，不继续递归
    children =
      if current_depth >= max_depth or subordinate_count == 0 do
        []
      else
        case get_agent_subordinates_with_info(user.id, include_inactive: include_inactive) do
          {:ok, subordinates} ->
            Enum.map(subordinates, fn sub_info ->
              child_node =
                build_agent_tree_node(
                  sub_info.user,
                  current_depth + 1,
                  max_depth,
                  include_inactive
                )

              Map.put(child_node, :relationship, sub_info.relationship)
            end)

          _ ->
            []
        end
      end

    %{
      user: user,
      points: points,
      subordinate_count: subordinate_count,
      level: current_depth,
      children: children,
      # 默认展开前两层
      expanded: current_depth < 2
    }
  end

  @doc """
  更新代理关系
  """
  def update_agent_relationship(relationship, attrs) do
    relationship
    |> Ash.Changeset.for_update(:update, attrs)
    |> Ash.update()
  end

  @doc """
  删除代理关系
  """
  def delete_agent_relationship(relationship) do
    relationship
    |> Ash.Changeset.for_destroy(:destroy)
    |> Ash.destroy()
  end

  @doc """
  获取用户的下级用户
  """
  def get_user_subordinates(agent_id, search_query \\ "", page \\ 1, per_page \\ 20) do
    require Ash.Query
    import Ash.Expr

    query =
      Cypridina.Accounts.AgentRelationship
      |> Ash.Query.filter(expr(agent_id == ^agent_id and status == 1))
      |> Ash.Query.load([:subordinate])

    # 如果有搜索条件，添加过滤
    query =
      if search_query != "" do
        # 这里需要根据实际的搜索需求调整
        query
      else
        query
      end

    # 分页
    query
    |> Ash.Query.offset((page - 1) * per_page)
    |> Ash.Query.limit(per_page)
    |> Ash.read()
    |> case do
      {:ok, relationships} ->
        subordinates = Enum.map(relationships, & &1.subordinate)
        {:ok, subordinates}

      error ->
        error
    end
  end

  @doc """
  获取代理层级结构
  """
  def get_agent_hierarchy(agent_id) do
    case get_user_subordinates(agent_id) do
      {:ok, subordinates} ->
        hierarchy =
          Enum.map(subordinates, fn subordinate ->
            %{
              user: subordinate,
              subordinates:
                case get_agent_hierarchy(subordinate.id) do
                  {:ok, sub_hierarchy} -> sub_hierarchy
                  _ -> []
                end
            }
          end)

        {:ok, hierarchy}

      error ->
        error
    end
  end

  @doc """
  计算佣金
  """
  def calculate_commission(agent_id, subordinate_id, amount) do
    require Ash.Query
    import Ash.Expr

    case Cypridina.Accounts.AgentRelationship
         |> Ash.Query.filter(
           expr(agent_id == ^agent_id and subordinate_id == ^subordinate_id and status == 1)
         )
         |> Ash.read_one() do
      {:ok, relationship} when not is_nil(relationship) ->
        commission = Decimal.mult(amount, relationship.commission_rate)
        {:ok, commission}

      {:ok, nil} ->
        {:error, "代理关系不存在"}

      error ->
        error
    end
  end

  @doc """
  创建下级账户
  """
  def create_subordinate_account(agent_id, user_params) do
    with {:ok, agent} <- get_user_by_id(agent_id),
         :ok <- validate_agent_permission(agent),
         {:ok, user} <- create_subordinate_user(agent, user_params) do
      {:ok, user}
    end
  end

  @doc """
  设置佣金率
  """
  def set_commission_rate(agent_id, subordinate_id, commission_rate) do
    case get_agent_relationship(agent_id, subordinate_id) do
      {:ok, relationship} when not is_nil(relationship) ->
        update_agent_relationship(relationship, %{commission_rate: commission_rate})

      {:ok, nil} ->
        {:error, "代理关系不存在"}

      error ->
        error
    end
  end

  @doc """
  获取用户名
  """
  def get_username(user_id) do
    case get_user_by_id(user_id) do
      {:ok, user} -> user.username
      _ -> "未知用户"
    end
  end
end
