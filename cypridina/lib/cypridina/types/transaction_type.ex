defmodule Cypridina.Types.TransactionType do
  @moduledoc """
  交易类型枚举

  集中管理所有交易类型及其中文描述，使用 Ash.Type.Enum 实现。
  支持不同项目模式下的交易类型。
  """

  use Ash.Type.Enum,
    values: [
      # 基础交易类型（所有模式都支持）
      transfer: "用户间转账",
      refund: "退款",
      commission: "佣金",
      system_adjust: "系统调整",
      admin_operation: "管理员操作",
      bonus: "奖励",
      deposit: "充值",
      withdrawal: "提现",
      withdrawal_request: "提现请求",

      # 游戏相关交易类型
      game_bet: "游戏投注",
      game_win: "游戏获奖",
      game_rake: "游戏抽水",
      game_jackpot: "游戏奖池",
      jackpot_init: "奖池初始化",
      jackpot_win: "奖池中奖",

      # 股票交易类型（Race模式）
      buy_stock: "买入股票",
      sell_stock: "卖出股票",
      force_liquidation: "强制平仓",
      liquidate_all_stocks: "清仓所有股票",

      # 商店系统交易类型
      shop_purchase: "商店购买",
      shop_payment: "商店支付"
    ]

  # 定义分类映射
  @type_categories %{
    # 基础交易类型
    transfer: :base,
    refund: :base,
    commission: :base,
    system_adjust: :base,
    deposit: :base,
    withdrawal: :base,
    withdrawal_request: :base,

    # 游戏相关交易类型
    game_bet: :game,
    game_win: :game,
    game_rake: :game,
    game_jackpot: :game,
    jackpot_init: :game,
    jackpot_win: :game,

    # 股票交易类型
    buy_stock: :stock,
    sell_stock: :stock,
    force_liquidation: :stock,
    liquidate_all_stocks: :stock,

    # 商店系统交易类型
    shop_purchase: :shop,
    shop_payment: :shop,

    # 管理员操作
    admin_operation: :admin
  }

  @doc """
  获取交易类型的中文描述

  ## Examples

      iex> Cypridina.Types.TransactionType.description(:game_bet)
      "游戏投注"

      iex> Cypridina.Types.TransactionType.description(:buy_stock)
      "买入股票"

      iex> Cypridina.Types.TransactionType.description(:unknown)
      "未知交易类型"
  """
  @spec description(atom()) :: String.t()
  def description(type) do
    # 直接从枚举定义中获取描述，避免重复定义
    case Enum.find(values(), fn
           {key, _desc} -> key == type
           key when is_atom(key) -> key == type
         end) do
      {_key, desc} when is_binary(desc) -> desc
      key when is_atom(key) and key != nil -> to_string(key)
      _ -> "未知交易类型"
    end
  end

  @doc """
  获取所有交易类型列表

  ## Examples

      iex> Cypridina.Types.TransactionType.all_types()
      [:transfer, :refund, :commission, ...]
  """
  @spec all_types() :: [atom()]
  def all_types do
    # 直接从枚举定义中获取所有类型，避免重复定义
    Enum.map(values(), fn
      {key, _desc} -> key
      key when is_atom(key) -> key
    end)
  end

  @doc """
  获取基础交易类型（所有模式都支持）

  ## Examples

      iex> Cypridina.Types.TransactionType.base_types()
      [:transfer, :refund, :commission, ...]
  """
  @spec base_types() :: [atom()]
  def base_types do
    get_types_by_category(:base)
  end

  @doc """
  获取游戏相关交易类型

  ## Examples

      iex> Cypridina.Types.TransactionType.game_types()
      [:game_bet, :game_win]
  """
  @spec game_types() :: [atom()]
  def game_types do
    get_types_by_category(:game)
  end

  @doc """
  获取股票相关交易类型（Race模式）

  ## Examples

      iex> Cypridina.Types.TransactionType.stock_types()
      [:buy_stock, :sell_stock, :force_liquidation]
  """
  @spec stock_types() :: [atom()]
  def stock_types do
    get_types_by_category(:stock)
  end

  @doc """
  获取商店相关交易类型

  ## Examples

      iex> Cypridina.Types.TransactionType.shop_types()
      [:shop_purchase, :shop_payment]
  """
  @spec shop_types() :: [atom()]
  def shop_types do
    get_types_by_category(:shop)
  end

  @doc """
  获取管理员操作相关交易类型

  ## Examples

      iex> Cypridina.Types.TransactionType.admin_types()
      [:admin_operation]
  """
  @spec admin_types() :: [atom()]
  def admin_types do
    get_types_by_category(:admin)
  end

  # 辅助函数：根据分类获取类型
  defp get_types_by_category(category) do
    @type_categories
    |> Enum.filter(fn {_type, cat} -> cat == category end)
    |> Enum.map(fn {type, _cat} -> type end)
  end

  @doc """
  根据项目模式获取可用的交易类型

  ## Examples

      iex> Cypridina.Types.TransactionType.types_for_mode(:teen)
      [:transfer, :refund, :game_bet, ...]

      iex> Cypridina.Types.TransactionType.types_for_mode(:race)
      [:transfer, :refund, :buy_stock, ...]
  """
  @spec types_for_mode(atom()) :: [atom()]
  def types_for_mode(mode) do
    case mode do
      :teen ->
        base_types() ++ game_types() ++ shop_types() ++ admin_types()

      :race ->
        base_types() ++ stock_types() ++ shop_types() ++ admin_types()

      :self ->
        base_types() ++ shop_types()

      _ ->
        all_types()
    end
  end

  @doc """
  检查交易类型是否在指定模式下可用

  ## Examples

      iex> Cypridina.Types.TransactionType.available_in_mode?(:buy_stock, :race)
      true

      iex> Cypridina.Types.TransactionType.available_in_mode?(:buy_stock, :teen)
      false
  """
  @spec available_in_mode?(atom(), atom()) :: boolean()
  def available_in_mode?(type, mode) do
    type in types_for_mode(mode)
  end

  @doc """
  获取交易类型的分类

  ## Examples

      iex> Cypridina.Types.TransactionType.category(:game_bet)
      :game

      iex> Cypridina.Types.TransactionType.category(:buy_stock)
      :stock
  """
  @spec category(atom()) :: atom()
  def category(type) do
    Map.get(@type_categories, type, :unknown)
  end

  @doc """
  获取交易类型及其描述的映射

  ## Examples

      iex> Cypridina.Types.TransactionType.type_descriptions()
      %{transfer: "用户间转账", game_bet: "游戏投注", ...}
  """
  @spec type_descriptions() :: %{atom() => String.t()}
  def type_descriptions do
    all_types()
    |> Enum.into(%{}, fn type -> {type, description(type)} end)
  end
end
