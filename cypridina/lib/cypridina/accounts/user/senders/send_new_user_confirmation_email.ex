defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.User.Senders.SendNewUserConfirmationEmail do
  @moduledoc """
  Sends an email for a new user to confirm their email address.
  """

  use AshAuthentication.Sender
  use <PERSON><PERSON>ridinaWeb, :verified_routes

  import Swoosh.Email

  alias <PERSON><PERSON><PERSON><PERSON>.Mailer

  @impl true
  def send(user, token, _) do
    # 如果用户没有邮箱地址，跳过发送邮件
    case user.email do
      nil ->
        {:ok, :skipped_no_email}

      email when email == "" ->
        {:ok, :skipped_empty_email}

      email ->
        new()
        # TODO: Replace with your email
        |> from({"noreply", "<EMAIL>"})
        |> to(to_string(email))
        |> subject("Confirm your email address")
        |> html_body(body(token: token))
        |> Mailer.deliver!()
    end
  end

  defp body(params) do
    url = url(~p"/confirm_new_user/#{params[:token]}")

    """
    <p>Click this link to confirm your email:</p>
    <p><a href="#{url}">#{url}</a></p>
    """
  end
end
