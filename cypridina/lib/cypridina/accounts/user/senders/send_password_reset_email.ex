defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.User.Senders.SendPasswordResetEmail do
  @moduledoc """
  Sends a password reset email
  """

  use AshAuthentication.Sender
  use Cy<PERSON>ridinaWeb, :verified_routes

  import Swoosh.Email

  alias <PERSON><PERSON>ridina.Mailer

  @impl true
  def send(user, token, _) do
    # 如果用户没有邮箱地址，跳过发送邮件
    case user.email do
      nil ->
        {:ok, :skipped_no_email}

      email when email == "" ->
        {:ok, :skipped_empty_email}

      email ->
        new()
        # TODO: Replace with your email
        |> from({"noreply", "<EMAIL>"})
        |> to(to_string(email))
        |> subject("Reset your password")
        |> html_body(body(token: token))
        |> Mailer.deliver!()
    end
  end

  defp body(params) do
    url = url(~p"/password-reset/#{params[:token]}")

    """
    <p>Click this link to reset your password:</p>
    <p><a href="#{url}">#{url}</a></p>
    """
  end
end
