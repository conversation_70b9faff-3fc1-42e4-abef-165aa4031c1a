defmodule Cypridina.Ledger.AccountIdentifier do
  @moduledoc """
  统一的账户标识符管理模块

  提供统一的账户标识符生成、解析和验证功能，支持：
  - 用户账户标识符：`user:currency:user_id`
  - 系统账户标识符：`system:currency:account_type`
  - 游戏账户标识符：`game:currency:game_id`
  - 缓存键标识符：`user:user_id`, `system:account_type`, `game:game_id`

  ## 使用示例

      # 生成账户标识符（简化版）
      AccountIdentifier.user("user_123")
      # => "user:XAA:user_123"

      AccountIdentifier.system(:main)
      # => "system:XAA:main"

      AccountIdentifier.game("game_456")
      # => "game:XAA:game_456"

      # 生成缓存键
      AccountIdentifier.cache_key(:user, "user_123")
      # => "user:user_123"

      # 解析标识符
      AccountIdentifier.parse("user:XAA:user_123")
      # => {:ok, %{type: :user, currency: :XAA, id: "user_123"}}

      AccountIdentifier.parse("system:XAA:main")
      # => {:ok, %{type: :system, currency: :XAA, account_type: :main}}

      # 验证标识符
      AccountIdentifier.valid?("user:XAA:user_123")
      # => true
  """

  @type account_type :: :user | :system | :game | :jackpot
  @type currency :: :XAA | atom()
  @type system_account_type :: :main | :rewards | :fees | :commission | :refund | atom()

  @default_currency :XAA

  # ========== 账户标识符生成 ==========

  @doc """
  生成用户账户标识符

  ## 参数
  - `user_id`: 用户ID (string | UUID)
  - `currency`: 货币类型，默认为 :XAA

  ## 返回值
  - 用户账户标识符字符串：`"user:currency:user_id"`

  ## 示例
      iex> AccountIdentifier.user("user_123")
      "user:XAA:user_123"

      iex> AccountIdentifier.user("user_123", :USD)
      "user:USD:user_123"
  """
  @spec user(String.t(), currency()) :: String.t()
  def user(user_id, currency \\ @default_currency) when is_binary(user_id) do
    "user:#{currency}:#{user_id}"
  end

  @doc """
  生成系统账户标识符

  ## 参数
  - `account_type`: 系统账户类型
  - `currency`: 货币类型，默认为 :XAA

  ## 返回值
  - 系统账户标识符字符串：`"system:currency:account_type"`

  ## 示例
      iex> AccountIdentifier.system(:main)
      "system:XAA:main"

      iex> AccountIdentifier.system(:rewards, :USD)
      "system:USD:rewards"
  """
  @spec system(system_account_type(), currency()) :: String.t()
  def system(account_type, currency \\ @default_currency) when is_atom(account_type) do
    "system:#{currency}:#{account_type}"
  end

  @doc """
  生成游戏账户标识符

  ## 参数
  - `game_id`: 游戏ID (string)
  - `currency`: 货币类型，默认为 :XAA

  ## 返回值
  - 游戏账户标识符字符串：`"game:currency:game_id"`

  ## 示例
      iex> AccountIdentifier.game("game_456")
      "game:XAA:game_456"

      iex> AccountIdentifier.game("game_456", :USD)
      "game:USD:game_456"
  """
  @spec game(String.t() | integer(), currency()) :: String.t()
  def game(game_id, currency \\ @default_currency)

  def game(game_id, currency) do
    "game:#{currency}:#{game_id}"
  end

  @doc """
  生成奖池账户标识符

  ## 参数
  - `game_id`: 游戏ID (string | integer)
  - `jackpot_id`: 奖池ID (string | integer)
  - `currency`: 货币类型，默认为 :XAA

  ## 返回值
  - 奖池账户标识符字符串：`"jackpot:currency:game_id:jackpot_id"`

  ## 示例
      iex> AccountIdentifier.jackpot("game_123", "jackpot_456")
      "jackpot:XAA:game_123:jackpot_456"

      iex> AccountIdentifier.jackpot("game_123", "jackpot_456", :USD)
      "jackpot:USD:game_123:jackpot_456"
  """
  @spec jackpot(String.t() | integer(), String.t() | integer(), currency()) :: String.t()
  def jackpot(game_id, jackpot_id, currency \\ @default_currency) do
    "jackpot:#{currency}:#{game_id}:#{jackpot_id}"
  end

  # ========== 缓存键生成 ==========

  @doc """
  生成缓存键标识符

  ## 参数
  - `type`: 账户类型 (:user | :system | :game | :jackpot)
  - `id`: 账户ID (用户ID、系统账户类型、游戏ID)
  - `jackpot_id`: 奖池ID (仅用于 :jackpot 类型)
  # ========== 标识符解析 ==========

  @doc \"""
  解析账户标识符

  ## 参数
  - `identifier`: 账户标识符字符串

  ## 返回值
  - `{:ok, parsed_data}`: 解析成功
  - `{:error, reason}`: 解析失败

  ## 示例
      iex> AccountIdentifier.parse("user:XAA:user_123")
      {:ok, %{type: :user, currency: :XAA, id: "user_123"}}

      iex> AccountIdentifier.parse("system:XAA:main")
      {:ok, %{type: :system, currency: :XAA, account_type: :main}}

      iex> AccountIdentifier.parse("game:XAA:game_456")
      {:ok, %{type: :game, currency: :XAA, id: "game_456"}}

      iex> AccountIdentifier.parse("jackpot:XAA:game_123:jackpot_456")
      {:ok, %{type: :jackpot, currency: :XAA, game_id: "game_123", jackpot_id: "jackpot_456"}}
  """
  @spec parse(String.t()) :: {:ok, map()} | {:error, atom()}
  def parse(identifier) when is_binary(identifier) do
    case String.split(identifier, ":") do
      ["user", currency, user_id] ->
        {:ok, %{type: :user, currency: String.to_atom(currency), id: user_id}}

      ["system", currency, account_type] ->
        {:ok,
         %{
           type: :system,
           currency: String.to_atom(currency),
           account_type: String.to_atom(account_type)
         }}

      ["game", currency, game_id] ->
        {:ok, %{type: :game, currency: String.to_atom(currency), id: game_id}}

      ["jackpot", currency, game_id, jackpot_id] ->
        {:ok,
         %{
           type: :jackpot,
           currency: String.to_atom(currency),
           game_id: game_id,
           jackpot_id: jackpot_id
         }}

      # 向后兼容旧的系统账户格式 system:account_type
      ["system", account_type] ->
        {:ok,
         %{type: :system, currency: @default_currency, account_type: String.to_atom(account_type)}}

      _ ->
        {:error, :invalid_format}
    end
  end

  # 向后兼容的别名
  def parse_identifier(identifier), do: parse(identifier)

  # ========== 标识符验证 ==========

  @doc """
  验证账户标识符格式是否正确

  ## 参数
  - `identifier`: 账户标识符字符串

  ## 返回值
  - `true`: 格式正确
  - `false`: 格式错误

  ## 示例
      iex> AccountIdentifier.valid?("user:XAA:user_123")
      true

      iex> AccountIdentifier.valid?("invalid:format")
      false
  """
  @spec valid?(String.t()) :: boolean()
  def valid?(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, _} -> true
      {:error, _} -> false
    end
  end

  # 向后兼容的别名
  def valid_identifier?(identifier), do: valid?(identifier)
  # ========== 类型提取 ==========

  @doc """
  从账户标识符中提取账户类型

  ## 参数
  - `identifier`: 账户标识符字符串

  ## 返回值
  - `{:ok, account_type}`: 提取成功
  - `{:error, reason}`: 提取失败
  """
  @spec type(String.t()) :: {:ok, account_type()} | {:error, atom()}
  def type(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, %{type: type}} -> {:ok, type}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  从游戏账户标识符中提取游戏ID

  ## 参数
  - `identifier`: 游戏账户标识符字符串

  ## 返回值
  - `{:ok, game_id}`: 提取成功
  - `{:error, reason}`: 提取失败

  ## 示例
      iex> AccountIdentifier.game_id("game:XAA:game_456")
      {:ok, "game_456"}
  """
  @spec game_id(String.t()) :: {:ok, String.t()} | {:error, atom()}
  def game_id(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, %{type: :game, id: game_id}} -> {:ok, game_id}
      {:ok, %{type: other}} -> {:error, {:wrong_type, other}}
      {:error, reason} -> {:error, reason}
    end
  end

  # 向后兼容的别名
  def extract_game_id(identifier), do: game_id(identifier)

  @doc """
  从用户账户标识符中提取用户ID

  ## 参数
  - `identifier`: 用户账户标识符字符串

  ## 返回值
  - `{:ok, user_id}`: 提取成功
  - `{:error, reason}`: 提取失败
  """
  @spec extract_user_id(String.t()) :: {:ok, String.t()} | {:error, atom()}
  def extract_user_id(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, %{type: :user, id: user_id}} -> {:ok, user_id}
      {:ok, %{type: other}} -> {:error, {:wrong_type, other}}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  从奖池账户标识符中提取游戏ID

  ## 参数
  - `identifier`: 奖池账户标识符字符串

  ## 返回值
  - `{:ok, game_id}`: 提取成功
  - `{:error, reason}`: 提取失败

  ## 示例
      iex> AccountIdentifier.jackpot_game_id("jackpot:XAA:game_123:jackpot_456")
      {:ok, "game_123"}
  """
  @spec jackpot_game_id(String.t()) :: {:ok, String.t()} | {:error, atom()}
  def jackpot_game_id(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, %{type: :jackpot, game_id: game_id}} -> {:ok, game_id}
      {:ok, %{type: other}} -> {:error, {:wrong_type, other}}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  从奖池账户标识符中提取奖池ID

  ## 参数
  - `identifier`: 奖池账户标识符字符串

  ## 返回值
  - `{:ok, jackpot_id}`: 提取成功
  - `{:error, reason}`: 提取失败

  ## 示例
      iex> AccountIdentifier.jackpot_id("jackpot:XAA:game_123:jackpot_456")
      {:ok, "jackpot_456"}
  """
  @spec jackpot_id(String.t()) :: {:ok, String.t()} | {:error, atom()}
  def jackpot_id(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, %{type: :jackpot, jackpot_id: jackpot_id}} -> {:ok, jackpot_id}
      {:ok, %{type: other}} -> {:error, {:wrong_type, other}}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  从奖池账户标识符中提取游戏ID和奖池ID

  ## 参数
  - `identifier`: 奖池账户标识符字符串

  ## 返回值
  - `{:ok, {game_id, jackpot_id}}`: 提取成功
  - `{:error, reason}`: 提取失败

  ## 示例
      iex> AccountIdentifier.jackpot_ids("jackpot:XAA:game_123:jackpot_456")
      {:ok, {"game_123", "jackpot_456"}}
  """
  @spec jackpot_ids(String.t()) :: {:ok, {String.t(), String.t()}} | {:error, atom()}
  def jackpot_ids(identifier) when is_binary(identifier) do
    case parse(identifier) do
      {:ok, %{type: :jackpot, game_id: game_id, jackpot_id: jackpot_id}} ->
        {:ok, {game_id, jackpot_id}}

      {:ok, %{type: other}} ->
        {:error, {:wrong_type, other}}

      {:error, reason} ->
        {:error, reason}
    end
  end
end
