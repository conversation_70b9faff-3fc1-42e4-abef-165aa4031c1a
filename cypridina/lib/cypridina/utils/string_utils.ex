defmodule Cypridina.Utils.StringUtils do
  @moduledoc """
  提供字符串处理相关的工具函数
  """

  @doc """
  将字符串转换为下划线格式

  ## 示例

      iex> Cypridina.Utils.StringUtils.to_underscore("HelloWorld")
      "hello_world"
  """
  def to_underscore(str) when is_binary(str) do
    str
    |> String.replace(~r/([A-Z])/, "_\\1")
    |> String.downcase()
    |> String.trim_leading("_")
  end

  @doc """
  将字符串转换为驼峰格式

  ## 示例

      iex> Cypridina.Utils.StringUtils.to_camel_case("hello_world")
      "helloWorld"
  """
  def to_camel_case(str) when is_binary(str) do
    parts = String.split(str, "_")
    first = List.first(parts)
    rest = Enum.map(tl(parts), &String.capitalize/1)

    Enum.join([first | rest], "")
  end

  @doc """
  将字符串截断到指定长度，并添加省略号

  ## 示例

      iex> Cypridina.Utils.StringUtils.truncate("这是一个很长的字符串", 5)
      "这是一个..."
  """
  def truncate(str, max_length, ellipsis \\ "...") when is_binary(str) do
    if String.length(str) > max_length do
      "#{String.slice(str, 0, max_length)}#{ellipsis}"
    else
      str
    end
  end

  @doc """
  格式化手机号码

  移除空格、制表符、换行符等空白字符，并进行基本的格式验证。
  支持国际格式（带国家代码）和本地格式。

  ## 示例

      iex> Cypridina.Utils.StringUtils.normalize_phone(" 91 9516437178 ")
      "919516437178"

      iex> Cypridina.Utils.StringUtils.normalize_phone("  13800138000  ")
      "13800138000"

      iex> Cypridina.Utils.StringUtils.normalize_phone("")
      ""

      iex> Cypridina.Utils.StringUtils.normalize_phone("91-9516-437-178")
      "919516437178"
  """
  def normalize_phone(phone) when is_binary(phone) do
    phone
    |> String.trim()
    |> String.replace(~r/[\s\-\(\)\+]/, "")
    |> String.replace(~r/[^\d]/, "")
  end

  def normalize_phone(nil), do: ""
  def normalize_phone(_), do: ""

  def add_default_country_code(phone, country_code \\ "86") when is_binary(phone) do
    country_code = to_string(country_code)

    cond do
      String.starts_with?(phone, country_code) ->
        phone

      true ->
        "#{country_code}#{phone}"
    end
  end

  @doc """
  验证手机号码格式是否有效

  支持中国大陆手机号（11位，1开头）和印度手机号（10位数字，前面可选91国家代码）

  ## 示例

      iex> Cypridina.Utils.StringUtils.valid_phone?("13800138000")
      true

      iex> Cypridina.Utils.StringUtils.valid_phone?("919516437178")
      true

      iex> Cypridina.Utils.StringUtils.valid_phone?("9516437178")
      true

      iex> Cypridina.Utils.StringUtils.valid_phone?("123")
      false
  """
  def valid_phone?(phone) when is_binary(phone) do
    normalized = normalize_phone(phone)

    cond do
      # 中国大陆手机号：11位，1开头
      String.match?(normalized, ~r/^1\d{10}$/) -> true
      # 印度手机号：10位数字
      String.match?(normalized, ~r/^\d{10}$/) -> true
      # 印度手机号带国家代码：91 + 10位数字
      String.match?(normalized, ~r/^91\d{10}$/) -> true
      # 其他格式暂不支持
      true -> false
    end
  end

  def valid_phone?(_), do: false
end
