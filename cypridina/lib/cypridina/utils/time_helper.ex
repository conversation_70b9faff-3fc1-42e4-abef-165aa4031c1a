defmodule Cypridina.Utils.TimeHelper do
  @moduledoc """
  时间格式化辅助模块

  提供UTC时间转换为本地时间的功能，用于整个应用程序
  """

  @doc """
  将UTC时间转换为本地时间并格式化显示

  ## 参数
  - `utc_datetime` - UTC时间（DateTime结构体）
  - `format` - 格式化字符串，默认为 "%m-%d %H:%M"
  - `timezone` - 目标时区，默认为 "Asia/Shanghai"

  ## 示例
      iex> utc_time = ~U[2024-01-15 10:30:00Z]
      iex> format_local_time(utc_time)
      "01-15 18:30"

      iex> format_local_time(utc_time, "%Y-%m-%d")
      "2024-01-15"
  """
  def format_local_time(utc_datetime, format \\ "%m-%d %H:%M", timezone \\ "Asia/Shanghai")

  def format_local_time(nil, _format, _timezone), do: "-"

  def format_local_time(utc_datetime, format, timezone) when is_struct(utc_datetime, DateTime) do
    # 转换为指定时区的本地时间
    local_datetime = DateTime.shift_zone!(utc_datetime, timezone)

    # 格式化显示
    Calendar.strftime(local_datetime, format)
  end

  def format_local_time(utc_datetime, format, timezone)
      when is_struct(utc_datetime, NaiveDateTime) do
    # 将NaiveDateTime转换为UTC DateTime
    utc_dt = DateTime.from_naive!(utc_datetime, "Etc/UTC")
    format_local_time(utc_dt, format, timezone)
  end

  def format_local_time(other, format, _timezone) do
    # 对于其他类型，尝试直接格式化
    Calendar.strftime(other, format)
  end

  @doc """
  格式化日期显示（年-月-日）
  """
  def format_local_date(utc_datetime, timezone \\ "Asia/Shanghai") do
    format_local_time(utc_datetime, "%Y-%m-%d", timezone)
  end

  @doc """
  格式化日期时间显示（年-月-日 时:分:秒）
  """
  def format_local_datetime(utc_datetime, timezone \\ "Asia/Shanghai") do
    format_local_time(utc_datetime, "%Y-%m-%d %H:%M:%S", timezone)
  end

  @doc """
  格式化简短时间显示（月-日 时:分）
  """
  def format_local_short_time(utc_datetime, timezone \\ "Asia/Shanghai") do
    format_local_time(utc_datetime, "%m-%d %H:%M", timezone)
  end

  @doc """
  获取当前本地时间
  """
  def now_local(timezone \\ "Asia/Shanghai") do
    DateTime.now!(timezone)
  end

  @doc """
  计算时间差（相对于当前时间）
  """
  def time_ago(utc_datetime, timezone \\ "Asia/Shanghai") do
    local_datetime =
      case utc_datetime do
        %DateTime{} ->
          DateTime.shift_zone!(utc_datetime, timezone)

        %NaiveDateTime{} ->
          utc_dt = DateTime.from_naive!(utc_datetime, "Etc/UTC")
          DateTime.shift_zone!(utc_dt, timezone)

        _ ->
          nil
      end

    if local_datetime do
      now = DateTime.now!(timezone)
      diff_seconds = DateTime.diff(now, local_datetime, :second)

      cond do
        diff_seconds < 60 -> "刚刚"
        diff_seconds < 3600 -> "#{div(diff_seconds, 60)}分钟前"
        diff_seconds < 86400 -> "#{div(diff_seconds, 3600)}小时前"
        diff_seconds < 2_592_000 -> "#{div(diff_seconds, 86400)}天前"
        true -> format_local_date(utc_datetime, timezone)
      end
    else
      "未知时间"
    end
  end

  @doc """
  格式化时间为Telegram风格的短时间显示
  今天显示时:分，昨天显示"昨天"，更早显示月-日
  """
  def format_telegram_time(utc_datetime, timezone \\ "Asia/Shanghai") do
    case utc_datetime do
      nil ->
        ""

      _ ->
        local_datetime =
          case utc_datetime do
            %DateTime{} ->
              DateTime.shift_zone!(utc_datetime, timezone)

            %NaiveDateTime{} ->
              utc_dt = DateTime.from_naive!(utc_datetime, "Etc/UTC")
              DateTime.shift_zone!(utc_dt, timezone)

            _ ->
              nil
          end

        if local_datetime do
          now = DateTime.now!(timezone)
          today = DateTime.to_date(now)
          msg_date = DateTime.to_date(local_datetime)

          cond do
            Date.compare(msg_date, today) == :eq ->
              # 今天 - 显示时:分
              Calendar.strftime(local_datetime, "%H:%M")

            Date.diff(today, msg_date) == 1 ->
              # 昨天
              "昨天"

            Date.diff(today, msg_date) <= 7 ->
              # 一周内 - 显示星期
              case Date.day_of_week(msg_date) do
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
              end

            true ->
              # 更早 - 显示月-日
              Calendar.strftime(local_datetime, "%m-%d")
          end
        else
          ""
        end
    end
  end
end
