defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.LongHu.LongHuAI do
  @moduledoc """
  龙虎斗游戏AI机器人模块

  功能：
  - 管理机器人的创建和生命周期
  - 实现智能下注策略
  - 处理机器人行为和个性系统
  - 机器人金币管理和状态维护
  """

  require Logger

  alias Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame
  alias Cypridina.Teen.GameSystem.PlayerDataBuilder

  @doc """
  为房间添加所需的机器人

  ## 参数
  - state: 房间状态
  """
  def add_robots_if_needed(state) do
    current_robot_count = count_robots(state)
    target_robot_count = state.game_data.config.robot_count

    if current_robot_count < target_robot_count do
      needed_robots = target_robot_count - current_robot_count

      Logger.info(
        "🤖 [ADD_ROBOTS] 当前机器人: #{current_robot_count}, 目标: #{target_robot_count}, 需要添加: #{needed_robots}"
      )

      Enum.reduce(1..needed_robots, state, fn i, acc_state ->
        add_single_robot(acc_state, i)
      end)
    else
      state
    end
  end

  @doc """
  动态管理机器人 - 清退低积分机器人并补充新机器人

  ## 参数
  - state: 房间状态
  """
  def manage_robots_dynamically(state) do
    # 1. 清退积分不足的机器人
    state_after_cleanup = cleanup_broke_robots(state)

    # 2. 补充新机器人到目标数量
    state_after_refill = add_robots_if_needed(state_after_cleanup)

    # 3. 随机轮换部分老机器人（保持新鲜感）
    rotate_old_robots(state_after_refill)
  end

  @doc """
  添加单个机器人

  ## 参数
  - state: 房间状态
  - index: 机器人序号
  """
  def add_single_robot(state, index \\ 1) do
    # 使用负数作为机器人ID，基于房间ID、时间戳和序号生成唯一负数
    room_hash = :erlang.phash2(state.id, 1000)
    time_hash = :erlang.phash2(DateTime.utc_now(), 1000)
    robot_id = -(1_000_000 + room_hash * 1000 + time_hash + index)

    # 使用统一配置的机器人名称
    robot_name = LongHuGame.random_robot_name()

    # 使用统一的机器人数据构造方法
    robot_base_data = %{
      numeric_id: robot_id,
      id: robot_id,
      nickname: robot_name,
      # 使用统一配置的头像
      avatar: LongHuGame.random_robot_avatar(),
      # 使用统一配置的积分生成
      points: LongHuGame.generate_robot_initial_money(),
      # 随机等级 1-10
      level: :rand.uniform(10),
      # 机器人个性参数 - 使用统一配置
      # 激进程度 0-1
      aggression: :rand.uniform(),
      # 下注频率 80%-100%
      bet_frequency: 0.8 + :rand.uniform() * 0.2,
      # 偏好区域
      favorite_area: LongHuGame.random_robot_favorite_area(),
      # 下注风格
      bet_style: LongHuGame.random_robot_bet_style(),
      # 风险承受能力
      risk_tolerance: :rand.uniform(),
      # 是否跟随趋势
      follow_trend: :rand.uniform() > 0.5,
      # 学习率 0.05-0.2
      learning_rate: 0.05 + :rand.uniform() * 0.15,
      # 记忆深度 5-15
      memory_depth: 5 + :rand.uniform(10),
      # 创建时间
      created_at: DateTime.utc_now()
    }

    # 调用统一的数据构造方法
    robot_data =
      Cypridina.Teen.GameSystem.PlayerDataBuilder.create_robot_player_data(robot_base_data,
        is_ready: true
      )

    Logger.info(
      "🤖 [ADD_ROBOT] 添加新机器人: #{robot_id} (#{robot_name}) - 积分: #{robot_data.user.points}"
    )

    %{state | players: Map.put(state.players, robot_id, robot_data)}
  end

  @doc """
  为机器人安排下注时间

  ## 参数
  - state: 房间状态
  """
  def schedule_robot_bets(state) do
    # 让机器人在整个下注阶段都保持活跃
    robot_players = Enum.filter(state.players, fn {_id, player} -> player.is_robot end)
    # 转换为毫秒
    bet_time_ms = state.game_data.config.bet_time * 1000

    # 使用统一配置的机器人行为参数
    robot_behavior = LongHuGame.robot_behavior()

    Logger.info(
      "🤖 [SCHEDULE_ROBOT_BETS] 调度 #{length(robot_players)} 个机器人在 #{state.game_data.config.bet_time}秒内积极下注"
    )

    Enum.each(robot_players, fn {robot_id, player} ->
      # 每个机器人都会下注，时间分散到整个下注阶段
      aggression =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :aggression) || 0.5

      bet_style =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :bet_style) ||
          :moderate

      # 第一次下注 - 所有机器人都参与，分散在前1/3时间段
      # 使用统一配置的最小思考时间
      min_think_time = robot_behavior.min_think_time
      first_delay = min_think_time + :rand.uniform(trunc(bet_time_ms / 3))
      Process.send_after(self(), {:robot_bet, robot_id}, first_delay)
      Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第1次下注: #{first_delay}ms")

      # 第二次下注 - 70%的机器人，分散在中间1/3时间段
      if aggression > 0.3 do
        # 5-10秒
        second_delay = trunc(bet_time_ms / 3) + :rand.uniform(trunc(bet_time_ms / 3))
        Process.send_after(self(), {:robot_bet, robot_id}, second_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第2次下注: #{second_delay}ms")
      end

      # 第三次下注 - 激进型机器人，分散在后1/3时间段
      if aggression > 0.7 and bet_style == :aggressive do
        # 10-15秒
        third_delay = trunc(bet_time_ms * 2 / 3) + :rand.uniform(trunc(bet_time_ms / 3))
        Process.send_after(self(), {:robot_bet, robot_id}, third_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第3次下注: #{third_delay}ms")
      end

      # 第四次下注 - 超级激进机器人，在最后阶段
      # 提高概率到40%
      if aggression > 0.9 and :rand.uniform() < 0.4 do
        # 12-15秒
        fourth_delay = trunc(bet_time_ms * 0.8) + :rand.uniform(trunc(bet_time_ms * 0.2))
        Process.send_after(self(), {:robot_bet, robot_id}, fourth_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第4次下注: #{fourth_delay}ms")
      end

      # 额外的随机下注 - 让一些机器人在随机时间再次下注
      # 30%概率
      if :rand.uniform() < 0.3 do
        # 2秒到倒数1秒
        random_delay = 2000 + :rand.uniform(bet_time_ms - 3000)
        Process.send_after(self(), {:robot_bet, robot_id}, random_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 随机下注: #{random_delay}ms")
      end
    end)
  end

  @doc """
  生成智能机器人下注决策

  ## 参数
  - robot_player: 机器人玩家数据
  - state: 游戏状态

  ## 返回
  {area, amount} - 下注区域和金额
  """
  def generate_smart_robot_bet(robot_player, state) do
    history = state.game_data.history
    total_bets = state.game_data.total_bets
    config = state.game_data.config

    # 获取机器人AI数据
    ai_data = Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_data(robot_player)
    aggression = Map.get(ai_data, :aggression, 0.5)
    bet_style = Map.get(ai_data, :bet_style, :moderate)

    # 选择下注区域的策略
    area = choose_robot_bet_area(ai_data, history, total_bets)

    # 使用统一配置的筹码面值
    chip_values = LongHuGame.chip_values()
    amount = calculate_robot_bet_amount(bet_style, aggression, chip_values, config)

    {area, amount}
  end

  @doc """
  选择机器人下注区域

  ## 参数
  - ai_data: 机器人AI数据
  - history: 历史记录
  - total_bets: 当前总下注

  ## 返回
  下注区域原子
  """
  def choose_robot_bet_area(ai_data, history, total_bets) do
    favorite_area = Map.get(ai_data, :favorite_area, :long)
    follow_trend = Map.get(ai_data, :follow_trend, false)
    risk_tolerance = Map.get(ai_data, :risk_tolerance, 0.5)

    # 策略权重 - 基于AI属性调整
    strategy_roll = :rand.uniform(100)

    # 根据风险承受能力调整策略分布
    {favorite_weight, trend_weight, popular_weight, random_weight} =
      if risk_tolerance > 0.7 do
        # 高风险：更多跟随热门和趋势
        {20, 30, 40, 10}
      else
        # 低风险：更多偏好区域
        {40, 20, 30, 10}
      end

    cond do
      # 偏好区域
      strategy_roll <= favorite_weight ->
        favorite_area

      # 跟随趋势（如果机器人有这个特性）
      strategy_roll <= favorite_weight + trend_weight and follow_trend ->
        get_trending_area(history)

      # 跟随热门区域
      strategy_roll <= favorite_weight + trend_weight + popular_weight ->
        get_popular_area(total_bets)

      # 完全随机
      true ->
        Enum.random([:long, :hu, :he])
    end
  end

  @doc """
  计算机器人下注金额 - 更像真人的下注方式

  ## 参数
  - bet_style: 下注风格 (:conservative, :moderate, :aggressive)
  - aggression: 激进程度 (0-1)
  - chip_values: 筹码面值列表
  - config: 游戏配置

  ## 返回
  下注金额
  """
  def calculate_robot_bet_amount(bet_style, aggression, chip_values, config) do
    # 使用统一配置的筹码权重
    chip_weights = LongHuGame.robot_chip_weights()

    # 使用统一配置的下注风格参数
    bet_style_config = LongHuGame.robot_bet_style_config()
    {min_quantity, max_quantity} = Map.get(bet_style_config, bet_style, {2, 5})

    # 根据激进程度调整数量上限
    adjusted_max_quantity = trunc(max_quantity * (0.6 + aggression * 0.4))
    chip_quantity = min_quantity + :rand.uniform(max(1, adjusted_max_quantity - min_quantity))

    # 按权重随机选择一种筹码面值
    selected_chip_value = select_chip_by_weight(chip_values, chip_weights)

    # 计算总下注金额（只使用一种筹码）
    total_amount = selected_chip_value * chip_quantity

    # 确保在游戏限制范围内
    final_amount = max(config.min_bet, min(total_amount, config.max_bet))

    # 如果超出限制，调整为最大允许的该筹码倍数
    if final_amount < total_amount do
      max_quantity_allowed = div(config.max_bet, selected_chip_value)
      selected_chip_value * max(1, max_quantity_allowed)
    else
      final_amount
    end
  end

  @doc """
  按权重选择筹码面值

  ## 参数
  - chip_values: 筹码面值列表
  - chip_weights: 筹码权重映射

  ## 返回
  选中的筹码面值
  """
  defp select_chip_by_weight(chip_values, chip_weights) do
    # 计算总权重
    total_weight =
      chip_values
      |> Enum.map(fn value -> Map.get(chip_weights, value, 1) end)
      |> Enum.sum()

    # 生成随机数
    random_value = :rand.uniform(total_weight)

    # 按权重选择筹码
    {selected_chip, _} =
      chip_values
      |> Enum.reduce_while({nil, 0}, fn chip_value, {_selected, accumulated_weight} ->
        weight = Map.get(chip_weights, chip_value, 1)
        new_accumulated = accumulated_weight + weight

        if random_value <= new_accumulated do
          {:halt, {chip_value, new_accumulated}}
        else
          {:cont, {chip_value, new_accumulated}}
        end
      end)

    # 备用方案
    selected_chip || Enum.random(chip_values)
  end

  @doc """
  更新机器人的虚拟积分

  ## 参数
  - state: 房间状态
  - robot_id: 机器人ID
  - amount_change: 积分变化量（正数为增加，负数为减少）

  ## 返回
  更新后的状态
  """
  def update_robot_money(state, robot_id, amount_change) do
    case Map.get(state.players, robot_id) do
      nil ->
        state

      robot_player ->
        current_points = Cypridina.Teen.GameSystem.PlayerData.get_points(robot_player)
        new_points = max(0, current_points + amount_change)

        # 如果机器人积分不足，让其退出房间
        if new_points <= 0 do
          Logger.info("🤖 [ROBOT_BROKE] 机器人积分耗尽，退出房间: #{robot_id}")
          remove_robot_from_room(state, robot_id)
        else
          # 更新积分和AI统计数据
          updated_robot =
            Cypridina.Teen.GameSystem.PlayerData.set_points(robot_player, new_points)

          # 更新AI统计（如果是结算）
          updated_robot =
            if amount_change != 0 do
              Cypridina.Teen.GameSystem.PlayerDataBuilder.update_robot_stats(
                updated_robot,
                abs(amount_change),
                max(0, amount_change)
              )
            else
              updated_robot
            end

          updated_players = Map.put(state.players, robot_id, updated_robot)
          %{state | players: updated_players}
        end
    end
  end

  @doc """
  移除机器人

  ## 参数
  - state: 房间状态
  - robot_id: 机器人ID

  ## 返回
  更新后的状态
  """
  def remove_robot_from_room(state, robot_id) do
    # 清除机器人的下注
    updated_bets = Map.delete(state.game_data.bets, robot_id)

    # 从玩家列表移除机器人
    updated_players = Map.delete(state.players, robot_id)

    # 重新计算总下注（移除机器人下注后）
    updated_total_bets = calculate_total_bets(updated_bets)

    Logger.info("🤖 [REMOVE_ROBOT] 机器人离开房间: #{robot_id}")

    %{
      state
      | players: updated_players,
        game_data: %{state.game_data | bets: updated_bets, total_bets: updated_total_bets}
    }
  end

  # ==================== 私有辅助函数 ====================

  # 获取当前最热门的下注区域
  defp get_popular_area(total_bets) do
    if map_size(total_bets) == 0 do
      Enum.random([:long, :hu, :he])
    else
      total_bets
      |> Enum.max_by(fn {_area, amount} -> amount end)
      |> elem(0)
    end
  end

  # 基于历史趋势选择区域
  defp get_trending_area(history) do
    if length(history) < 3 do
      Enum.random([:long, :hu, :he])
    else
      # 分析最近3局的结果
      recent_results =
        history
        |> Enum.take(3)
        |> Enum.map(& &1.result)

      # 统计各区域出现次数
      result_counts =
        Enum.reduce(recent_results, %{long: 0, hu: 0, he: 0}, fn result, acc ->
          Map.update(acc, result, 1, &(&1 + 1))
        end)

      # 选择出现次数最少的区域（反向投注策略）
      result_counts
      |> Enum.min_by(fn {_area, count} -> count end)
      |> elem(0)
    end
  end

  @doc """
  清理积分不足的机器人

  ## 参数
  - state: 房间状态
  - min_money: 最低积分要求（默认1000）

  ## 返回
  清理后的状态
  """
  def cleanup_broke_robots(state, min_points \\ nil) do
    # 使用统一配置的最低积分要求
    min_points = min_points || LongHuGame.robot_behavior().min_money_threshold

    broke_robots =
      state.players
      |> Enum.filter(fn {user_id, player} ->
        player.is_robot and
          is_integer(user_id) and user_id < 0 and
          Cypridina.Teen.GameSystem.PlayerData.get_points(player) < min_points
      end)

    if length(broke_robots) > 0 do
      Logger.info("🤖 [CLEANUP_ROBOTS] 清理 #{length(broke_robots)} 个积分不足的机器人")

      Enum.reduce(broke_robots, state, fn {robot_id, _player}, acc_state ->
        remove_robot_from_room(acc_state, robot_id)
      end)
    else
      state
    end
  end

  @doc """
  轮换老机器人

  ## 参数
  - state: 房间状态
  - rotation_probability: 轮换概率（默认0.1，即10%）

  ## 返回
  轮换后的状态
  """
  def rotate_old_robots(state, rotation_probability \\ nil) do
    # 使用统一配置的轮换参数
    robot_behavior = LongHuGame.robot_behavior()
    rotation_probability = rotation_probability || robot_behavior.rotation_probability
    max_lifetime_minutes = robot_behavior.max_lifetime_minutes

    old_robots =
      state.players
      |> Enum.filter(fn {user_id, player} ->
        player.is_robot and
          is_integer(user_id) and user_id < 0 and
          DateTime.diff(
            DateTime.utc_now(),
            Map.get(player.user, :created_at, DateTime.utc_now()),
            :minute
          ) > max_lifetime_minutes
      end)

    robots_to_rotate =
      old_robots
      |> Enum.filter(fn _ -> :rand.uniform() < rotation_probability end)

    if length(robots_to_rotate) > 0 do
      Logger.info("🤖 [ROTATE_ROBOTS] 轮换 #{length(robots_to_rotate)} 个老机器人")

      # 移除老机器人并添加新机器人
      state_after_removal =
        Enum.reduce(robots_to_rotate, state, fn {robot_id, _player}, acc_state ->
          remove_robot_from_room(acc_state, robot_id)
        end)

      # 添加相同数量的新机器人
      Enum.reduce(1..length(robots_to_rotate), state_after_removal, fn i, acc_state ->
        # 使用不同的索引避免ID冲突
        add_single_robot(acc_state, i + 1000)
      end)
    else
      state
    end
  end

  @doc """
  统计当前机器人数量

  ## 参数
  - state: 房间状态

  ## 返回
  机器人数量
  """
  def count_robots(state) do
    state.players
    |> Enum.count(fn {user_id, player} ->
      player.is_robot and is_integer(user_id) and user_id < 0
    end)
  end

  # 注意：generate_robot_initial_money 函数已移至 LongHuGame 统一配置模块

  # 计算总下注
  defp calculate_total_bets(bets) do
    Enum.reduce(bets, %{long: 0, hu: 0, he: 0}, fn {_user_id, user_bets}, acc ->
      Enum.reduce(user_bets, acc, fn {area, amount}, area_acc ->
        Map.update(area_acc, area, amount, &(&1 + amount))
      end)
    end)
  end
end
