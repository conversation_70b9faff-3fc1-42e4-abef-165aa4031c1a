defmodule Cypridina.Teen.GameSystem.Games.LongHu.LongHuLogic do
  @moduledoc """
  龙虎斗游戏逻辑模块

  游戏规则：
  - 使用标准52张扑克牌（不含大小王）
  - 每局发两张牌：龙牌和虎牌
  - 比较牌面大小，A最小(1)，K最大(13)
  - 龙大于虎 -> 龙赢
  - 虎大于龙 -> 虎赢
  - 龙等于虎 -> 和
  """

  require Logger

  # 扑克牌定义
  # 黑桃、红心、方块、梅花
  @suits [:spades, :hearts, :diamonds, :clubs]
  # A, 2-10, J, Q, K
  @ranks [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]

  # 游戏结果
  @results %{
    # 龙赢
    long: :long,
    # 虎赢
    hu: :hu,
    # 和
    he: :he
  }

  @doc """
  创建一副标准扑克牌

  ## 返回
  包含52张牌的列表，每张牌格式为 %{suit: :spades, rank: 1, value: 1}
  """
  def create_deck() do
    for suit <- @suits, rank <- @ranks do
      %{
        suit: suit,
        rank: rank,
        # 牌面值，用于比较大小
        value: rank
      }
    end
  end

  @doc """
  洗牌

  ## 参数
  - deck: 牌组

  ## 返回
  洗牌后的牌组
  """
  def shuffle_deck(deck) do
    Enum.shuffle(deck)
  end

  @doc """
  发牌 - 发出龙牌和虎牌

  ## 返回
  {龙牌, 虎牌} 元组
  """
  def deal_cards() do
    deck = create_deck() |> shuffle_deck()

    [long_card, hu_card | _rest] = deck

    Logger.info("🐉 [LONGHU_LOGIC] 发牌: 龙=#{format_card(long_card)}, 虎=#{format_card(hu_card)}")

    {long_card, hu_card}
  end

  @doc """
  计算游戏结果

  ## 参数
  - long_card: 龙牌
  - hu_card: 虎牌

  ## 返回
  :long | :hu | :he
  """
  def calculate_result(long_card, hu_card) do
    long_value = long_card.value
    hu_value = hu_card.value

    result =
      cond do
        long_value > hu_value -> @results.long
        hu_value > long_value -> @results.hu
        long_value == hu_value -> @results.he
      end

    Logger.info("🐉 [LONGHU_LOGIC] 游戏结果: 龙=#{long_value}, 虎=#{hu_value} -> #{result}")

    result
  end

  @doc """
  格式化扑克牌显示

  ## 参数
  - card: 扑克牌 %{suit: :spades, rank: 1, value: 1}

  ## 返回
  格式化的字符串，如 "♠A", "♥K"
  """
  def format_card(%{suit: suit, rank: rank}) do
    suit_symbol =
      case suit do
        :spades -> "♠"
        :hearts -> "♥"
        :diamonds -> "♦"
        :clubs -> "♣"
      end

    rank_symbol =
      case rank do
        1 -> "A"
        11 -> "J"
        12 -> "Q"
        13 -> "K"
        n -> to_string(n)
      end

    "#{suit_symbol}#{rank_symbol}"
  end
end
