defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.LongHu.LongHuDataFormatter do
  @moduledoc """
  龙虎斗游戏数据格式化器

  负责将游戏内部数据格式化为客户端期望的格式
  """

  alias Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame
  import Bitwise

  @doc """
  计算剩余下注限制
  """
  def calculate_remaining_bet_limits(state) do
    area_limits = state.game_data.config.area_bet_limits
    total_bets = state.game_data.total_bets

    %{
      "1" => max(0, Map.get(area_limits, :long, 100_000) - Map.get(total_bets, :long, 0)),
      "2" => max(0, Map.get(area_limits, :hu, 100_000) - Map.get(total_bets, :hu, 0)),
      "3" => max(0, Map.get(area_limits, :he, 50000) - Map.get(total_bets, :he, 0))
    }
  end

  @doc """
  格式化游戏状态为客户端格式
  """
  def format_game_state(state) do
    remaining_time = calculate_remaining_time(state)
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    case state.game_data.phase do
      :betting ->
        %{
          "state" => 1,
          "round" => state.game_data.round,
          "bet_time" => state.game_data.config.bet_time,
          "remaining_time" => remaining_time,
          "total_bets" => state.game_data.total_bets,
          "leftbet" => remaining_bet_limits
        }

      :dealing ->
        %{
          "state" => 2,
          "round" => state.game_data.round,
          "deal_time" => state.game_data.config.deal_time,
          "remaining_time" => remaining_time
        }

      :revealing ->
        %{
          "state" => 2,
          "round" => state.game_data.round,
          "reveal_time" => state.game_data.config.reveal_time,
          "remaining_time" => remaining_time
        }

      :settling ->
        %{
          "state" => 3,
          "round" => state.game_data.round,
          "settle_time" => state.game_data.config.settle_time,
          "remaining_time" => remaining_time
        }

      :waiting ->
        %{
          "state" => 3,
          "round" => state.game_data.round,
          "next_round_delay" => 2000,
          "remaining_time" => remaining_time
        }

      _ ->
        %{
          "state" => 0,
          "round" => state.game_data.round,
          "remaining_time" => 0
        }
    end
  end

  @doc """
  将区域转换为客户端期望的direction
  """
  def area_to_direction(area) do
    bet_areas = LongHuGame.bet_areas()

    case area do
      area when area in ["long", :long] -> bet_areas.long
      area when area in ["hu", :hu] -> bet_areas.hu
      area when area in ["he", :he] -> bet_areas.he
      _ -> bet_areas.long
    end
  end

  @doc """
  编码下注信息为客户端期望的位运算格式
  """
  def encode_bet_for_sync(amount, area) do
    chip_values = LongHuGame.chip_values()

    {encoded_long, encoded_hu, encoded_he} =
      case area do
        area when area in [:long, "long"] -> {encode_chips(amount, chip_values), 0, 0}
        area when area in [:hu, "hu"] -> {0, encode_chips(amount, chip_values), 0}
        area when area in [:he, "he"] -> {0, 0, encode_chips(amount, chip_values)}
        _ -> {encode_chips(amount, chip_values), 0, 0}
      end

    %{
      "long" => encoded_long,
      "hu" => encoded_hu,
      "he" => encoded_he,
      "dirctionall" => amount
    }
  end

  @doc """
  格式化牌面数据为客户端格式
  """
  def format_card(card) do
    case card do
      nil -> %{"suit" => 0, "value" => 0}
      %{suit: suit, value: value} -> %{"suit" => suit, "value" => value}
      _ -> %{"suit" => 0, "value" => 0}
    end
  end

  @doc """
  格式化结果为客户端格式
  """
  def format_result(result) do
    case result do
      :long -> 1
      :hu -> 2
      :he -> 3
      _ -> 1
    end
  end

  # 私有函数

  defp calculate_remaining_time(state) do
    case state.game_data.phase_timer do
      nil ->
        0

      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> 0
          time_left -> div(time_left, 1000)
        end
    end
  end

  defp encode_chips(amount, chip_values) do
    {encoded, _remaining} =
      Enum.reduce(chip_values, {0, amount}, fn chip_value, {acc_encoded, remaining} ->
        if remaining >= chip_value do
          chip_count = min(div(remaining, chip_value), 15)
          used_amount = chip_count * chip_value
          new_remaining = remaining - used_amount
          shift_bits = LongHuGame.chip_bit_shift(chip_value)
          new_encoded = Bitwise.bor(acc_encoded, Bitwise.bsl(chip_count, shift_bits))
          {new_encoded, new_remaining}
        else
          {acc_encoded, remaining}
        end
      end)

    encoded
  end
end
