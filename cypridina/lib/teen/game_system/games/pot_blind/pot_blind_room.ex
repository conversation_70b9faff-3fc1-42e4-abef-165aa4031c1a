defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.PotBlind.PotBlindRoom do
  @moduledoc """
  PotBlind游戏房间模块 - 基于前端 TPPBDefine.ts 重新实现

  ## 协议定义 (参考前端 TPPBDefine.ts)
  - SC_TEENPATTI_START_P = 1000        # 开始
  - SC_TEENPATTI_SENDCARD_P = 1001     # 发牌
  - CS_TEENPATTI_BET_P = 1002          # 下注
  - SC_TEENPATTI_BET_P = 1003          # 下注
  - CS_TEENPATTI_COMPETITION_P = 1004  # 比牌
  - SC_TEENPATTI_COMPETITION_P = 1005  # 比牌
  - CS_TEENPATTI_FOLD_P = 1006         # 弃牌
  - SC_TEENPATTI_FOLD_P = 1007         # 弃牌
  - SC_TEENPATTI_WAITOPT_P = 1010      # 等待操作
  - SC_TEENPATTI_JIESHUAN_P = 1011     # 结算

  ## 游戏状态 (参考前端 GameState)
  - EM_TEENPATTI_GAMESTATE_START = 0       # 游戏开始
  - EM_TEENPATTI_GAMESTATE_SENDCARD = 1    # 发牌状态
  - EM_TEENPATTI_GAMESTATE_BET = 2         # 下注状态
  - EM_TEENPATTI_GAMESTATE_COMPETITION = 3 # 比牌状态
  - EM_TEENPATTI_GAMESTATE_END = 4         # 结束状态

  ## 玩家状态 (参考前端 EM_TEENPATTI_PLAYERSTATE)
  - EM_TEENPATTI_PLAYER_NONE = 0  # 无效状态
  - EM_TEENPATTI_PLAYER_PLAY = 1  # 游戏状态
  - EM_TEENPATTI_PLAYER_FOLD = 2  # 弃牌状态
  - EM_TEENPATTI_PLAYER_LOSE = 3  # 输牌状态
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :pot_blind

  require Logger

  # 引入游戏逻辑模块
  alias Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindGameLogic
  alias Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindRobotManager

  # 导入玩家数据结构
  alias Cypridina.Teen.GameSystem.PlayerData

  # ==================== 常量定义 (参考前端 TPPBDefine.ts) ====================

  # 协议号定义 (mainId=5)
  @protocol_main_id 5
  @protocols %{
    # 服务端发送协议
    # 断线重连/房间信息
    sc_to_other_room: 0,
    # 开始游戏
    game_start: 1000,
    # 发牌
    sc_sendcard: 1001,
    # 下注结果
    sc_bet: 1003,
    # 比牌结果
    sc_competition: 1005,
    # 弃牌结果
    sc_fold: 1007,
    # 轮次开始/等待操作
    turn_start: 1010,
    # 结算
    sc_jieshuan: 1011,

    # 客户端发送协议
    # 下注请求
    cs_bet: 1002,
    # 比牌请求
    cs_competition: 1004,
    # 弃牌请求
    cs_fold: 1006
  }

  # 游戏状态 (参考前端 GameState)
  @game_states %{
    # EM_TEENPATTI_GAMESTATE_START
    start: 0,
    # EM_TEENPATTI_GAMESTATE_SENDCARD
    sendcard: 1,
    # EM_TEENPATTI_GAMESTATE_BET
    bet: 2,
    # EM_TEENPATTI_GAMESTATE_COMPETITION
    competition: 3,
    # EM_TEENPATTI_GAMESTATE_END
    end: 4,
    # 比牌阶段，使用与competition相同的状态值
    showdown: 3
  }

  # 玩家状态 (参考前端 EM_TEENPATTI_PLAYERSTATE)
  @player_states %{
    # EM_TEENPATTI_PLAYER_NONE
    none: 0,
    # EM_TEENPATTI_PLAYER_PLAY
    play: 1,
    # EM_TEENPATTI_PLAYER_FOLD
    fold: 2,
    # EM_TEENPATTI_PLAYER_LOSE
    lose: 3
  }

  # 下注类型 (参考前端 BetType)
  @bet_types %{
    # 底注
    bottom: 0,
    # 下注
    fill: 1,
    # 比牌
    competition: 2,
    # 弃牌
    fold: 3
  }

  # PotBlind 下注倍数配置 (AddBeiLv)
  # 下注倍数数组，fill值对应索引
  @bet_multipliers [1, 2, 4, 8]

  # PotBlind 游戏配置
  @default_config %{
    # 底注
    difen: 100,
    # 最小玩家数
    min_players: 2,
    # 最大玩家数
    max_players: 5,
    # 奖池限制
    pot_limit: 61440,
    # 盲注限制
    blind_limit: 3840,
    # 盲注轮数限制
    blind_round_limit: 1,
    # 最小比牌轮数
    comp_min_turn_num: 3,
    # 操作等待时间 (毫秒)
    oper_wait_time: 13000
  }

  # 房间状态继承自 RoomBase，无需重复定义

  # ==================== RoomBase 回调函数实现 ====================

  @doc """
  玩家加入房间
  """
  @impl true
  def on_player_joined(state, player) do
    Logger.info("🚀 [POT_BLIND] 玩家加入房间: #{player.numeric_id}")

    # 1. 分配座位
    new_state = assign_seat_to_player(state, player)

    # 2. 获取更新后的游戏玩家数据
    game_player = Map.get(new_state.players, player.numeric_id, player)

    # 3. 发送房间信息协议 [4][2] 给新加入的玩家
    send_room_info_to_player(new_state, game_player)

    # # 4. 发送游戏房间信息协议 [5][0] 给新加入的玩家 (断线重连用)
    # send_to_other_room_to_player(new_state, game_player)

    # 5. 广播玩家进入协议 [4][12] 给房间内其他玩家
    broadcast_player_enter(new_state, game_player)

    # 6. 发送玩家状态协议 [4][4] 给新加入的玩家
    send_player_state_to_player(new_state, game_player)

    # 7. 广播房间状态协议 [4][5] 给所有玩家
    broadcast_pot_blind_room_state(new_state)

    # 8. 🤖 检查是否需要添加机器人
    robot_state = maybe_add_robots_for_player(new_state, game_player)

    # 9. 检查PotBlind游戏开始条件
    final_state = check_pot_blind_start(robot_state)

    final_state
  end

  @doc """
  玩家重连
  """
  @impl true
  def on_player_rejoined(state, player) do
    Logger.info("🔄 [POT_BLIND] 玩家重连: #{player.numeric_id}")

    # 获取重连玩家的游戏数据
    game_player = Map.get(state.players, player.numeric_id, player)

    # 1. 发送房间信息协议 [4][2] - 基础房间配置
    send_room_info_to_player(state, game_player)

    # 2. 发送游戏房间信息协议 [5][0] - 完整游戏状态 (包含所有必要数据)
    send_to_other_room_to_player(state, game_player)

    Logger.info("✅ [POT_BLIND] 玩家重连完成: #{player.numeric_id} - 已发送 [4][2] 和 [5][0] 协议")

    state
  end

  @doc """
  玩家离开房间
  """
  @impl true
  def on_player_left(state, player) do
    Logger.info("👋 [POT_BLIND] 玩家离开房间: #{player.numeric_id}")

    # 获取离开玩家的游戏数据
    game_player = Map.get(state.players, player.numeric_id)

    if game_player do
      seat_id = Map.get(game_player, :seatid)
      Logger.info("🪑 [POT_BLIND] 玩家 #{player.numeric_id} 释放座位 #{seat_id}")

      # 1. 广播玩家离开协议 [4][14]
      broadcast_pot_blind_player_quit(state, game_player)

      # 2. 如果玩家在游戏中，处理游戏逻辑
      updated_state =
        if Map.get(game_player, :is_gaming, 0) == 1 do
          handle_player_quit_during_game(state, game_player)
        else
          state
        end

      # 3. 从玩家列表中移除玩家 (座位自动释放)
      final_state = %{
        updated_state
        | players: Map.delete(updated_state.players, player.numeric_id)
      }

      # 4. 验证座位分配状态
      validate_seat_allocation(final_state)
      seat_info = get_seat_allocation_info(final_state)
      Logger.info("🪑 [POT_BLIND] 玩家离开后座位状态: #{inspect(seat_info)}")

      # 5. 广播房间状态变化 [4][5] (人数变化)
      broadcast_pot_blind_room_state(final_state)

      # 6. 取消下一局游戏定时器（如果人数不足）
      remaining_count = map_size(final_state.players)
      min_players = final_state.config[:min_players] || 2

      timer_checked_state =
        if remaining_count < min_players do
          Logger.info("⏰ [POT_BLIND] 玩家离开后人数不足，取消相关定时器")

          final_state
          |> cancel_next_game_timer()
          |> cancel_competition_animation_timer()
          |> cancel_player_away_timers(player.numeric_id)
        else
          # 即使人数足够，也要取消离开玩家的暂离定时器
          cancel_player_away_timers(final_state, player.numeric_id)
        end

      # 7. 检查PotBlind游戏开始条件 (如果人数不足，取消定时器)
      start_checked_state = check_pot_blind_start(timer_checked_state)

      # 8. 检查游戏结束条件
      check_game_end_conditions(start_checked_state)
    else
      Logger.warning("⚠️ [POT_BLIND] 玩家 #{player.numeric_id} 不在房间中，无法处理离开")
      state
    end
  end

  # ==================== 核心游戏流程 (参照C++流程) ====================

  # 我们自己的游戏开始函数 - 不受room_base约束
  defp begin_pot_blind_game(state) do
    Logger.info("🎮 [POT_BLIND] BeginPotBlindGame() - 房间ID: #{state.id}")

    # 1. 初始化游戏数据
    game_state = initialize_game_data(state)

    # 2. 确定庄家
    new_state = assign_dealer(game_state)

    # 3. 发牌给所有玩家
    dealt_state = deal_cards_to_all_players(new_state)

    # 4. 设置玩家游戏状态
    playing_state = set_all_players_gaming(dealt_state)

    # 5. 更新房间状态为游戏中
    gaming_state = %{playing_state | room_state: :playing}

    # 6. 广播房间状态变化 [4][5]
    broadcast_pot_blind_room_state(gaming_state)

    # 7. 广播游戏开始协议 [5][1000]
    broadcast_game_start(gaming_state)

    # 8. 收取盲注
    blind_state = collect_blind_bets(gaming_state)

    # 9. 发牌并开始第一轮，确保返回状态
    final_state = deal_cards_and_start_betting(blind_state)
    final_state
  end

  @doc """
  处理游戏消息
  """
  @impl true
  def handle_game_message(state, player, message) do
    Logger.info("📨 [POT_BLIND] 收到游戏消息: #{inspect(message)}")

    # 统一处理data字段
    normalized_message =
      case message do
        %{"data" => _} -> message
        _ -> Map.put(message, "data", %{})
      end

    case normalized_message do
      %{"mainId" => 4, "subId" => sub_id} = msg ->
        handle_room_protocol(state, player, sub_id, msg)

      %{"mainId" => 5, "subId" => sub_id} = msg ->
        handle_game_protocol(state, player, sub_id, msg)

      _ ->
        Logger.warning("❌ [POT_BLIND] 未知消息格式: #{inspect(message)}")
        state
    end
  end

  # ==================== 协议处理函数 ====================

  # 处理房间协议 (mainId=4)
  defp handle_room_protocol(state, player, sub_id, message) do
    case sub_id do
      # 玩家主动退出请求
      40 ->
        handle_player_quit_request(state, player, message)

      # 🚫 暂离功能已屏蔽
      # 10 -> handle_player_away_return(state, player, message)  # 玩家暂离回复

      _ ->
        Logger.warning("❌ [POT_BLIND] 未知房间协议: mainId=4, subId=#{sub_id}")
        state
    end
  end

  # 处理游戏协议 (mainId=5)
  defp handle_game_protocol(state, player, sub_id, message) do
    case sub_id do
      # 下注请求
      1002 ->
        handle_bet_request(state, player, message)

      # 比牌请求
      1004 ->
        handle_competition_request(state, player, message)

      # 弃牌请求
      1006 ->
        handle_fold_request(state, player, message)

      _ ->
        Logger.warning("❌ [POT_BLIND] 未知游戏协议: mainId=5, subId=#{sub_id}")
        state
    end
  end

  # 处理玩家主动退出请求 [4][40]
  defp handle_player_quit_request(state, player, message) do
    Logger.info("🚪 [POT_BLIND] 处理玩家主动退出请求: #{player.numeric_id}")

    data = message["data"] || %{}

    # 获取退出玩家的游戏数据
    game_player = Map.get(state.players, player.numeric_id)

    if game_player do
      # 发送退出确认响应给玩家 [4][40]
      send_quit_response(state, player)

      # 处理玩家退出逻辑
      handle_player_voluntary_quit(state, game_player)
    else
      Logger.warning("⚠️ [POT_BLIND] 玩家 #{player.numeric_id} 不在房间中，无法处理退出请求")
      state
    end
  end

  # 处理下注请求 - 调用Bet()
  defp handle_bet_request(state, player, message) do
    data = message["data"] || %{}
    fill_value = Map.get(data, "fill", 1)
    bet(state, player, fill_value)
  end

  # Bet() - 下注
  defp bet(state, player, fill_value) do
    current_player_id = Map.get(state, :current_player_id)
    Logger.info("💰 [POT_BLIND] Bet() - 玩家: #{player.numeric_id}, fill: #{fill_value}")

    Logger.info(
      "🔍 [POT_BLIND] 当前玩家ID: #{inspect(current_player_id)}, 请求玩家ID: #{player.numeric_id}"
    )

    # 验证是否轮到该玩家行动
    if current_player_id != player.numeric_id do
      Logger.warning(
        "❌ [POT_BLIND] 不是玩家 #{player.numeric_id} 的回合，当前玩家: #{inspect(current_player_id)}"
      )

      state
    else
      # 取消当前玩家的行动定时器
      cancel_previous_timer(state)

      # 根据PotBlind游戏规则处理下注
      case process_pot_blind_bet(state, player, fill_value) do
        {:ok, updated_state} ->
          # 下注成功，process_pot_blind_bet 已经处理了轮次切换
          updated_state

        {:error, reason} ->
          Logger.warning("❌ [POT_BLIND] 下注处理失败: #{reason}")
          # 发送错误响应给玩家
          send_bet_error(state, player, reason)
          # 重新启动当前玩家的定时器
          start_action_timer(state)
      end
    end
  end

  # Competition() - 比牌
  defp competition(state, player, target_player_id) do
    Logger.info("⚔️ [POT_BLIND] Competition() - 玩家: #{player.numeric_id} vs #{target_player_id}")

    # 验证是否轮到该玩家行动
    if Map.get(state, :current_player_id) != player.numeric_id do
      Logger.warning("❌ [POT_BLIND] 不是玩家 #{player.numeric_id} 的回合")
      state
    else
      # 取消当前玩家的行动定时器
      cancel_previous_timer(state)

      case validate_competition_request(state, player, target_player_id) do
        {:ok, target_player} ->
          # 执行比牌
          execute_competition(state, player, target_player)

        {:error, reason} ->
          Logger.warning("❌ [POT_BLIND] 比牌验证失败: #{reason}")
          # 发送错误响应给玩家
          send_competition_error(state, player, reason)
          # 重新启动当前玩家的定时器
          start_action_timer(state)
      end
    end
  end

  # 处理比牌请求 - 调用Competition()
  defp handle_competition_request(state, player, message) do
    data = message["data"] || %{}
    target_player_id = Map.get(data, "target_player_id")

    # 如果没有指定比牌目标，自动选择上家
    final_target_id =
      if is_nil(target_player_id) do
        find_previous_active_player(state, player.numeric_id)
      else
        target_player_id
      end

    competition(state, player, final_target_id)
  end

  # 处理弃牌请求 - 调用Fold()
  defp handle_fold_request(state, player, message) do
    fold(state, player)
  end

  # Fold() - 弃牌
  defp fold(state, player) do
    Logger.info("🚫 [POT_BLIND] Fold() - 玩家: #{player.numeric_id}")

    # 验证是否轮到该玩家行动
    if Map.get(state, :current_player_id) != player.numeric_id do
      Logger.warning("❌ [POT_BLIND] 不是玩家 #{player.numeric_id} 的回合")
      state
    else
      # 取消当前玩家的行动定时器
      cancel_previous_timer(state)

      # 设置玩家弃牌状态
      fold_state = set_player_fold(state, player.numeric_id)

      # 广播弃牌结果
      broadcast_fold_result(fold_state, player)

      # 检查游戏是否结束
      case check_game_end_condition(fold_state) do
        {:game_end, winner_id} ->
          # 游戏结束，进行结算
          end_game_with_winner(fold_state, winner_id)

        :continue ->
          # 游戏继续，切换到下一个玩家
          Logger.info("🔄 [POT_BLIND] SetNextSeatID() - 切换轮次4")

          set_next_seat_id(fold_state)
      end
    end
  end

  # SetNextSeatID() - 轮次切换
  defp set_next_seat_id(state) do
    Logger.info("🔄 [POT_BLIND] SetNextSeatID() - 切换轮次")

    # 获取下一个活跃玩家
    next_player_id = get_next_active_player(state, Map.get(state, :current_player_id))

    case next_player_id do
      nil ->
        # 没有下一个玩家，检查游戏结束条件
        Logger.info("🔚 [POT_BLIND] 没有下一个活跃玩家，检查游戏结束条件")
        check_round_end(state)

      player_id ->
        # 切换到下一个玩家
        next_state = %{
          state
          | current_player_id: player_id,
            turn_start_time: System.system_time(:millisecond)
        }

        # 检查是否回到第一个玩家（完成一轮）
        dealer_id = Map.get(state, :dealer_id)

        first_player_id =
          dealer_id ||
            case get_players_by_seat_order(state) do
              [{first_id, _} | _] -> first_id
              [] -> nil
            end

        # 检查是否回到第一个玩家（完成一轮）并且是第二轮开始时发牌
        updated_state =
          if player_id == first_player_id do
            # 完成了一轮，增加轮次计数
            current_turn = Map.get(state, :turn_num, 1)
            new_turn = current_turn + 1

            turn_updated_state =
              next_state
              |> Map.put(:turn_num, new_turn)
              # 同时更新:turn字段，用于1010协议的turnnum字段
              |> Map.put(:turn, new_turn)

            # 参照C++: if (GetTurnNum() == 2) // 第一轮结束发手牌
            if new_turn == 2 do
              Logger.info("🃏 [POT_BLIND] 第一轮结束，第二轮开始，发送发牌协议 [5][1001] (TurnNum=#{new_turn})")
              broadcast_deal_cards(turn_updated_state)
              turn_updated_state
            else
              Logger.info("🔄 [POT_BLIND] 完成一轮，当前轮次: #{new_turn}")
              turn_updated_state
            end
          else
            next_state
          end

        # 广播轮次开始
        broadcast_turn_start(updated_state)

        # 🤖 检查是否为机器人，如果是则触发机器人行动
        robot_state = PotBlindRobotManager.maybe_trigger_robot_turn(updated_state)

        # 启动定时器
        start_action_timer(robot_state)
    end
  end

  # ==================== 辅助函数 ====================

  # 获取协议号
  defp get_protocol(key), do: Map.get(@protocols, key)

  # 获取游戏状态码
  defp get_game_state(key), do: Map.get(@game_states, key)

  # 获取玩家状态码
  defp get_player_state(key), do: Map.get(@player_states, key)

  # 找到指定玩家的上家（活跃玩家）
  defp find_previous_active_player(state, current_player_id) do
    Logger.info("🔍 [POT_BLIND] 寻找玩家 #{current_player_id} 的上家")

    # 获取按座位号排序的玩家列表
    players_by_seat = get_players_by_seat_order(state)

    # 找到当前玩家在列表中的位置
    current_index =
      Enum.find_index(players_by_seat, fn {player_id, _} ->
        player_id == current_player_id
      end)

    case current_index do
      nil ->
        Logger.warning("❌ [POT_BLIND] 找不到当前玩家 #{current_player_id}")
        nil

      index ->
        # 从当前玩家的前一个位置开始向前查找活跃玩家
        find_previous_active_recursive(state, players_by_seat, index, length(players_by_seat))
    end
  end

  # 递归查找上家活跃玩家
  defp find_previous_active_recursive(_state, _players_list, _current_index, 0) do
    # 已经查找了所有玩家，没有找到活跃的上家
    Logger.warning("❌ [POT_BLIND] 没有找到活跃的上家玩家")
    nil
  end

  defp find_previous_active_recursive(state, players_list, current_index, remaining_checks) do
    # 计算上一个玩家的索引（循环）
    prev_index =
      if current_index == 0 do
        length(players_list) - 1
      else
        current_index - 1
      end

    case Enum.at(players_list, prev_index) do
      {player_id, _seat} ->
        player = Map.get(state.players, player_id)

        # 检查玩家是否活跃（未弃牌且在游戏中）
        if player && !Map.get(player, :is_fold, false) do
          Logger.info("✅ [POT_BLIND] 找到上家活跃玩家: #{player_id}")
          player_id
        else
          Logger.info("⏭️ [POT_BLIND] 玩家 #{player_id} 已弃牌或不活跃，继续查找")
          # 继续查找上上家
          find_previous_active_recursive(state, players_list, prev_index, remaining_checks - 1)
        end

      nil ->
        Logger.warning("❌ [POT_BLIND] 无效的玩家索引: #{prev_index}")
        nil
    end
  end

  # ==================== 玩家数据访问函数 ====================

  @doc """
  获取指定玩家的数据
  """
  def get_game_player(state, numeric_id) do
    Map.get(state.players, numeric_id)
  end

  @doc """
  获取所有玩家的数据
  """
  def get_all_players(state) do
    state.players
  end

  @doc """
  获取其他玩家的数据（排除指定玩家）
  """
  def get_other_players(state, exclude_numeric_id) do
    state.players
    |> Enum.filter(fn {numeric_id, _player} -> numeric_id != exclude_numeric_id end)
    |> Enum.into(%{})
  end

  @doc """
  获取在游戏中的玩家（未弃牌的玩家）
  """
  def get_active_players(state) do
    state.players
    |> Enum.filter(fn {_id, player} ->
      not Map.get(player, :is_fold, false) and
        Map.get(player, :player_state, 0) == get_player_state(:play)
    end)
    |> Enum.into(%{})
  end

  @doc """
  获取已弃牌的玩家
  """
  def get_folded_players(state) do
    state.players
    |> Enum.filter(fn {_id, player} ->
      Map.get(player, :is_fold, false) or
        Map.get(player, :player_state, 0) == get_player_state(:fold)
    end)
    |> Enum.into(%{})
  end

  @doc """
  按座位号排序获取玩家列表
  """
  def get_players_by_seat_order(state) do
    state.players
    |> Enum.sort_by(fn {_id, player} -> Map.get(player, :seatid, 999) end)
  end

  # ==================== 玩家状态更新函数 ====================

  @doc """
  更新玩家的下注信息
  """
  def update_player_bet(state, numeric_id, bet_amount, bet_type \\ :bet) do
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{numeric_id} 不在房间中")
        state

      player ->
        updated_player = %{
          player
          | bet_amount: bet_amount,
            total_bet: Map.get(player, :total_bet, 0) + bet_amount,
            current_bet: bet_amount,
            last_bet_type: bet_type,
            last_bet_amount: bet_amount,
            last_bet_at: System.system_time(:millisecond),
            last_action_at: System.system_time(:millisecond),
            has_acted: true
        }

        updated_players = Map.put(state.players, numeric_id, updated_player)
        Logger.info("💰 [POT_BLIND] 玩家 #{numeric_id} 下注: #{bet_amount}, 类型: #{bet_type}")
        %{state | players: updated_players}
    end
  end

  @doc """
  设置玩家弃牌状态
  """
  def set_player_fold(state, numeric_id) do
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{numeric_id} 不在房间中")
        state

      player ->
        updated_player = %{
          player
          | is_fold: true,
            player_state: get_player_state(:fold),
            status: get_player_state(:fold),
            state: get_player_state(:fold),
            last_bet_type: :fold,
            last_action_at: System.system_time(:millisecond),
            has_acted: true
        }

        updated_players = Map.put(state.players, numeric_id, updated_player)
        Logger.info("🚫 [POT_BLIND] 玩家 #{numeric_id} 弃牌")
        %{state | players: updated_players}
    end
  end

  @doc """
  设置玩家看牌状态
  """
  def set_player_seen_cards(state, numeric_id) do
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{numeric_id} 不在房间中")
        state

      player ->
        updated_player = %{
          player
          | is_seen_cards: true,
            is_gaming: 1,
            last_action_at: System.system_time(:millisecond)
        }

        updated_players = Map.put(state.players, numeric_id, updated_player)
        Logger.info("👁️ [POT_BLIND] 玩家 #{numeric_id} 看牌")
        %{state | players: updated_players}
    end
  end

  @doc """
  发牌给玩家
  """
  def deal_cards_to_player(state, numeric_id, cards) do
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{numeric_id} 不在房间中")
        state

      player ->
        updated_player = %{
          player
          | cards: cards,
            is_gaming: 1,
            last_action_at: System.system_time(:millisecond)
        }

        updated_players = Map.put(state.players, numeric_id, updated_player)
        Logger.info("🃏 [POT_BLIND] 给玩家 #{numeric_id} 发牌: #{length(cards)} 张")
        %{state | players: updated_players}
    end
  end

  @doc """
  重置玩家的轮次状态（新一轮开始时调用）
  """
  def reset_player_round_state(state, numeric_id) do
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{numeric_id} 不在房间中")
        state

      player ->
        updated_player = %{
          player
          | bet_amount: 0,
            current_bet: 0,
            has_acted: false,
            last_bet_type: :none,
            last_bet_amount: 0,
            last_action_at: System.system_time(:millisecond)
        }

        updated_players = Map.put(state.players, numeric_id, updated_player)
        %{state | players: updated_players}
    end
  end

  # 发送游戏协议消息给指定玩家 (mainId=5)
  defp send_game_message_to_player(state, player, sub_id, data) do
    message = %{
      "mainId" => @protocol_main_id,
      "subId" => sub_id,
      "data" => data
    }

    send_to_player(state, player, message)
  end

  # ==================== 玩家加入相关函数 ====================

  # 分配座位给玩家并初始化玩家游戏状态
  defp assign_seat_to_player(state, player) do
    # 验证当前座位分配状态
    validate_seat_allocation(state)

    # 查找下一个可用座位
    available_seat = find_next_available_seat(state)

    case available_seat do
      nil ->
        Logger.warning("❌ [POT_BLIND] 没有可用座位，玩家 #{player.numeric_id} 无法加入")
        seat_info = get_seat_allocation_info(state)
        Logger.warning("❌ [POT_BLIND] 当前座位状态: #{inspect(seat_info)}")
        state

      seat_id ->
        # 为 PlayerData 结构体添加游戏特定字段
        game_player = add_game_fields_to_player(player, seat_id)
        updated_players = Map.put(state.players, player.numeric_id, game_player)
        updated_state = %{state | players: updated_players}

        # 验证更新后的座位分配
        validate_seat_allocation(updated_state)

        Logger.info("🪑 [POT_BLIND] 玩家 #{player.numeric_id} 分配到座位 #{seat_id}")
        seat_info = get_seat_allocation_info(updated_state)
        Logger.info("🪑 [POT_BLIND] 更新后座位状态: #{inspect(seat_info)}")

        updated_state
    end
  end

  # 为 PlayerData 结构体添加游戏特定字段
  defp add_game_fields_to_player(player, seat_id) do
    # 添加 PotBlind 游戏所需的所有字段到 PlayerData 结构体
    game_fields = %{
      # ==================== 座位和基础状态 ====================
      # 座位号 (1-5) - 统一使用seatid
      seatid: seat_id,

      # ==================== 玩家状态字段 ====================
      # 玩家状态 (0=无效, 1=游戏, 2=弃牌, 3=输牌)
      player_state: get_player_state(:play),
      # 游戏阶段状态 (0=无效状态)
      game_state: get_player_state(:none),
      # 是否在游戏中 (0=否, 1=是)
      is_gaming: 0,
      # 兼容字段，与 player_state 相同
      status: get_player_state(:play),
      # 兼容字段，与 player_state 相同
      state: get_player_state(:play),

      # ==================== 游戏数据字段 ====================
      # 手牌 [%{suit: 3, value: 14}, ...]
      cards: [],
      # 当前轮下注金额
      bet_amount: 0,
      # 总下注金额
      total_bet: 0,
      # 当前下注 (兼容字段)
      current_bet: 0,

      # ==================== 游戏行为状态 ====================
      # 是否弃牌
      is_fold: false,
      # 是否全押
      is_all_in: false,
      # 是否看过牌
      is_seen_cards: false,
      # 本轮是否已行动
      has_acted: false,

      # ==================== 下注相关 ====================
      # 盲注金额
      blind_bet: 0,
      # 最后一次下注类型 (:blind, :bet, :raise, :call, :fold)
      last_bet_type: :none,
      # 最后一次下注金额
      last_bet_amount: 0,

      # ==================== 比牌相关 ====================
      # 比牌目标玩家ID
      competition_target: nil,
      # 比牌结果 (:win, :lose)
      competition_result: nil,

      # ==================== 统计数据 ====================
      # 已玩手数
      hands_played: 0,
      # 获胜手数
      hands_won: 0,
      # 总奖金
      total_winnings: 0,

      # ==================== 客户端显示字段 ====================
      # 玩家昵称
      name: get_player_name(player),
      # 玩家昵称 (兼容字段)
      nickname: get_player_name(player),
      # 头像ID
      headid: get_player_headid(player),
      # 头像URL
      wxheadurl: get_player_avatar_url(player),
      # 玩家金币
      money: get_player_money(player),
      # 玩家金币 (兼容字段)
      coin: get_player_money(player),
      # 玩家金币 (兼容字段)
      playercoin: get_player_money(player),
      # 积分 (兼容字段)
      points: get_player_money(player),

      # ==================== 时间戳 ====================
      # 加入时间
      joined_at: System.system_time(:millisecond),
      # 最后行动时间
      last_action_at: System.system_time(:millisecond),
      # 最后下注时间
      last_bet_at: nil,

      # ==================== 机器人相关 ====================
      # 是否机器人
      is_robot: Map.get(player, :is_robot, false),
      robot_ai_data:
        if(Map.get(player, :is_robot, false), do: get_robot_ai_data(player), else: nil)
    }

    # 将游戏字段合并到 PlayerData 结构体中
    Map.merge(player, game_fields)
  end

  # 获取机器人AI数据的辅助函数
  defp get_robot_ai_data(player) do
    case player do
      %PlayerData{} = p -> PlayerData.get_robot_ai_data(p)
      _ -> nil
    end
  end

  # 获取已占用的座位列表
  defp get_occupied_seats(state) do
    state.players
    |> Enum.map(fn {_id, player} -> Map.get(player, :seatid) end)
    |> Enum.filter(& &1)
  end

  # 查找下一个可用的座位号 (从1开始)
  defp find_next_available_seat(state) do
    max_seats = 5
    occupied_seats = get_occupied_seats(state)

    # 查找最小的未占用座位号
    Enum.find(1..max_seats, fn seat_num ->
      seat_num not in occupied_seats
    end)
  end

  # ==================== 机器人相关函数 ====================

  # 检查是否需要为新玩家添加机器人
  defp maybe_add_robots_for_player(state, player) do
    case PotBlindRobotManager.maybe_add_robots_for_real_player(state, player) do
      {updated_state, {:add_robots_with_seats, robots}} ->
        Logger.info("🤖 [POT_BLIND] 为真实玩家添加 #{length(robots)} 个机器人")
        add_robots_to_room(updated_state, robots)

      {updated_state, nil} ->
        updated_state
    end
  end

  # 添加机器人到房间
  defp add_robots_to_room(state, robots) do
    Logger.info("🤖 [POT_BLIND] 添加 #{length(robots)} 个机器人到房间")

    # 将机器人添加到房间状态（不立即广播）
    {updated_state, robots_with_seats} =
      Enum.reduce(robots, {state, []}, fn robot, {acc_state, acc_robots} ->
        # 为机器人分配座位
        state_with_seat = assign_seat_to_player(acc_state, robot)

        # 获取分配座位后的机器人数据
        robot_with_seat = Map.get(state_with_seat.players, robot.numeric_id, robot)

        Logger.info(
          "🤖 [POT_BLIND_ROBOT] 机器人 #{robot.numeric_id} 已分配座位: #{robot_with_seat.seatid}"
        )

        {state_with_seat, [robot_with_seat | acc_robots]}
      end)

    # 反转列表以保持原始顺序
    robots_with_seats = Enum.reverse(robots_with_seats)

    # 延迟500ms后广播机器人加入（确保真实玩家订阅完成）
    Process.send_after(self(), {:broadcast_robots, robots_with_seats}, 500)

    updated_state
  end

  # 获取座位分配信息 (用于调试)
  defp get_seat_allocation_info(state) do
    occupied_seats = get_occupied_seats(state)
    available_seats = Enum.filter(1..5, fn seat -> seat not in occupied_seats end)

    %{
      occupied_seats: Enum.sort(occupied_seats),
      available_seats: available_seats,
      total_players: map_size(state.players)
    }
  end

  # 验证座位分配的一致性
  defp validate_seat_allocation(state) do
    seat_info = get_seat_allocation_info(state)

    # 检查是否有重复座位
    occupied_seats = seat_info.occupied_seats
    unique_seats = Enum.uniq(occupied_seats)

    if length(occupied_seats) != length(unique_seats) do
      Logger.warning("⚠️ [POT_BLIND] 发现重复座位分配: #{inspect(occupied_seats)}")
      false
    else
      Logger.debug(
        "✅ [POT_BLIND] 座位分配正常: 已占用 #{inspect(occupied_seats)}, 可用 #{inspect(seat_info.available_seats)}"
      )

      true
    end
  end

  # 处理玩家在游戏中离开
  defp handle_player_quit_during_game(state, game_player) do
    player_id = game_player.numeric_id
    Logger.info("🎮 [POT_BLIND] 处理游戏中玩家 #{player_id} 离开，直接算弃牌")

    # 取消当前玩家的行动定时器（如果是当前行动玩家）
    timer_cancelled_state =
      if Map.get(state, :current_player_id) == player_id do
        Logger.info("⏰ [POT_BLIND] 当前行动玩家离开，取消行动定时器")
        cancel_previous_timer(state)
      else
        state
      end

    # 标记玩家为弃牌状态
    updated_player = %{
      game_player
      | is_fold: true,
        player_state: get_player_state(:fold),
        status: get_player_state(:fold),
        state: get_player_state(:fold),
        last_bet_type: :fold,
        last_action_at: System.system_time(:millisecond),
        has_acted: true,
        is_gaming: 0
    }

    fold_state = %{
      timer_cancelled_state
      | players: Map.put(timer_cancelled_state.players, player_id, updated_player)
    }

    Logger.info("🚫 [POT_BLIND] 玩家 #{player_id} 因离开被标记为弃牌")

    # 广播弃牌结果
    broadcast_fold_result(fold_state, updated_player)

    # 检查游戏是否结束（只剩一个活跃玩家）
    case check_game_end_condition(fold_state) do
      {:game_end, winner_id} ->
        Logger.info("🏆 [POT_BLIND] 玩家离开导致游戏结束，获胜者: #{winner_id}")
        # 游戏结束，进行结算
        end_game_with_winner(fold_state, winner_id)

      :continue ->
        # 游戏继续，如果离开的是当前行动玩家，需要切换到下一个玩家
        if Map.get(state, :current_player_id) == player_id do
          Logger.info("🔄 [POT_BLIND] 当前行动玩家离开，切换到下一个玩家")
          set_next_seat_id(fold_state)
        else
          Logger.info("🎮 [POT_BLIND] 非当前行动玩家离开，游戏继续")
          fold_state
        end
    end
  end

  # 检查游戏结束条件
  defp check_game_end_conditions(state) do
    if state.room_state == :playing do
      active_players = get_active_players(state)
      active_count = length(active_players)

      Logger.info("🔍 [POT_BLIND] 检查游戏结束条件 - 活跃玩家数: #{active_count}")

      cond do
        active_count <= 1 ->
          Logger.info("🏁 [POT_BLIND] 只剩1个或0个活跃玩家，游戏结束")
          handle_game_end_by_elimination(state, active_players)

        active_count < 2 ->
          Logger.info("🏁 [POT_BLIND] 玩家数不足，游戏结束")
          reset_game_to_waiting(state)

        true ->
          Logger.info("🎮 [POT_BLIND] 游戏继续，活跃玩家数: #{active_count}")
          state
      end
    else
      state
    end
  end

  # 通过淘汰结束游戏
  defp handle_game_end_by_elimination(state, remaining_players) do
    case remaining_players do
      [winner] ->
        Logger.info("🏆 [POT_BLIND] 游戏结束，获胜者: #{winner.numeric_id}")
        # TODO: 实现结算逻辑
        reset_game_to_waiting(state)

      [] ->
        Logger.info("🏁 [POT_BLIND] 所有玩家都离开，重置游戏")
        reset_game_to_waiting(state)

      _ ->
        Logger.info("🎮 [POT_BLIND] 仍有多个玩家，游戏继续")
        state
    end
  end

  # 重置游戏到等待状态
  defp reset_game_to_waiting(state) do
    Logger.info("🔄 [POT_BLIND] 重置游戏到等待状态")

    # 重置所有玩家的游戏状态
    updated_players =
      state.players
      |> Enum.map(fn {player_id, player} ->
        reset_player = %{
          player
          | is_gaming: 0,
            player_state: get_player_state(:play),
            is_fold: false,
            cards: [],
            bet_amount: 0,
            total_bet: 0
        }

        {player_id, reset_player}
      end)
      |> Enum.into(%{})

    # 重置房间状态
    reset_state = %{
      state
      | room_state: :waiting,
        players: updated_players,
        pot_amount: 0,
        current_player_id: nil,
        dealer_id: nil,
        game_phase: :waiting
    }

    # 广播房间状态变化
    broadcast_pot_blind_room_state(reset_state)

    reset_state
  end

  # 发送房间信息协议 [4][2] 给指定玩家
  defp send_room_info_to_player(state, player) do
    room_info_data = build_room_info_data(state)

    message = %{
      "mainId" => 4,
      "subId" => 2,
      "data" => room_info_data
    }

    # 发送给指定玩家
    send_to_player(state, player, message)
    Logger.info("📤 [POT_BLIND] 发送房间信息 [4][2] 给玩家 #{player.numeric_id}")
  end

  # 发送游戏房间信息协议 [5][0] 给指定玩家 (断线重连用)
  defp send_to_other_room_to_player(state, player) do
    to_other_room_data = build_to_other_room_data(state)

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_to_other_room),
      "data" => to_other_room_data
    }

    # 发送给指定玩家
    send_to_player(state, player, message)
    Logger.info("📤 [POT_BLIND] 发送游戏房间信息 [5][0] 给玩家 #{player.numeric_id}")
  end

  # 广播玩家进入协议 [4][12] 给房间内其他玩家
  defp broadcast_player_enter(state, new_player) do
    player_enter_data = build_player_enter_data(new_player)

    message = %{
      "mainId" => 4,
      "subId" => 12,
      "data" => player_enter_data
    }

    Logger.info(
      "📡 [POT_BLIND] 准备广播玩家进入 [4][12]: 玩家 #{new_player.numeric_id}, topic: #{inspect(state.topic)}"
    )

    broadcast_to_room(state, message)
    Logger.info("📡 [POT_BLIND] 广播玩家进入 [4][12] 完成: 玩家 #{new_player.numeric_id}")
  end

  # 发送玩家状态协议 [4][4] 给指定玩家
  defp send_player_state_to_player(state, player) do
    # 从状态中获取完整的游戏玩家对象
    game_player = Map.get(state.players, player.numeric_id)

    player_state_data = %{
      "playerid" => Map.get(game_player, :numeric_id, player.numeric_id),
      "seatid" => Map.get(game_player, :seatid, 1),
      "state" => Map.get(game_player, :player_state, 1),
      "status" => Map.get(game_player, :player_state, 1),
      "name" => get_player_name(game_player || player),
      "nickname" => get_player_name(game_player || player),
      "money" => get_player_money(game_player || player),
      "coin" => get_player_money(game_player || player)
    }

    message = %{
      "mainId" => 4,
      "subId" => 4,
      "data" => player_state_data
    }

    # 发送给指定玩家
    send_to_player(state, player, message)
    Logger.info("📤 [POT_BLIND] 发送玩家状态 [4][4] 给玩家 #{player.numeric_id}")
  end

  # ==================== 协议数据构建函数 ====================

  # 构建房间信息数据 [4][2] - 匹配用户提供的正确格式
  defp build_room_info_data(state) do
    # 获取游戏配置
    config = state.config || %{}

    # 计算自动开始倒计时
    auto_ready_time =
      case state.room_state do
        :waiting ->
          # 满足人数直接开始，不显示倒计时
          0

        _ ->
          0
      end

    # 构建玩家列表，下标从1开始
    player_list_with_index = build_room_info_player_list(state)

    %{
      # 金币类型
      "cointype" => 1,
      # 底注
      "difen" => config[:difen] || 30,
      # 定分
      "dingfen" => 0,
      # 进入金额
      "inmoney" => 2000,
      # 最小金额
      "minmoney" => 1000,
      # 房间状态 (0=等待, 1=游戏中)
      "roomstate" => get_room_state_code(state.room_state),
      # 额外数据
      "data" => %{
        "configid" => 1
      },
      # 房间ID
      "roomid" => state.id || 1,
      # 小费庄家费用
      "tipdealerfee" => 10,
      # 单聊费用
      "singlechatfee" => 10,
      # 自动开始倒计时
      "autoreadytime" => auto_ready_time,
      # 玩家列表 (下标从1开始)
      "playerlist" => player_list_with_index
    }
  end

  # 构建房间信息中的玩家列表，下标从1开始
  defp build_room_info_player_list(state) do
    # 获取所有玩家数据
    player_data_list =
      state.players
      |> Enum.map(fn {_id, player} ->
        build_room_info_player_data(player)
      end)
      # 按座位号排序
      |> Enum.sort_by(fn player_data -> player_data["seat"] end)

    # 转换为下标从1开始的Map格式
    player_data_list
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {player_data, index}, acc ->
      Map.put(acc, to_string(index), player_data)
    end)
  end

  # 构建房间信息中的单个玩家数据
  defp build_room_info_player_data(player) do
    %{
      # 玩家ID
      "playerid" => Map.get(player, :numeric_id, 0),
      # 玩家昵称
      "nickname" => get_player_name(player),
      # 玩家金币
      "money" => get_player_money(player),
      # 是否自定义头像
      "iscustomhead" => 0,
      # 系统头像ID
      "headid" => Map.get(player, :headid, 3),
      # 性别
      "sex" => -1,
      # 头像框ID
      "headframeid" => 0,
      # 自定义头像URL
      "wxheadurl" => Map.get(player, :wxheadurl, ""),
      # 城市
      "city" => "Vietnam",
      # IP地址
      "ip" => "*************",
      # 等级
      "level" => 1,
      # 彩票
      "lottery" => 0,
      # 是否VIP
      "isvip" => 0,
      # 座位号
      "seat" => Map.get(player, :seatid, 1),
      # 是否观察者
      "bwatcher" => 0,
      # 昵称2
      "nickname2" => Map.get(player, :numeric_id, 0),
      # 是否在游戏中
      "isgaming" => Map.get(player, :is_gaming, 0),
      # 玩家状态
      "playerstate" => Map.get(player, :player_state, 0),
      # 金币
      "coin" => get_player_money(player),
      # 结束时间
      "endtime" => 0,
      # 扩展属性
      "exattrib" => %{
        "chargeAmount" => 6_000_000,
        "isCheat" => 0,
        "currenttime" => format_current_time(),
        "lastgametime" => format_current_time(),
        "luckamount" => "",
        "renterid" => 0,
        "serverip" => "127.0.0.1",
        "serverport" => 10301,
        "ClearScore" => "",
        "ForceScore" => ""
      },
      # 游戏状态
      "gameState" => Map.get(player, :game_state, 0)
    }
  end

  # 格式化当前时间
  defp format_current_time() do
    DateTime.utc_now()
    |> DateTime.shift_zone!("Asia/Ho_Chi_Minh")
    |> DateTime.to_string()
    |> String.replace("T", " ")
    |> String.slice(0, 19)
  end

  # 构建玩家进入数据 [4][12] - 匹配用户提供的正确格式
  defp build_player_enter_data(game_player) do
    %{
      # 玩家ID
      "playerid" => Map.get(game_player, :numeric_id, 0),
      # 玩家昵称
      "nickname" => get_player_name(game_player),
      # 玩家金币
      "money" => get_player_money(game_player),
      # 是否自定义头像
      "iscustomhead" => 0,
      # 系统头像ID
      "headid" => get_player_headid(game_player),
      # 性别
      "sex" => 0,
      # 头像框ID
      "headframeid" => 0,
      # 自定义头像URL
      "wxheadurl" => get_player_avatar_url(game_player),
      # 城市
      "city" => "Vietnam",
      # IP地址
      "ip" => "*************",
      # 等级
      "level" => 1,
      # 彩票
      "lottery" => 0,
      # 是否VIP
      "isvip" => 0,
      # 座位号
      "seat" => Map.get(game_player, :seatid, 1),
      # 是否观察者
      "bwatcher" => 0,
      # 昵称2
      "nickname2" => Map.get(game_player, :numeric_id, 0),
      # 是否在游戏中
      "isgaming" => Map.get(game_player, :is_gaming, 0),
      # 玩家状态
      "playerstate" => Map.get(game_player, :player_state, 0),
      # 金币
      "coin" => get_player_money(game_player),
      # 结束时间
      "endtime" => 0,
      # 扩展属性
      "exattrib" => %{
        "chargeAmount" => 100_000,
        "isCheat" => 0,
        "currenttime" => format_current_time(),
        "lastgametime" => format_current_time(),
        "luckamount" => "",
        "renterid" => 0,
        "serverip" => "127.0.0.1",
        "serverport" => 10301,
        "ClearScore" => "",
        "ForceScore" => ""
      },
      # 游戏状态
      "gameState" => Map.get(game_player, :game_state, 0)
    }
  end

  # 构建玩家列表 (使用玩家已分配的座位号)
  defp build_player_list(state) do
    state.players
    |> Enum.map(fn {_id, player} ->
      # 使用玩家已经分配好的座位号，不要重新分配
      build_player_enter_data(player)
    end)
    # 按座位号排序
    |> Enum.sort_by(fn player_data -> player_data["seatid"] end)
  end

  # 构建游戏房间信息数据 [5][0] - 匹配用户提供的正确格式
  defp build_to_other_room_data(state) do
    config = state.config || %{}

    # 获取当前操作玩家信息
    current_player_id = Map.get(state, :current_player_id)

    current_player_seat =
      if current_player_id do
        pot_blind_get_player_seat(state, current_player_id)
      else
        # 没有当前操作玩家时返回0
        0
      end

    # 获取庄家信息
    dealer_id = Map.get(state, :dealer_id)

    dealer_seat =
      if dealer_id do
        pot_blind_get_player_seat(state, dealer_id)
      else
        # 没有庄家时返回0
        0
      end

    # 获取比牌座位信息
    compseat_id = Map.get(state, :comparison_target_seat, 0)

    # 构建玩家列表，下标从1开始
    player_list_with_index = build_to_other_room_player_list(state)

    # 计算等待时间
    wait_time = calculate_wait_time(state)

    %{
      # 底注
      "difen" => config[:difen] || 30,
      # 庄家座位号
      "banker" => dealer_seat,
      # 当前操作玩家座位号
      "optseatid" => current_player_seat,
      # 比牌座位号
      "compseatid" => compseat_id,
      # 奖池限制
      "potlimit" => config[:pot_limit] || 61440,
      # 盲注限制
      "blindlimit" => config[:blind_limit] || 3840,
      # 最小比牌轮数
      "compminturnnum" => config[:comp_min_turn_num] || 3,
      # 当前倍数
      "curtimes" => Map.get(state, :curtimes, 1),
      # 总下注金额
      "allbet" => Map.get(state, :pot_amount, 0),
      # 游戏状态
      "state" => get_game_state_code(state),
      # 轮次数
      "turnnum" => Map.get(state, :turn, 1),
      # 操作时间限制(秒)
      "optsumtime" => get_current_operation_time(state),
      # 等待时间
      "waittime" => wait_time,
      # 玩家列表 (下标从1开始)
      "playerlist" => player_list_with_index,
      # 观察者标识
      "bWatcher" => 0,
      # 房间ID
      "_roomid" => state.id || 1,
      # 当前操作玩家ID
      "_playerid" => current_player_id || 0
    }
  end

  # 构建 [5][0] 协议中的玩家列表，下标从1开始
  defp build_to_other_room_player_list(state) do
    # 获取所有玩家数据
    player_data_list =
      state.players
      |> Enum.map(fn {_id, player} ->
        build_to_other_room_player_data(player, state)
      end)
      # 按座位号排序
      |> Enum.sort_by(fn player_data -> player_data["seatid"] end)

    # 转换为下标从1开始的Map格式
    player_data_list
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {player_data, index}, acc ->
      Map.put(acc, to_string(index), player_data)
    end)
  end

  # 构建 [5][0] 协议中的玩家数据 - 匹配用户提供的正确格式
  defp build_to_other_room_player_data(player, state) do
    # 构建牌数据
    cards_data = build_cards_data_for_to_other_room(Map.get(player, :cards, []))

    # 获取玩家下注类型
    bettype = get_player_bettype(player)

    # 获取玩家状态
    player_state = get_player_state_for_to_other_room(player)

    %{
      # 玩家ID
      "playerid" => Map.get(player, :numeric_id, 0),
      # 座位号
      "seatid" => Map.get(player, :seatid, 1),
      # 下注类型
      "bettype" => bettype,
      # 玩家金币
      "pmoney" => get_player_money(player),
      # 玩家总下注
      "mybetall" => Map.get(player, :total_bet, 0),
      # 玩家状态
      "state" => player_state
    }
    # 根据情况添加牌数据
    |> maybe_add_cards(cards_data)
  end

  # 根据情况添加牌数据到玩家信息中
  defp maybe_add_cards(player_data, cards_data) do
    if map_size(cards_data) > 0 do
      Map.put(player_data, "cards", cards_data)
    else
      player_data
    end
  end

  # 构建 [5][0] 协议中的牌数据格式
  defp build_cards_data_for_to_other_room(cards) do
    if length(cards) > 0 do
      cards
      |> Enum.with_index(1)
      |> Enum.reduce(%{}, fn {card, index}, acc ->
        card_data = %{
          "color" => convert_suit_to_color(Map.get(card, :suit, 1)),
          "number" => Map.get(card, :value, 0)
        }

        Map.put(acc, to_string(index), card_data)
      end)
    else
      %{}
    end
  end

  # 获取玩家下注类型
  defp get_player_bettype(player) do
    case Map.get(player, :last_bet_type, :none) do
      # 盲注
      :blind -> 0
      # 下注
      :fill -> 1
      # 比牌
      :competition -> 2
      # 弃牌
      :fold -> 3
      # 默认
      _ -> 0
    end
  end

  # 获取玩家状态 (用于 [5][0] 协议)
  defp get_player_state_for_to_other_room(player) do
    cond do
      # 弃牌状态
      Map.get(player, :is_fold, false) -> 2
      # 游戏中状态
      Map.get(player, :is_gaming, 0) == 1 -> 1
      # 默认状态
      true -> 1
    end
  end

  # 计算等待时间
  defp calculate_wait_time(state) do
    case Map.get(state, :turn_start_time) do
      nil ->
        get_current_operation_time(state)

      start_time ->
        # 计算剩余时间
        elapsed = (System.system_time(:millisecond) - start_time) / 1000.0
        max_time = get_current_operation_time(state)
        remaining = max(0, max_time - elapsed)
        Float.round(remaining, 2)
    end
  end

  # 格式化玩家牌数据 (用于 [5][0] 协议)
  defp format_player_cards_for_to_other_room(player) do
    cards = Map.get(player, :cards, [])

    if length(cards) > 0 do
      PotBlindGameLogic.format_cards_for_potblind_client(cards)
    else
      []
    end
  end

  # 获取玩家牌型
  defp get_player_card_type(player) do
    cards = Map.get(player, :cards, [])

    if length(cards) > 0 do
      PotBlindGameLogic.get_card_type_number(cards)
    else
      0
    end
  end

  # 获取游戏状态代码
  defp get_game_state_code(state) do
    case Map.get(state, :game_phase, :waiting) do
      # 等待状态
      :waiting -> 0
      # 下注状态
      :betting -> 2
      # 比牌状态
      :showdown -> 3
      # 结算状态
      :settlement -> 4
      _ -> 0
    end
  end

  # 获取当前操作时间（秒）
  defp get_current_operation_time(state) do
    # 根据游戏阶段返回操作时间，不依赖房间状态
    case Map.get(state, :game_phase, :betting) do
      # 下注阶段：10秒
      :betting -> 10
      # 比牌阶段：3秒（比牌动画时间）
      :showdown -> 3
      # 结算阶段：5秒
      :settlement -> 5
      # 默认：10秒
      _ -> 10
    end
  end

  # ==================== 玩家信息获取函数 ====================

  # 获取玩家昵称 - 使用 PlayerData 结构体的方法
  defp get_player_name(player) do
    case player do
      %PlayerData{} = p ->
        PlayerData.get_display_name(p)

      %{user: user} when is_map(user) ->
        Map.get(user, :nickname, "玩家#{Map.get(player, :numeric_id, 0)}")

      %{nickname: nickname} when is_binary(nickname) ->
        nickname

      _ ->
        "玩家#{Map.get(player, :numeric_id, 0)}"
    end
  end

  # 获取玩家头像ID - 使用 PlayerData 结构体的方法
  defp get_player_headid(player) do
    case player do
      %PlayerData{} = p ->
        client_data = PlayerData.format_for_client(p)
        Map.get(client_data, "headid", 1)

      %{user: user} when is_map(user) ->
        Map.get(user, :avatar_id, 1)

      %{headid: headid} when is_integer(headid) ->
        headid

      # 默认头像ID
      _ ->
        1
    end
  end

  # 获取玩家头像URL - 使用 PlayerData 结构体的方法
  defp get_player_avatar_url(player) do
    case player do
      %PlayerData{} = p ->
        client_data = PlayerData.format_for_client(p)
        Map.get(client_data, "wxheadurl", "")

      %{user: user} when is_map(user) ->
        Map.get(user, :avatar_url, "")

      %{wxheadurl: url} when is_binary(url) ->
        url

      # 空字符串表示使用系统头像
      _ ->
        ""
    end
  end

  # 获取玩家金币 - 使用 PlayerData 结构体的方法
  defp get_player_money(player) do
    case player do
      %PlayerData{} = p -> PlayerData.get_points(p)
      %{user: user} when is_map(user) -> Map.get(user, :points, 0)
      %{money: money} when is_number(money) -> money
      %{points: points} when is_number(points) -> points
      _ -> 0
    end
  end

  # 获取房间状态码 (前端 RoomState: WAIT=0, GAME=1)
  defp get_room_state_code(room_state) do
    case room_state do
      # RoomState.WAIT
      :waiting -> 0
      # RoomState.GAME
      :playing -> 1
      # 结算中也算游戏中
      :settling -> 1
      # 已结束回到等待状态
      :finished -> 0
      # 默认等待状态
      _ -> 0
    end
  end

  # ==================== [4][4] 和 [4][5] 协议实现 ====================

  @doc """
  广播玩家状态协议 [4][4] SC_ROOM_SET_PLAYER_STATE_P
  用于通知房间内所有玩家某个玩家的状态变化
  """
  def broadcast_player_state(state, player, player_state) do
    player_state_data = build_player_state_data(player, player_state)

    message = %{
      "mainId" => 4,
      "subId" => 4,
      "data" => player_state_data
    }

    broadcast_to_room(state, message)
    Logger.info("📡 [POT_BLIND] 广播玩家状态 [4][4]: 玩家 #{player.numeric_id}, 状态 #{player_state}")
  end

  # 构建玩家状态数据 [4][4]
  defp build_player_state_data(player, player_state) do
    %{
      # 玩家ID
      "playerid" => player.numeric_id,
      # 座位号
      "seatid" => Map.get(player, :seatid, 1),
      # 玩家状态
      "state" => player_state,
      # 兼容字段
      "status" => player_state,
      # 玩家昵称
      "name" => get_player_name(player),
      # 玩家昵称
      "nickname" => get_player_name(player),
      # 玩家金币
      "money" => get_player_money(player),
      # 玩家金币
      "coin" => get_player_money(player)
    }
  end

  # 获取当前游戏状态
  defp get_current_game_state(state) do
    case state.room_state do
      # 等待状态
      :waiting -> get_game_state(:start)
      # 游戏中默认为下注状态
      :playing -> get_game_state(:bet)
      # 结算状态
      :settling -> get_game_state(:end)
      # 结束后回到开始状态
      :finished -> get_game_state(:start)
      # 默认开始状态
      _ -> get_game_state(:start)
    end
  end

  # ==================== 协议调用辅助函数 ====================

  @doc """
  玩家状态变化时调用此函数广播
  """
  def notify_player_state_change(state, player, new_state) do
    broadcast_player_state(state, player, new_state)
    state
  end

  @doc """
  PotBlind 专用的房间状态广播函数
  确保发送给前端的是数字而不是原子
  """
  defp broadcast_pot_blind_room_state(state) do
    # 将后端的原子状态转换为前端期望的数字
    room_state_code = get_room_state_code(state.room_state)

    message = %{
      # MainProto.Game
      "mainId" => 4,
      # SC_ROOM_SET_STATE_P
      "subId" => 5,
      "data" => %{
        # 发送数字而不是原子
        "roomstate" => room_state_code
      }
    }

    Logger.info("📡 [POT_BLIND] 广播房间状态 [4][5]: #{state.room_state} -> #{room_state_code}")
    broadcast_to_room(state, message)
  end

  @doc """
  [4][14] SC_ROOM_PLAYER_QUIT_P - 玩家离开协议
  对应前端 onPlayerQuit 方法
  """
  def broadcast_pot_blind_player_quit(state, player) do
    player_quit_data = %{
      # 玩家ID
      "playerid" => player.numeric_id,
      # 座位号
      "seatid" => Map.get(player, :seatid, 1),
      # 玩家昵称
      "name" => get_player_name(player),
      # 玩家昵称
      "nickname" => get_player_name(player),
      # 离开原因
      "reason" => "normal_quit",
      # 时间戳
      "timestamp" => System.system_time(:millisecond)
    }

    message = %{
      "mainId" => 4,
      "subId" => 14,
      "data" => player_quit_data
    }

    broadcast_to_room(state, message)
    Logger.info("📡 [POT_BLIND] 广播玩家离开 [4][14]: 玩家 #{player.numeric_id}")
  end

  @doc """
  [4][18] SC_ROOM_DEL_PLAYER_P - 删除玩家协议
  对应前端 onDeletePlayer 方法
  """
  def broadcast_delete_player(state, player, reason \\ "kicked") do
    # 检查state是否有效
    if is_map(state) and Map.has_key?(state, :topic) do
      delete_player_data = %{
        # 玩家ID
        "playerid" => player.numeric_id,
        # 座位号
        "seatid" => Map.get(player, :seatid, 1),
        # 玩家昵称
        "name" => get_player_name(player),
        # 玩家昵称
        "nickname" => get_player_name(player),
        # 删除原因 (kicked, timeout, etc.)
        "reason" => reason,
        # 时间戳
        "timestamp" => System.system_time(:millisecond)
      }

      message = %{
        "mainId" => 4,
        "subId" => 18,
        "data" => delete_player_data
      }

      broadcast_to_room(state, message)
      Logger.info("📡 [POT_BLIND] 广播删除玩家 [4][18]: 玩家 #{player.numeric_id}, 原因 #{reason}")
    else
      Logger.warning("⚠️ [POT_BLIND] 无法广播删除玩家消息，状态无效: 玩家 #{player.numeric_id}, 原因 #{reason}")
    end
  end

  @doc """
  [4][8] SC_ROOM_RESET_COIN_P - 更新玩家金币协议
  对应前端 updatePlayerMoney 方法
  """
  def broadcast_player_money_update(state, player, new_money) do
    money_update_data = %{
      # 玩家ID
      "playerid" => player.numeric_id,
      # 新的金币数量
      "coin" => new_money,
      # 游戏模式
      "gamemode" => 0,
      # 房间ID
      "roomid" => state.id || 1
    }

    message = %{
      "mainId" => 4,
      "subId" => 8,
      "data" => money_update_data
    }

    broadcast_to_room(state, message)
    Logger.info("📡 [POT_BLIND] 广播金币更新 [4][8]: 玩家 #{player.numeric_id}, 金币 #{new_money}")
  end

  # ==================== 协议调用辅助函数扩展 ====================

  @doc """
  发送玩家主动退出响应 [4][40]
  """
  defp send_quit_response(state, player) do
    quit_response_data = %{
      # 退出结果
      "result" => "success",
      # 玩家ID
      "playerid" => player.numeric_id,
      # 退出原因
      "reason" => "voluntary_quit",
      # 时间戳
      "timestamp" => System.system_time(:millisecond)
    }

    message = %{
      "mainId" => 4,
      "subId" => 14,
      "data" => quit_response_data
    }

    send_to_player(state, player, message)
    Logger.info("📤 [POT_BLIND] 发送退出响应 [4][40]: 玩家 #{player.numeric_id}")
  end

  @doc """
  处理玩家主动退出逻辑 - 复用现有的 on_player_left 逻辑
  """
  defp handle_player_voluntary_quit(state, game_player) do
    Logger.info("🚪 [POT_BLIND] 处理玩家主动退出: #{game_player.numeric_id}")

    # 直接调用现有的 on_player_left 逻辑，避免重复实现
    # 这样可以确保主动退出和被动离开的处理逻辑完全一致
    on_player_left(state, game_player)
  end

  @doc """
  玩家离开时调用此函数广播
  """
  def notify_player_quit(state, player) do
    broadcast_pot_blind_player_quit(state, player)
    state
  end

  @doc """
  删除玩家时调用此函数广播
  """
  def notify_delete_player(state, player, reason \\ "kicked") do
    broadcast_delete_player(state, player, reason)
    state
  end

  @doc """
  玩家金币变化时调用此函数广播
  """
  def notify_player_money_update(state, player, new_money) do
    broadcast_player_money_update(state, player, new_money)
    state
  end

  # ==================== 积分更新通知函数 (参考jhandi_munda) ====================

  # 发送积分更新通知给单个玩家 (完全参考jhandi_munda实现)
  defp send_money_update_notification(state, player) do
    # 获取玩家的numeric_id和user_id
    numeric_id =
      case player do
        %{numeric_id: id} ->
          id

        %{user_id: _, numeric_id: id} ->
          id

        _ ->
          Logger.warning("❌ [POT_BLIND] 无法获取玩家numeric_id: #{inspect(player)}")
          nil
      end

    if numeric_id do
      current_points = get_player_points(state, numeric_id)

      money_update_data = %{
        "playerid" => numeric_id,
        "coin" => current_points
      }

      response_money_update = %{
        # MainProto.Game
        "mainId" => 4,
        # Game.SC_ROOM_RESET_COIN_P
        "subId" => 8,
        "data" => money_update_data
      }

      Logger.info(
        "💰 [POT_BLIND_MONEY_UPDATE] 发送积分更新协议 - numeric_id: #{numeric_id}, 积分: #{current_points}"
      )

      send_to_player(state, player, response_money_update)
    end
  end

  # ==================== 玩家状态管理函数 ====================

  @doc """
  更新玩家的游戏状态
  """
  def update_player_game_state(state, player_id, new_game_state) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不在房间中")
        state

      game_player ->
        updated_player = %{
          game_player
          | game_state: new_game_state,
            last_action_at: System.system_time(:millisecond)
        }

        updated_players = Map.put(state.players, player_id, updated_player)

        Logger.info("🔄 [POT_BLIND] 更新玩家 #{player_id} 游戏状态: #{new_game_state}")
        %{state | players: updated_players}
    end
  end

  @doc """
  更新玩家的状态 (准备、游戏中、弃牌等)
  """
  def update_player_state(state, player_id, new_player_state) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不在房间中")
        state

      game_player ->
        updated_player = %{
          game_player
          | player_state: new_player_state,
            last_action_at: System.system_time(:millisecond)
        }

        updated_players = Map.put(state.players, player_id, updated_player)

        Logger.info("🔄 [POT_BLIND] 更新玩家 #{player_id} 状态: #{new_player_state}")
        %{state | players: updated_players}
    end
  end

  @doc """
  设置玩家是否在游戏中
  """
  def set_player_gaming(state, player_id, is_gaming) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不在房间中")
        state

      game_player ->
        updated_player = %{
          game_player
          | is_gaming: if(is_gaming, do: 1, else: 0),
            last_action_at: System.system_time(:millisecond)
        }

        updated_players = Map.put(state.players, player_id, updated_player)

        Logger.info("🎮 [POT_BLIND] 设置玩家 #{player_id} 游戏状态: #{is_gaming}")
        %{state | players: updated_players}
    end
  end

  @doc """
  获取玩家的游戏状态对象
  """
  def get_game_player(state, player_id) do
    Map.get(state.players, player_id)
  end

  @doc """
  检查玩家是否在游戏中
  """
  def is_player_gaming?(state, player_id) do
    case get_game_player(state, player_id) do
      nil -> false
      game_player -> game_player.is_gaming == 1
    end
  end

  @doc """
  获取房间内所有游戏中的玩家
  """
  def get_gaming_players(state) do
    state.players
    |> Enum.filter(fn {_id, game_player} -> game_player.is_gaming == 1 end)
    |> Enum.map(fn {_id, game_player} -> game_player end)
  end

  # ==================== 游戏开始相关函数 ====================

  # 检查PotBlind游戏开始条件 - 完全由子游戏控制
  defp check_pot_blind_start(state) do
    player_count = map_size(state.players)
    # PotBlind 最少需要2个玩家
    min_players = 2

    Logger.info("🔍 [POT_BLIND] 检查游戏开始条件 - 当前玩家数: #{player_count}, 最少需要: #{min_players}")

    cond do
      # 如果人数不足，取消现有的开始定时器
      player_count < min_players ->
        Logger.info("🔍 [POT_BLIND] 人数不足，取消游戏开始定时器")
        cancel_pot_blind_timer(state)

      # 如果房间不在等待状态，不处理
      state.room_state != :waiting ->
        Logger.info("🔍 [POT_BLIND] 房间不在等待状态: #{state.room_state}")
        state

      # 如果已经有开始定时器在运行，不重复启动
      Map.get(state, :pot_blind_timer_ref) != nil ->
        Logger.info("⏰ [POT_BLIND] PotBlind定时器已在运行")
        state

      # 满足开始条件，直接开始游戏
      true ->
        Logger.info("🎯 [POT_BLIND] 满足开始条件，直接开始游戏")
        # send(self(), :start_new_round)
        Process.send_after(self(), :start_new_round, 0)
        state
        # start_pot_blind_game_immediately(state)
    end
  end

  # 直接开始PotBlind游戏（不等待5秒）
  defp on_game_start(state) do
    Logger.info("🚀 [POT_BLIND] 满足人数，直接开始游戏")

    # 取消之前的定时器（如果存在）
    cancel_pot_blind_timer(state)

    # 更新房间状态为游戏中
    game_state = %{state | room_state: :playing}

    # 直接调用游戏开始逻辑
    begin_pot_blind_game(game_state)
  end

  # 取消PotBlind游戏定时器
  defp cancel_pot_blind_timer(state) do
    case Map.get(state, :pot_blind_timer_ref) do
      nil ->
        state

      timer_ref ->
        Process.cancel_timer(timer_ref)
        updated_state = Map.put(state, :pot_blind_timer_ref, nil)
        Logger.info("🚫 [POT_BLIND] 取消PotBlind游戏定时器")

        updated_state
    end
  end

  # 确定庄家
  defp assign_dealer(state) do
    # 🔧 修复：优先使用上一局赢家作为庄家，安全获取字段
    next_dealer_id = Map.get(state, :next_dealer_id, nil)

    # 检查上一局赢家是否还在房间中
    if next_dealer_id && Map.has_key?(state.players, next_dealer_id) do
      Logger.info("🎲 [POT_BLIND] 上一局赢家 #{next_dealer_id} 成为庄家")
      Map.put(state, :dealer_id, next_dealer_id)
    else
      # 如果没有上一局赢家或赢家已离开，按座位号选择第一个玩家
      players_by_seat = get_players_by_seat_order(state)

      case players_by_seat do
        [] ->
          Logger.warning("❌ [POT_BLIND] 没有玩家，无法确定庄家")
          state

        [{first_player_id, _first_player} | _] ->
          # 选择第一个玩家作为庄家
          dealer_id = first_player_id
          Logger.info("🎲 [POT_BLIND] 确定庄家: #{dealer_id} (按座位顺序)")

          Map.put(state, :dealer_id, dealer_id)
      end
    end
  end

  # 初始化游戏数据
  defp initialize_game_data(state) do
    game_data = %{
      # 游戏轮次
      round: 1,
      turn: 1,
      # 参照C++的GetTurnNum()，用于判断发牌时机
      turn_num: 1,

      # 底池信息 - 🔧 修复：保持现有底池，不重置
      pot_amount: Map.get(state, :pot_amount, 0),
      total_pot: Map.get(state, :total_pot, 0),

      # 下注信息
      current_bet: 0,
      min_bet: state.config[:difen] || 100,
      # 当前下注倍数，初始值为1（1x低分）
      curtimes: 1,

      # 游戏状态
      # :betting, :showdown, :finished
      game_phase: :betting,

      # 当前行动玩家
      current_player_id: nil,

      # 庄家ID
      dealer_id: nil,

      # 轮次相关
      # 轮次开始时间
      turn_start_time: nil,

      # 游戏开始时间
      game_start_time: System.system_time(:millisecond),

      # 盲注配置
      blind_amount: state.config[:difen] || 100
    }

    Logger.info("🎮 [POT_BLIND] 初始化游戏数据: #{inspect(game_data)}")
    Map.merge(state, game_data)
  end

  # 发牌给所有玩家
  defp deal_cards_to_all_players(state) do
    # 创建一副牌并洗牌
    deck = PotBlindGameLogic.create_and_shuffle_deck()

    # 给每个玩家发3张牌
    {final_state, _remaining_deck} =
      state.players
      |> Enum.reduce({state, deck}, fn {player_id, _player}, {acc_state, remaining_deck} ->
        # 从牌堆中取3张牌
        {player_cards, new_deck} = Enum.split(remaining_deck, 3)

        # 给玩家发牌
        updated_state = deal_cards_to_player(acc_state, player_id, player_cards)

        Logger.info("🃏 [POT_BLIND] 给玩家 #{player_id} 发牌: #{inspect(player_cards)}")
        {updated_state, new_deck}
      end)

    Logger.info("🃏 [POT_BLIND] 发牌完成，所有玩家已收到手牌")
    final_state
  end

  # 设置所有玩家为游戏状态
  defp set_all_players_gaming(state) do
    updated_players =
      state.players
      |> Enum.map(fn {player_id, player} ->
        updated_player = %{
          player
          | is_gaming: 1,
            player_state: get_player_state(:play),
            status: get_player_state(:play),
            state: get_player_state(:play),
            last_action_at: System.system_time(:millisecond)
        }

        {player_id, updated_player}
      end)
      |> Enum.into(%{})

    Logger.info("🎮 [POT_BLIND] 设置所有玩家为游戏状态")
    %{state | players: updated_players}
  end

  # 收取盲注 - 参考jhandi_munda的积分处理
  defp collect_blind_bets(state) do
    blind_amount = state.blind_amount || 100

    # 所有玩家都需要下盲注
    {final_state, total_blind} =
      state.players
      |> Enum.reduce({state, 0}, fn {player_id, player}, {acc_state, acc_blind} ->
        # 检查玩家余额是否足够
        current_points = get_player_points(acc_state, player_id)

        if current_points < blind_amount do
          Logger.warning(
            "❌ [POT_BLIND] 玩家 #{player_id} 余额不足支付盲注: #{current_points} < #{blind_amount}"
          )

          # 余额不足的玩家自动弃牌
          fold_state = set_player_fold(acc_state, player_id)
          {fold_state, acc_blind}
        else
          # 1. 扣除玩家积分 (参考jhandi_munda)
          deduct_state = subtract_player_points(acc_state, player_id, blind_amount)

          # 2. 更新玩家下注信息
          bet_state = update_player_bet(deduct_state, player_id, blind_amount, :blind)

          # 3. 🔧 修复：立即更新底池，确保广播时 pot_amount 是正确的
          updated_pot_state = %{bet_state | pot_amount: bet_state.pot_amount + blind_amount}

          # 4. 发送积分更新通知 (参考jhandi_munda)
          send_money_update_notification(updated_pot_state, player)

          # 5. 广播盲注下注结果（使用 BetType.BOTTOM = 0）
          broadcast_pot_blind_bet_result(updated_pot_state, player, blind_amount, 1, :blind)

          Logger.info(
            "💰 [POT_BLIND] 玩家 #{player_id} 下盲注: #{blind_amount}, 剩余积分: #{get_player_points(updated_pot_state, player_id)}"
          )

          {updated_pot_state, acc_blind + blind_amount}
        end
      end)

    # 🔧 修复：底池已在循环中更新，这里只需要设置 current_bet
    updated_state = %{final_state | current_bet: blind_amount}

    Logger.info("💰 [POT_BLIND] 收取盲注完成 - 总盲注: #{total_blind}, 底池: #{updated_state.pot_amount}")
    updated_state
  end

  # 广播游戏开始协议 [5][1000]
  defp broadcast_game_start(state) do
    # 构建游戏开始数据
    game_start_data = build_game_start_data(state)

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:game_start),
      "data" => game_start_data
    }

    Logger.info("📡 [POT_BLIND] 广播游戏开始 [5][1000]: #{inspect(game_start_data)}")
    broadcast_to_room(state, message)

    state
  end

  # 广播发牌协议 [5][1001]
  defp broadcast_deal_cards(state) do
    Logger.info("📡 [POT_BLIND] 广播发牌协议 [5][1001]")

    # 给每个玩家发送个性化的发牌数据
    state.players
    |> Enum.each(fn {player_id, player} ->
      # 构建该玩家看到的发牌数据
      deal_data = build_deal_cards_data_for_player(state, player_id)

      message = %{
        "mainId" => @protocol_main_id,
        "subId" => get_protocol(:sc_sendcard),
        "data" => deal_data
      }

      # 发送给特定玩家
      send_to_player(state, player, message)
      Logger.info("📤 [POT_BLIND] 发送发牌数据给玩家 #{player_id}")
    end)
  end

  # 构建玩家看到的发牌数据 - 匹配前端 onSendCard 期望格式
  defp build_deal_cards_data_for_player(state, viewer_player_id) do
    viewer_player = Map.get(state.players, viewer_player_id)

    # 构建其他玩家的牌列表，下标从1开始
    cardlist = build_cardlist_for_deal_cards(state, viewer_player_id)

    # 构建基础数据
    base_data = %{
      # 发牌状态 (1)
      "state" => get_game_state(:sendcard),
      # 当前玩家ID
      "playerid" => viewer_player_id,
      # 当前玩家座位号
      "seatid" => Map.get(viewer_player, :seatid, 1),
      # 下注类型 (3=发牌)
      "bettype" => 3,
      # 当前倍数
      "curtimes" => Map.get(state, :curtimes, 1),
      # 🔧 修复：房间下注总金额（底池）
      "allbet" => Map.get(state, :pot_amount, 0),
      # 玩家金币
      "pmoney" => Map.get(viewer_player, :money, 0),
      # 其他玩家的牌列表
      "cardlist" => cardlist,
      # 房间ID
      "_roomid" => state.id,
      # 玩家ID (重复字段)
      "_playerid" => viewer_player_id
    }

    # 如果玩家没有弃牌，添加自己的牌数据
    if not Map.get(viewer_player, :is_fold, false) do
      own_cards = build_own_cards_data(viewer_player)
      Map.put(base_data, "cards", own_cards)
    else
      base_data
    end
  end

  # 构建发牌协议中的cardlist，下标从1开始
  defp build_cardlist_for_deal_cards(state, viewer_player_id) do
    # 获取除自己外的其他玩家
    other_players =
      state.players
      |> Enum.filter(fn {player_id, _player} -> player_id != viewer_player_id end)
      |> Enum.sort_by(fn {_id, player} -> Map.get(player, :seatid, 999) end)

    # 转换为下标从1开始的Map格式
    other_players
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {{_player_id, player}, index}, acc ->
      # 其他玩家只能看到1张牌（第一张牌的真实数据）
      player_cards = Map.get(player, :cards, [])

      first_card =
        if length(player_cards) > 0, do: Enum.at(player_cards, 0), else: %{suit: 1, value: 2}

      card_data = %{
        "seatid" => Map.get(player, :seatid, 1),
        "cards" => %{
          "1" => %{
            "color" => convert_suit_to_color(Map.get(first_card, :suit, 1)),
            "number" => Map.get(first_card, :value, 2)
          }
        }
      }

      Map.put(acc, to_string(index), card_data)
    end)
  end

  # 构建自己的牌数据（能看到2张牌）
  defp build_own_cards_data(player) do
    player_cards = Map.get(player, :cards, [])

    # 取前2张牌
    cards_to_show = Enum.take(player_cards, 2)

    cards_to_show
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {card, index}, acc ->
      card_data = %{
        "color" => convert_suit_to_color(Map.get(card, :suit, 1)),
        "number" => Map.get(card, :value, 2)
      }

      Map.put(acc, to_string(index), card_data)
    end)
  end

  # 广播轮次开始协议 [5][1010]
  defp broadcast_turn_start(state) do
    turn_data = build_turn_start_data(state)

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:turn_start),
      "data" => turn_data
    }

    Logger.info("📡 [POT_BLIND] 广播轮次开始 [5][1010]: 当前玩家 #{Map.get(state, :current_player_id)}")
    broadcast_to_room(state, message)

    state
  end

  # 启动行动倒计时
  defp start_action_timer(state) do
    # 获取操作等待时间（动态获取）
    wait_time_seconds = get_current_operation_time(state)
    # 转换为毫秒
    wait_time = wait_time_seconds * 1000

    # 取消之前的定时器（如果存在）
    cancel_previous_timer(state)

    # 启动新的定时器，如果玩家在规定时间内没有行动，自动弃牌
    current_player_id = Map.get(state, :current_player_id)
    timer_ref = Process.send_after(self(), {:player_action_timeout, current_player_id}, wait_time)

    # 保存定时器引用到状态中
    updated_state = Map.put(state, :action_timer_ref, timer_ref)

    Logger.info("⏰ [POT_BLIND] 启动行动倒计时: #{wait_time}ms，玩家: #{current_player_id}")
    updated_state
  end

  # 取消之前的行动定时器
  defp cancel_previous_timer(state) do
    case Map.get(state, :action_timer_ref) do
      nil ->
        Logger.debug("🚫 [POT_BLIND] 没有需要取消的定时器")
        :ok

      timer_ref ->
        Process.cancel_timer(timer_ref)
        Logger.debug("🚫 [POT_BLIND] 取消之前的行动定时器")
        :ok
    end
  end

  # 构建游戏开始数据 [5][1000] - 匹配用户提供的正确格式
  defp build_game_start_data(state) do
    # 获取庄家座位号
    banker_seat = pot_blind_get_player_seat(state, Map.get(state, :dealer_id))

    # 获取当前玩家ID (用于_playerid字段)
    current_player_id =
      Map.get(state, :dealer_id) ||
        case get_players_by_seat_order(state) do
          [{first_player_id, _} | _] -> first_player_id
          [] -> 0
        end

    %{
      # 底注
      "difen" => state.config[:difen] || 30,
      # 庄家座位号
      "banker" => banker_seat,
      # 游戏状态 (0=开始)
      "state" => get_game_state(:start),
      # 奖池限制
      "potlimit" => state.config[:pot_limit] || 61440,
      # 盲注限制
      "blindlimit" => state.config[:blind_limit] || 3840,
      # 最小比牌轮数
      "compminturnnum" => state.config[:comp_min_turn_num] || 3,
      # 玩家ID
      "_playerid" => current_player_id
    }
  end

  # ==================== 轮次和下注相关函数 ====================

  # 构建轮次开始数据 [5][1010] - 匹配用户提供的正确格式
  defp build_turn_start_data(state) do
    current_player_id = Map.get(state, :current_player_id)
    current_player_seat = pot_blind_get_player_seat(state, current_player_id)
    # 动态获取操作时间
    wait_time = get_current_operation_time(state)

    # 根据游戏阶段确定游戏状态
    game_state =
      case Map.get(state, :game_phase, :betting) do
        # 下注阶段
        :betting -> get_game_state(:bet)
        # 比牌阶段
        :showdown -> get_game_state(:showdown)
        _ -> get_game_state(:bet)
      end

    %{
      # 游戏状态
      "state" => game_state,
      # 轮次数
      "turnnum" => Map.get(state, :turn, 1),
      # 操作玩家座位号
      "optseatid" => current_player_seat,
      # 操作时间限制(秒)
      "optsumtime" => wait_time,
      # 房间ID
      "_roomid" => state.id || 1,
      # 当前操作玩家ID
      "_playerid" => current_player_id || 0
    }
  end

  # PotBlind 专用的座位号获取函数 - 处理 nil 值
  defp pot_blind_get_player_seat(_state, nil) do
    Logger.warning("❌ [POT_BLIND] 尝试获取 nil 玩家的座位号")
    0
  end

  # PotBlind 专用的座位号获取函数 - 通过玩家ID（数字）
  defp pot_blind_get_player_seat(state, player_id) when is_integer(player_id) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家不存在: #{player_id}")
        # 如果玩家不存在，返回0表示无效座位
        0

      player ->
        seat = Map.get(player, :seatid, 0)
        Logger.debug("🪑 [POT_BLIND] 玩家 #{player_id} 的座位号: #{seat}")
        seat
    end
  end

  # PotBlind 专用的座位号获取函数 - 通过玩家对象
  defp pot_blind_get_player_seat(state, %{numeric_id: numeric_id}) do
    Logger.debug("🪑 [POT_BLIND] 通过 numeric_id 获取座位: #{numeric_id}")
    pot_blind_get_player_seat(state, numeric_id)
  end

  # PotBlind 专用的座位号获取函数 - 通过 PlayerData 结构体
  defp pot_blind_get_player_seat(state, %PlayerData{numeric_id: numeric_id}) do
    Logger.debug("🪑 [POT_BLIND] 通过 PlayerData 获取座位: #{numeric_id}")
    pot_blind_get_player_seat(state, numeric_id)
  end

  # PotBlind 专用的座位号获取函数 - 处理其他格式的玩家数据
  defp pot_blind_get_player_seat(state, player) when is_map(player) do
    Logger.debug("🪑 [POT_BLIND] 通过 map 获取座位: #{inspect(player)}")
    # 尝试从不同字段获取玩家ID
    player_id =
      Map.get(player, :numeric_id) ||
        Map.get(player, :id) ||
        Map.get(player, :player_id)

    if player_id do
      pot_blind_get_player_seat(state, player_id)
    else
      Logger.warning("❌ [POT_BLIND] 无法从玩家数据中获取ID: #{inspect(player)}")
      0
    end
  end

  # PotBlind 专用的座位号获取函数 - 处理其他类型（兜底）
  defp pot_blind_get_player_seat(_state, player) do
    Logger.warning("❌ [POT_BLIND] 不支持的玩家数据类型: #{inspect(player)}")
    0
  end

  # ==================== 重写 RoomBase 函数 ====================

  @doc """
  重写 RoomBase 的 build_playerlist_for_client 函数
  完全使用 PotBlind 自己的实现，避免调用 RoomBase 的 get_player_seat
  """
  defp build_playerlist_for_client(state) do
    state.players
    |> Enum.reduce(%{}, fn {_key, player}, acc ->
      # 使用 PotBlind 专用的座位号获取函数
      seat = pot_blind_get_player_seat(state, player)

      player_data = %{
        "playerid" => Map.get(player, :numeric_id, 0),
        "seat" => seat,
        "money" => get_player_money(player),
        "name" => get_player_name(player),
        "avatar" => get_player_headid(player),
        "level" => 1,
        "state" => Map.get(player, :player_state, 0)
      }

      Map.put(acc, to_string(seat), player_data)
    end)
  end

  # 获取活跃玩家数量
  defp get_active_player_count(state) do
    state.players
    |> Enum.count(fn {_id, player} ->
      not Map.get(player, :is_fold, false)
    end)
  end

  # 执行弃牌 - 只处理弃牌逻辑，返回状态
  defp execute_fold(state, player) do
    Logger.info("🚫 [POT_BLIND] 执行弃牌: 玩家 #{player.numeric_id}")

    # 1. 设置玩家弃牌状态
    fold_state = set_player_fold(state, player.numeric_id)

    # 2. 广播弃牌结果
    broadcast_fold_result(fold_state, player)

    # 3. 检查游戏是否结束
    case check_game_end_condition(fold_state) do
      {:game_end, winner_id} ->
        # 游戏结束，进行结算并返回最终状态
        Logger.info("🏆 [POT_BLIND] 弃牌导致游戏结束，获胜者: #{winner_id}")
        final_state = end_game_with_winner(fold_state, winner_id)
        # 返回一个特殊的标记，告诉调用者游戏已结束
        {:game_ended, final_state}

      :continue ->
        # 游戏继续，返回状态让调用者决定下一步
        {:continue, fold_state}
    end
  end

  # 发送下注错误响应
  defp send_bet_error(state, player, reason) do
    error_data = %{
      "status" => 1,
      "error" => reason,
      "player_id" => player.numeric_id
    }

    send_game_message_to_player(state, player, get_protocol(:sc_bet), error_data)
  end

  # 广播弃牌结果 [5][1007] - 匹配用户提供的正确格式
  defp broadcast_fold_result(state, player) do
    fold_data = %{
      # 弃牌玩家ID
      "playerid" => player.numeric_id,
      # 弃牌玩家座位号
      "seatid" => Map.get(player, :seatid, 1),
      # 玩家ID (重复字段)
      "_playerid" => player.numeric_id
    }

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_fold),
      "data" => fold_data
    }

    Logger.info("📡 [POT_BLIND] 广播弃牌结果 [5][1007]: 玩家 #{player.numeric_id}")
    broadcast_to_room(state, message)
  end

  # 获取活跃玩家列表（未弃牌的玩家）
  defp get_active_players_list(state) do
    state.players
    |> Enum.filter(fn {_id, player} ->
      not Map.get(player, :is_fold, false)
    end)
    |> Enum.map(fn {id, player} -> {id, player} end)
    |> Enum.sort_by(fn {_id, player} -> Map.get(player, :seatid, 999) end)
  end

  # 获取下一个活跃玩家
  defp get_next_active_player(state, current_player_id) do
    # 按座位顺序获取所有玩家（包括已弃牌的）
    all_players_by_seat =
      state.players
      |> Enum.sort_by(fn {_id, player} -> Map.get(player, :seatid, 999) end)

    # 按座位顺序获取所有活跃玩家
    active_players =
      all_players_by_seat
      |> Enum.filter(fn {_id, player} ->
        not Map.get(player, :is_fold, false)
      end)

    case active_players do
      [] ->
        # 没有活跃玩家
        nil

      [{single_id, _}] when single_id == current_player_id ->
        # 只有当前玩家一个活跃玩家，没有下一个
        nil

      _ ->
        # 找到当前玩家在所有玩家中的位置
        current_index =
          Enum.find_index(all_players_by_seat, fn {id, _player} ->
            id == current_player_id
          end)

        case current_index do
          nil ->
            # 当前玩家不存在，返回第一个活跃玩家
            case active_players do
              [{first_id, _} | _] -> first_id
              [] -> nil
            end

          index ->
            # 🔧 修复：从当前玩家的下一个位置开始，按座位顺序找下一个活跃玩家
            total_players = length(all_players_by_seat)

            # 循环查找下一个活跃玩家
            find_next_active_player(
              all_players_by_seat,
              active_players,
              index,
              total_players,
              current_player_id
            )
        end
    end
  end

  # 辅助函数：从指定位置开始循环查找下一个活跃玩家
  defp find_next_active_player(
         all_players,
         active_players,
         start_index,
         total_count,
         current_player_id
       ) do
    # 从下一个位置开始查找
    Enum.reduce_while(1..total_count, nil, fn offset, _acc ->
      next_index = rem(start_index + offset, total_count)

      case Enum.at(all_players, next_index) do
        {player_id, _player} ->
          # 检查这个玩家是否是活跃玩家
          is_active = Enum.any?(active_players, fn {active_id, _} -> active_id == player_id end)

          if is_active do
            # 找到下一个活跃玩家
            if player_id == current_player_id do
              # 如果绕了一圈回到当前玩家，说明只有一个活跃玩家
              {:halt, nil}
            else
              {:halt, player_id}
            end
          else
            # 这个玩家不活跃，继续找下一个
            {:cont, nil}
          end

        nil ->
          {:cont, nil}
      end
    end)
  end

  # 检查游戏结束条件
  defp check_game_end_condition(state) do
    active_players = get_active_players(state)

    case map_size(active_players) do
      # 没有活跃玩家
      0 ->
        {:game_end, nil}

      1 ->
        # 只剩一个玩家，该玩家获胜
        [{winner_id, _}] = Map.to_list(active_players)
        {:game_end, winner_id}

      # 游戏继续
      _ ->
        :continue
    end
  end

  # 发牌并开始下注 - 只开始下注，发牌协议在轮次切换时发送
  defp deal_cards_and_start_betting(state) do
    Logger.info("🃏 [POT_BLIND] 开始下注阶段")

    # 1. 更新游戏状态为下注阶段
    current_player_id =
      Map.get(state, :dealer_id) ||
        case get_players_by_seat_order(state) do
          [{first_player_id, _} | _] -> first_player_id
          [] -> nil
        end

    betting_state = %{
      state
      | game_phase: :betting,
        # 重新从庄家开始
        current_player_id: current_player_id,
        turn_start_time: System.system_time(:millisecond)
    }

    # 2. 开始下注轮次
    start_betting_round(betting_state)
  end

  # 开始下注轮次
  defp start_betting_round(state) do
    Logger.info("💰 [POT_BLIND] 开始下注轮次")

    # 广播轮次开始协议 [5][1010]
    broadcast_turn_start(state)

    # 启动行动倒计时，并返回更新后的状态
    start_action_timer(state)
  end

  # 检查游戏结束条件 - 只有在没有下一个玩家时调用
  defp check_round_end(state) do
    Logger.info("🔄 [POT_BLIND] 检查游戏结束条件")

    # 检查游戏结束条件
    case check_game_end_condition(state) do
      {:game_end, winner_id} ->
        Logger.info("🏆 [POT_BLIND] 游戏结束，获胜者: #{winner_id}")
        end_game_with_winner(state, winner_id)

      :continue ->
        Logger.info("🎮 [POT_BLIND] 游戏继续，但没有下一个玩家，这可能是异常情况")
        state
    end
  end

  # 游戏结束，确定获胜者
  defp end_game_with_winner(state, winner_id) do
    Logger.info("🏆 [POT_BLIND] 游戏结束，获胜者: #{winner_id}")

    # 执行游戏结算
    execute_game_settlement(state, winner_id)
  end

  # ==================== 比牌相关函数 ====================

  # 验证比牌请求
  defp validate_competition_request(state, player, target_player_id) do
    cond do
      is_nil(target_player_id) ->
        {:error, "没有找到可比牌的上家"}

      target_player_id == player.numeric_id ->
        {:error, "不能与自己比牌"}

      true ->
        case Map.get(state.players, target_player_id) do
          nil ->
            {:error, "目标玩家不存在"}

          target_player ->
            if Map.get(target_player, :is_fold, false) do
              {:error, "目标玩家已弃牌"}
            else
              {:ok, target_player}
            end
        end
    end
  end

  # 执行比牌
  defp execute_competition(state, player, target_player) do
    Logger.info("⚔️ [POT_BLIND] 执行比牌: #{player.numeric_id} vs #{target_player.numeric_id}")

    # 1. 比较牌型
    comparison_result = PotBlindGameLogic.compare_cards(player.cards, target_player.cards)

    # 2. 确定胜负
    {winner, loser} =
      case comparison_result do
        :player1_wins -> {player, target_player}
        :player2_wins -> {target_player, player}
        # 平局时当前玩家获胜
        :tie -> {player, target_player}
      end

    # 3. 设置失败者弃牌
    fold_state = set_player_fold(state, loser.numeric_id)

    # 4. 广播比牌结果
    broadcast_competition_result(fold_state, player, target_player, winner, comparison_result)

    # 5. 启动比牌动画2秒定时器，等待动画播放完成后再执行后续操作
    start_competition_animation_timer(fold_state, winner.numeric_id)
  end

  # 发送比牌错误响应
  defp send_competition_error(state, player, reason) do
    error_data = %{
      "status" => 1,
      "error" => reason,
      "player_id" => player.numeric_id
    }

    send_game_message_to_player(state, player, get_protocol(:sc_competition), error_data)
  end

  # 广播比牌结果 [5][1005] - 根据房间情况决定是否发送牌的具体数据
  defp broadcast_competition_result(state, player, target_player, winner, comparison_result) do
    # 确定谁是操作玩家（发起比牌的玩家）和被比牌玩家
    is_player_winner = winner.numeric_id == player.numeric_id
    is_target_winner = winner.numeric_id == target_player.numeric_id

    # 检查房间中活跃玩家数量（未弃牌的玩家）
    active_players = get_active_players(state)
    active_count = map_size(active_players)

    # 如果只剩2个活跃玩家（就是这两个比牌的玩家），发送牌的具体数据
    # 如果还有其他未弃牌的玩家，不发送牌的具体数据
    should_send_cards = active_count <= 2

    Logger.info("🃏 [POT_BLIND] 比牌时活跃玩家数: #{active_count}, 是否发送牌数据: #{should_send_cards}")

    # 构建比牌结果数据格式
    competition_data =
      if should_send_cards do
        # 只剩2人，发送完整的牌数据（因为比牌后游戏必然结束）
        %{
          # 发起比牌的玩家ID
          "playerid" => player.numeric_id,
          # 发起比牌的玩家座位号
          "seatid" => Map.get(player, :seatid, 1),
          "compseat" => %{
            # 被比牌玩家是否获胜
            "iswin" => if(is_target_winner, do: 1, else: 0),
            # 被比牌玩家座位号
            "seatid" => Map.get(target_player, :seatid, 1),
            # 牌型
            "cardtype" =>
              PotBlindGameLogic.get_card_type_number(Map.get(target_player, :cards, [])),
            # 被比牌玩家的牌
            "cards" => build_cards_data_for_competition(Map.get(target_player, :cards, []))
          },
          "optseat" => %{
            # 操作玩家是否获胜
            "iswin" => if(is_player_winner, do: 1, else: 0),
            # 操作玩家座位号
            "seatid" => Map.get(player, :seatid, 1),
            # 牌型
            "cardtype" => PotBlindGameLogic.get_card_type_number(Map.get(player, :cards, [])),
            # 操作玩家的牌
            "cards" => build_cards_data_for_competition(Map.get(player, :cards, []))
          },
          # 玩家ID
          "_playerid" => player.numeric_id
        }
      else
        # 还有其他玩家，只发送胜负结果，不发送牌数据（游戏继续）
        %{
          # 发起比牌的玩家ID
          "playerid" => player.numeric_id,
          # 发起比牌的玩家座位号
          "seatid" => Map.get(player, :seatid, 1),
          "compseat" => %{
            # 被比牌玩家是否获胜
            "iswin" => if(is_target_winner, do: 1, else: 0),
            # 被比牌玩家座位号
            "seatid" => Map.get(target_player, :seatid, 1)
          },
          "optseat" => %{
            # 操作玩家是否获胜
            "iswin" => if(is_player_winner, do: 1, else: 0),
            # 操作玩家座位号
            "seatid" => Map.get(player, :seatid, 1)
          },
          # 玩家ID
          "_playerid" => player.numeric_id
        }
      end

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_competition),
      "data" => competition_data
    }

    Logger.info(
      "📡 [POT_BLIND] 广播比牌结果 [5][1005]: 玩家#{player.numeric_id}(座位#{Map.get(player, :seatid, 1)}) vs 玩家#{target_player.numeric_id}(座位#{Map.get(target_player, :seatid, 1)}), 获胜者: #{winner.numeric_id}, 发送牌数据: #{should_send_cards}"
    )

    broadcast_to_room(state, message)
  end

  # 构建比牌协议中的牌数据格式（只在最后2人比牌时使用）
  defp build_cards_data_for_competition(cards) do
    cards
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {card, index}, acc ->
      card_data = %{
        "color" => convert_suit_to_color(Map.get(card, :suit, 1)),
        "number" => Map.get(card, :value, 0)
      }

      Map.put(acc, to_string(index), card_data)
    end)
  end

  # 将内部 suit 值 (1,2,3,4) 转换为前端期望的 color 值 (3,4,5,6)
  defp convert_suit_to_color(suit) do
    case suit do
      # 黑桃 -> 3
      1 -> 3
      # 红桃 -> 4
      2 -> 4
      # 梅花 -> 5
      3 -> 5
      # 方块 -> 6
      4 -> 6
      # 默认值
      _ -> 3
    end
  end

  # 将内部 suit 值 (1,2,3,4) 转换为前端期望的 color 值 (3,4,5,6)
  defp convert_suit_to_color(suit) do
    case suit do
      # 黑桃 -> 3
      1 -> 3
      # 红桃 -> 4
      2 -> 4
      # 梅花 -> 5
      3 -> 5
      # 方块 -> 6
      4 -> 6
      # 默认值
      _ -> 3
    end
  end

  # ==================== 游戏结算函数 ====================

  # 执行游戏结算
  defp execute_game_settlement(state, winner_id) do
    Logger.info("💰 [POT_BLIND] 开始游戏结算，获胜者: #{winner_id}")

    # 1. 计算奖金
    pot_amount = state.pot_amount || 0

    # 2. 更新获胜者金币
    settlement_state = award_pot_to_winner(state, winner_id, pot_amount)

    # 3. 广播结算结果
    broadcast_game_settlement(settlement_state, winner_id, pot_amount)

    # 4. 重置房间状态，🔧 修复：传递赢家ID作为下一局庄家
    reset_room_for_next_game(settlement_state, winner_id)
  end

  # 将底池奖励给获胜者 (参考jhandi_munda积分处理)
  defp award_pot_to_winner(state, winner_id, pot_amount) do
    case Map.get(state.players, winner_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 获胜者 #{winner_id} 不存在")
        state

      winner ->
        # 1. 增加获胜者积分 (参考jhandi_munda)
        award_state = add_player_points(state, winner_id, pot_amount)

        # 2. 更新获胜者统计数据
        updated_winner = Map.get(award_state.players, winner_id)

        final_winner = %{
          updated_winner
          | total_winnings: Map.get(updated_winner, :total_winnings, 0) + pot_amount,
            hands_won: Map.get(updated_winner, :hands_won, 0) + 1
        }

        updated_players = Map.put(award_state.players, winner_id, final_winner)
        final_state = %{award_state | players: updated_players}

        # 3. 发送积分更新通知 (参考jhandi_munda)
        send_money_update_notification(final_state, winner)

        Logger.info(
          "💰 [POT_BLIND] 获胜者 #{winner_id} 获得奖金: #{pot_amount}, 新余额: #{get_player_points(final_state, winner_id)}"
        )

        final_state
    end
  end

  # 广播游戏结算结果 [5][1011]
  defp broadcast_game_settlement(state, winner_id, pot_amount) do
    # 构建结算数据
    settlement_data = build_settlement_data(state, winner_id, pot_amount)

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_jieshuan),
      "data" => settlement_data
    }

    Logger.info("📡 [POT_BLIND] 广播游戏结算 [5][1011]: 获胜者 #{winner_id}, 奖金: #{pot_amount}")
    broadcast_to_room(state, message)

    state
  end

  # 构建结算数据 - 匹配用户提供的正确格式
  defp build_settlement_data(state, winner_id, pot_amount) do
    # 获取获胜者座位号
    winner_seat =
      case Map.get(state.players, winner_id) do
        nil -> 1
        winner -> Map.get(winner, :seatid, 1)
      end

    # 构建玩家列表，下标从1开始
    player_list = build_settlement_player_list(state, winner_id, pot_amount)

    %{
      # 系统开牌标识
      "sysopencard" => 0,
      # 获胜者座位号
      "winner" => winner_seat,
      # 奖池金额
      "draw" => pot_amount,
      # 玩家列表 (下标从1开始)
      "playerlist" => player_list,
      # 获胜者玩家ID
      "_playerid" => winner_id
    }
  end

  # 构建结算中的玩家列表，下标从1开始
  defp build_settlement_player_list(state, winner_id, pot_amount) do
    # 获取所有玩家的结算信息
    player_data_list =
      state.players
      |> Enum.map(fn {player_id, player} ->
        build_settlement_player_data(player, player_id, winner_id, pot_amount)
      end)
      # 按座位号排序
      |> Enum.sort_by(fn player_data -> player_data["seatid"] end)

    # 转换为下标从1开始的Map格式
    player_data_list
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {player_data, index}, acc ->
      Map.put(acc, to_string(index), player_data)
    end)
  end

  # 构建结算中的单个玩家数据
  defp build_settlement_player_data(player, player_id, winner_id, pot_amount) do
    is_winner = player_id == winner_id

    # 计算金币变化
    total_bet = Map.get(player, :total_bet, 0)
    win_amount = if is_winner, do: pot_amount, else: 0
    change_money = win_amount - total_bet

    # 确定玩家状态
    player_state =
      cond do
        # 获胜者
        is_winner -> 1
        # 弃牌
        Map.get(player, :is_fold, false) -> 2
        # 失败者
        true -> 3
      end

    # 构建牌数据
    cards_data = build_cards_data_for_settlement(Map.get(player, :cards, []))

    %{
      # 玩家ID
      "playerid" => player_id,
      # 座位号
      "seatid" => Map.get(player, :seatid, 1),
      # 玩家状态 (1=获胜, 2=弃牌, 3=失败)
      "state" => player_state,
      # 金币变化 (正数=赢钱, 负数=输钱)
      "changemoney" => change_money,
      # 牌型
      "cardtype" => PotBlindGameLogic.get_card_type_number(Map.get(player, :cards, [])),
      # 牌数据
      "cards" => cards_data
    }
  end

  # 构建结算协议中的牌数据格式
  defp build_cards_data_for_settlement(cards) do
    cards
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {card, index}, acc ->
      card_data = %{
        "color" => convert_suit_to_color(Map.get(card, :suit, 1)),
        "number" => Map.get(card, :value, 0)
      }

      Map.put(acc, to_string(index), card_data)
    end)
  end

  # 重置房间状态准备下一局游戏
  defp reset_room_for_next_game(state, winner_id \\ nil) do
    Logger.info("🔄 [POT_BLIND] 重置房间状态准备下一局，上一局赢家: #{winner_id}")

    # 取消所有定时器
    cancel_previous_timer(state)

    # 重置游戏状态，🔧 修复：保存上一局赢家作为下一局庄家
    reset_state = %{
      state
      | room_state: :waiting,
        game_phase: :waiting,
        current_player_id: nil,
        pot_amount: 0,
        total_pot: 0,
        current_bet: 0,
        round: 1,
        turn: 1,
        # 重置当前下注倍数
        curtimes: 1,
        # 清除定时器引用
        action_timer_ref: nil,
        # 清除轮次开始时间
        turn_start_time: nil
    }

    # 🔧 修复：安全地添加 next_dealer_id 字段
    final_reset_state = Map.put(reset_state, :next_dealer_id, winner_id)

    # 重置所有玩家状态（保留暂离相关状态）
    reset_players =
      final_reset_state.players
      |> Enum.map(fn {player_id, player} ->
        reset_player = %{
          player
          | cards: [],
            bet_amount: 0,
            total_bet: 0,
            current_bet: 0,
            is_fold: false,
            is_seen_cards: false,
            has_acted: false,
            last_bet_type: :none,
            last_bet_amount: 0,
            competition_target: nil,
            competition_result: nil,
            hands_played: Map.get(player, :hands_played, 0) + 1,
            player_state: get_player_state(:play),
            is_gaming: 0
            # 注意：保留暂离相关字段
            # away_notify_timer_ref, away_kick_timer_ref, away_status, away_start_time
            # 这些字段不重置，让暂离流程继续进行
        }

        {player_id, reset_player}
      end)
      |> Enum.into(%{})

    final_state = %{final_reset_state | players: reset_players}

    # 广播房间状态变化 [4][5] - 通知前端游戏已结束，回到等待状态
    broadcast_pot_blind_room_state(final_state)

    # 启动3秒倒计时，检查是否可以开始下一局
    start_next_game_timer(final_state)

    final_state
  end

  @doc """
  启动下一局游戏的3秒倒计时
  """
  defp start_next_game_timer(state) do
    Logger.info("⏰ [POT_BLIND] 启动下一局游戏3秒倒计时")

    # 取消之前的下一局定时器（如果存在）
    cancel_next_game_timer(state)

    # 启动3秒倒计时
    timer_ref = Process.send_after(self(), :next_game_timer_expired, 5000)

    # 保存定时器引用到状态中
    updated_state = Map.put(state, :next_game_timer_ref, timer_ref)

    Logger.info("⏰ [POT_BLIND] 下一局游戏定时器已启动: 3秒后检查开始条件")

    updated_state
  end

  @doc """
  取消下一局游戏定时器
  """
  defp cancel_next_game_timer(state) do
    case Map.get(state, :next_game_timer_ref) do
      nil ->
        Logger.debug("⏰ [POT_BLIND] 没有下一局游戏定时器需要取消")

      timer_ref ->
        Process.cancel_timer(timer_ref)
        Logger.info("⏰ [POT_BLIND] 已取消下一局游戏定时器")
    end

    # 清除定时器引用
    Map.put(state, :next_game_timer_ref, nil)
  end

  @doc """
  启动比牌动画2秒定时器

  ## 参数
  - state: 当前游戏状态
  - winner_id: 比牌获胜者ID

  ## 返回
  - 更新后的游戏状态
  """
  defp start_competition_animation_timer(state, winner_id) do
    Logger.info("🎬 [POT_BLIND] 启动比牌动画2秒定时器，获胜者: #{winner_id}")

    # 取消之前的比牌动画定时器（如果存在）
    cancel_competition_animation_timer(state)

    # 启动2秒倒计时
    timer_ref = Process.send_after(self(), {:competition_animation_expired, winner_id}, 4000)

    # 保存定时器引用和获胜者信息到状态中
    updated_state =
      state
      |> Map.put(:competition_animation_timer_ref, timer_ref)
      |> Map.put(:competition_winner_id, winner_id)
      # 设置游戏阶段为比牌动画
      |> Map.put(:game_phase, :competition_animation)

    Logger.info("🎬 [POT_BLIND] 比牌动画定时器已启动: 2秒后继续游戏流程")

    updated_state
  end

  @doc """
  取消比牌动画定时器
  """
  defp cancel_competition_animation_timer(state) do
    case Map.get(state, :competition_animation_timer_ref) do
      nil ->
        Logger.debug("🎬 [POT_BLIND] 没有比牌动画定时器需要取消")

      timer_ref ->
        Process.cancel_timer(timer_ref)
        Logger.info("🎬 [POT_BLIND] 已取消比牌动画定时器")
    end

    # 清除定时器引用和相关状态
    state
    |> Map.put(:competition_animation_timer_ref, nil)
    |> Map.put(:competition_winner_id, nil)
    # 恢复到下注阶段
    |> Map.put(:game_phase, :betting)
  end

  @doc """
  启动玩家暂离流程

  ## 目的
  防止玩家一直被自动弃牌而输光所有金币，给玩家重新连接的机会

  ## 触发条件
  只要玩家操作超时被自动弃牌，无论游戏是否结束都要启动此流程

  ## 流程
  1. 立即启动3秒定时器，到期后发送[4][9]暂离通知
  2. 发送[4][9]后启动5秒定时器，等待玩家回复[4][10]
  3. 如果5秒内收到[4][10]，取消定时器，玩家回归
  4. 如果5秒内没收到[4][10]，踢出玩家

  ## 参数
  - state: 当前游戏状态
  - player_id: 暂离的玩家ID

  ## 返回
  - 更新后的游戏状态
  """
  defp start_player_away_process(state, player_id) do
    Logger.info("🚶 [POT_BLIND] 启动玩家 #{player_id} 暂离流程")

    # 取消该玩家之前的暂离定时器（如果存在）
    cancel_player_away_timers(state, player_id)

    # 启动3秒定时器，到期后发送[4][9]暂离通知
    timer_ref = Process.send_after(self(), {:player_away_notify, player_id}, 3000)

    # 保存暂离定时器信息到玩家数据中
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在，无法启动暂离流程")
        state

      player ->
        # 安全地更新玩家状态，添加暂离定时器信息
        updated_player =
          player
          |> Map.put(:away_notify_timer_ref, timer_ref)
          # 等待发送暂离通知
          |> Map.put(:away_status, :waiting_notify)
          |> Map.put(:away_start_time, System.system_time(:millisecond))

        updated_state = %{state | players: Map.put(state.players, player_id, updated_player)}

        Logger.info("🚶 [POT_BLIND] 玩家 #{player_id} 暂离流程已启动: 3秒后发送暂离通知")
        updated_state
    end
  end

  @doc """
  取消玩家的所有暂离定时器

  ## 参数
  - state: 当前游戏状态
  - player_id: 玩家ID

  ## 返回
  - 更新后的游戏状态
  """
  defp cancel_player_away_timers(state, player_id) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.debug("🚶 [POT_BLIND] 玩家 #{player_id} 不存在，无需取消暂离定时器")
        state

      player ->
        # 安全地取消暂离通知定时器
        away_notify_timer_ref = Map.get(player, :away_notify_timer_ref)

        if away_notify_timer_ref do
          Process.cancel_timer(away_notify_timer_ref)
          Logger.info("🚶 [POT_BLIND] 已取消玩家 #{player_id} 的暂离通知定时器")
        end

        # 安全地取消踢出定时器
        away_kick_timer_ref = Map.get(player, :away_kick_timer_ref)

        if away_kick_timer_ref do
          Process.cancel_timer(away_kick_timer_ref)
          Logger.info("🚶 [POT_BLIND] 已取消玩家 #{player_id} 的踢出定时器")
        end

        # 安全地清除玩家的暂离状态
        updated_player =
          player
          |> Map.put(:away_notify_timer_ref, nil)
          |> Map.put(:away_kick_timer_ref, nil)
          |> Map.put(:away_status, nil)
          |> Map.put(:away_start_time, nil)

        updated_state = %{state | players: Map.put(state.players, player_id, updated_player)}

        Logger.info("🚶 [POT_BLIND] 已清除玩家 #{player_id} 的所有暂离定时器和状态")
        updated_state
    end
  end

  @doc """
  发送玩家暂离通知 [4][9]

  ## 参数
  - state: 当前游戏状态
  - player: 玩家数据
  """
  defp send_player_away_notification(state, player) do
    away_data = %{
      "playerid" => player.numeric_id,
      # 暂离原因：操作超时
      "reason" => "action_timeout",
      # 5秒超时时间
      "timeout" => 5000,
      "_playerid" => player.numeric_id
    }

    message = %{
      "mainId" => 4,
      "subId" => 9,
      "data" => away_data
    }

    send_to_player(state, player, message)
    Logger.info("📡 [POT_BLIND] 发送暂离通知 [4][9] 给玩家 #{player.numeric_id}")
    # send_game_message_to_player(state, player, 9, away_data)
  end

  @doc """
  处理玩家回复暂离通知 [4][10]

  ## 参数
  - state: 当前游戏状态
  - player: 玩家数据
  - message: 消息数据

  ## 返回
  - 更新后的游戏状态
  """
  def handle_player_away_return(state, player, message) do
    player_id = player.numeric_id
    Logger.info("🚶 [POT_BLIND] 收到玩家 #{player_id} 的暂离回复 [4][10]")

    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在")
        state

      current_player ->
        # 检查玩家是否在暂离状态
        away_status = Map.get(current_player, :away_status)
        kick_on_next_timeout = Map.get(current_player, :kick_on_next_timeout, false)

        if away_status in [:waiting_return, :pending_kick] or kick_on_next_timeout do
          Logger.info("✅ [POT_BLIND] 玩家 #{player_id} 成功回复暂离通知，取消所有暂离流程")

          # 取消所有暂离相关定时器和状态
          updated_state = cancel_player_away_timers(state, player_id)

          # 清除踢出标记
          case Map.get(updated_state.players, player_id) do
            nil ->
              updated_state

            player_data ->
              cleared_player =
                player_data
                |> Map.put(:kick_on_next_timeout, false)
                |> Map.put(:away_status, nil)

              final_state = %{
                updated_state
                | players: Map.put(updated_state.players, player_id, cleared_player)
              }

              # 发送回复确认给玩家
              send_away_return_confirmation(final_state, cleared_player)

              Logger.info("✅ [POT_BLIND] 玩家 #{player_id} 已回归，所有暂离状态已清除")
              final_state
          end
        else
          Logger.info("⚠️ [POT_BLIND] 玩家 #{player_id} 不在暂离状态，忽略回复")
          state
        end
    end
  end

  @doc """
  发送暂离回复确认给玩家

  ## 参数
  - state: 当前游戏状态
  - player: 玩家数据
  """
  defp send_away_return_confirmation(state, player) do
    data = %{
      "playerid" => player.numeric_id,
      # 已回归状态
      "status" => "returned",
      "_playerid" => player.numeric_id
    }

    message = %{
      "mainId" => 4,
      "subId" => 11,
      "data" => data
    }

    send_to_player(state, player, message)
    Logger.info("📡 [POT_BLIND] 发送暂离回复确认给玩家 #{player.numeric_id}")
    # send_game_message_to_player(state, player, 10, confirmation_data)
  end

  @doc """
  在玩家自动弃牌后踢出暂离玩家

  ## 参数
  - state: 当前游戏状态
  - player_id: 要踢出的玩家ID

  ## 返回
  - 更新后的游戏状态
  """
  defp kick_away_player_after_fold(state, player_id) do
    Logger.info("🚫 [POT_BLIND] 自动弃牌后踢出暂离玩家 #{player_id}")

    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在，无法踢出")
        state

      player ->
        Logger.info("🚫 [POT_BLIND] 使用标准on_player_left流程移除暂离玩家 #{player_id}")

        # 使用标准的on_player_left处理玩家离开
        # 这样可以确保所有离开逻辑都被正确处理（广播、座位释放、定时器清理等）
        updated_state = on_player_left(state, player)

        Logger.info("🚫 [POT_BLIND] 暂离玩家 #{player_id} 已通过on_player_left标准流程移除")
        updated_state
    end
  end

  @doc """
  检查是否可以开始下一局游戏

  检查条件：
  1. 人数足够（至少2人）
  2. 所有玩家都有足够的金币
  3. 房间状态为等待中
  """
  defp check_next_game_conditions(state) do
    Logger.info("🔍 [POT_BLIND] 检查下一局游戏开始条件")

    # 检查房间状态
    if state.room_state != :waiting do
      Logger.info("❌ [POT_BLIND] 房间状态不是等待中: #{state.room_state}")
      {:error, "房间状态不正确"}
    else
      # 检查人数
      player_count = map_size(state.players)
      min_players = state.config[:min_players] || 2

      if player_count < min_players do
        Logger.info("❌ [POT_BLIND] 人数不足: #{player_count}/#{min_players}")
        {:error, "人数不足"}
      else
        # 检查所有玩家的金币
        insufficient_players = check_players_money(state)

        if length(insufficient_players) > 0 do
          Logger.info("❌ [POT_BLIND] 以下玩家金币不足: #{inspect(insufficient_players)}")
          # 踢出金币不足的玩家
          kick_insufficient_players(state, insufficient_players)
          {:error, "部分玩家金币不足已踢出"}
        else
          Logger.info("✅ [POT_BLIND] 所有条件满足，可以开始下一局")
          {:ok, "条件满足"}
        end
      end
    end
  end

  @doc """
  检查所有玩家的金币是否足够

  ## 返回
  - 返回金币不足的玩家ID列表
  """
  defp check_players_money(state) do
    config = state.config || %{}
    # 至少需要一个底注的金币
    min_money = config[:difen] || 100

    state.players
    |> Enum.filter(fn {_player_id, player} ->
      player_money = get_player_money(player)
      player_money < min_money
    end)
    |> Enum.map(fn {player_id, _player} -> player_id end)
  end

  @doc """
  踢出金币不足的玩家
  """
  defp kick_insufficient_players(state, insufficient_player_ids) do
    Logger.info("🚫 [POT_BLIND] 踢出金币不足的玩家: #{inspect(insufficient_player_ids)}")

    # 为每个金币不足的玩家发送踢出消息
    Enum.each(insufficient_player_ids, fn player_id ->
      case Map.get(state.players, player_id) do
        nil ->
          Logger.warning("⚠️ [POT_BLIND] 玩家 #{player_id} 不存在，无法踢出")

        player ->
          # 广播玩家被踢出消息
          broadcast_delete_player(state, player, "insufficient_funds")
          Logger.info("🚫 [POT_BLIND] 玩家 #{player_id} 因金币不足被踢出")
      end
    end)

    # 从房间中移除这些玩家
    updated_players =
      state.players
      |> Enum.reject(fn {player_id, _player} ->
        player_id in insufficient_player_ids
      end)
      |> Enum.into(%{})

    updated_state = %{state | players: updated_players}

    # 广播房间状态变化
    broadcast_pot_blind_room_state(updated_state)

    Logger.info("🔄 [POT_BLIND] 已移除 #{length(insufficient_player_ids)} 个金币不足的玩家")
    updated_state
  end

  # ==================== PotBlind 下注处理函数 ====================

  # 处理PotBlind游戏的下注逻辑 - 负责完整的下注流程控制
  defp process_pot_blind_bet(state, player, fill_value) do
    Logger.info("💰 [POT_BLIND] 处理PotBlind下注: 玩家 #{player.numeric_id}, fill: #{fill_value}")

    # 获取当前curtimes和低分
    current_curtimes = Map.get(state, :curtimes, 1)
    difen = state.config[:difen] || 100

    # 计算下注金额和新的curtimes
    case calculate_pot_blind_bet_amount(current_curtimes, difen, fill_value) do
      {:ok, bet_amount, new_curtimes} ->
        # 验证下注条件
        case validate_pot_blind_bet(state, player, bet_amount) do
          {:ok, validated_amount} ->
            # 执行下注
            case execute_pot_blind_bet(state, player, validated_amount, fill_value, new_curtimes) do
              {:ok, updated_state} ->
                # 下注成功后，检查游戏流程控制
                handle_post_bet_flow(updated_state)

              {:error, reason} ->
                {:error, reason}
            end

          {:error, reason} ->
            {:error, reason}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 处理下注后的流程控制 - 系统比牌检查和轮次切换
  defp handle_post_bet_flow(state) do
    Logger.info("🎮 [POT_BLIND] 处理下注后流程控制")

    # 检查是否触发系统比牌
    case check_system_showdown_trigger(state) do
      {:trigger_showdown, reason} ->
        Logger.info("🎯 [POT_BLIND] 触发系统比牌: #{reason}")
        # 系统比牌会返回 {:ok, final_state}，直接返回
        start_system_showdown(state, reason)

      :continue_betting ->
        # 继续下注，切换到下一个玩家
        Logger.info("🔄 [POT_BLIND] 继续下注，切换到下一个玩家")
        next_turn_state = set_next_seat_id(state)
        {:ok, next_turn_state}
    end
  end

  # 计算下注金额 - 正确的 PotBlind 逻辑
  defp calculate_pot_blind_bet_amount(curtimes, difen, fill_value) do
    # 验证fill值是否在有效范围内 (1-4)
    if fill_value >= 1 and fill_value <= length(@bet_multipliers) do
      # fill_value 是 multiplier 数组的下标（1-4）
      # 获取对应的倍数值：fill=1 -> multiplier=1, fill=2 -> multiplier=2, etc.
      multiplier = Enum.at(@bet_multipliers, fill_value - 1)

      # 下注金额计算：multiplier × difen × curtimes
      # 前端的 curbet = this.curtimes * this.difen * fill 中，fill 实际上就是 multiplier 值
      bet_amount = multiplier * difen * curtimes

      # 计算新的curtimes = multiplier × curtimes
      new_curtimes = multiplier * curtimes

      Logger.info(
        "💰 [POT_BLIND] 下注计算: fill=#{fill_value}, multiplier=#{multiplier}, curtimes=#{curtimes}, difen=#{difen}"
      )

      Logger.info("💰 [POT_BLIND] 计算结果: 金额=#{bet_amount}, 新curtimes=#{new_curtimes}")

      {:ok, bet_amount, new_curtimes}
    else
      {:error, "无效的fill值: #{fill_value}，有效范围: 1-#{length(@bet_multipliers)}"}
    end
  end

  # 检查是否触发系统比牌
  defp check_system_showdown_trigger(state) do
    pot_amount = Map.get(state, :pot_amount, 0)
    pot_limit = state.config[:pot_limit] || 61440
    blind_limit = state.config[:blind_limit] || 3840
    current_curtimes = Map.get(state, :curtimes, 1)

    cond do
      # 底池金额达到限制
      pot_amount >= pot_limit ->
        {:trigger_showdown, "底池金额达到限制: #{pot_amount}/#{pot_limit}"}

      # 盲注倍数达到限制
      current_curtimes >= blind_limit ->
        {:trigger_showdown, "盲注倍数达到限制: #{current_curtimes}/#{blind_limit}"}

      # 继续下注
      true ->
        :continue_betting
    end
  end

  # 开始系统比牌
  defp start_system_showdown(state, reason) do
    Logger.info("🎯 [POT_BLIND] 开始系统比牌: #{reason}")

    # 获取所有活跃玩家（未弃牌的玩家）
    active_players = get_active_players_for_showdown(state)

    if length(active_players) < 2 do
      # 如果只有一个玩家，直接结算
      Logger.info("🎯 [POT_BLIND] 只有一个活跃玩家，直接结算")
      final_state = settle_game_with_single_winner(state, hd(active_players))
      {:ok, final_state}
    else
      # 在开始比牌前，让所有活跃玩家补齐下注到当前最高金额
      equalized_state = equalize_all_active_players_bets(state, active_players)

      # 开始轮次比牌
      showdown_state = %{
        equalized_state
        | game_phase: :showdown,
          showdown_players: active_players,
          showdown_round: 1,
          current_showdown_pair: nil
      }

      start_next_showdown_round(showdown_state)
    end
  end

  # 获取所有活跃玩家（用于比牌）
  defp get_active_players_for_showdown(state) do
    state.players
    |> Enum.filter(fn {_id, player} ->
      Map.get(player, :status, :playing) == :playing and
        Map.get(player, :player_state, :play) == :play
    end)
    |> Enum.map(fn {_id, player} -> player end)
    |> Enum.sort_by(fn player -> Map.get(player, :seatid, 1) end)
  end

  # 开始下一轮比牌
  defp start_next_showdown_round(state) do
    remaining_players = Map.get(state, :showdown_players, [])

    case length(remaining_players) do
      0 ->
        Logger.error("🎯 [POT_BLIND] 比牌错误：没有剩余玩家")
        {:ok, state}

      1 ->
        # 只剩一个玩家，比牌结束
        winner = hd(remaining_players)
        Logger.info("🎯 [POT_BLIND] 比牌结束，最终赢家: #{winner.numeric_id}")
        final_state = settle_game_with_single_winner(state, winner)
        {:ok, final_state}

      _ ->
        # 取前两个玩家进行比牌
        [player1, player2 | rest] = remaining_players

        # 设置当前比牌对
        showdown_state = %{
          state
          | current_showdown_pair: {player1, player2},
            # 剩余玩家等待下一轮
            showdown_players: rest
        }

        # 广播比牌开始
        broadcast_showdown_start(showdown_state, player1, player2)

        # 设置3秒后的比牌结果定时器
        Process.send_after(self(), {:showdown_result, player1, player2}, 3000)

        {:ok, showdown_state}
    end
  end

  # 验证PotBlind下注条件 (参考jhandi_munda积分处理)
  defp validate_pot_blind_bet(state, player, bet_amount) do
    # 使用RoomBase的积分系统获取真实余额
    player_money = get_player_points(state, player.numeric_id)

    cond do
      bet_amount <= 0 ->
        {:error, "下注金额必须大于0"}

      bet_amount > player_money ->
        {:error, "金币不足，当前金币: #{player_money}，需要: #{bet_amount}"}

      # 检查是否超过盲注限制
      bet_amount > (state.config[:blind_limit] || 3840) ->
        {:error, "下注金额超过限制: #{state.config[:blind_limit] || 3840}"}

      true ->
        {:ok, bet_amount}
    end
  end

  # 执行PotBlind下注 - 只负责下注逻辑，不处理轮次切换 (参考jhandi_munda积分处理)
  defp execute_pot_blind_bet(state, player, bet_amount, fill_value, new_curtimes) do
    Logger.info(
      "💰 [POT_BLIND] 执行PotBlind下注: 玩家 #{player.numeric_id}, 金额: #{bet_amount}, fill: #{fill_value}, 新curtimes: #{new_curtimes}"
    )

    # 1. 扣除玩家积分 (参考jhandi_munda)
    deduct_state = subtract_player_points(state, player.numeric_id, bet_amount)

    # 2. 更新玩家下注信息
    bet_state = update_player_bet(deduct_state, player.numeric_id, bet_amount, :bet)

    # 3. 更新底池和游戏状态
    updated_state = %{
      bet_state
      | pot_amount: bet_state.pot_amount + bet_amount,
        current_bet: max(bet_state.current_bet, bet_amount),
        # 更新curtimes
        curtimes: new_curtimes
    }

    # 4. 发送积分更新通知 (参考jhandi_munda)
    send_money_update_notification(updated_state, player)

    # 5. 广播下注结果
    broadcast_pot_blind_bet_result(updated_state, player, bet_amount, fill_value)

    # 6. 返回更新后的状态，让调用者决定下一步操作
    {:ok, updated_state}
  end

  # 注意：deduct_player_money 函数已删除，现在使用 RoomBase 的 subtract_player_points 函数

  # 获取下注类型原子
  defp get_bet_type_atom(bet_type) do
    case bet_type do
      # 跟注
      1 -> :call
      # 加注
      2 -> :raise
      # 默认下注
      _ -> :bet
    end
  end

  # 广播PotBlind下注结果 [5][1003] - 匹配用户提供的正确格式
  defp broadcast_pot_blind_bet_result(state, player, bet_amount, fill_value, bet_type \\ :fill) do
    # 获取更新后的玩家信息
    updated_player = Map.get(state.players, player.numeric_id, player)

    # 根据下注类型确定 bettype 值
    bettype_value =
      case bet_type do
        # 0 - 底注（盲注）
        :blind -> @bet_types.bottom
        # 1 - 下注
        :fill -> @bet_types.fill
        # 2 - 比牌
        :competition -> @bet_types.competition
        # 3 - 弃牌
        :fold -> @bet_types.fold
        # 默认为下注
        _ -> @bet_types.fill
      end

    bet_data = %{
      # 下注玩家ID
      "playerid" => player.numeric_id,
      # 下注玩家座位号
      "seatid" => Map.get(updated_player, :seatid, 1),
      # 本次下注金额
      "bet" => bet_amount,
      # 下注类型
      "bettype" => bettype_value,
      # 当前倍数
      "curtimes" => Map.get(state, :curtimes, 1),
      # 🔧 修复：房间下注总金额（底池）
      "allbet" => Map.get(state, :pot_amount, 0),
      # 玩家剩余金币
      "pmoney" => Map.get(updated_player, :money, 0),
      # 该玩家总下注
      "mybetall" => Map.get(updated_player, :total_bet, 0),
      # 玩家ID (重复字段)
      "_playerid" => player.numeric_id
    }

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_bet),
      "data" => bet_data
    }

    Logger.info(
      "📡 [POT_BLIND] 广播PotBlind下注结果 [5][1003]: 玩家 #{player.numeric_id}, 类型: #{bet_type}, 金额: #{bet_amount}， 总金额: #{Map.get(state, :pot_amount, 0)}"
    )

    broadcast_to_room(state, message)
  end

  # ==================== 系统比牌相关函数 ====================

  @doc """
  在系统比牌前，让所有活跃玩家补齐下注到当前最高金额

  场景：A压5分，B跟5分达到上限，此时C和D都要补齐5分才能参与比牌

  ## 参数
  - state: 当前游戏状态
  - active_players: 活跃玩家列表（未弃牌的玩家）

  ## 返回
  - 更新后的游戏状态，所有活跃玩家的下注金额已补齐
  """
  defp equalize_all_active_players_bets(state, active_players) do
    Logger.info("⚖️ [POT_BLIND] 开始补齐所有活跃玩家的下注金额")

    # 计算当前最高下注金额
    max_bet_amount = calculate_max_bet_amount_in_round(state, active_players)

    if max_bet_amount <= 0 do
      Logger.info("⚖️ [POT_BLIND] 当前最高下注为0，无需补齐")
      state
    else
      Logger.info("⚖️ [POT_BLIND] 当前最高下注金额: #{max_bet_amount}")

      # 为每个活跃玩家补齐下注
      final_state =
        Enum.reduce(active_players, state, fn player_id, acc_state ->
          equalize_single_player_bet(acc_state, player_id, max_bet_amount)
        end)

      Logger.info("✅ [POT_BLIND] 所有活跃玩家下注补齐完成")
      final_state
    end
  end

  @doc """
  计算当前轮次中所有活跃玩家的最高下注金额

  ## 参数
  - state: 当前游戏状态
  - active_players: 活跃玩家列表

  ## 返回
  - 最高下注金额
  """
  defp calculate_max_bet_amount_in_round(state, active_players) do
    active_players
    |> Enum.map(fn player_id ->
      case Map.get(state.players, player_id) do
        nil -> 0
        player -> Map.get(player, :current_bet, 0)
      end
    end)
    |> Enum.max(fn -> 0 end)
  end

  @doc """
  为单个玩家补齐下注到指定金额

  ## 参数
  - state: 当前游戏状态
  - player_id: 玩家ID
  - target_amount: 目标下注金额

  ## 返回
  - 更新后的游戏状态
  """
  defp equalize_single_player_bet(state, player_id, target_amount) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在，跳过补齐")
        state

      player ->
        current_bet = Map.get(player, :current_bet, 0)

        if current_bet >= target_amount do
          # 玩家已经下注足够，无需补齐
          Logger.debug("✅ [POT_BLIND] 玩家 #{player_id} 已下注 #{current_bet}，无需补齐")
          state
        else
          # 计算需要补齐的金额
          additional_amount = target_amount - current_bet

          # 检查玩家余额是否足够
          player_money = Map.get(player, :money, 0)

          if player_money < additional_amount do
            # 余额不足，玩家自动弃牌
            Logger.warning(
              "💸 [POT_BLIND] 玩家 #{player_id} 余额不足补齐下注(需要#{additional_amount}，余额#{player_money})，自动弃牌"
            )

            auto_fold_player_for_insufficient_funds(state, player_id)
          else
            # 执行补齐下注
            Logger.info(
              "💰 [POT_BLIND] 玩家 #{player_id} 补齐下注: #{current_bet} → #{target_amount} (补齐#{additional_amount})"
            )

            execute_equalization_bet(state, player, additional_amount, target_amount)
          end
        end
    end
  end

  @doc """
  因余额不足而自动弃牌

  ## 参数
  - state: 当前游戏状态
  - player_id: 玩家ID

  ## 返回
  - 更新后的游戏状态
  """
  defp auto_fold_player_for_insufficient_funds(state, player_id) do
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在，无法自动弃牌")
        state

      player ->
        # 设置玩家弃牌状态
        fold_state = set_player_fold(state, player_id)

        # 广播弃牌结果（使用特殊的自动弃牌类型）
        broadcast_auto_fold_result(fold_state, player, "余额不足，自动弃牌")

        Logger.info("🚫 [POT_BLIND] 玩家 #{player_id} 因余额不足自动弃牌")
        fold_state
    end
  end

  @doc """
  执行补齐下注

  ## 参数
  - state: 当前游戏状态
  - player: 玩家数据
  - additional_amount: 需要补齐的金额
  - target_amount: 目标总下注金额

  ## 返回
  - 更新后的游戏状态
  """
  defp execute_equalization_bet(state, player, additional_amount, target_amount) do
    player_id = player.numeric_id

    # 1. 扣除玩家积分 (参考jhandi_munda)
    deduct_state = subtract_player_points(state, player_id, additional_amount)

    # 2. 更新玩家下注信息（设置为目标总金额）
    updated_player = Map.get(deduct_state.players, player_id)

    equalized_player = %{
      updated_player
      | current_bet: target_amount,
        total_bet: Map.get(updated_player, :total_bet, 0) + additional_amount,
        last_bet_type: :equalization,
        last_bet_amount: additional_amount,
        last_action_at: System.system_time(:millisecond)
    }

    # 3. 更新玩家数据到状态中
    bet_state = %{
      deduct_state
      | players: Map.put(deduct_state.players, player_id, equalized_player)
    }

    # 4. 更新底池
    updated_state = %{bet_state | pot_amount: bet_state.pot_amount + additional_amount}

    # 5. 发送积分更新通知 (参考jhandi_munda)
    send_money_update_notification(updated_state, player)

    # 6. 广播补齐下注结果
    broadcast_equalization_bet_result(
      updated_state,
      equalized_player,
      additional_amount,
      target_amount
    )

    updated_state
  end

  @doc """
  广播自动弃牌结果

  ## 参数
  - state: 当前游戏状态
  - player: 玩家数据
  - reason: 弃牌原因
  """
  defp broadcast_auto_fold_result(state, player, reason) do
    fold_data = %{
      "playerid" => player.numeric_id,
      "seatid" => Map.get(player, :seatid, 1),
      # 弃牌类型
      "bettype" => 3,
      "reason" => reason,
      # 标识为自动弃牌
      "auto_fold" => true,
      "_playerid" => player.numeric_id
    }

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_fold),
      "data" => fold_data
    }

    Logger.info("📡 [POT_BLIND] 广播自动弃牌 [5][1007]: 玩家 #{player.numeric_id}, 原因: #{reason}")
    broadcast_to_room(state, message)
  end

  @doc """
  广播补齐下注结果

  ## 参数
  - state: 当前游戏状态
  - player: 玩家数据
  - additional_amount: 补齐的金额
  - target_amount: 目标总下注金额
  """
  defp broadcast_equalization_bet_result(state, player, additional_amount, target_amount) do
    bet_data = %{
      "playerid" => player.numeric_id,
      "seatid" => Map.get(player, :seatid, 1),
      # 本次补齐的金额
      "bet" => additional_amount,
      # 补齐下注类型（新增类型）
      "bettype" => 4,
      "curtimes" => Map.get(state, :curtimes, 1),
      # 🔧 修复：房间下注总金额（底池）
      "allbet" => Map.get(state, :pot_amount, 0),
      "pmoney" => Map.get(player, :money, 0),
      "mybetall" => Map.get(player, :total_bet, 0),
      # 目标总下注金额
      "target_amount" => target_amount,
      # 标识为补齐下注
      "equalization" => true,
      "_playerid" => player.numeric_id
    }

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_bet),
      "data" => bet_data
    }

    Logger.info(
      "📡 [POT_BLIND] 广播补齐下注 [5][1003]: 玩家 #{player.numeric_id}, 补齐#{additional_amount} → 总计#{target_amount}"
    )

    broadcast_to_room(state, message)
  end

  # 广播比牌开始 [5][1004]
  defp broadcast_showdown_start(state, player1, player2) do
    showdown_data = %{
      "player1_id" => player1.numeric_id,
      "player1_seatid" => Map.get(player1, :seatid, 1),
      "player1_cards" => PotBlindGameLogic.format_cards_for_client(Map.get(player1, :cards, [])),
      "player1_card_type" => PotBlindGameLogic.get_card_type(Map.get(player1, :cards, [])),
      "player2_id" => player2.numeric_id,
      "player2_seatid" => Map.get(player2, :seatid, 1),
      "player2_cards" => PotBlindGameLogic.format_cards_for_client(Map.get(player2, :cards, [])),
      "player2_card_type" => PotBlindGameLogic.get_card_type(Map.get(player2, :cards, [])),
      # 系统比牌
      "showdown_type" => "system",
      # 3秒动画时间
      "animation_time" => 3000
    }

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:cs_competition),
      "data" => showdown_data
    }

    Logger.info(
      "📡 [POT_BLIND] 广播系统比牌开始 [5][1004]: #{player1.numeric_id} vs #{player2.numeric_id}"
    )

    broadcast_to_room(state, message)
  end

  # 广播比牌结果 [5][1005]
  defp broadcast_showdown_result(state, winner, loser) do
    showdown_result_data = %{
      "player1_id" => winner.numeric_id,
      "player1_seatid" => Map.get(winner, :seatid, 1),
      "player1_cards" => PotBlindGameLogic.format_cards_for_client(Map.get(winner, :cards, [])),
      "player1_card_type" => PotBlindGameLogic.get_card_type(Map.get(winner, :cards, [])),
      "player2_id" => loser.numeric_id,
      "player2_seatid" => Map.get(loser, :seatid, 1),
      "player2_cards" => PotBlindGameLogic.format_cards_for_client(Map.get(loser, :cards, [])),
      "player2_card_type" => PotBlindGameLogic.get_card_type(Map.get(loser, :cards, [])),
      "winner_id" => winner.numeric_id,
      "winner_seatid" => Map.get(winner, :seatid, 1),
      "showdown_type" => "system",
      "remaining_players" => length(Map.get(state, :showdown_players, []))
    }

    message = %{
      "mainId" => @protocol_main_id,
      "subId" => get_protocol(:sc_competition),
      "data" => showdown_result_data
    }

    Logger.info("📡 [POT_BLIND] 广播系统比牌结果 [5][1005]: 获胜者 #{winner.numeric_id}")
    broadcast_to_room(state, message)
  end

  # 单个玩家获胜的结算
  defp settle_game_with_single_winner(state, winner) do
    Logger.info("🏆 [POT_BLIND] 单个玩家获胜结算: #{winner.numeric_id}")

    # 执行游戏结算
    execute_game_settlement(state, winner.numeric_id)
  end

  # 处理PotBlind游戏定时器到期
  def handle_info(:pot_blind_timer_expired, state) do
    Logger.info("⏰ [POT_BLIND] PotBlind游戏定时器到期")

    # 清除定时器引用
    updated_state = Map.put(state, :pot_blind_timer_ref, nil)

    # 再次检查人数是否足够
    player_count = map_size(updated_state.players)
    min_players = 2

    if player_count >= min_players and updated_state.room_state == :waiting do
      Logger.info("🎮 [POT_BLIND] 定时器到期，开始游戏 - 玩家数: #{player_count}")

      # 更新房间状态为游戏中
      game_state = %{updated_state | room_state: :playing}

      # 调用我们自己的游戏开始逻辑
      final_state = begin_pot_blind_game(game_state)
      {:noreply, final_state}
    else
      Logger.info("🔍 [POT_BLIND] 定时器到期但人数不足，继续等待 - 玩家数: #{player_count}")
      {:noreply, updated_state}
    end
  end

  # 处理下一局游戏定时器到期
  def handle_info(:next_game_timer_expired, state) do
    Logger.info("⏰ [POT_BLIND] 下一局游戏定时器到期，检查开始条件")

    # 清除定时器引用
    updated_state = Map.put(state, :next_game_timer_ref, nil)

    # 检查是否可以开始下一局
    case check_next_game_conditions(updated_state) do
      {:ok, _reason} ->
        Logger.info("🚀 [POT_BLIND] 条件满足，开始下一局游戏")

        # 直接开始下一局游戏
        # final_state = start_pot_blind_game_immediately(updated_state)
        send(self(), :start_new_round)

        # send(self(), :start_new_round)

        {:noreply, updated_state}

      {:error, reason} ->
        Logger.info("❌ [POT_BLIND] 无法开始下一局: #{reason}")

        # 如果是因为踢出玩家导致的，需要重新检查
        if reason == "部分玩家金币不足已踢出" do
          # 重新检查剩余玩家是否足够开始游戏
          remaining_count = map_size(updated_state.players)
          min_players = updated_state.config[:min_players] || 2

          if remaining_count >= min_players do
            Logger.info("🔄 [POT_BLIND] 踢出玩家后仍有足够人数，重新启动倒计时")
            # 重新启动3秒倒计时
            timer_state = start_next_game_timer(updated_state)
            {:noreply, timer_state}
          else
            Logger.info("⏸️ [POT_BLIND] 踢出玩家后人数不足，等待新玩家加入")
            {:noreply, updated_state}
          end
        else
          # 其他原因导致无法开始，保持等待状态
          {:noreply, updated_state}
        end
    end
  end

  # 处理比牌动画定时器到期
  def handle_info({:competition_animation_expired, winner_id}, state) do
    Logger.info("🎬 [POT_BLIND] 比牌动画定时器到期，继续游戏流程，获胜者: #{winner_id}")

    # 清除比牌动画定时器引用
    updated_state = cancel_competition_animation_timer(state)

    # 检查游戏是否结束
    case check_game_end_condition(updated_state) do
      {:game_end, final_winner_id} ->
        Logger.info("🏆 [POT_BLIND] 比牌后游戏结束，最终获胜者: #{final_winner_id}")
        # 游戏结束，进行结算
        final_state = end_game_with_winner(updated_state, final_winner_id)
        {:noreply, final_state}

      :continue ->
        Logger.info("🔄 [POT_BLIND] 比牌后游戏继续，切换到下一个玩家")
        # 游戏继续，切换到下一个玩家
        next_state = set_next_seat_id(updated_state)
        {:noreply, next_state}
    end
  end

  # 处理玩家暂离通知定时器到期（3秒后发送[4][9]）
  def handle_info({:player_away_notify, player_id}, state) do
    Logger.info("🚶 [POT_BLIND] 玩家 #{player_id} 暂离通知定时器到期，发送[4][9]暂离通知")

    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在，无法发送暂离通知")
        {:noreply, state}

      player ->
        # 发送[4][9]暂离通知
        send_player_away_notification(state, player)

        # 启动5秒踢出定时器
        kick_timer_ref = Process.send_after(self(), {:player_away_kick, player_id}, 5000)

        # 安全地更新玩家状态
        updated_player =
          player
          # 清除通知定时器
          |> Map.put(:away_notify_timer_ref, nil)
          # 设置踢出定时器
          |> Map.put(:away_kick_timer_ref, kick_timer_ref)
          # 等待玩家回复[4][10]
          |> Map.put(:away_status, :waiting_return)

        updated_state = %{state | players: Map.put(state.players, player_id, updated_player)}

        Logger.info("🚶 [POT_BLIND] 已发送暂离通知给玩家 #{player_id}，5秒后踢出")
        {:noreply, updated_state}
    end
  end

  # 处理玩家踢出定时器到期（5秒后处理踢出）
  def handle_info({:player_away_kick, player_id}, state) do
    Logger.info("🚶 [POT_BLIND] 玩家 #{player_id} 踢出定时器到期，检查是否需要踢出")

    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("❌ [POT_BLIND] 玩家 #{player_id} 不存在，无法踢出")
        {:noreply, state}

      player ->
        # 检查游戏状态，决定踢出策略
        game_state = Map.get(state, :room_state, :waiting)

        if game_state == :playing do
          Logger.info("🎮 [POT_BLIND] 游戏进行中，玩家 #{player_id} 暂离但不立即踢出，等待下次操作超时")

          # 游戏进行中，标记玩家为"等待踢出"状态，不立即踢出
          updated_player =
            player
            # 清除踢出定时器
            |> Map.put(:away_kick_timer_ref, nil)
            # 标记为等待踢出
            |> Map.put(:away_status, :pending_kick)
            # 下次超时时踢出
            |> Map.put(:kick_on_next_timeout, true)

          updated_state = %{state | players: Map.put(state.players, player_id, updated_player)}

          Logger.info("🎮 [POT_BLIND] 玩家 #{player_id} 已标记为下次操作超时时踢出")
          {:noreply, updated_state}
        else
          Logger.info("⏸️ [POT_BLIND] 游戏未进行，立即踢出暂离玩家 #{player_id}")

          # 游戏未进行，使用标准的on_player_left处理玩家离开
          # 这样可以确保所有离开逻辑都被正确处理（广播、座位释放、定时器清理等）
          updated_state = on_player_left(state, player)

          Logger.info("🚫 [POT_BLIND] 暂离玩家 #{player_id} 已通过on_player_left标准流程移除")
          {:noreply, updated_state}
        end
    end
  end

  # 处理玩家行动超时
  def handle_info({:player_action_timeout, player_id}, state) do
    Logger.info("⏰ [POT_BLIND] 玩家行动超时: #{player_id}")

    # 检查是否是当前行动玩家
    current_player_id = Map.get(state, :current_player_id)

    if current_player_id == player_id do
      # 获取玩家信息
      case Map.get(state.players, player_id) do
        nil ->
          Logger.warn("⚠️ [POT_BLIND] 超时玩家不存在: #{player_id}")
          {:noreply, state}

        player ->
          Logger.info("🚫 [POT_BLIND] 玩家 #{player_id} 超时，自动弃牌")

          # 检查游戏阶段
          game_phase = Map.get(state, :game_phase, :betting)

          # 下注阶段超时，自动弃牌
          case execute_fold(state, player) do
            {:game_ended, final_state} ->
              # 🚫 暂离功能已屏蔽 - 游戏结束后不启动暂离流程
              Logger.info("🏆 [POT_BLIND] 超时弃牌导致游戏结束")
              {:noreply, final_state}

            # # 在弃牌后重新获取玩家数据，检查是否被标记为踢出
            # updated_player = Map.get(final_state.players, player_id)
            # should_kick_after_fold = updated_player && Map.get(updated_player, :kick_on_next_timeout, false)
            #
            # if should_kick_after_fold do
            #   Logger.info("🚫 [POT_BLIND] 游戏结束，踢出暂离玩家 #{player_id}")
            #   # 游戏结束后踢出暂离玩家
            #   kick_away_player_after_fold(final_state, player_id)
            # else
            #   # 游戏结束，启动暂离流程保护玩家
            #   Logger.info("🏆 [POT_BLIND] 超时弃牌导致游戏结束，启动暂离流程保护玩家")
            #   away_state = start_player_away_process(final_state, player_id)
            #   {:noreply, away_state}
            # end

            {:continue, fold_state} ->
              # 🚫 暂离功能已屏蔽 - 游戏继续后不启动暂离流程
              Logger.info("🔄 [POT_BLIND] 超时弃牌后继续游戏")
              # 切换到下一个玩家
              Logger.info("🔄 [POT_BLIND] SetNextSeatID() - 切换轮次2")
              new_state = set_next_seat_id(fold_state)
              {:noreply, new_state}

              # # 在弃牌后重新获取玩家数据，检查是否被标记为踢出
              # updated_player = Map.get(fold_state.players, player_id)
              # should_kick_after_fold = updated_player && Map.get(updated_player, :kick_on_next_timeout, false)
              #
              # if should_kick_after_fold do
              #   Logger.info("🚫 [POT_BLIND] 游戏继续，踢出暂离玩家 #{player_id}")
              #   # 游戏继续，踢出暂离玩家
              #   kick_state = kick_away_player_after_fold(fold_state, player_id)
              #   # 切换到下一个玩家
              #   Logger.info("🔄 [POT_BLIND] SetNextSeatID() - 切换轮次2")
              #   new_state = set_next_seat_id(kick_state)
              #   {:noreply, new_state}
              # else
              #   # 游戏继续，启动暂离流程
              #   Logger.info("🔄 [POT_BLIND] 超时弃牌后启动暂离流程")
              #   away_state = start_player_away_process(fold_state, player_id)
              #   # 切换到下一个玩家
              #   Logger.info("🔄 [POT_BLIND] SetNextSeatID() - 切换轮次2")
              #   new_state = set_next_seat_id(away_state)
              #   {:noreply, new_state}
              # end
          end
      end
    else
      # 不是当前行动玩家，忽略超时消息
      Logger.debug("🔍 [POT_BLIND] 忽略非当前玩家的超时消息: #{player_id}, 当前玩家: #{current_player_id}")
      {:noreply, state}
    end
  end

  # 处理比牌结果定时器
  def handle_info({:showdown_result, player1, player2}, state) do
    Logger.info("🎯 [POT_BLIND] 处理比牌结果: #{player1.numeric_id} vs #{player2.numeric_id}")

    # 比较两个玩家的牌
    winner = PotBlindGameLogic.compare_player_cards(player1, player2)
    loser = if winner.numeric_id == player1.numeric_id, do: player2, else: player1

    # 广播比牌结果
    broadcast_showdown_result(state, winner, loser)

    # 将赢家加入剩余玩家列表，继续下一轮比牌
    remaining_players = [winner | Map.get(state, :showdown_players, [])]
    updated_state = %{state | showdown_players: remaining_players, current_showdown_pair: nil}

    # 开始下一轮比牌
    case start_next_showdown_round(updated_state) do
      {:ok, new_state} -> {:noreply, new_state}
      {:error, _reason} -> {:noreply, state}
    end
  end

  # ==================== 机器人消息处理 ====================

  # 处理延迟广播机器人加入
  def handle_info({:broadcast_robots, robots}, state) do
    Logger.info("🤖 [POT_BLIND] 延迟广播 #{length(robots)} 个机器人加入")

    # 广播每个机器人的加入消息
    Enum.each(robots, fn robot ->
      Logger.info("🤖 [POT_BLIND] 广播机器人 #{robot.numeric_id} 加入房间，座位: #{robot.seatid}")
      broadcast_player_enter(state, robot)
    end)

    # 广播房间状态更新
    broadcast_pot_blind_room_state(state)

    {:noreply, state}
  end

  # 处理机器人行动
  def handle_info({:robot_action, robot_id, decision}, state) do
    Logger.info("🤖 [POT_BLIND] 收到机器人 #{robot_id} 行动: #{inspect(decision)}")

    updated_state = PotBlindRobotManager.execute_robot_action(state, robot_id, decision)
    {:noreply, updated_state}
  end

  # 处理机器人下注
  def handle_info({:robot_bet, robot_id, fill}, state) do
    Logger.info("🤖 [POT_BLIND] 机器人 #{robot_id} 下注: fill=#{fill}")

    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [POT_BLIND] 机器人 #{robot_id} 不存在")
        {:noreply, state}

      robot_player ->
        # 模拟机器人下注请求
        message = %{"data" => %{"fill" => fill}}
        updated_state = handle_bet_request(state, robot_player, message)
        {:noreply, updated_state}
    end
  end

  # 处理机器人弃牌
  def handle_info({:robot_fold, robot_id}, state) do
    Logger.info("🤖 [POT_BLIND] 机器人 #{robot_id} 弃牌")

    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [POT_BLIND] 机器人 #{robot_id} 不存在")
        {:noreply, state}

      robot_player ->
        # 模拟机器人弃牌请求
        message = %{"data" => %{}}
        updated_state = handle_fold_request(state, robot_player, message)
        {:noreply, updated_state}
    end
  end

  # 处理机器人比牌
  def handle_info({:robot_competition, robot_id, target_id}, state) do
    Logger.info("🤖 [POT_BLIND] 机器人 #{robot_id} 比牌，目标: #{target_id}")

    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [POT_BLIND] 机器人 #{robot_id} 不存在")
        {:noreply, state}

      robot_player ->
        # 模拟机器人比牌请求
        message = %{"data" => %{"targetPlayerID" => target_id}}
        updated_state = handle_competition_request(state, robot_player, message)
        {:noreply, updated_state}
    end
  end
end
