defmodule <PERSON>pridina.Teen.GameSystem.Games.Crash.CrashGame do
  @moduledoc """
  Crash游戏定义模块

  实现游戏工厂行为，定义Crash游戏的基本信息和配置
  包含所有游戏相关的配置参数，提供统一的配置管理
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  # ==================== 游戏基础配置 ====================

  # 游戏阶段定义
  @game_phases %{
    # 等待开始
    waiting: :waiting,
    # 下注阶段
    betting: :betting,
    # 飞行阶段（倍数上升）
    flying: :flying,
    # 结算阶段
    settling: :settling
  }

  # 游戏状态码
  @game_status %{
    # 空闲
    free: 0,
    # 下注
    betting: 100,
    # 飞行
    flying: 101,
    # 结束
    ending: 102
  }

  # 错误码定义
  @error_codes %{
    # 不在下注阶段
    not_betting_phase: 1,
    # 余额不足
    insufficient_balance: 2,
    # 已经下注
    already_bet: 3,
    # 不在飞行阶段
    not_flying_phase: 11,
    # 没有下注
    no_bet_placed: 12,
    # 已经下车
    already_cashed_out: 13
  }

  # ==================== 公共访问函数 ====================

  @doc """
  获取游戏阶段定义
  """
  def game_phases, do: @game_phases

  @doc """
  获取游戏状态码
  """
  def game_status, do: @game_status

  @doc """
  获取错误码定义
  """
  def error_codes, do: @error_codes

  # 默认配置
  @default_config %{
    # 时间配置（秒）
    # 等待时间
    free_time: 5,
    # 下注时间
    betting_time: 15,
    # 结算时间
    settling_time: 5,
    # 最大飞行时间（毫秒）
    max_fly_time: 110_460,

    # 筹码配置（分）
    chips: [500, 1000, 5000, 10000, 50000, 500_000],
    # 最小下注（分）
    min_bet: 500,
    # 下注必须是此数的倍数
    bet_multiple: 100,

    # 玩家配置
    # 最大玩家数
    max_players: 100,
    # 最小玩家数
    min_players: 1,

    # 税率配置
    # 税率 5% (50/1000)
    revenue_ratio: 50,

    # 机器人配置
    # 最大机器人数
    max_robots: 60,
    # 机器人下注概率
    robot_bet_probability: 0.3,

    # 倍数配置
    multiplier_configs: [
      # 1.00x, 0ms
      %{id: 1, multiplier: 100, time: 0},
      # 1.01x, 20ms
      %{id: 2, multiplier: 101, time: 20},
      # 1.02x, 40ms
      %{id: 3, multiplier: 102, time: 40},
      # 1.03x, 60ms
      %{id: 4, multiplier: 103, time: 60},
      # 1.04x, 80ms
      %{id: 5, multiplier: 104, time: 80},
      # 1.05x, 100ms
      %{id: 6, multiplier: 105, time: 100},
      # 1.10x, 200ms
      %{id: 7, multiplier: 110, time: 200},
      # 1.20x, 400ms
      %{id: 8, multiplier: 120, time: 400},
      # 1.50x, 1000ms
      %{id: 9, multiplier: 150, time: 1000},
      # 2.00x, 2000ms
      %{id: 10, multiplier: 200, time: 2000},
      # 3.00x, 5000ms
      %{id: 11, multiplier: 300, time: 5000},
      # 5.00x, 10000ms
      %{id: 12, multiplier: 500, time: 10000},
      # 10.00x, 20000ms
      %{id: 13, multiplier: 1000, time: 20000},
      # 20.00x, 40000ms
      %{id: 14, multiplier: 2000, time: 40000},
      # 50.00x, 80000ms
      %{id: 15, multiplier: 5000, time: 80000},
      # 100.00x, 110000ms
      %{id: 16, multiplier: 10000, time: 110_000}
    ]
  }

  # ==================== 游戏工厂行为实现 ====================

  @impl true
  def game_type, do: :crash

  @impl true
  def game_name, do: "Crash"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.Crash.CrashRoom

  @impl true
  def default_config, do: @default_config

  @impl true
  def is_lobby_game?, do: true

  @impl true
  def supported_game_ids, do: [23]

  # ==================== 配置管理函数 ====================

  @doc """
  获取完整配置（合并默认配置和自定义配置）
  """
  def full_config(custom_config \\ %{}) do
    Map.merge(@default_config, custom_config)
  end

  @doc """
  获取筹码配置
  """
  def chips(config \\ @default_config) do
    Map.get(config, :chips, @default_config.chips)
  end

  @doc """
  验证下注金额是否有效
  """
  def valid_bet_amount?(amount, config \\ @default_config) do
    min_bet = Map.get(config, :min_bet, 500)
    bet_multiple = Map.get(config, :bet_multiple, 100)

    amount >= min_bet and rem(amount, bet_multiple) == 0
  end

  @doc """
  计算收益（扣除税率）
  """
  def calculate_payout(bet_amount, multiplier, revenue_ratio \\ 50) do
    # multiplier是整数，需要除以100得到实际倍数
    actual_multiplier = multiplier / 100.0
    gross_payout = trunc(bet_amount * actual_multiplier)

    # 计算税收
    revenue = trunc(gross_payout * revenue_ratio / 1000)
    net_payout = gross_payout - revenue
    profit = net_payout - bet_amount

    %{
      gross_payout: gross_payout,
      net_payout: net_payout,
      revenue: revenue,
      profit: profit,
      multiplier: actual_multiplier
    }
  end

  @doc """
  根据倍数获取对应的时间
  """
  def get_time_by_multiplier(multiplier, config \\ @default_config) do
    multiplier_configs = Map.get(config, :multiplier_configs, @default_config.multiplier_configs)

    # 找到第一个大于等于目标倍数的配置项
    found_config =
      Enum.find(multiplier_configs, fn config_item ->
        config_item.multiplier >= multiplier
      end)

    case found_config do
      nil ->
        # 如果没找到，使用最大飞行时间
        Map.get(config, :max_fly_time, 110_460)

      config_item ->
        config_item.time
    end
  end

  @doc """
  根据时间获取对应的倍数点
  """
  def get_multiplier_point_by_time(elapsed_time_ms, config \\ @default_config) do
    multiplier_configs = Map.get(config, :multiplier_configs, @default_config.multiplier_configs)

    # 找到最后一个时间小于等于elapsed_time的配置项
    found_config =
      multiplier_configs
      |> Enum.reverse()
      |> Enum.find(fn config_item ->
        config_item.time <= elapsed_time_ms
      end)

    case found_config do
      # 默认第一个点
      nil -> 1
      config_item -> config_item.id
    end
  end

  @doc """
  根据点获取倍数
  """
  def get_multiplier_by_point(point, config \\ @default_config) do
    multiplier_configs = Map.get(config, :multiplier_configs, @default_config.multiplier_configs)

    found_config =
      Enum.find(multiplier_configs, fn config_item ->
        config_item.id == point
      end)

    case found_config do
      # 默认1.00x
      nil -> 100
      config_item -> config_item.multiplier
    end
  end

  @doc """
  获取倍数配置
  """
  def multiplier_configs(config \\ @default_config) do
    Map.get(config, :multiplier_configs, @default_config.multiplier_configs)
  end

  @doc """
  生成爆炸倍数（简化版本，实际应该更复杂）
  """
  def generate_crash_multiplier(_config \\ @default_config) do
    # 简化的随机算法，实际应该考虑库存控制等因素
    rand = :rand.uniform(100)

    cond do
      # 75%概率: 151-350
      rand <= 75 -> :rand.uniform(200) + 151
      # 18%概率: 350-550
      rand <= 93 -> :rand.uniform(200) + 350
      # 4%概率: 550-750
      rand <= 97 -> :rand.uniform(200) + 550
      # 3%概率: 750-950
      true -> :rand.uniform(200) + 750
    end
  end
end
