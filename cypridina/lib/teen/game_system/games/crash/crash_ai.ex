defmodule <PERSON><PERSON>rid<PERSON>.Teen.GameSystem.Games.Crash.CrashAI do
  @moduledoc """
  Crash游戏AI/机器人模块

  管理机器人的行为，包括：
  - 机器人下注决策
  - 机器人下车决策
  - 机器人行为模拟
  """

  alias Cypridina.Teen.GameSystem.Games.Crash.{CrashGame, CrashLogic}
  require Logger

  # 机器人类型定义
  @robot_types %{
    # 保守型：低倍数下车
    conservative: :conservative,
    # 激进型：高倍数下车
    aggressive: :aggressive,
    # 平衡型：中等倍数下车
    balanced: :balanced,
    # 随机型：随机行为
    random: :random
  }

  @doc """
  生成机器人下注决策
  """
  def should_robot_bet?(robot_player, state) do
    config = state.game_data.config
    bet_probability = Map.get(config, :robot_bet_probability, 0.3)

    # 基础概率判断
    base_should_bet = :rand.uniform() < bet_probability

    # 根据机器人类型调整概率
    robot_type = Map.get(robot_player, :robot_type, :balanced)
    adjusted_probability = adjust_bet_probability(robot_type, bet_probability, state)

    :rand.uniform() < adjusted_probability and base_should_bet
  end

  @doc """
  生成机器人下注金额
  """
  def generate_robot_bet_amount(robot_player, state) do
    config = state.game_data.config
    chips = CrashGame.chips(config)
    robot_type = Map.get(robot_player, :robot_type, :balanced)

    case robot_type do
      :conservative ->
        # 保守型：选择较小的筹码
        Enum.take(chips, 3) |> Enum.random()

      :aggressive ->
        # 激进型：选择较大的筹码
        Enum.drop(chips, 2) |> Enum.random()

      :balanced ->
        # 平衡型：选择中等筹码
        middle_chips = Enum.slice(chips, 1, 4)
        Enum.random(middle_chips)

      :random ->
        # 随机型：随机选择
        Enum.random(chips)
    end
  end

  @doc """
  生成机器人下车决策
  """
  def should_robot_cash_out?(robot_player, current_multiplier, elapsed_time) do
    robot_type = Map.get(robot_player, :robot_type, :balanced)
    target_multiplier = get_robot_target_multiplier(robot_type)

    # 基础判断：达到目标倍数
    base_should_cash_out = current_multiplier >= target_multiplier

    # 添加一些随机性和时间因素
    time_pressure = calculate_time_pressure(elapsed_time)
    # 10%的随机下车概率
    random_factor = :rand.uniform() < 0.1

    base_should_cash_out or time_pressure or random_factor
  end

  @doc """
  获取机器人目标倍数
  """
  def get_robot_target_multiplier(robot_type) do
    case robot_type do
      :conservative ->
        # 保守型：1.2x - 1.8x
        120 + :rand.uniform(60)

      :aggressive ->
        # 激进型：2.0x - 5.0x
        200 + :rand.uniform(300)

      :balanced ->
        # 平衡型：1.5x - 2.5x
        150 + :rand.uniform(100)

      :random ->
        # 随机型：1.1x - 10.0x
        110 + :rand.uniform(890)
    end
  end

  @doc """
  创建机器人玩家
  """
  def create_robot_player(robot_id) do
    robot_type = Enum.random(Map.values(@robot_types))

    %{
      user_id: robot_id,
      numeric_id: robot_id,
      nickname: generate_robot_name(),
      is_robot: true,
      robot_type: robot_type,
      bet_amount: 0,
      cashed_out: false,
      cash_out_multiplier: nil,
      crashed: false,
      payout: 0,
      profit: 0,
      target_multiplier: get_robot_target_multiplier(robot_type)
    }
  end

  @doc """
  处理机器人下注阶段行为
  """
  def handle_robot_betting_phase(state) do
    robots = get_robots(state)

    Enum.reduce(robots, state, fn {_user_id, robot}, acc_state ->
      if should_robot_bet?(robot, acc_state) and Map.get(robot, :bet_amount, 0) == 0 do
        bet_amount = generate_robot_bet_amount(robot, acc_state)

        case CrashLogic.place_bet(acc_state, robot, bet_amount) do
          {:ok, updated_state, _updated_robot} ->
            Logger.info("🤖 [CRASH_AI] 机器人下注: #{robot.nickname}, 金额: #{bet_amount}")
            updated_state

          {:error, _reason} ->
            acc_state
        end
      else
        acc_state
      end
    end)
  end

  @doc """
  处理机器人飞行阶段行为
  """
  def handle_robot_flying_phase(state, current_multiplier, elapsed_time) do
    robots = get_robots(state)

    Enum.reduce(robots, state, fn {_user_id, robot}, acc_state ->
      if Map.get(robot, :bet_amount, 0) > 0 and not Map.get(robot, :cashed_out, false) do
        if should_robot_cash_out?(robot, current_multiplier, elapsed_time) do
          case CrashLogic.cash_out(acc_state, robot, elapsed_time) do
            {:ok, updated_state, _updated_robot, payout_info} ->
              Logger.info(
                "🤖 [CRASH_AI] 机器人下车: #{robot.nickname}, 倍数: #{current_multiplier}, 收益: #{payout_info.gross_payout}"
              )

              updated_state

            {:error, _reason} ->
              acc_state
          end
        else
          acc_state
        end
      else
        acc_state
      end
    end)
  end

  @doc """
  添加机器人到房间（基于C++的OnRobotEnter逻辑）
  """
  def add_robots_to_room(state, count \\ 5) do
    current_robot_count = count_robots(state)
    real_player_count = count_real_players(state)
    max_robots = Map.get(state.game_data.config, :max_robots, 60)

    # 基于真实玩家数量调整机器人数量
    target_robot_count = calculate_target_robot_count(real_player_count)

    robots_to_add =
      min(count, min(target_robot_count - current_robot_count, max_robots - current_robot_count))

    if robots_to_add > 0 do
      Logger.info("🤖 [CRASH_AI] 添加 #{robots_to_add} 个机器人，当前真实玩家: #{real_player_count}")

      Enum.reduce(1..robots_to_add, state, fn _i, acc_state ->
        robot_id = generate_robot_id(acc_state)
        robot = create_robot_player_with_bet(robot_id, acc_state)

        # 添加机器人并自动下注（如果在下注阶段）
        updated_state = add_robot_to_state(acc_state, robot_id, robot)

        # 如果在下注阶段，机器人自动下注
        if acc_state.game_data.phase == :betting do
          auto_bet_robot(updated_state, robot)
        else
          updated_state
        end
      end)
    else
      state
    end
  end

  @doc """
  移除机器人从房间（基于C++的OnRobotLeave逻辑）
  """
  def remove_robots_from_room(state, count \\ 1) do
    robots = get_robots(state)

    # 优先移除没有下注的机器人，其次移除已下车的机器人
    removable_robots = prioritize_removable_robots(robots, count)

    if length(removable_robots) > 0 do
      Logger.info("🤖 [CRASH_AI] 移除 #{length(removable_robots)} 个机器人")

      updated_players =
        Enum.reduce(removable_robots, state.players, fn {user_id, robot}, acc_players ->
          Logger.info("🤖 [CRASH_AI] 机器人离开: #{robot.nickname} (ID: #{user_id})")
          Map.delete(acc_players, user_id)
        end)

      %{state | players: updated_players}
    else
      state
    end
  end

  @doc """
  处理机器人进出房间的智能管理（对应C++的机器人管理逻辑）
  """
  def manage_robot_population(state) do
    current_robot_count = count_robots(state)
    real_player_count = count_real_players(state)
    target_robot_count = calculate_target_robot_count(real_player_count)

    cond do
      # 需要添加机器人
      current_robot_count < target_robot_count ->
        robots_to_add = min(3, target_robot_count - current_robot_count)
        add_robots_to_room(state, robots_to_add)

      # 需要移除机器人
      current_robot_count > target_robot_count + 5 ->
        robots_to_remove = min(2, current_robot_count - target_robot_count)
        remove_robots_from_room(state, robots_to_remove)

      true ->
        state
    end
  end

  # ==================== 私有函数 ====================

  defp get_robots(state) do
    state.players
    |> Enum.filter(fn {_user_id, player} ->
      Map.get(player, :is_robot, false)
    end)
  end

  defp count_robots(state) do
    get_robots(state) |> length()
  end

  defp count_real_players(state) do
    state.players
    |> Enum.count(fn {_user_id, player} ->
      not Map.get(player, :is_robot, false)
    end)
  end

  # 根据真实玩家数量计算目标机器人数量
  defp calculate_target_robot_count(real_player_count) do
    case real_player_count do
      # 没有真实玩家，不需要机器人
      0 -> 0
      # 1个真实玩家，2-5个机器人
      1 -> :rand.uniform(3) + 2
      # 2个真实玩家，3-7个机器人
      2 -> :rand.uniform(4) + 3
      # 3个真实玩家，4-9个机器人
      3 -> :rand.uniform(5) + 4
      # 更多真实玩家，5-13个机器人
      _ -> :rand.uniform(8) + 5
    end
  end

  # 创建带下注的机器人玩家
  defp create_robot_player_with_bet(robot_id, state) do
    robot_type = Enum.random(Map.values(@robot_types))
    bet_amount = generate_robot_bet_amount_by_type(robot_type, state)

    %{
      user_id: robot_id,
      numeric_id: robot_id,
      nickname: generate_robot_name(),
      is_robot: true,
      robot_type: robot_type,
      bet_amount: bet_amount,
      bet_time: System.system_time(:millisecond),
      cashed_out: false,
      cash_out_multiplier: nil,
      crashed: false,
      payout: 0,
      profit: 0,
      target_multiplier: get_robot_target_multiplier(robot_type),
      # 机器人有足够余额
      balance: 1_000_000,
      # 机器人行为参数
      # 耐心程度
      patience_level: :rand.uniform(100),
      # 风险承受度
      risk_tolerance: :rand.uniform(100)
    }
  end

  # 根据机器人类型生成下注金额
  defp generate_robot_bet_amount_by_type(robot_type, state) do
    config = state.game_data.config
    chips = CrashGame.chips(config)

    case robot_type do
      :conservative ->
        # 保守型：选择较小的筹码
        Enum.take(chips, 3) |> Enum.random()

      :aggressive ->
        # 激进型：选择较大的筹码
        Enum.drop(chips, 2) |> Enum.random()

      :balanced ->
        # 平衡型：选择中等筹码
        middle_chips = Enum.slice(chips, 1, 4)
        Enum.random(middle_chips)

      :random ->
        # 随机型：随机选择
        Enum.random(chips)
    end
  end

  # 添加机器人到状态
  defp add_robot_to_state(state, robot_id, robot) do
    updated_players = Map.put(state.players, robot_id, robot)
    %{state | players: updated_players}
  end

  # 机器人自动下注
  defp auto_bet_robot(state, robot) do
    if state.game_data.phase == :betting and robot.bet_amount > 0 do
      case CrashLogic.place_bet(state, robot, robot.bet_amount) do
        {:ok, updated_state, _updated_robot} ->
          Logger.info("🤖 [CRASH_AI] 机器人自动下注: #{robot.nickname}, 金额: #{robot.bet_amount}")
          updated_state

        {:error, reason} ->
          Logger.warning("🤖 [CRASH_AI] 机器人自动下注失败: #{robot.nickname}, 原因: #{reason}")
          state
      end
    else
      state
    end
  end

  # 优先选择可移除的机器人
  defp prioritize_removable_robots(robots, count) do
    # 按优先级排序：未下注 > 已下车 > 已爆炸 > 正在游戏中
    sorted_robots =
      Enum.sort_by(robots, fn {_id, robot} ->
        cond do
          # 最优先：未下注
          Map.get(robot, :bet_amount, 0) == 0 -> 1
          # 次优先：已下车
          Map.get(robot, :cashed_out, false) -> 2
          # 第三：已爆炸
          Map.get(robot, :crashed, false) -> 3
          # 最后：正在游戏中
          true -> 4
        end
      end)

    Enum.take(sorted_robots, count)
  end

  defp generate_robot_id(state) do
    # 生成一个不冲突的机器人ID（使用正数，但大于1000000以区分）
    existing_ids = Map.keys(state.players)

    Stream.iterate(1_000_000, &(&1 + 1))
    |> Enum.find(fn id -> id not in existing_ids end)
  end

  defp generate_robot_name do
    # 生成随机机器人名称（对应C++的机器人名称生成）
    prefixes = ["玩家", "用户", "游客", "Lucky", "Winner", "Player", "Guest", "User"]
    suffix = :rand.uniform(9999)

    Enum.random(prefixes) <> to_string(suffix)
  end

  defp adjust_bet_probability(robot_type, base_probability, _state) do
    case robot_type do
      # 保守型下注概率降低
      :conservative -> base_probability * 0.8
      # 激进型下注概率提高
      :aggressive -> base_probability * 1.2
      # 平衡型保持不变
      :balanced -> base_probability
      # 随机型随机调整
      :random -> base_probability * (0.5 + :rand.uniform())
    end
  end

  defp calculate_time_pressure(elapsed_time) do
    # 时间越长，机器人越倾向于下车
    # 超过30秒后开始有时间压力
    if elapsed_time > 30000 do
      # 60秒后压力最大
      pressure_factor = (elapsed_time - 30000) / 60000
      :rand.uniform() < min(pressure_factor, 0.8)
    else
      false
    end
  end
end
