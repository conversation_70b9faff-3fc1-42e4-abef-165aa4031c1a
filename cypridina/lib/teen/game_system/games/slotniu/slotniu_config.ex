defmodule Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig do
  @moduledoc """
  SlotNiu游戏配置模块

  提供SlotNiu游戏的所有配置参数，包括：
  - 图标权重和概率
  - 赔率表配置
  - 免费游戏配置
  - Jackpot配置
  - 转盘奖励配置
  - 下注配置
  - RTP配置
  """

  require Logger

  @doc """
  获取默认配置
  """
  def get_default_config do
    %{
      # ===== 游戏基础配置 =====
      game_info: %{
        name: "SlotN<PERSON>",
        version: "1.0.0",
        description: "SlotNiu老虎机游戏"
      },

      # ===== 房间配置 =====
      room: %{
        # 最大玩家数 (单人游戏)
        max_players: 1,
        # 最小玩家数 (单人游戏)
        min_players: 1,
        # 立即开始
        auto_start_delay: 100,
        # 不启用机器人 (单人游戏)
        enable_robots: false,
        # 机器人数量
        robot_count: 0
      },

      # ===== 老虎机配置 =====
      slot_config: %{
        # 行数
        rows: 3,
        # 列数
        cols: 5,
        # 支付线数
        lines: 9,
        # 最大线数
        max_lines: 9
      },

      # ===== 图标配置 =====
      symbol_config: %{
        # WILD图标
        wild_symbol: 0,
        # 牛头图标(Scatter)
        scatter_symbol: 10,
        # 鹿头图标(特殊)
        deer_symbol: 9,
        # 普通图标
        normal_symbols: [1, 2, 3, 4, 5, 6, 7, 8],
        # 总图标数
        total_symbols: 11
      },

      # ===== 图标权重配置 =====
      icon_weights: %{
        # 图标ID => 权重 (总权重1000)
        # WILD - 6%
        0 => 60,
        # 10 - 15%
        1 => 150,
        # J - 15%
        2 => 150,
        # Q - 14%
        3 => 140,
        # K - 13%
        4 => 130,
        # A - 9%
        5 => 90,
        # 狼 - 8%
        6 => 80,
        # 虎 - 5%
        7 => 50,
        # 鹰 - 4%
        8 => 40,
        # 鹿 - 8%
        9 => 80,
        # 牛 - 5%
        10 => 50
      },
      # icon_weights: %{
      #   # 图标ID => 权重 (总权重1000)
      #   0 => 100,    # WILD - 6%
      #   9 => 22,    # 鹿 - 8%
      #   1 => 22,   # 10 - 15%
      #   2 => 22,   # J - 15%
      #   3 => 22,   # Q - 14%
      #   4 => 22,   # K - 13%
      #   5 => 22,    # A - 9%
      #   6 => 22,    # 狼 - 8%
      #   7 => 22,    # 虎 - 5%
      #   8 => 22,    # 鹰 - 4%
      #   10 => 500    # 牛 - 5%
      # },
      # ===== 赔率表配置 =====
      payout_table: %{
        # 图标ID => [3连, 4连, 5连]的倍率
        # WILD (最高倍率)
        0 => [100, 200, 1750],
        # 10
        1 => [3, 10, 75],
        # J
        2 => [3, 10, 90],
        # Q
        3 => [15, 40, 150],
        # K
        4 => [25, 50, 200],
        # A
        5 => [30, 70, 250],
        # 狼头
        6 => [35, 80, 300],
        # 虎头
        7 => [45, 100, 350],
        # 鹰头
        8 => [70, 170, 400],
        # 鹿头 (特殊图标)
        9 => [0, 0, 0],
        # 牛头 (Scatter)
        10 => [25, 40, 400]
      },

      # ===== 下注配置 =====
      betting: %{
        # 最小下注
        min_bet: 1.8,
        # 最大下注
        max_bet: 1800,
        # 下注金额选项
        bet_amounts: [1.8, 9, 18, 90, 180, 900, 1800],
        # 下注倍率配置 (对应game_config.odds_config)
        odds_config: [1, 5, 10, 50, 100, 500, 1000],
        # 底分配置 (对应game_config.difen)
        difen: 20
      },

      # ===== RTP配置 =====
      rtp_config: %{
        # 目标返还率
        target_rtp: 96.5,
        # 最小返还率
        min_rtp: 94.0,
        # 最大返还率
        max_rtp: 98.0,
        # RTP调整因子
        adjustment_factor: 0.1
      },

      # ===== 中奖概率配置 =====
      win_probability: %{
        # 基础中奖概率 (按下注倍率)
        base_win_rates: %{
          # 高倍率：25%中奖概率
          1000 => 25,
          # 中高倍率：24%中奖概率
          500 => 24,
          # 中倍率：23%中奖概率
          100 => 23,
          # 中低倍率：22%中奖概率
          50 => 22,
          # 低倍率：25%中奖概率
          10 => 25,
          # 最低倍率：20%中奖概率
          1 => 20
        },

        # 中奖倍数分布
        win_multiplier_distribution: %{
          # 50%概率：1-3倍 (小奖)
          small_win: %{probability: 50, range: {1, 3}},
          # 30%概率：3-8倍 (中奖)
          medium_win: %{probability: 30, range: {3, 8}},
          # 15%概率：8-18倍 (大奖)
          big_win: %{probability: 15, range: {8, 18}},
          # 5%概率：20-40倍 (超级大奖)
          super_win: %{probability: 5, range: {20, 40}}
        }
      },

      # ===== 免费游戏配置 =====
      free_game: %{
        # 触发免费游戏需要的牛头数量
        trigger_count: 3,
        # 牛头数量 => 免费次数
        free_spins_table: %{
          # 3个牛头 => 5次免费
          3 => 5,
          # 4个牛头 => 10次免费
          4 => 10,
          # 5个牛头 => 15次免费
          5 => 15
        },
        # 免费游戏倍率
        multiplier: 2,
        # 最大免费次数
        max_free_spins: 50,
        # 是否允许重新触发
        retrigger_enabled: true
      },

      # ===== 牛头出现概率配置 =====
      bull_probability: %{
        # 中奖时牛头出现概率
        win_distribution: %{
          # 70%概率：0个牛头
          0 => 70,
          # 15%概率：1个牛头
          1 => 15,
          # 10%概率：2个牛头 (触发转盘)
          2 => 10,
          # 3%概率：3个牛头 (触发免费游戏)
          3 => 3,
          # 1%概率：4个牛头
          4 => 1,
          # 1%概率：5个牛头
          5 => 1
        },
        # 不中奖时牛头出现概率
        lose_distribution: %{
          # 90%概率：0个牛头
          0 => 90,
          # 7%概率：1个牛头
          1 => 7,
          # 3%概率：2个牛头 (触发转盘)
          2 => 3
        }
      },

      # ===== 转盘奖励配置 =====
      turntable_rewards: [
        # %{type: "free_spins", value: 5, weight: 0, luckyspinid: 1, name: "Free 5次"},
        # %{type: "free_spins", value: 10, weight: 0, luckyspinid: 2, name: "Free 10次"},
        # %{type: "free_spins", value: 15, weight: 0, luckyspinid: 3, name: "Free 15次"},
        # %{type: "wild_single", value: 1, weight: 0, luckyspinid: 4, name: "Expanding Wild-1"},
        # %{type: "wild_double", value: 2, weight: 5000, luckyspinid: 5, name: "Expanding Wild-2"},
        # %{type: "jackpot", value: 1, weight: 0, luckyspinid: 6, name: "Jackpot 1倍"},
        # %{type: "jackpot", value: 2, weight: 0, luckyspinid: 7, name: "Jackpot 2倍"},
        # %{type: "jackpot", value: 3, weight: 0, luckyspinid: 8, name: "Jackpot 3倍"}

        %{type: "free_spins", value: 5, weight: 300, luckyspinid: 1, name: "Free 5次"},
        %{type: "free_spins", value: 10, weight: 250, luckyspinid: 2, name: "Free 10次"},
        %{type: "free_spins", value: 15, weight: 150, luckyspinid: 3, name: "Free 15次"},
        %{type: "wild_single", value: 1, weight: 100, luckyspinid: 4, name: "Expanding Wild-1"},
        %{type: "wild_double", value: 2, weight: 50, luckyspinid: 5, name: "Expanding Wild-2"},
        %{type: "jackpot", value: 1, weight: 150, luckyspinid: 6, name: "Jackpot 1倍"},
        %{type: "jackpot", value: 2, weight: 40, luckyspinid: 7, name: "Jackpot 2倍"},
        %{type: "jackpot", value: 3, weight: 10, luckyspinid: 8, name: "Jackpot 3倍"}
      ],

      # ===== Jackpot配置 =====
      jackpot: %{
        # 总奖池贡献率
        # 1% 总奖池贡献率
        contribution_rate: 0.01,

        # 单奖池配置
        pools: [
          %{
            identifier: :jackpot,
            # 80万底金
            base_amount: 800_000,
            # 最小金额 (底金的5%)
            min_amount: 40000,
            # 最大金额 (底金的20倍)
            max_amount: 16_000_000,
            # 100%贡献率 (单奖池)
            contribution_rate: 1.0,
            # 权重
            weight: 1,
            # 优先级
            priority: 1,
            # 不重置，持续累积
            reset_on_win: false
          }
        ],

        # 兼容旧配置
        # 初始Jackpot金额 (实际金额乘以100)
        initial_amount: 1_500_000,
        # 最小Jackpot金额
        min_amount: 150_000,
        # 最大Jackpot金额
        max_amount: 15_000_000,
        # Jackpot中奖概率 (0.1%)
        win_probability: 0.001,
        # 广播间隔时间(毫秒)
        broadcast_interval: 2000,
        # 随机范围
        random_range: %{
          min: -300,
          max: 300
        },
        # Jackpot倍率表 - 根据下注金额和牛头数量计算倍率
        multipliers: %{
          180 => %{3 => 2, 4 => 3, 5 => 4, 6 => 5, 7 => 6, 8 => 7, 9 => 8},
          900 => %{3 => 10, 4 => 15, 5 => 20, 6 => 25, 7 => 30, 8 => 35, 9 => 40},
          1800 => %{3 => 20, 4 => 30, 5 => 40, 6 => 50, 7 => 60, 8 => 70, 9 => 80},
          9000 => %{3 => 100, 4 => 150, 5 => 200, 6 => 250, 7 => 300, 8 => 350, 9 => 400},
          18000 => %{3 => 200, 4 => 300, 5 => 400, 6 => 500, 7 => 600, 8 => 700, 9 => 800},
          90000 => %{3 => 1000, 4 => 1500, 5 => 2000, 6 => 2500, 7 => 3000, 8 => 3500, 9 => 4000},
          180_000 => %{
            3 => 2000,
            4 => 3000,
            5 => 4000,
            6 => 5000,
            7 => 6000,
            8 => 7000,
            9 => 8000
          }
        }
      },

      # ===== EXPANDING WILD配置 =====
      expanding_wild: %{
        # 是否启用
        enabled: true,
        # 最大列数
        max_columns: 5,
        # 动画持续时间(毫秒)
        animation_duration: 500,
        # 结算延迟(毫秒)
        settlement_delay: 1000,
        # 🎯 双列WILD第五列停留概率配置
        # 双列WILD时第五列停留不动的概率(%)
        double_wild_fifth_column_stay_probability: 100
      },

      # ===== 机器人配置 =====
      robot: %{
        # 是否启用机器人
        enabled: true,
        # 机器人下注时间间隔
        bet_time_span: %{
          # 最小间隔(毫秒)
          min: 4000,
          # 最大间隔(毫秒)
          max: 6000
        },
        # 机器人下注选择权重
        bet_choice_weights: [100, 200, 150, 100, 80, 50, 20]
      },

      # ===== 风控配置 =====
      risk_control: %{
        # 暗税比例(千分比)
        dark_revenue_rate: 100,
        # 上限比例(千分比)
        center_line_up_rate: 5000,
        # 上安全线固定线
        up_fix_safe_score: 300_000,
        # 下限比例(千分比)
        center_line_down_rate: 300,
        # 下安全线固定线
        down_fix_safe_score: 100_000,
        # 上线最大值
        safe_up_score_max: 350_000,
        # 下线最大值
        safe_bottom_score_max: 150_000,
        # 上下前置线比例(千分比)
        pre_rate: 700,
        # 放分概率(千分比)
        put_score_prob: 800,
        # 收分概率(千分比)
        get_score_prob: 800
      }
    }
  end

  def get_current_config() do
    Logger.info("🎰 [SLOTNIU_CONFIG] 返回默认配置")
    get_default_config()
  end

  @doc """
  获取合并后的配置
  接收 RoomManager 传入的合并后配置，进行最终处理
  """
  def get_merged_config(room_config) when is_map(room_config) do
    Logger.info("🎰 [SLOTNIU_CONFIG] 使用合并后的房间配置")

    # 获取默认配置作为基础
    default_config = get_default_config()

    # 与房间配置进行最终合并，确保配置完整性
    final_config = deep_merge_configs(default_config, room_config)

    Logger.info("🎰 [SLOTNIU_CONFIG] 配置合并完成，配置项数: #{map_size(final_config)}")
    final_config
  end

  @doc """
  获取图标权重配置
  """
  def get_icon_weights do
    config = get_current_config()
    config.icon_weights
  end

  @doc """
  获取赔率表配置
  """
  def get_payout_table do
    config = get_current_config()
    config.payout_table
  end

  @doc """
  获取下注配置
  """
  def get_betting_config do
    config = get_current_config()
    config.betting
  end

  @doc """
  获取免费游戏配置
  """
  def get_free_game_config do
    config = get_current_config()
    config.free_game
  end

  @doc """
  获取转盘奖励配置
  """
  def get_turntable_rewards do
    config = get_current_config()
    config.turntable_rewards
  end

  @doc """
  获取Jackpot配置
  """
  def get_jackpot_config do
    config = get_current_config()
    config.jackpot
  end

  @doc """
  获取Jackpot倍率表配置
  """
  def get_jackpot_multipliers do
    config = get_current_config()
    config.jackpot.multipliers
  end

  @doc """
  获取中奖概率配置
  """
  def get_win_probability_config do
    config = get_current_config()
    config.win_probability
  end

  @doc """
  获取牛头概率配置
  """
  def get_bull_probability_config do
    config = get_current_config()
    config.bull_probability
  end

  @doc """
  获取RTP配置
  """
  def get_rtp_config do
    config = get_current_config()
    config.rtp_config
  end

  @doc """
  获取风控配置
  """
  def get_risk_control_config do
    config = get_current_config()
    config.risk_control
  end

  # ===== 私有函数 =====

  # 加载自定义配置
  defp load_custom_config do
    # 这里可以从数据库、配置文件或环境变量中读取自定义配置
    # 暂时返回空配置
    %{}
  end

  # 合并默认配置和后台配置（参考 Slot777 的高级合并逻辑）
  defp deep_merge_configs(default_config, backend_config)
       when is_map(default_config) and is_map(backend_config) do
    if map_size(backend_config) == 0 do
      Logger.info("🎰 [SLOTNIU_CONFIG] 使用默认配置 - 后台配置为空")
      default_config
    else
      Logger.info("🎰 [SLOTNIU_CONFIG] 合并配置 - 后台配置项数: #{map_size(backend_config)}")

      # 标准化后台配置的键类型，使其与默认配置匹配
      normalized_backend_config = normalize_keys_to_match_default(backend_config, default_config)

      # 深度合并配置
      deep_merge(default_config, normalized_backend_config)
    end
  end

  defp deep_merge_configs(default_config, _backend_config) do
    Logger.warning("🎰 [SLOTNIU_CONFIG] 配置格式错误，使用默认配置")
    default_config || %{}
  end

  # 深度合并Map - 支持列表、原子等特殊数据类型（参考 Slot777Config）
  defp deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, &merge_values/3)
  end

  defp deep_merge(_left, right), do: right

  # 合并值的策略函数
  defp merge_values(key, left_val, right_val) do
    cond do
      # 两个都是Map：递归深度合并
      is_map(left_val) and is_map(right_val) ->
        deep_merge(left_val, right_val)

      # 两个都是列表：用右边的列表完全替换左边的列表
      is_list(left_val) and is_list(right_val) ->
        Logger.debug("🎰 [SLOTNIU_CONFIG] 列表合并 #{key}: 使用后台配置")
        right_val

      # 其他类型（包括原子、字符串、数字等）：直接用右边的值覆盖左边
      true ->
        if String.contains?(to_string(key), "odds") or key in [:odds_config, "odds_config"] do
          Logger.info(
            "🎰 [SLOTNIU_CONFIG] 重要配置项 #{key}: #{inspect(left_val)} -> #{inspect(right_val)}"
          )
        end

        right_val
    end
  end

  # 标准化键类型以匹配默认配置
  defp normalize_keys_to_match_default(backend_config, default_config) do
    Enum.reduce(backend_config, %{}, fn {key, value}, acc ->
      # 尝试找到匹配的键（原子或字符串）
      normalized_key = find_matching_key(key, default_config)

      # 如果值也是Map，递归标准化
      normalized_value =
        if is_map(value) and Map.has_key?(default_config, normalized_key) do
          default_sub_config = Map.get(default_config, normalized_key, %{})

          if is_map(default_sub_config) do
            normalize_keys_to_match_default(value, default_sub_config)
          else
            value
          end
        else
          value
        end

      Map.put(acc, normalized_key, normalized_value)
    end)
  end

  # 查找匹配的键
  defp find_matching_key(key, default_config) do
    cond do
      # 直接匹配
      Map.has_key?(default_config, key) ->
        key

      # 字符串转原子
      is_binary(key) and Map.has_key?(default_config, String.to_existing_atom(key)) ->
        String.to_existing_atom(key)

      # 原子转字符串
      is_atom(key) and Map.has_key?(default_config, Atom.to_string(key)) ->
        Atom.to_string(key)

      # 默认返回原键
      true ->
        key
    end
  rescue
    # 如果原子不存在，返回原键
    ArgumentError -> key
  end

  # 合并配置（保留原有函数以兼容）
  defp merge_configs(default_config, custom_config) do
    deep_merge(default_config, custom_config)
  end
end
