defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuLogic do
  @moduledoc """
  SlotNiu老虎机游戏逻辑模块

  实现老虎机的核心算法，包括：
  - 转轮结果生成
  - 中奖线计算
  - 免费游戏触发
  - Jackpot计算
  - 转盘功能
  """

  require Logger
  alias <PERSON><PERSON>rid<PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig

  # 图标定义 - 与前端保持一致
  @symbols %{
    # WILD
    wild: 0,
    # 10
    ten: 1,
    # J
    j: 2,
    # Q
    q: 3,
    # K
    k: 4,
    # A
    a: 5,
    # 狼
    wolf: 6,
    # 虎
    tiger: 7,
    # 鹰
    eagle: 8,
    # 鹿
    deer: 9,
    # 牛
    bull: 10
  }

  # 支付线定义 (与前端保持一致)
  @paylines [
    # 线1: 中间行
    [1, 1, 1, 1, 1],
    # 线2: 上行
    [0, 0, 0, 0, 0],
    # 线3: 下行
    [2, 2, 2, 2, 2],
    # 线4: V形
    [0, 1, 2, 1, 0],
    # 线5: 倒V形
    [2, 1, 0, 1, 2],
    # 线6: 阶梯上
    [0, 0, 1, 2, 2],
    # 线7: 阶梯下
    [2, 2, 1, 0, 0],
    # 线8: W形
    [1, 2, 1, 0, 1],
    # 线9: M形
    [1, 0, 1, 2, 1]
  ]
  @doc """
  获取当前游戏配置
  """
  def get_game_config do
    SlotNiuConfig.get_current_config()
  end

  @doc """
  获取中奖概率配置
  """
  def get_win_probability_config do
    SlotNiuConfig.get_win_probability_config()
  end

  @doc """
  获取牛头概率配置
  """
  def get_bull_probability_config do
    SlotNiuConfig.get_bull_probability_config()
  end

  @doc """
  获取转盘奖励配置
  """
  def get_turntable_rewards_config do
    SlotNiuConfig.get_turntable_rewards()
  end

  @doc """
  获取图标权重配置
  """
  def get_icon_weights_config do
    SlotNiuConfig.get_icon_weights()
  end

  @doc """
  获取赔率表配置
  """
  def get_payout_table_config do
    SlotNiuConfig.get_payout_table()
  end

  @doc """
  获取Jackpot倍率表配置
  """
  def get_jackpot_multipliers_config do
    SlotNiuConfig.get_jackpot_multipliers()
  end

  # ==================== 动态配置获取函数 ====================

  @doc """
  从动态配置中获取图标权重配置
  """
  def get_icon_weights_from_config(config) do
    case Map.get(config, :icon_weights) do
      # 回退到默认配置
      nil -> get_icon_weights_config()
      weights -> weights
    end
  end

  @doc """
  从动态配置中获取赔率表配置
  """
  def get_payout_table_from_config(config) do
    case Map.get(config, :payout_table) do
      # 回退到默认配置
      nil -> get_payout_table_config()
      table -> table
    end
  end

  @doc """
  从动态配置中获取转盘奖励配置
  """
  def get_turntable_rewards_from_config(config) do
    case Map.get(config, :turntable_rewards) do
      # 回退到默认配置
      nil -> get_turntable_rewards_config()
      rewards -> rewards
    end
  end

  @doc """
  生成老虎机转轮结果
  """
  def generate_spin_result(rows, cols, lines, bet_amount, config \\ %{}) do
    # 获取配置化的参数 - 优先使用传入的动态配置
    icon_weights = get_icon_weights_from_config(config)
    payout_table = get_payout_table_from_config(config)

    # 生成随机图标矩阵
    icon_matrix = generate_icon_matrix_with_weights(rows, cols, icon_weights)

    # 计算中奖线
    winning_lines = calculate_winning_lines(icon_matrix, lines, bet_amount, payout_table)

    # 计算总倍率
    total_multiplier = Enum.reduce(winning_lines, 0, fn line, acc -> acc + line.multiplier end)

    # 计算总中奖金额
    odds_from_bet = trunc(bet_amount / 180)
    difen_times_odds = 20 * odds_from_bet
    total_win = total_multiplier * difen_times_odds

    # 检查特殊功能触发
    {free_spins, turntable_trigger, jackpot_type} =
      check_special_features(icon_matrix, bet_amount)

    result = %{
      icon_matrix: icon_matrix,
      winning_lines: winning_lines,
      total_win: total_win,
      # 添加总倍率字段
      total_multiplier: total_multiplier,
      free_spins: free_spins,
      turntable_trigger: turntable_trigger,
      jackpot_type: jackpot_type,
      bet_amount: bet_amount
    }

    result
  end

  @doc """
  生成免费游戏转轮结果 - 限制牛头和鹿头数量不超过2个
  """
  def generate_free_game_spin_result(rows, cols, lines, bet_amount, config \\ %{}) do
    # 获取配置化的参数 - 优先使用传入的动态配置
    icon_weights = get_icon_weights_from_config(config)
    payout_table = get_payout_table_from_config(config)

    # 生成免费游戏专用的图标矩阵（限制牛头和鹿头数量）
    icon_matrix = generate_free_game_icon_matrix_with_weights(rows, cols, icon_weights)

    # 计算中奖线
    winning_lines = calculate_winning_lines(icon_matrix, lines, bet_amount, payout_table)

    # 计算总倍率
    total_multiplier = Enum.reduce(winning_lines, 0, fn line, acc -> acc + line.multiplier end)

    # 计算总中奖金额
    odds_from_bet = trunc(bet_amount / 180)
    difen_times_odds = 20 * odds_from_bet
    total_win = total_multiplier * difen_times_odds

    # 免费游戏中检查特殊功能触发（但应用免费游戏规则）
    {free_spins, turntable_trigger, jackpot_type} =
      check_special_features_for_free_game(icon_matrix, bet_amount)

    result = %{
      icon_matrix: icon_matrix,
      winning_lines: winning_lines,
      total_win: total_win,
      # 添加总倍率字段
      total_multiplier: total_multiplier,
      free_spins: free_spins,
      turntable_trigger: turntable_trigger,
      jackpot_type: jackpot_type,
      bet_amount: bet_amount
    }

    result
  end

  @doc """
  生成随机图标矩阵 - 参考IndiaGameServer的IconRation概率配置
  """
  def generate_icon_matrix(rows, cols) do
    for _row <- 1..rows do
      for _col <- 1..cols do
        # 🎯 使用IndiaGameServer的图标概率配置生成图标
        # 参考IndiaGameServer/Config/Lua/SlotNiuConfig.lua的IconRation配置
        generate_weighted_icon()
      end
    end
  end

  @doc """
  生成随机图标矩阵 - 使用配置化的权重，参考旧项目逻辑确保牛头和鹿头不会同时大量出现
  """
  def generate_icon_matrix_with_weights(rows, cols, icon_weights) do
    # 先生成基础矩阵
    base_matrix =
      for _row <- 1..rows do
        for _col <- 1..cols do
          # 使用配置化的权重生成图标
          generate_weighted_icon_from_config(icon_weights)
        end
      end

    # 🎯 参考旧项目C++逻辑：检查并调整特殊图标分布
    # 确保牛头和鹿头不会同时达到触发条件
    adjust_special_symbols_distribution(base_matrix)
  end

  @doc """
  生成免费游戏专用的图标矩阵 - 限制牛头和鹿头数量不超过2个
  """
  def generate_free_game_icon_matrix_with_weights(rows, cols, icon_weights) do
    # 先生成基础矩阵
    base_matrix =
      for _row <- 1..rows do
        for _col <- 1..cols do
          # 使用配置化的权重生成图标
          generate_weighted_icon_from_config(icon_weights)
        end
      end

    # 🎯 免费游戏专用：限制牛头和鹿头数量不超过2个
    adjust_special_symbols_for_free_game(base_matrix)
  end

  @doc """
  生成Wild游戏专用的图标矩阵 - 限制牛头和鹿头数量不超过2个
  """
  def generate_wild_game_icon_matrix_with_weights(rows, cols, icon_weights) do
    # 先生成基础矩阵
    base_matrix =
      for _row <- 1..rows do
        for _col <- 1..cols do
          # 使用配置化的权重生成图标
          generate_weighted_icon_from_config(icon_weights)
        end
      end

    # 🎯 Wild游戏专用：限制牛头和鹿头数量不超过2个
    adjust_special_symbols_for_wild_game(base_matrix)
  end

  # 根据权重生成图标 - 使用配置化的权重
  defp generate_weighted_icon do
    # 获取配置化的图标权重
    icon_weights_map = get_icon_weights_config()

    # 转换为权重列表格式
    icon_weights = Enum.map(icon_weights_map, fn {icon_id, weight} -> {icon_id, weight} end)

    # 总权重
    total_weight = 1000
    random_value = :rand.uniform(total_weight)

    # 根据权重选择图标
    Enum.reduce_while(icon_weights, 0, fn {icon_id, weight}, acc ->
      new_acc = acc + weight

      if random_value <= new_acc do
        {:halt, icon_id}
      else
        {:cont, new_acc}
      end
    end)
  end

  # 根据配置化权重生成图标
  defp generate_weighted_icon_from_config(icon_weights_map) do
    # 将Map转换为权重列表
    icon_weights = Enum.map(icon_weights_map, fn {icon_id, weight} -> {icon_id, weight} end)

    # 计算总权重
    total_weight = Enum.reduce(icon_weights, 0, fn {_icon_id, weight}, acc -> acc + weight end)
    random_value = :rand.uniform(total_weight)

    # 根据权重选择图标
    Enum.reduce_while(icon_weights, 0, fn {icon_id, weight}, acc ->
      new_acc = acc + weight

      if random_value <= new_acc do
        {:halt, icon_id}
      else
        {:cont, new_acc}
      end
    end)
  end

  @doc """
  计算中奖线 - 包含Scatter符号特殊处理
  """
  def calculate_winning_lines(icon_matrix, lines, bet_amount, payout_table) do
    # 使用传入的配置化赔率表

    # 🎯 计算普通中奖线（按照图片中显示的9条线）
    line_wins =
      @paylines
      |> Enum.take(lines)
      |> Enum.with_index(1)
      |> Enum.map(fn {line_pattern, line_number} ->
        calculate_line_win(icon_matrix, line_pattern, line_number, bet_amount, payout_table)
      end)
      |> Enum.filter(fn line -> line.multiplier > 0 end)

    # 🎯 计算Scatter符号（牛头）的特殊中奖 - 不需要在线上，任意位置即可
    scatter_wins = calculate_scatter_wins(icon_matrix, bet_amount)

    # 合并普通中奖线和Scatter中奖
    line_wins ++ scatter_wins
  end

  @doc """
  计算单条线的中奖情况 - 参考IndiaGameServer C++实现
  """
  def calculate_line_win(icon_matrix, line_pattern, line_number, _bet_amount, payout_table) do
    # 获取该线上的图标
    line_symbols =
      line_pattern
      |> Enum.with_index()
      |> Enum.map(fn {row, col} ->
        Enum.at(Enum.at(icon_matrix, row), col)
      end)

    # 检查连续符号
    {symbol, count} = count_consecutive_symbols_cpp_style(line_symbols)

    # 计算倍率
    win_amount =
      if count >= 3 do
        get_symbol_multiplier(symbol, count, payout_table)
      else
        0
      end

    %{
      line: line_number,
      symbol: symbol,
      count: count,
      multiplier: win_amount,
      positions: if(count >= 3, do: Enum.take(0..(count - 1), count), else: [])
    }
  end

  @doc """
  计算连续相同图标数量
  """
  def count_consecutive_symbols([first | rest]) do
    count = 1 + count_consecutive_from_left(rest, first)
    {first, count}
  end

  defp count_consecutive_from_left([], _symbol), do: 0

  defp count_consecutive_from_left([symbol | rest], symbol) do
    1 + count_consecutive_from_left(rest, symbol)
  end

  defp count_consecutive_from_left([_other | _rest], _symbol), do: 0

  @doc """
  计算连续相同图标数量 - C++风格实现，完全按照旧项目逻辑
  参考IndiaGameServer/Code/Game/SlotNiu/SlotNiu/SlotNiu/Code/GameRoom.cpp 第732-755行
  """
  def count_consecutive_symbols_cpp_style([]), do: {nil, 0}

  def count_consecutive_symbols_cpp_style(line_symbols) do
    [first_icon | rest_icons] = line_symbols

    # 🎯 按照C++逻辑：从第一个符号开始检查连续性
    {final_symbol, consecutive_count} = check_consecutive_cpp_style(first_icon, rest_icons, 1)

    # WILD和牛头不产生普通中奖
    if final_symbol == @symbols.wild or final_symbol == @symbols.bull do
      {final_symbol, 0}
    else
      {final_symbol, consecutive_count}
    end
  end

  # C++风格的连续检查逻辑
  defp check_consecutive_cpp_style(current_symbol, [], count), do: {current_symbol, count}

  defp check_consecutive_cpp_style(current_symbol, [next_icon | rest], count) do
    cond do
      # 情况1：下一个符号与当前符号相同
      next_icon == current_symbol ->
        check_consecutive_cpp_style(current_symbol, rest, count + 1)

      # 情况2：下一个符号是WILD
      next_icon == @symbols.wild ->
        check_consecutive_cpp_style(current_symbol, rest, count + 1)

      # 情况3：当前符号是WILD，下一个符号不是WILD
      current_symbol == @symbols.wild ->
        # WILD不能变成鹿和牛头
        if next_icon == @symbols.deer or next_icon == @symbols.bull do
          {current_symbol, count}
        else
          check_consecutive_cpp_style(next_icon, rest, count + 1)
        end

      # 情况4：不连续，中断
      true ->
        {current_symbol, count}
    end
  end

  @doc """
  计算Scatter符号（牛头）的特殊中奖 - 不需要在线上，任意位置即可
  参考IndiaGameServer的Scatter中奖规则
  """
  def calculate_scatter_wins(icon_matrix, _bet_amount) do
    # 牛头主要用于触发免费游戏和转盘，不产生直接的Scatter中奖
    []
  end

  @doc """
  获取图标倍率
  """
  def get_symbol_multiplier(symbol, count, payout_table) do
    case Map.get(payout_table, symbol) do
      nil ->
        0

      multipliers when is_list(multipliers) ->
        case count do
          3 -> Enum.at(multipliers, 0, 0)
          4 -> Enum.at(multipliers, 1, 0)
          5 -> Enum.at(multipliers, 2, 0)
          _ -> 0
        end

      _ ->
        0
    end
  end

  @doc """
  检查特殊功能触发 - 参考旧项目C++逻辑，确保牛头和鹿头不会同时触发

  旧项目逻辑：
  1. 先判断是否触发牛头（转盘/Jackpot）
  2. 只有在没有牛头的情况下，才判断是否触发鹿头（免费游戏）
  3. 一次游戏只能触发一种特殊功能，确保互斥
  """
  def check_special_features(icon_matrix, bet_amount) do
    # 统计牛头图标数量
    bull_count = count_symbol_in_matrix(icon_matrix, @symbols.bull)

    # 统计鹿图标数量 - 用于触发免费游戏
    deer_count = count_symbol_in_matrix(icon_matrix, @symbols.deer)

    # 🎯 参考旧项目C++逻辑：牛头和鹿头互斥判断
    # 优先级：牛头（转盘）> 鹿头（免费游戏）

    # 检查转盘触发（牛头优先）
    turntable_trigger = bull_count >= 3

    # 检查免费游戏触发（只有在没有触发转盘的情况下才检查）
    # 参考C++代码：if (randNum < config.FreeTotal[index] && SevenCount == 0)
    free_spins =
      if turntable_trigger do
        # 如果触发了转盘（牛头>=3），则不触发免费游戏
        0
      else
        # 只有在没有触发转盘的情况下，才检查免费游戏
        case deer_count do
          n when n >= 5 -> 15
          4 -> 10
          3 -> 5
          _ -> 0
        end
      end

    # 检查Jackpot触发 (根据牛头数量和下注金额)
    jackpot_type = check_jackpot_trigger(bull_count, bet_amount)

    {free_spins, turntable_trigger, jackpot_type}
  end

  @doc """
  统计矩阵中特定图标的数量
  """
  def count_symbol_in_matrix(icon_matrix, symbol) do
    icon_matrix
    |> List.flatten()
    |> Enum.count(fn s -> s == symbol end)
  end

  @doc """
  检查Jackpot触发 - 基于正确的下注金额计算
  """
  def check_jackpot_trigger(bull_count, bet_amount) do
    cond do
      bull_count >= 9 and bet_amount >= 180_000 -> :mega_jackpot
      bull_count >= 7 and bet_amount >= 90000 -> :major_jackpot
      bull_count >= 5 and bet_amount >= 18000 -> :minor_jackpot
      bull_count >= 3 and bet_amount >= 1800 -> :mini_jackpot
      true -> :no_jackpot
    end
  end

  @doc """
  生成游戏结果 - 使用正确的中奖检查逻辑

  参数：
  - odds: 倍率
  - current_money: 当前金币
  - difen: 底分
  - config: 游戏配置
  - player_wild_state: 玩家的EXPANDING WILD状态 (可选)
  """
  def generate_game_result(odds, current_money, difen, config \\ %{}, player_wild_state \\ nil) do
    # 计算下注金额
    bet_rate_num = 9
    frontend_difen = difen / 100
    bet_amount = trunc(odds * bet_rate_num * frontend_difen * 100)

    # 生成老虎机结果
    spin_result = generate_spin_result(3, 5, 9, bet_amount, config)

    # 继续处理游戏结果
    process_game_result(spin_result, odds, current_money, difen, config, player_wild_state, bet_amount)
  end

  @doc """
  生成免费游戏结果 - 使用免费游戏专用的符号限制逻辑

  参数：
  - odds: 倍率
  - current_money: 当前金币
  - difen: 底分
  - config: 游戏配置
  - player_wild_state: 玩家的EXPANDING WILD状态 (可选)
  """
  def generate_free_game_result(odds, current_money, difen, config \\ %{}, player_wild_state \\ nil) do
    # 计算下注金额
    bet_rate_num = 9
    frontend_difen = difen / 100
    bet_amount = trunc(odds * bet_rate_num * frontend_difen * 100)

    # 生成免费游戏专用的老虎机结果
    spin_result = generate_free_game_spin_result(3, 5, 9, bet_amount, config)

    # 继续处理游戏结果
    process_game_result(spin_result, odds, current_money, difen, config, player_wild_state, bet_amount)
  end

  # 处理游戏结果的通用逻辑
  defp process_game_result(spin_result, odds, current_money, difen, config, player_wild_state, bet_amount) do

    # 应用EXPANDING WILD效果
    final_icon_matrix =
      if player_wild_state do
        {updated_matrix, _updated_wild_state} =
          apply_expanding_wild_to_matrix(spin_result.icon_matrix, player_wild_state)

        updated_matrix
      else
        spin_result.icon_matrix
      end

    # 重新计算中奖线
    final_winning_lines =
      if player_wild_state do
        payout_table = get_payout_table_from_config(config)
        calculate_winning_lines(final_icon_matrix, 9, bet_amount, payout_table)
      else
        spin_result.winning_lines
      end

    # 重新计算总中奖金额
    final_total_win =
      if player_wild_state do
        Enum.reduce(final_winning_lines, 0, fn line, acc ->
          acc + Map.get(line, :multiplier, 0)
        end) * trunc(bet_amount / 9)
      else
        spin_result.total_win
      end

    # 转换图标矩阵为前端期望的格式
    icon_result = matrix_to_frontend_format(final_icon_matrix)

    # 构建中奖线结果
    line_result =
      final_winning_lines
      |> Enum.map(fn line ->
        %{"line" => line.line, "num" => line.count}
      end)

    # 统计牛头和鹿数量
    niunum = count_symbol_in_matrix(final_icon_matrix, @symbols.bull)
    deer_count = count_symbol_in_matrix(final_icon_matrix, @symbols.deer)

    # 使用实际的中奖结果
    win_money = final_total_win

    # 🎯 参考旧项目C++逻辑：牛头和鹿头互斥判断
    # 检查转盘触发条件（牛头优先）
    lucky_spin =
      if player_wild_state do
        # 玩家已有EXPANDING WILD状态，不再触发转盘
        0
      else
        if niunum >= 3, do: 1, else: 0
      end

    # 计算免费游戏次数（只有在没有触发转盘的情况下才触发）
    # 参考C++代码：if (randNum < config.FreeTotal[index] && SevenCount == 0)
    free_times =
      if lucky_spin > 0 do
        # 如果触发了转盘（牛头>=3），则不触发免费游戏
        0
      else
        # 只有在没有触发转盘的情况下，才检查免费游戏
        case deer_count do
          n when n >= 5 -> 15
          4 -> 10
          3 -> 5
          _ -> 0
        end
      end

    # 计算转盘显示的奖池金额
    lucky_jackpot =
      if lucky_spin > 0 do
        jackpot_pool_amount = Map.get(config, :jackpot_amount, 15_000_000)
        actual_bet_amount = trunc(bet_amount)
        calculate_lucky_jackpot(jackpot_pool_amount, niunum, actual_bet_amount)
      else
        0
      end

    # 计算金币变化
    change_money = win_money - bet_amount

    # 获取总倍率
    total_multiplier = Map.get(spin_result, :total_multiplier, 0)

    # 设置EXPANDING WILD参数
    {wildtype, wildnum} =
      if player_wild_state do
        wild_type = Map.get(player_wild_state, :wild_type, 0)
        current_column = Map.get(player_wild_state, :current_column, 0)

        actual_wildnum =
          case wild_type do
            2 ->
              if current_column <= 4 do
                current_column
              else
                current_column - 1
              end

            1 ->
              current_column

            _ ->
              0
          end

        {wild_type, actual_wildnum}
      else
        {0, 0}
      end

    result = %{
      "freetimes" => free_times,
      "niunum" => niunum,
      "wildtype" => wildtype,
      "wildnum" => wildnum,
      "iconresult" => icon_result,
      "linecount" => length(final_winning_lines),
      "lineresult" => line_result,
      "totalmult" => total_multiplier,
      "winmoney" => win_money,
      "changemoney" => change_money,
      "luckyjackpot" => lucky_jackpot,
      "luckyspin" => lucky_spin,
      "prize" => 0
    }

    result
  end

  @doc """
  生成转盘结果 - 参考TpMasterClient前端期望格式和IndiaGameServer配置
  """
  def generate_turntable_result(config \\ %{}) do
    # 使用配置化的转盘奖励系统 - 优先使用传入的动态配置
    rewards = get_turntable_rewards_from_config(config)

    # 过滤掉权重为0的奖励
    active_rewards = Enum.filter(rewards, fn reward -> reward.weight > 0 end)

    # 根据权重随机选择奖励
    total_weight = Enum.sum(Enum.map(active_rewards, & &1.weight))
    random_value = :rand.uniform(total_weight)

    selected_reward =
      Enum.reduce_while(active_rewards, 0, fn reward, acc ->
        new_acc = acc + reward.weight

        if random_value <= new_acc do
          {:halt, reward}
        else
          {:cont, new_acc}
        end
      end)

    # 构建转盘结果格式
    result = %{
      "luckyspinid" => selected_reward.luckyspinid,
      "type" => selected_reward.type,
      "value" => selected_reward.value,
      "winmoney" => 0,
      "freetimes" => if(selected_reward.type == "free_spins", do: selected_reward.value, else: 0),
      "wildtype" => get_wild_type(selected_reward),
      "jackpotlevel" => if(selected_reward.type == "jackpot", do: selected_reward.value, else: 0)
    }

    result
  end

  # 获取Wild类型
  defp get_wild_type(reward) do
    case reward.type do
      # 单线Wild
      "wild_single" -> 1
      # 双线Wild
      "wild_double" -> 2
      # 无Wild
      _ -> 0
    end
  end

  @doc """
  计算Jackpot基础金额 - 参考IndiaGameServer实现
  根据当前Jackpot奖池金额和牛头数量计算luckyjackpot（转盘显示的金额）
  """
  def calculate_lucky_jackpot(jackpot_pool_amount, bull_count, bet_amount) do
    # 获取Jackpot倍率配置
    jackpot_mult = get_jackpot_mult(bet_amount, bull_count)

    # 计算基础Jackpot金额
    base_ratio = jackpot_pool_amount / 10000.0
    trunc(base_ratio * jackpot_mult)
  end

  @doc """
  获取Jackpot倍率 - 参考IndiaGameServer的GetJackpotMult实现
  """
  defp get_jackpot_mult(bet_amount, bull_count) do
    # 使用配置化的Jackpot倍率表
    jackpot_config = get_jackpot_multipliers_config()

    # 查找匹配的配置
    bet_config = Map.get(jackpot_config, bet_amount)

    if bet_config do
      Map.get(bet_config, bull_count, 0)
    else
      # 如果没有直接匹配，按比例计算（基于900的配置）
      base_config = Map.get(jackpot_config, 900, %{})
      base_mult = Map.get(base_config, bull_count, 0)

      if base_mult > 0 do
        trunc(base_mult * bet_amount / 900)
      else
        0
      end
    end
  end

  @doc """
  计算Jackpot最终奖励 - 参考IndiaGameServer实现
  """
  def calculate_jackpot_final_reward(lucky_jackpot, jackpot_level) do
    # 计算最终奖励
    lucky_jackpot * jackpot_level
  end

  # 🗑️ 旧的Jackpot计算函数已被新的calculate_lucky_jackpot和calculate_jackpot_final_reward替代
  # 保留此函数仅用于向后兼容测试文件
  @deprecated "使用 calculate_lucky_jackpot/3 和 calculate_jackpot_final_reward/2 替代"
  def calculate_jackpot_reward(bull_count, bet_amount, jackpot_level) do
    Logger.warn("🚨 [DEPRECATED] calculate_jackpot_reward已弃用，请使用新的Jackpot计算方法")

    # 使用新的计算方法
    # 默认奖池金额
    jackpot_pool_amount = 15_000_000
    lucky_jackpot = calculate_lucky_jackpot(jackpot_pool_amount, bull_count, bet_amount)
    calculate_jackpot_final_reward(lucky_jackpot, jackpot_level)
  end

  @doc """
  将3x5图标矩阵转换为前端期望的格式
  """
  def matrix_to_frontend_format(icon_matrix) do
    # 将3x5矩阵转换为1-15的Map格式
    icon_matrix
    |> List.flatten()
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {icon, index}, acc ->
      Map.put(acc, Integer.to_string(index), icon)
    end)
  end

  # 🎯 获取当前配置
  def get_win_config(config \\ %{}) do
    Map.get(config, :win_config, @default_win_config)
  end

  @doc """
  生成EXPANDING WILD游戏结果

  🚨 注意：根据旧项目分析，EXPANDING WILD的实际实现与用户描述不同！

  **旧项目的真实EXPANDING WILD机制**：
  - 不是5轮连续游戏页面，而是在正常游戏中生效的机制
  - WILD1（单列）：从第5列开始，持续5次游戏，每次向前移动一列（5→4→3→2→1）
  - WILD2（双列）：从第4,5列开始，持续4次游戏，每次向前移动一列（4,5→3,4→2,3→1,2）
  - 每次正常游戏时消耗一次WILD次数
  - 前端有状态管理：SpinStateFruitMachine.Single和SpinStateFruitMachine.Double

  **当前实现**：
  为了兼容用户期望，保持5轮连续游戏的实现，但添加注释说明真实机制
  """
  def generate_expanding_wild_game(bet_amount, config \\ %{}) do
    # 获取Wild类型（1=单线，2=双线）
    wild_type = Map.get(config, :wild_type, 1)

    # 获取图标权重配置
    icon_weights = get_icon_weights_from_config(config)

    # EXPANDING WILD游戏进行5轮
    rounds = 5
    total_win = 0
    round_results = []

    # 从第5列开始，每轮向前移动一列
    {final_total_win, final_round_results} =
      Enum.reduce(1..rounds, {total_win, round_results}, fn round,
                                                            {acc_total_win, acc_round_results} ->
        # 第1轮：第5列，第2轮：第4列，...，第5轮：第1列
        wild_column = 6 - round

        # 生成带有EXPANDING WILD的矩阵（支持双线Wild）- 限制牛头和鹿头数量
        icon_matrix = generate_expanding_wild_matrix(wild_column, wild_type, icon_weights)

        # 计算这一轮的中奖
        winning_lines = calculate_winning_lines(icon_matrix, 9, bet_amount, config)

        round_win =
          Enum.reduce(winning_lines, 0, fn line, acc ->
            acc + Map.get(line, :multiplier, 0)
          end) * trunc(bet_amount / 9)

        new_total_win = acc_total_win + round_win

        round_result = %{
          round: round,
          wild_column: wild_column,
          wild_type: wild_type,
          icon_matrix: icon_matrix,
          winning_lines: winning_lines,
          round_win: round_win
        }

        new_round_results = [round_result | acc_round_results]

        {new_total_win, new_round_results}
      end)

    # 第5轮后有机会保留一列Wild
    {final_total_win_with_bonus, final_round_results_with_bonus, total_rounds} =
      if :rand.uniform(100) <= 30 do
        # 随机选择一列保留Wild
        bonus_wild_column = :rand.uniform(5)
        bonus_matrix = generate_expanding_wild_matrix(bonus_wild_column, 1, icon_weights)
        bonus_winning_lines = calculate_winning_lines(bonus_matrix, 9, bet_amount, config)

        bonus_win =
          Enum.reduce(bonus_winning_lines, 0, fn line, acc ->
            acc + Map.get(line, :multiplier, 0)
          end) * trunc(bet_amount / 9)

        bonus_total_win = final_total_win + bonus_win

        bonus_round = %{
          round: 6,
          wild_column: bonus_wild_column,
          icon_matrix: bonus_matrix,
          winning_lines: bonus_winning_lines,
          round_win: bonus_win,
          is_bonus: true
        }

        bonus_round_results = [bonus_round | final_round_results]

        {bonus_total_win, bonus_round_results, 6}
      else
        {final_total_win, final_round_results, 5}
      end

    result = %{
      type: "expanding_wild",
      total_rounds: total_rounds,
      total_win: final_total_win_with_bonus,
      round_results: Enum.reverse(final_round_results_with_bonus),
      bet_amount: bet_amount
    }

    result
  end

  @doc """
  初始化玩家的EXPANDING WILD状态（基于旧项目实现）

  **参数**：
  - wild_type: 1=单列WILD，2=双列WILD
  - 返回玩家的EXPANDING WILD状态

  **机制**：
  - WILD1（单列）：从第5列开始，持续5次游戏
  - WILD2（双列）：从第4,5列开始，持续4次游戏
  """
  def init_expanding_wild_state(wild_type) do
    case wild_type do
      1 ->
        # 单列WILD：从第5列开始，持续5次
        %{
          wild_type: 1,
          # 剩余次数
          wild_num: 5,
          # 当前WILD所在列
          current_column: 5
        }

      2 ->
        # 双列WILD：从第4,5列开始，持续4次
        %{
          wild_type: 2,
          # 剩余次数
          wild_num: 4,
          # 当前WILD起始列（4,5列）
          current_column: 4
        }

      _ ->
        nil
    end
  end

  @doc """
  应用EXPANDING WILD到游戏矩阵（基于旧项目实现）- 限制牛头和鹿头数量

  **参数**：
  - icon_matrix: 3x5图标矩阵
  - wild_state: 玩家的EXPANDING WILD状态

  **返回**：
  - {updated_matrix, updated_wild_state}
  """
  def apply_expanding_wild_to_matrix(icon_matrix, wild_state) do
    if is_nil(wild_state) or wild_state.wild_num <= 0 do
      {icon_matrix, nil}
    else
      # 🎯 首先对基础矩阵应用Wild游戏符号限制（限制牛头和鹿头数量不超过2个）
      symbol_limited_matrix = adjust_special_symbols_for_wild_game(icon_matrix)

      # 根据Wild类型设置WILD列
      wild_columns =
        case wild_state.wild_type do
          1 ->
            # 单列WILD
            [wild_state.current_column]

          2 ->
            # 双列WILD
            if wild_state.current_column <= 4 do
              [wild_state.current_column, wild_state.current_column + 1]
            else
              [wild_state.current_column]
            end

          _ ->
            []
        end

      # 将指定列设置为WILD
      updated_matrix =
        symbol_limited_matrix
        |> Enum.with_index()
        |> Enum.map(fn {row, _row_idx} ->
          row
          |> Enum.with_index()
          |> Enum.map(fn {icon, col_idx} ->
            # 转换为1-based索引比较
            if Enum.member?(wild_columns, col_idx + 1) do
              @symbols.wild
            else
              icon
            end
          end)
        end)

      # 更新EXPANDING WILD状态
      updated_wild_state = %{
        wild_state
        | wild_num: wild_state.wild_num - 1,
          current_column: max(1, wild_state.current_column - 1)
      }

      # 如果次数用完，清除状态
      final_wild_state =
        if updated_wild_state.wild_num <= 0 do
          nil
        else
          updated_wild_state
        end

      {updated_matrix, final_wild_state}
    end
  end

  # 生成带有EXPANDING WILD的矩阵（支持双线Wild）- 限制牛头和鹿头数量
  defp generate_expanding_wild_matrix(wild_column, wild_type \\ 1, icon_weights \\ nil) do
    # 生成基础矩阵 - 使用Wild游戏专用的矩阵生成（限制牛头和鹿头数量）
    base_matrix =
      if icon_weights do
        generate_wild_game_icon_matrix_with_weights(3, 5, icon_weights)
      else
        # 回退到默认配置
        default_icon_weights = get_icon_weights_config()
        generate_wild_game_icon_matrix_with_weights(3, 5, default_icon_weights)
      end

    # 根据Wild类型设置Wild列
    wild_columns =
      case wild_type do
        2 ->
          # 双线Wild：当前列和相邻列都设为Wild
          if wild_column <= 3 do
            # 当前列和右侧列
            [wild_column, wild_column + 1]
          else
            # 左侧列和当前列
            [wild_column - 1, wild_column]
          end

        _ ->
          # 单线Wild：只有当前列
          [wild_column]
      end

    # 将指定列设置为WILD
    if Enum.all?(wild_columns, fn col -> col >= 1 and col <= 5 end) do
      base_matrix
      |> Enum.with_index()
      |> Enum.map(fn {row, _row_idx} ->
        row
        |> Enum.with_index()
        |> Enum.map(fn {icon, col_idx} ->
          # 转换为1-based索引比较
          if Enum.member?(wild_columns, col_idx + 1) do
            @symbols.wild
          else
            icon
          end
        end)
      end)
    else
      base_matrix
    end
  end

  @doc """
  更新PRIZE奖池

  PRIZE奖池规则（基于IndiaGameServer C++项目分析）：

  **收集阶段**：
  - 只有当牛头数量 1-2 个时才收集到PRIZE奖池
  - 收集公式：牛头数量 × 底分 × 倍率 (参考C++代码第449行)
  - 3个以上牛头不收集到PRIZE奖池，而是触发转盘

  **使用阶段**：
  - 当牛头数量 >= 3 时，PRIZE奖池金额会加到中奖金额中 (参考C++代码第428行)
  - 使用后PRIZE奖池立即清零 (参考C++代码第180行)

  **切换倍率影响**：
  - 奖池金额不受倍率切换影响，持续积累
  - 但收集速度受当前倍率影响（倍率越高，收集越快）

  **免费游戏中的行为**：
  - 免费游戏中同样遵循收集和使用规则
  - 免费游戏中的PRIZE奖池使用会增加免费游戏总奖金
  """
  def update_prize_pool(bull_count, difen, bet_multiplier, current_pool \\ 0) do
    # 参考C++代码：pPlayer->ChangeFrize(gameIconResult.nNiuNum * nDiFen * nOdds);
    if bull_count > 0 and bull_count < 3 do
      # C++公式：牛头数量 × 底分 × 倍率
      pool_increase = bull_count * difen * bet_multiplier
      new_pool = current_pool + pool_increase

      {new_pool, pool_increase}
    else
      {current_pool, 0}
    end
  end

  @doc """
  使用PRIZE奖池（当触发转盘时）

  参考C++代码分析：
  - 第428行：nUserFrize = pPlayer->GetFrize(); 获取PRIZE奖池金额并加到中奖中
  - 第180行：m_nPrize = 0; 触发转盘时PRIZE奖池清零

  **使用条件**：
  - 牛头数量 >= 3 个（触发转盘）
  - 当前PRIZE奖池 > 0

  **使用效果**：
  - PRIZE奖池金额直接加到中奖金额中
  - 奖池立即清零
  - 这是PRIZE奖池的唯一兑现方式

  **返回值**：
  - {使用的奖池金额, 清零后的奖池金额}
  """
  def use_prize_pool(bull_count, current_pool) do
    if bull_count >= 3 and current_pool > 0 do
      {current_pool, 0}
    else
      # 不使用奖池
      {0, current_pool}
    end
  end

  @doc """
  处理切换倍率时的PRIZE奖池逻辑

  **重要特性**：
  - PRIZE奖池金额不受倍率切换影响，持续积累
  - 切换倍率只影响后续收集速度，不影响已有奖池
  - 奖池是按玩家个人维护的，不是全局共享

  **参数**：
  - old_multiplier: 旧倍率
  - new_multiplier: 新倍率
  - current_pool: 当前奖池金额

  **返回值**：
  - {保持不变的奖池金额, 倍率变化信息}
  """
  def handle_multiplier_change(old_multiplier, new_multiplier, current_pool) do
    multiplier_info = %{
      old_multiplier: old_multiplier,
      new_multiplier: new_multiplier,
      speed_change:
        if new_multiplier > old_multiplier do
          "faster"
        else
          "slower"
        end
    }

    {current_pool, multiplier_info}
  end

  @doc """
  获取PRIZE奖池状态信息

  **用途**：
  - 前端显示PRIZE奖池金额
  - 调试和监控奖池状态
  - 统计分析
  """
  def get_prize_pool_status(current_pool, current_multiplier) do
    %{
      current_amount: current_pool,
      current_multiplier: current_multiplier,
      # 每个牛头的收集金额
      collection_rate: current_multiplier * 180,
      is_empty: current_pool == 0,
      # 前端显示金额（除以SCORE_RATE）
      display_amount: trunc(current_pool / 100)
    }
  end

  # 保持向后兼容
  def update_free_pool(bull_count, bet_multiplier, current_pool \\ 0) do
    update_prize_pool(bull_count, 180, bet_multiplier, current_pool)
  end

  @doc """
  获取动画类型 - 根据中奖倍数播放不同动画

  动画播放规则：
  - 2~4倍: BIG
  - 5~19倍: MEGA
  - 20~39倍: EPIC
  - 39倍以上: SUPER
  - 特殊动画TOTAL: 用于免费玩法中中奖时播放
  """
  def get_animation_type(win_multiplier, is_free_game \\ false) do
    cond do
      is_free_game and win_multiplier > 0 ->
        # 免费游戏中奖时播放TOTAL动画
        "TOTAL"

      win_multiplier >= 39 ->
        "SUPER"

      win_multiplier >= 20 ->
        "EPIC"

      win_multiplier >= 5 ->
        "MEGA"

      win_multiplier >= 2 ->
        "BIG"

      true ->
        # 无动画
        nil
    end
  end

  @doc """
  生成免费游戏结果 - 包含FREE奖池逻辑和免费游戏中继续触发免费游戏
  """
  def generate_free_rounds_result(free_times, bet_amount, config \\ %{}) do
    total_win = 0
    free_results = []
    current_prize_pool = Map.get(config, :prize_pool, 0)
    # 剩余免费次数
    remaining_free_times = free_times

    {final_total_win, final_free_results, final_prize_pool, final_remaining_times} =
      generate_free_rounds(
        remaining_free_times,
        {total_win, free_results, current_prize_pool},
        bet_amount,
        config,
        1
      )

    result = %{
      type: "free_game",
      total_rounds: free_times,
      # 实际游戏轮数（可能因为继续触发而增加）
      actual_rounds: length(final_free_results),
      total_win: final_total_win,
      final_prize_pool: final_prize_pool,
      round_results: Enum.reverse(final_free_results),
      bet_amount: bet_amount,
      # 是否触发了额外免费游戏
      additional_free_triggered: final_remaining_times > 0
    }

    result
  end

  @doc """
  递归生成免费游戏轮次 - 支持免费游戏中继续触发免费游戏
  """
  defp generate_free_rounds(
         0,
         {acc_total_win, acc_free_results, acc_prize_pool},
         _bet_amount,
         _config,
         round
       ) do
    # 免费游戏结束，返回最终结果
    {acc_total_win, Enum.reverse(acc_free_results), acc_prize_pool, 0}
  end

  defp generate_free_rounds(
         remaining_times,
         {acc_total_win, acc_free_results, acc_prize_pool},
         bet_amount,
         config,
         round
       ) do
    # 生成免费游戏的转轮结果 - 使用免费游戏专用函数
    spin_result = generate_free_game_spin_result(3, 5, 9, bet_amount, config)

    # 统计牛头数量并更新PRIZE奖池
    bull_count = count_symbol_in_matrix(spin_result.icon_matrix, @symbols.bull)
    # 基础底分
    difen = 180
    # 修正：从正确的下注金额反推倍率
    # 前端difen = 20 / 100 = 0.2
    frontend_difen = difen / 100
    # 反推倍率
    bet_multiplier = trunc(bet_amount / 100 / 9 / frontend_difen)

    # 先检查是否使用PRIZE奖池
    {prize_used, remaining_pool} = use_prize_pool(bull_count, acc_prize_pool)

    # 然后更新PRIZE奖池
    {new_prize_pool, pool_increase} =
      update_prize_pool(bull_count, difen, bet_multiplier, remaining_pool)

    # 🎯 检查免费游戏中是否继续触发免费游戏 - 根据用户要求，免费游戏中再次触发免费游戏应该叠加
    deer_count = count_symbol_in_matrix(spin_result.icon_matrix, @symbols.deer)

    additional_free_times =
      case deer_count do
        n when n >= 5 -> 15
        4 -> 10
        3 -> 5
        _ -> 0
      end

    # 计算包含PRIZE奖池的最终中奖金额
    final_round_win = spin_result.total_win + prize_used

    # 获取动画类型
    win_multiplier =
      if spin_result.total_win > 0 and bet_amount > 0 do
        trunc(spin_result.total_win / bet_amount)
      else
        0
      end

    # 免费游戏标记为true
    animation_type = get_animation_type(win_multiplier, true)

    # 使用包含PRIZE奖池的最终中奖金额
    new_total_win = acc_total_win + final_round_win

    round_result = %{
      round: round,
      spin_result: spin_result,
      bull_count: bull_count,
      deer_count: deer_count,
      additional_free_times: additional_free_times,
      pool_increase: pool_increase,
      animation_type: animation_type,
      # 包含PRIZE奖池的最终中奖金额
      round_win: final_round_win,
      # 基础中奖金额
      base_win: spin_result.total_win,
      # PRIZE奖池使用金额
      prize_used: prize_used
    }

    new_free_results = [round_result | acc_free_results]

    # 如果触发了额外免费游戏，增加剩余次数
    new_remaining_times = remaining_times - 1 + additional_free_times

    # 递归继续游戏（终止条件由模式匹配处理）
    generate_free_rounds(
      new_remaining_times,
      {new_total_win, new_free_results, new_prize_pool},
      bet_amount,
      config,
      round + 1
    )
  end

  @doc """
  调整特殊图标分布 - 参考旧项目C++逻辑，确保牛头和鹿头不会同时达到触发条件

  旧项目逻辑：
  1. 如果牛头数量>=3，则将部分鹿头替换为其他图标
  2. 如果鹿头数量>=3，则将部分牛头替换为其他图标
  3. 优先保留牛头（转盘优先级更高）
  """
  def adjust_special_symbols_distribution(icon_matrix) do
    # 统计当前牛头和鹿头数量
    bull_count = count_symbol_in_matrix(icon_matrix, @symbols.bull)
    deer_count = count_symbol_in_matrix(icon_matrix, @symbols.deer)

    # 🎯 参考旧项目C++逻辑：牛头和鹿头互斥处理
    cond do
      # 如果同时有3个以上牛头和鹿头，优先保留牛头（转盘优先级更高）
      bull_count >= 3 and deer_count >= 3 ->
        Logger.info("🎰 [SYMBOL_ADJUST] 检测到同时有#{bull_count}个牛头和#{deer_count}个鹿头，优先保留牛头")

        replace_symbols_in_matrix(
          icon_matrix,
          @symbols.deer,
          get_replacement_symbols(),
          deer_count - 2
        )

      # 如果只有鹿头>=3，保持不变（允许免费游戏）
      deer_count >= 3 and bull_count < 3 ->
        Logger.info("🎰 [SYMBOL_ADJUST] 检测到#{deer_count}个鹿头，#{bull_count}个牛头，保持免费游戏触发")
        icon_matrix

      # 如果只有牛头>=3，保持不变（允许转盘）
      bull_count >= 3 and deer_count < 3 ->
        Logger.info("🎰 [SYMBOL_ADJUST] 检测到#{bull_count}个牛头，#{deer_count}个鹿头，保持转盘触发")
        icon_matrix

      # 其他情况保持不变
      true ->
        icon_matrix
    end
  end

  @doc """
  免费游戏专用：调整特殊图标分布 - 确保牛头和鹿头数量都不超过2个

  免费游戏规则：
  1. 牛头数量不能超过2个（避免触发转盘）
  2. 鹿头数量不能超过2个（避免重新触发免费游戏）
  3. 超过限制的符号会被替换为其他普通符号
  """
  def adjust_special_symbols_for_free_game(icon_matrix) do
    # 统计当前牛头和鹿头数量
    bull_count = count_symbol_in_matrix(icon_matrix, @symbols.bull)
    deer_count = count_symbol_in_matrix(icon_matrix, @symbols.deer)

    Logger.info("🎰 [FREE_GAME_ADJUST] 免费游戏符号检查 - 牛头: #{bull_count}个, 鹿头: #{deer_count}个")

    # 先处理牛头数量限制
    matrix_after_bull_adjust =
      if bull_count > 2 do
        excess_bulls = bull_count - 2
        Logger.info("🎰 [FREE_GAME_ADJUST] 牛头超限，需要替换#{excess_bulls}个牛头")

        replace_symbols_in_matrix(
          icon_matrix,
          @symbols.bull,
          get_replacement_symbols(),
          excess_bulls
        )
      else
        icon_matrix
      end

    # 再处理鹿头数量限制 - 重新统计调整后的鹿头数量
    updated_deer_count = count_symbol_in_matrix(matrix_after_bull_adjust, @symbols.deer)

    final_matrix =
      if updated_deer_count > 2 do
        excess_deer = updated_deer_count - 2
        Logger.info("🎰 [FREE_GAME_ADJUST] 鹿头超限，需要替换#{excess_deer}个鹿头")

        replace_symbols_in_matrix(
          matrix_after_bull_adjust,
          @symbols.deer,
          get_replacement_symbols(),
          excess_deer
        )
      else
        matrix_after_bull_adjust
      end

    # 验证最终结果
    final_bull_count = count_symbol_in_matrix(final_matrix, @symbols.bull)
    final_deer_count = count_symbol_in_matrix(final_matrix, @symbols.deer)

    Logger.info("🎰 [FREE_GAME_ADJUST] 调整后 - 牛头: #{final_bull_count}个, 鹿头: #{final_deer_count}个")

    final_matrix
  end

  @doc """
  Wild游戏专用：调整特殊图标分布 - 确保牛头数量为0个，鹿头数量不超过2个

  Wild游戏规则：
  1. 牛头数量不能超过0个（完全避免触发转盘）
  2. 鹿头数量不能超过2个（避免触发免费游戏）
  3. 超过限制的符号会被替换为其他普通符号
  """
  def adjust_special_symbols_for_wild_game(icon_matrix) do
    # 统计当前牛头和鹿头数量
    bull_count = count_symbol_in_matrix(icon_matrix, @symbols.bull)
    deer_count = count_symbol_in_matrix(icon_matrix, @symbols.deer)

    Logger.info("🎰 [WILD_GAME_ADJUST] Wild游戏符号检查 - 牛头: #{bull_count}个, 鹿头: #{deer_count}个")

    # 先处理牛头数量限制 - Wild游戏中牛头必须为0个
    matrix_after_bull_adjust =
      if bull_count > 0 do
        Logger.info("🎰 [WILD_GAME_ADJUST] 牛头超限，需要替换#{bull_count}个牛头（Wild游戏中牛头必须为0个）")

        replace_symbols_in_matrix(
          icon_matrix,
          @symbols.bull,
          get_replacement_symbols(),
          bull_count
        )
      else
        icon_matrix
      end

    # 再处理鹿头数量限制 - 重新统计调整后的鹿头数量
    updated_deer_count = count_symbol_in_matrix(matrix_after_bull_adjust, @symbols.deer)

    final_matrix =
      if updated_deer_count > 2 do
        excess_deer = updated_deer_count - 2
        Logger.info("🎰 [WILD_GAME_ADJUST] 鹿头超限，需要替换#{excess_deer}个鹿头")

        replace_symbols_in_matrix(
          matrix_after_bull_adjust,
          @symbols.deer,
          get_replacement_symbols(),
          excess_deer
        )
      else
        matrix_after_bull_adjust
      end

    # 验证最终结果
    final_bull_count = count_symbol_in_matrix(final_matrix, @symbols.bull)
    final_deer_count = count_symbol_in_matrix(final_matrix, @symbols.deer)

    Logger.info("🎰 [WILD_GAME_ADJUST] 调整后 - 牛头: #{final_bull_count}个, 鹿头: #{final_deer_count}个")

    final_matrix
  end

  # 在矩阵中替换指定数量的符号
  defp replace_symbols_in_matrix(icon_matrix, target_symbol, replacement_symbols, replace_count) do
    if replace_count <= 0 do
      icon_matrix
    else
      # 找到所有目标符号的位置
      positions = find_symbol_positions(icon_matrix, target_symbol)

      # 随机选择要替换的位置
      positions_to_replace = Enum.take_random(positions, replace_count)

      # 执行替换
      replace_symbols_at_positions(icon_matrix, positions_to_replace, replacement_symbols)
    end
  end

  # 找到矩阵中指定符号的所有位置
  defp find_symbol_positions(icon_matrix, target_symbol) do
    for {row, row_idx} <- Enum.with_index(icon_matrix),
        {symbol, col_idx} <- Enum.with_index(row),
        symbol == target_symbol do
      {row_idx, col_idx}
    end
  end

  # 在指定位置替换符号
  defp replace_symbols_at_positions(icon_matrix, positions, replacement_symbols) do
    Enum.reduce(positions, icon_matrix, fn {row_idx, col_idx}, matrix ->
      # 随机选择一个替换符号
      replacement_symbol = Enum.random(replacement_symbols)

      # 替换指定位置的符号
      List.update_at(matrix, row_idx, fn row ->
        List.update_at(row, col_idx, fn _old_symbol -> replacement_symbol end)
      end)
    end)
  end

  @doc """
  免费游戏专用：检查特殊功能触发 - 应用免费游戏规则

  免费游戏中的特殊规则：
  1. 由于牛头和鹿头数量已被限制在2个以内，不会触发新的转盘或免费游戏
  2. 但仍需要检查是否有意外情况
  """
  def check_special_features_for_free_game(icon_matrix, bet_amount) do
    # 统计牛头图标数量
    bull_count = count_symbol_in_matrix(icon_matrix, @symbols.bull)

    # 统计鹿图标数量
    deer_count = count_symbol_in_matrix(icon_matrix, @symbols.deer)

    # 免费游戏中，由于符号数量已被限制，理论上不应该触发新的特殊功能
    # 但为了安全起见，仍进行检查
    turntable_trigger = bull_count >= 3

    free_spins =
      if deer_count >= 3 do
        # 如果意外出现3个以上鹿头，给予少量免费游戏
        case deer_count do
          n when n >= 5 -> 3  # 减少奖励
          4 -> 2
          3 -> 1
          _ -> 0
        end
      else
        0
      end

    # 免费游戏中通常不触发Jackpot，但保留检查逻辑
    jackpot_type = nil

    if turntable_trigger or free_spins > 0 do
      Logger.warn("🎰 [FREE_GAME_SPECIAL] 免费游戏中意外触发特殊功能 - 牛头: #{bull_count}, 鹿头: #{deer_count}")
    end

    {free_spins, turntable_trigger, jackpot_type}
  end

  # 获取用于替换的符号列表（排除牛头和鹿头）
  defp get_replacement_symbols do
    [
      @symbols.ten,
      @symbols.j,
      @symbols.q,
      @symbols.k,
      @symbols.a,
      @symbols.wolf,
      @symbols.tiger,
      @symbols.eagle
    ]
  end
end
