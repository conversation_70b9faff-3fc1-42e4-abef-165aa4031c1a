defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuAI do
  @moduledoc """
  SlotNiu老虎机AI模块

  由于SlotNiu是单人游戏，这个模块主要负责：
  - RNG算法控制
  - 游戏平衡性调整
  - 返还率控制
  - 特殊事件触发概率
  """

  require Logger
  alias <PERSON>pridina.Teen.GameSystem.Games.SlotNiu.SlotNiuLogic
  alias Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig

  @doc """
  智能生成游戏结果
  根据玩家的游戏历史和当前状态调整RNG
  """
  def generate_intelligent_result(player_stats, bet_amount, config) do
    Logger.info("🤖 [SLOTNIU_AI] 开始智能结果生成 - 玩家统计: #{inspect(player_stats)}, 下注: #{bet_amount}")

    # 计算玩家当前的RTP (Return to Player)
    current_rtp = calculate_current_rtp(player_stats)

    # 从配置模块获取目标RTP
    rtp_config = SlotNiuConfig.get_rtp_config()
    target_rtp = Map.get(config, :rtp, rtp_config.target_rtp)

    Logger.info("🤖 [SLOTNIU_AI] 当前RTP: #{current_rtp}%, 目标RTP: #{target_rtp}%")

    # 根据RTP差异调整中奖概率
    win_probability = adjust_win_probability(current_rtp, target_rtp, bet_amount)

    # 生成结果
    result =
      if should_generate_win?(win_probability) do
        generate_winning_result(bet_amount, config, player_stats)
      else
        generate_losing_result(bet_amount, config)
      end

    Logger.info("🤖 [SLOTNIU_AI] AI生成结果: 总赢取 #{result.total_win}, 中奖概率: #{win_probability}%")
    result
  end

  @doc """
  计算玩家当前RTP
  """
  def calculate_current_rtp(player_stats) do
    total_bet = Map.get(player_stats, :total_bet, 0)
    total_win = Map.get(player_stats, :total_win, 0)

    if total_bet > 0 do
      total_win / total_bet * 100
    else
      0.0
    end
  end

  @doc """
  根据RTP差异调整中奖概率
  """
  def adjust_win_probability(current_rtp, target_rtp, bet_amount) do
    # 基础中奖概率25%
    base_probability = 25.0

    rtp_diff = target_rtp - current_rtp

    cond do
      # 如果当前RTP低于目标，增加中奖概率
      rtp_diff > 10 -> min(base_probability + 20, 80)
      rtp_diff > 5 -> min(base_probability + 10, 60)
      rtp_diff > 0 -> min(base_probability + 5, 45)
      # 如果当前RTP高于目标，降低中奖概率
      rtp_diff < -10 -> max(base_probability - 15, 5)
      rtp_diff < -5 -> max(base_probability - 10, 10)
      rtp_diff < 0 -> max(base_probability - 5, 15)
      # RTP接近目标值
      true -> base_probability
    end
  end

  @doc """
  判断是否应该生成中奖结果
  """
  def should_generate_win?(win_probability) do
    random_value = :rand.uniform(100)
    random_value <= win_probability
  end

  @doc """
  生成中奖结果
  """
  def generate_winning_result(bet_amount, config, player_stats) do
    Logger.info("🤖 [SLOTNIU_AI] 生成中奖结果")

    # 决定中奖类型
    win_type = determine_win_type(bet_amount, player_stats)

    case win_type do
      :small_win -> generate_small_win(bet_amount, config)
      :medium_win -> generate_medium_win(bet_amount, config)
      :big_win -> generate_big_win(bet_amount, config)
      :jackpot -> generate_jackpot_win(bet_amount, config)
      :free_game -> generate_free_game_win(bet_amount, config)
    end
  end

  @doc """
  生成失败结果
  """
  def generate_losing_result(bet_amount, config) do
    Logger.info("🤖 [SLOTNIU_AI] 生成失败结果")

    # 生成没有中奖线的随机矩阵
    rows = Map.get(config, :rows, 3)
    cols = Map.get(config, :cols, 5)

    icon_matrix = generate_losing_matrix(rows, cols)

    %{
      icon_matrix: icon_matrix,
      winning_lines: [],
      total_win: 0,
      free_spins: 0,
      turntable_trigger: false,
      jackpot_type: nil,
      bet_amount: bet_amount
    }
  end

  @doc """
  决定中奖类型
  """
  def determine_win_type(bet_amount, player_stats) do
    # 根据下注金额和玩家统计决定中奖类型
    recent_losses = Map.get(player_stats, :recent_losses, 0)

    weights =
      cond do
        recent_losses > 10 ->
          %{small_win: 40, medium_win: 30, big_win: 20, jackpot: 5, free_game: 5}

        recent_losses > 5 ->
          %{small_win: 50, medium_win: 25, big_win: 15, jackpot: 5, free_game: 5}

        bet_amount >= 180 ->
          %{small_win: 30, medium_win: 30, big_win: 25, jackpot: 10, free_game: 5}

        bet_amount >= 90 ->
          %{small_win: 40, medium_win: 30, big_win: 20, jackpot: 5, free_game: 5}

        true ->
          %{small_win: 60, medium_win: 25, big_win: 10, jackpot: 2, free_game: 3}
      end

    select_win_type_by_weight(weights)
  end

  defp select_win_type_by_weight(weights) do
    total_weight = Enum.reduce(weights, 0, fn {_type, weight}, acc -> acc + weight end)
    random_value = :rand.uniform(total_weight)

    select_type_by_value(Map.to_list(weights), random_value, 0)
  end

  defp select_type_by_value([{type, weight} | rest], target, current) do
    new_current = current + weight

    if target <= new_current do
      type
    else
      select_type_by_value(rest, target, new_current)
    end
  end

  @doc """
  生成小奖结果 (1-5倍下注)
  """
  def generate_small_win(bet_amount, config) do
    # 1-5倍
    target_win = bet_amount * (:rand.uniform(4) + 1)
    generate_result_with_target_win(target_win, bet_amount, config)
  end

  @doc """
  生成中奖结果 (5-20倍下注)
  """
  def generate_medium_win(bet_amount, config) do
    # 5-20倍
    target_win = bet_amount * (:rand.uniform(15) + 5)
    generate_result_with_target_win(target_win, bet_amount, config)
  end

  @doc """
  生成大奖结果 (20-100倍下注)
  """
  def generate_big_win(bet_amount, config) do
    # 20-100倍
    target_win = bet_amount * (:rand.uniform(80) + 20)
    generate_result_with_target_win(target_win, bet_amount, config)
  end

  @doc """
  生成Jackpot结果
  """
  def generate_jackpot_win(bet_amount, config) do
    jackpot_amount = Map.get(config, :current_jackpot, 100_000)
    # 最高500倍或当前Jackpot
    target_win = min(jackpot_amount, bet_amount * 500)

    result = generate_result_with_target_win(target_win, bet_amount, config)
    %{result | jackpot_type: :major_jackpot}
  end

  @doc """
  生成免费游戏结果
  """
  def generate_free_game_win(bet_amount, config) do
    # 生成包含3个或以上牛头的矩阵
    icon_matrix = generate_free_game_matrix()

    %{
      icon_matrix: icon_matrix,
      winning_lines: [],
      total_win: 0,
      free_spins: 10,
      turntable_trigger: false,
      jackpot_type: nil,
      bet_amount: bet_amount
    }
  end

  @doc """
  根据目标赢取金额生成结果
  """
  def generate_result_with_target_win(target_win, bet_amount, config) do
    # 这里可以实现更复杂的算法来生成接近目标赢取金额的矩阵
    # 为了简化，我们使用基础的随机生成然后调整
    rows = Map.get(config, :rows, 3)
    cols = Map.get(config, :cols, 5)
    lines = Map.get(config, :lines, 9)

    # 多次尝试生成接近目标的结果
    best_result = nil
    best_diff = target_win

    for _attempt <- 1..10 do
      result = SlotNiuLogic.generate_spin_result(rows, cols, lines, bet_amount, config)
      diff = abs(result.total_win - target_win)

      if diff < best_diff do
        best_result = result
        best_diff = diff
      end
    end

    best_result || SlotNiuLogic.generate_spin_result(rows, cols, lines, bet_amount, config)
  end

  @doc """
  生成失败矩阵 (确保没有中奖线)
  """
  def generate_losing_matrix(rows, cols) do
    # 生成一个确保没有连续3个相同图标的矩阵
    for row <- 1..rows do
      for col <- 1..cols do
        # 使用不同的图标避免连线
        rem(row + col, 6) + 1
      end
    end
  end

  @doc """
  生成免费游戏触发矩阵 (包含3个或以上牛头)
  """
  def generate_free_game_matrix do
    # 生成3x5矩阵，确保有3个牛头图标(10)
    base_matrix = [
      [1, 2, 10, 4, 5],
      [6, 10, 8, 9, 1],
      [2, 3, 10, 5, 6]
    ]

    # 随机化其他位置
    for row <- base_matrix do
      for symbol <- row do
        if symbol == 10 do
          # 保持牛头
          10
        else
          # 随机其他图标
          :rand.uniform(9)
        end
      end
    end
  end

  @doc """
  更新玩家统计信息
  """
  def update_player_stats(player_stats, bet_amount, win_amount) do
    total_bet = Map.get(player_stats, :total_bet, 0) + bet_amount
    total_win = Map.get(player_stats, :total_win, 0) + win_amount
    games_played = Map.get(player_stats, :games_played, 0) + 1

    recent_losses =
      if win_amount > 0 do
        # 重置连败计数
        0
      else
        Map.get(player_stats, :recent_losses, 0) + 1
      end

    %{
      total_bet: total_bet,
      total_win: total_win,
      games_played: games_played,
      recent_losses: recent_losses,
      last_updated: DateTime.utc_now()
    }
  end
end
