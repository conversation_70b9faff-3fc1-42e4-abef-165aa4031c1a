defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.SlotCat.SlotCatGame do
  @moduledoc """
  SlotCat 老虎机游戏定义模块

  实现游戏工厂行为，定义SlotCat游戏的基本信息和配置
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  alias Cypridina.Teen.GameSystem.Games.SlotCat.{SlotCatRoom, SlotCatConstants, SlotCatConfig}

  @impl true
  def game_type, do: :slotcat

  @impl true
  def game_name, do: "SlotCat老虎机"

  @impl true
  def room_module, do: SlotCatRoom

  @impl true
  def supported_game_ids do
    [
      # SlotCat游戏ID
      42
    ]
  end

  @impl true
  def is_lobby_game?, do: false

  @doc """
  默认配置 - 使用统一的配置管理
  """
  @impl true
  def default_config do
    SlotCatConfig.get_default_config()
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    %{
      total_rooms: get_total_rooms(),
      active_players: get_active_players(),
      total_spins: get_total_spins(),
      jackpot_pool: get_jackpot_pool()
    }
  end

  @doc """
  获取当前活跃房间数
  """
  def get_total_rooms do
    # 这里可以实现获取活跃房间数的逻辑
    0
  end

  @doc """
  获取当前活跃玩家数
  """
  def get_active_players do
    # 这里可以实现获取活跃玩家数的逻辑
    0
  end

  @doc """
  获取总旋转次数
  """
  def get_total_spins do
    # 这里可以实现获取总旋转次数的逻辑
    0
  end

  @doc """
  获取当前 Jackpot 奖池
  """
  def get_jackpot_pool do
    # 这里可以实现获取 Jackpot 奖池的逻辑
    0
  end

  @doc """
  获取游戏版本信息
  """
  def version_info do
    %{
      version: "1.0.0",
      build_date: "2024-01-01",
      features: [
        "5轴3行老虎机",
        "9条支付线",
        "免费游戏功能",
        "Jackpot奖池",
        "多倍率下注",
        "WILD替换功能",
        "实时奖池更新"
      ]
    }
  end

  @doc """
  验证游戏配置
  """
  def validate_config(config) do
    required_keys = [:max_players, :min_players, :game_config]

    case Enum.find(required_keys, fn key -> not Map.has_key?(config, key) end) do
      nil ->
        validate_game_config(config.game_config)

      missing_key ->
        {:error, "缺少必需的配置项: #{missing_key}"}
    end
  end

  # 验证游戏特定配置
  defp validate_game_config(game_config) do
    cond do
      game_config.min_bet <= 0 ->
        {:error, "最小下注必须大于0"}

      game_config.max_bet <= game_config.min_bet ->
        {:error, "最大下注必须大于最小下注"}

      game_config.rtp < 80 or game_config.rtp > 99 ->
        {:error, "RTP必须在80-99之间"}

      true ->
        {:ok, "配置验证通过"}
    end
  end

  @doc """
  获取游戏规则说明
  """
  def get_game_rules do
    %{
      basic_rules: [
        "SlotCat 是一款 5 轴 3 行的经典老虎机游戏",
        "游戏共有 9 条支付线，从左到右连续 3 个或以上相同图标即可中奖",
        "WILD 图标可以替代任何普通图标组成中奖组合",
        "苹果图标是免费游戏触发器，3 个或以上可触发免费游戏",
        "数字 7 图标是 Jackpot 触发器，3 个或以上可赢取累积奖池"
      ],
      icon_values: [
        "香蕉: 3连=3倍, 4连=10倍, 5连=75倍",
        "西瓜: 3连=3倍, 4连=10倍, 5连=85倍",
        "草莓: 3连=15倍, 4连=40倍, 5连=250倍",
        "葡萄: 3连=25倍, 4连=50倍, 5连=400倍",
        "芒果: 3连=30倍, 4连=70倍, 5连=550倍",
        "榴莲: 3连=35倍, 4连=80倍, 5连=650倍",
        "山竹: 3连=45倍, 4连=100倍, 5连=800倍",
        "BAR: 3连=75倍, 4连=175倍, 5连=1250倍",
        "苹果: 3连=25倍, 4连=40倍, 5连=400倍 (免费游戏)",
        "数字7: 3连=100倍, 4连=200倍, 5连=1750倍 (Jackpot)"
      ],
      free_game_rules: [
        "3 个苹果图标 = 5 次免费游戏",
        "4 个苹果图标 = 10 次免费游戏",
        "5 个苹果图标 = 15 次免费游戏",
        "免费游戏期间不会出现苹果和数字7图标",
        "免费游戏中的所有赢取都会累加到总赢取中"
      ],
      jackpot_rules: [
        "3 个数字7 = 20% 奖池",
        "4 个数字7 = 50% 奖池",
        "5 个数字7 = 100% 奖池",
        "每次下注的 1% 会贡献给奖池",
        "Jackpot 中奖会立即发放并广播给所有玩家"
      ]
    }
  end
end
