defmodule <PERSON>pridina.Teen.GameSystem.Games.SlotCat.SlotCatDemo do
  @moduledoc """
  SlotCat 游戏演示模块

  用于演示和测试 SlotCat 游戏的基本功能
  """

  alias Cypridina.Teen.GameSystem.Games.SlotCat.{SlotCatGameLogic, SlotCatConstants, SlotCatGame}
  require Logger

  @doc """
  运行 SlotCat 游戏演示
  """
  def run_demo do
    Logger.info("🎰 ========== SlotCat 游戏演示开始 ==========")

    # 1. 显示游戏基本信息
    show_game_info()

    # 2. 演示游戏配置
    show_game_config()

    # 3. 演示游戏逻辑
    demo_game_logic()

    # 4. 演示不同控制模式
    demo_control_modes()

    # 5. 演示免费游戏
    demo_free_game()

    Logger.info("🎰 ========== SlotCat 游戏演示结束 ==========")
  end

  @doc """
  显示游戏基本信息
  """
  defp show_game_info do
    Logger.info("🎰 [游戏信息]")
    Logger.info("  游戏类型: #{SlotCatGame.game_type()}")
    Logger.info("  游戏名称: #{SlotCatGame.game_name()}")
    Logger.info("  房间模块: #{SlotCatGame.room_module()}")
    Logger.info("  支持的游戏ID: #{inspect(SlotCatGame.supported_game_ids())}")
    Logger.info("  是否为大厅游戏: #{SlotCatGame.is_lobby_game?()}")

    version_info = SlotCatGame.version_info()
    Logger.info("  版本信息: #{version_info.version}")
    Logger.info("  构建日期: #{version_info.build_date}")
    Logger.info("  功能特性: #{inspect(version_info.features)}")
  end

  @doc """
  显示游戏配置
  """
  defp show_game_config do
    Logger.info("🎰 [游戏配置]")
    config = SlotCatGame.default_config()

    Logger.info("  最大玩家数: #{config.max_players}")
    Logger.info("  最小玩家数: #{config.min_players}")
    Logger.info("  自动开始延迟: #{config.auto_start_delay}ms")
    Logger.info("  启用机器人: #{config.enable_robots}")

    game_config = config.game_config
    Logger.info("  转轮配置: #{game_config.reels}x#{game_config.rows}")
    Logger.info("  支付线数: #{game_config.paylines}")
    Logger.info("  下注范围: #{game_config.min_bet} - #{game_config.max_bet}")
    Logger.info("  RTP: #{game_config.rtp}%")
    Logger.info("  下注倍率: #{inspect(game_config.bet_multipliers)}")
  end

  @doc """
  演示游戏逻辑
  """
  defp demo_game_logic do
    Logger.info("🎰 [游戏逻辑演示]")

    # 生成一个正常游戏结果
    case SlotCatGameLogic.generate_game_result(12345, 100, 0, false) do
      {:ok, result} ->
        Logger.info("  ✅ 游戏结果生成成功")
        Logger.info("  图标矩阵:")

        Enum.with_index(result.icons, 1)
        |> Enum.each(fn {column, col_index} ->
          Logger.info("    第#{col_index}列: #{inspect(column)}")
        end)

        Logger.info("  中奖线数: #{length(result.win_lines)}")

        if length(result.win_lines) > 0 do
          Enum.each(result.win_lines, fn line ->
            Logger.info(
              "    线#{line.line_id}: 图标#{line.icon_id}, 数量#{line.count}, 倍率#{line.multiplier}"
            )
          end)
        end

        Logger.info("  总倍率: #{result.total_multiplier}")
        Logger.info("  免费次数: #{result.free_times}")
        Logger.info("  Jackpot数量: #{result.jackpot_count}")
        Logger.info("  中奖金额: #{result.win_amount}")

        # 格式化为前端格式
        client_format = SlotCatGameLogic.format_result_for_client(result)
        Logger.info("  前端格式: #{inspect(client_format)}")

      {:error, reason} ->
        Logger.error("  ❌ 游戏结果生成失败: #{reason}")
    end
  end

  @doc """
  演示不同控制模式
  """
  defp demo_control_modes do
    Logger.info("🎰 [控制模式演示]")

    controls = [
      {-1, "强杀模式"},
      {0, "正常模式"},
      {1, "放分模式"}
    ]

    Enum.each(controls, fn {control, name} ->
      Logger.info("  测试#{name}...")

      # 运行5次测试
      results =
        for _i <- 1..5 do
          case SlotCatGameLogic.generate_game_result(12345, 100, control, false) do
            {:ok, result} -> result.win_amount
            {:error, _} -> 0
          end
        end

      total_win = Enum.sum(results)
      total_bet = 5 * 100
      win_rate = total_win / total_bet

      Logger.info(
        "    总下注: #{total_bet}, 总赢取: #{total_win}, 返还率: #{Float.round(win_rate * 100, 2)}%"
      )
    end)
  end

  @doc """
  演示免费游戏
  """
  defp demo_free_game do
    Logger.info("🎰 [免费游戏演示]")

    case SlotCatGameLogic.generate_game_result(12345, 100, 0, true) do
      {:ok, result} ->
        Logger.info("  ✅ 免费游戏结果生成成功")

        # 检查是否有特殊图标
        all_icons = List.flatten(result.icons)
        has_free_icon = Enum.any?(all_icons, &SlotCatConstants.is_free_icon?/1)
        has_jackpot_icon = Enum.any?(all_icons, &SlotCatConstants.is_jackpot_icon?/1)

        Logger.info("  包含免费图标: #{has_free_icon}")
        Logger.info("  包含Jackpot图标: #{has_jackpot_icon}")
        Logger.info("  中奖金额: #{result.win_amount}")
        Logger.info("  总倍率: #{result.total_multiplier}")

        if has_free_icon or has_jackpot_icon do
          Logger.warning("  ⚠️  免费游戏期间不应出现特殊图标!")
        else
          Logger.info("  ✅ 免费游戏特殊图标限制正确")
        end

      {:error, reason} ->
        Logger.error("  ❌ 免费游戏结果生成失败: #{reason}")
    end
  end

  @doc """
  演示常量和配置
  """
  def demo_constants do
    Logger.info("🎰 [常量演示]")

    Logger.info("  游戏尺寸: #{SlotCatConstants.icon_cols()}x#{SlotCatConstants.icon_rows()}")
    Logger.info("  支付线数: #{SlotCatConstants.win_lines()}")
    Logger.info("  最大图标数: #{SlotCatConstants.max_icons()}")

    Logger.info("  图标类型:")
    Logger.info("    WILD: #{SlotCatConstants.icon_wild()}")
    Logger.info("    香蕉: #{SlotCatConstants.icon_1()}")
    Logger.info("    西瓜: #{SlotCatConstants.icon_2()}")
    Logger.info("    草莓: #{SlotCatConstants.icon_3()}")
    Logger.info("    葡萄: #{SlotCatConstants.icon_4()}")
    Logger.info("    芒果: #{SlotCatConstants.icon_5()}")
    Logger.info("    榴莲: #{SlotCatConstants.icon_6()}")
    Logger.info("    山竹: #{SlotCatConstants.icon_7()}")
    Logger.info("    BAR: #{SlotCatConstants.icon_8()}")
    Logger.info("    苹果(FREE): #{SlotCatConstants.icon_9()}")
    Logger.info("    数字7(JACKPOT): #{SlotCatConstants.icon_10()}")

    Logger.info("  协议定义:")
    Logger.info("    游戏开始: #{SlotCatConstants.cs_slotcat_gamestart_p()}")
    Logger.info("    游戏结果: #{SlotCatConstants.sc_slotcat_gamestart_p()}")
    Logger.info("    免费游戏: #{SlotCatConstants.sc_slotcat_freegame_p()}")
    Logger.info("    Jackpot列表: #{SlotCatConstants.cs_slotcat_jplist_p()}")
    Logger.info("    Jackpot信息: #{SlotCatConstants.sc_slotcat_jackpot_p()}")

    Logger.info("  下注倍率: #{inspect(SlotCatConstants.bet_multipliers())}")

    Logger.info("  免费游戏配置:")

    Enum.each(SlotCatConstants.free_times_config(), fn {count, times} ->
      Logger.info("    #{count}个FREE图标 = #{times}次免费游戏")
    end)

    Logger.info("  Jackpot配置:")

    Enum.each(SlotCatConstants.jackpot_cash_config(), fn {count, percentage} ->
      Logger.info("    #{count}个JACKPOT图标 = #{percentage}%奖池")
    end)
  end

  @doc """
  演示图标赔率
  """
  def demo_multipliers do
    Logger.info("🎰 [图标赔率演示]")

    multipliers = SlotCatConstants.icon_multipliers()

    Enum.each(multipliers, fn {icon_id, rates} ->
      icon_name =
        case icon_id do
          0 -> "WILD"
          1 -> "香蕉"
          2 -> "西瓜"
          3 -> "草莓"
          4 -> "葡萄"
          5 -> "芒果"
          6 -> "榴莲"
          7 -> "山竹"
          8 -> "BAR"
          9 -> "苹果(FREE)"
          10 -> "数字7(JACKPOT)"
          _ -> "未知"
        end

      Logger.info(
        "  #{icon_name}(#{icon_id}): 3连=#{rates.three}倍, 4连=#{rates.four}倍, 5连=#{rates.five}倍"
      )
    end)
  end

  @doc """
  演示支付线配置
  """
  def demo_paylines do
    Logger.info("🎰 [支付线演示]")

    paylines = SlotCatConstants.win_lines_config()

    Enum.with_index(paylines, 1)
    |> Enum.each(fn {line_positions, line_id} ->
      positions_str =
        Enum.map(line_positions, fn {row, col} -> "(#{row},#{col})" end)
        |> Enum.join(" -> ")

      Logger.info("  第#{line_id}条线: #{positions_str}")
    end)
  end

  @doc """
  运行完整演示
  """
  def run_full_demo do
    run_demo()
    demo_constants()
    demo_multipliers()
    demo_paylines()
  end
end
