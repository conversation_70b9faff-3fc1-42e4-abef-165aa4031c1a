# SlotCat 老虎机游戏

## 概述

SlotCat 是一款经典的 5 轴 3 行老虎机游戏，基于 C++ 版本重新用 Elixir 实现。游戏支持 9 条支付线、免费游戏功能和累积奖池系统。

## ✅ 实现状态

**已完成的功能：**
- ✅ 游戏常量定义 (`SlotCatConstants`)
- ✅ 核心游戏逻辑 (`SlotCatGameLogic`)
- ✅ 房间管理系统 (`SlotCatRoom`)
- ✅ 游戏工厂注册 (`SlotCatGame`)
- ✅ 消息协议处理 (mainId=5, subId=1000-1012)
- ✅ 图标生成算法 (5x3矩阵，权重随机)
- ✅ 中奖线计算 (9条支付线，WILD替换)
- ✅ 免费游戏功能 (3-5个苹果触发)
- ✅ Jackpot奖池系统 (3-5个数字7触发)
- ✅ 游戏控制机制 (强杀/正常/放分)
- ✅ 测试和演示模块
- ✅ 完整文档说明

## 游戏特性

- **5x3 转轮布局**: 5 个转轮，每个转轮 3 行图标
- **9 条支付线**: 多种中奖组合方式
- **11 种图标**: 包括普通图标、WILD、免费游戏和 Jackpot 图标
- **免费游戏**: 3-5 个苹果图标触发 5-15 次免费游戏
- **累积奖池**: 3-5 个数字7图标可赢取 20%-100% 奖池
- **多倍率下注**: 支持 1x 到 1000x 的下注倍率
- **游戏控制**: 支持强杀、正常、放分三种控制模式
- **单人游戏**: 每个房间支持1个玩家
- **继承RoomBase**: 使用统一的房间管理框架

## 文件结构

```
lib/teen/game_system/games/slotcat/
├── slotcat_constants.ex     # 游戏常量定义 (图标、协议、赔率等)
├── slotcat_game_logic.ex    # 核心游戏逻辑 (图标生成、中奖计算)
├── slotcat_room.ex          # 房间管理 (继承RoomBase)
├── slotcat_game.ex          # 主游戏模块 (GameFactory实现)
├── slotcat_test.ex          # 测试模块 (性能、公平性测试)
├── slotcat_demo.ex          # 演示模块 (功能演示)
└── README.md                # 说明文档
```

## 图标说明

| 图标ID | 名称 | 3连倍率 | 4连倍率 | 5连倍率 | 特殊功能 |
|--------|------|---------|---------|---------|----------|
| 0 | WILD | - | - | - | 万能替换 |
| 1 | 香蕉 | 3 | 10 | 75 | - |
| 2 | 西瓜 | 3 | 10 | 85 | - |
| 3 | 草莓 | 15 | 40 | 250 | - |
| 4 | 葡萄 | 25 | 50 | 400 | - |
| 5 | 芒果 | 30 | 70 | 550 | - |
| 6 | 榴莲 | 35 | 80 | 650 | - |
| 7 | 山竹 | 45 | 100 | 800 | - |
| 8 | BAR | 75 | 175 | 1250 | - |
| 9 | 苹果 | 25 | 40 | 400 | 免费游戏 |
| 10 | 数字7 | 100 | 200 | 1750 | Jackpot |

## 支付线配置

游戏共有 9 条支付线，从左到右连续 3 个或以上相同图标即可中奖：

1. 中间行: (1,1,1,1,1)
2. 上行: (0,0,0,0,0)
3. 下行: (2,2,2,2,2)
4. V形: (0,1,2,1,0)
5. 倒V形: (2,1,0,1,2)
6. 上升: (0,0,1,2,2)
7. 下降: (2,2,1,0,0)
8. M形: (1,2,1,0,1)
9. W形: (1,0,1,2,1)

## 协议定义

| 协议号 | 名称 | 方向 | 说明 |
|--------|------|------|------|
| 1000 | CS_SLOTCAT_GAMESTART_P | C→S | 开始游戏 |
| 1001 | SC_SLOTCAT_GAMESTART_P | S→C | 游戏结果 |
| 1002 | SC_SLOTCAT_FREEGAME_P | S→C | 免费游戏结果 |
| 1003 | CS_SLOTCAT_JPLIST_P | C→S | 请求Jackpot记录 |
| 1004 | SC_SLOTCAT_JPLIST_P | S→C | Jackpot记录 |
| 1005 | SC_SLOTCAT_JACKPOT_P | S→C | 奖池更新 |
| 1006 | SC_SLOTCAT_JPAWARD_P | S→C | Jackpot中奖通知 |
| 1007 | SC_SLOTCAT_PLAYERLIST_P | S→C | 玩家列表 |
| 1008 | SC_SLOTCAT_GAMERESULT_P | S→C | 游戏结果广播 |
| 1012 | CS_SLOTS_SWITCH_BET_P | C→S | 切换下注 |

## 使用方法

### 1. 配置游戏

SlotCat 游戏已经在 `config/games.exs` 中配置：

```elixir
config :cypridina, :games,
  builtin: [
    Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuGame,
    Cypridina.Teen.GameSystem.Games.Slot777.Slot777Game,
    Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatGame  # ✅ 已添加
  ]
```

### 2. 启动游戏

游戏会自动注册到游戏系统中，玩家可以通过游戏ID 42 进入 SlotCat 房间。

### 3. 测试游戏

```elixir
# 运行完整演示
Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatDemo.run_full_demo()

# 运行基础测试
Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatTest.run_basic_test()

# 运行性能测试
Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatTest.run_performance_test(1000)

# 验证游戏公平性
Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatTest.verify_fairness(10000)

# 压力测试
Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatTest.run_stress_test()
```

### 4. 消息格式

SlotCat 使用以下消息格式：

```elixir
# 客户端发送消息
message = %{
  "mainId" => 5,     # MainProto.SlotCat
  "subId" => 1000,   # CS_SLOTCAT_GAMESTART_P
  "data" => %{
    "bet" => 100     # 下注金额
  }
}

# 服务端响应消息
response = %{
  "mainId" => 5,     # MainProto.SlotCat
  "subId" => 1001,   # SC_SLOTCAT_GAMESTART_P
  "data" => %{
    "iconresult" => %{1 => 3, 2 => 5, ...},  # 图标结果
    "lineresult" => %{1 => %{"line" => 1, "num" => 3}},  # 中奖线
    "linecount" => 2,        # 中奖线数
    "totalmult" => 15,       # 总倍率
    "freetimes" => 0,        # 免费次数
    "jackpotnum" => 0,       # Jackpot数量
    "winmoney" => 1500,      # 中奖金额
    "coin" => 98500,         # 当前金币
    "freetimesleft" => 0     # 剩余免费次数
  }
}
```

## 游戏逻辑

### 图标生成

1. 根据控制模式调整图标权重
2. 免费游戏期间不出现苹果和数字7图标
3. 使用加权随机算法生成 5x3 图标矩阵

### 中奖计算

1. 遍历所有 9 条支付线
2. 计算每条线上连续相同图标数量
3. WILD 图标可以替代任何普通图标
4. 根据图标类型和数量计算倍率

### 特殊功能

- **免费游戏**: 3/4/5 个苹果图标触发 5/10/15 次免费游戏
- **Jackpot**: 3/4/5 个数字7图标赢取 20%/50%/100% 奖池
- **游戏控制**: 根据玩家输赢情况动态调整出奖概率

## 配置参数

```elixir
%{
  max_players: 1,           # 最大玩家数
  min_players: 1,           # 最小玩家数
  min_bet: 10,             # 最小下注
  max_bet: 10000,          # 最大下注
  rtp: 96.5,               # 返还率
  bet_multipliers: [1, 5, 10, 50, 100, 500, 1000]  # 下注倍率
}
```

## 注意事项

1. **单人游戏**: SlotCat 是单人游戏，每个房间只支持一个玩家
2. **随机性**: 游戏结果完全基于随机数生成，确保公平性
3. **控制机制**: 游戏包含控制机制，可根据需要调整出奖概率
4. **性能优化**: 游戏逻辑经过优化，支持高并发访问
5. **兼容性**: 协议格式与前端 Cocos Creator 版本完全兼容

## 开发说明

### 扩展功能

如需添加新功能，可以：

1. 在 `SlotCatConstants` 中添加新常量
2. 在 `SlotCatGameLogic` 中实现新逻辑
3. 在 `SlotCatRoom` 中处理新协议
4. 在 `SlotCatTest` 中添加测试用例

### 调试技巧

1. 使用测试模块验证游戏逻辑
2. 查看日志了解游戏运行状态
3. 使用性能测试检查游戏效率
4. 通过公平性验证确保游戏平衡

## 版本历史

- **v1.0.0**: 初始版本，实现基础老虎机功能
  - 5x3 转轮布局
  - 9 条支付线
  - 免费游戏和 Jackpot 功能
  - 完整的协议支持
  - 测试和文档
