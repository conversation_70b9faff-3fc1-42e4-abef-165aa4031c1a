defmodule <PERSON><PERSON>rid<PERSON>.Teen.GameSystem.Games.SlotCat.SlotCatGameLogic do
  @moduledoc """
  SlotCat 游戏核心逻辑模块

  负责处理：
  - 图标生成算法
  - 中奖线计算
  - 免费游戏逻辑
  - Jackpot 计算
  - 游戏控制逻辑
  """

  alias Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatConstants, as: Constants
  require Logger

  @doc """
  游戏结果结构体
  """
  defstruct [
    # 3x5 图标矩阵 (3行5列，与前端一致)
    :icons,
    # 中奖线信息 [{line_id, count, multiplier}]
    :win_lines,
    # 总倍率
    :total_multiplier,
    # 免费游戏次数
    :free_times,
    # Jackpot 图标数量
    :jackpot_count,
    # 中奖金额
    :win_amount,
    # 是否为免费游戏
    :is_free_game
  ]

  @type game_result :: %__MODULE__{
          icons: [[integer()]],
          win_lines: [map()],
          total_multiplier: integer(),
          free_times: integer(),
          jackpot_count: integer(),
          win_amount: integer(),
          is_free_game: boolean()
        }

  @doc """
  生成游戏结果 (优化版本，包含更好的错误处理和性能优化)

  ## 参数
  - player_id: 玩家ID
  - single_line_bet: 单线下注金额
  - control: 游戏控制类型 (-1: 强杀, 0: 正常, 1: 放分)
  - is_free_game: 是否为免费游戏
  - game_config: 游戏配置 (可选，包含 bet_rate_num 和 score_rate)

  ## 返回
  - {:ok, game_result} 或 {:error, reason}
  """
  @spec generate_game_result(integer(), integer(), integer(), boolean(), map() | nil) ::
          {:ok, game_result()} | {:error, String.t()}
  def generate_game_result(
        _player_id,
        single_line_bet,
        control \\ 0,
        is_free_game \\ false,
        game_config \\ nil
      ) do
    # 输入验证
    with :ok <- validate_inputs(single_line_bet, control),
         {:ok, icons} <- safe_generate_icons(control, is_free_game, game_config),
         {:ok, win_lines} <- safe_calculate_win_lines(icons),
         {:ok, game_stats} <-
           calculate_game_statistics(icons, win_lines, single_line_bet, is_free_game) do
      result = %__MODULE__{
        icons: icons,
        win_lines: win_lines,
        total_multiplier: game_stats.total_multiplier,
        free_times: game_stats.free_times,
        jackpot_count: game_stats.jackpot_count,
        win_amount: game_stats.win_amount,
        is_free_game: is_free_game
      }

      {:ok, result}
    else
      {:error, reason} ->
        {:error, reason}

      error ->
        Logger.error("生成游戏结果时发生未知错误: #{inspect(error)}")
        {:error, "生成游戏结果失败"}
    end
  end

  # 输入验证
  @spec validate_inputs(number(), integer()) :: :ok | {:error, String.t()}
  defp validate_inputs(single_line_bet, control) do
    cond do
      not is_number(single_line_bet) -> {:error, "单线下注金额必须是数字"}
      single_line_bet <= 0 -> {:error, "单线下注金额必须大于0"}
      not is_integer(control) -> {:error, "控制类型必须是整数"}
      control not in [-1, 0, 1] -> {:error, "无效的控制类型"}
      true -> :ok
    end
  end

  # 安全的图标生成
  @spec safe_generate_icons(integer(), boolean(), map() | nil) ::
          {:ok, [[integer()]]} | {:error, String.t()}
  defp safe_generate_icons(control, is_free_game, game_config \\ nil) do
    try do
      icons = generate_icons(control, is_free_game, game_config)

      # 验证生成的图标矩阵
      if valid_icon_matrix?(icons) do
        {:ok, icons}
      else
        {:error, "生成的图标矩阵无效"}
      end
    rescue
      error ->
        Logger.error("生成图标矩阵失败: #{inspect(error)}")
        {:error, "生成图标矩阵失败"}
    end
  end

  # 安全的中奖线计算
  @spec safe_calculate_win_lines([[integer()]]) :: {:ok, [map()]} | {:error, String.t()}
  defp safe_calculate_win_lines(icons) do
    try do
      win_lines = calculate_win_lines(icons)
      {:ok, win_lines}
    rescue
      error ->
        Logger.error("计算中奖线失败: #{inspect(error)}")
        {:error, "计算中奖线失败"}
    end
  end

  # 计算游戏统计数据 (优化性能，一次性计算所有统计)
  @spec calculate_game_statistics([[integer()]], [map()], number(), boolean()) ::
          {:ok, map()} | {:error, String.t()}
  defp calculate_game_statistics(icons, win_lines, single_line_bet, is_free_game) do
    try do
      # 一次性扁平化图标矩阵，避免重复计算
      flat_icons = List.flatten(icons)

      # 并行计算各种统计数据
      total_multiplier = calculate_total_multiplier(win_lines)
      free_times = if is_free_game, do: 0, else: count_specific_icon(flat_icons, Constants.icon_9()) |> Constants.get_free_times()

      # 计算 Jackpot 数量并规范化：3个显示3，4个显示4，5个或更多显示5
      raw_jackpot_count = count_specific_icon(flat_icons, Constants.icon_10())
      jackpot_count = case raw_jackpot_count do
        3 -> 3
        4 -> 4
        n when n >= 5 -> 5
        _ -> raw_jackpot_count  # 0, 1, 2 保持原值
      end

      win_amount = calculate_win_amount(single_line_bet, total_multiplier)

      stats = %{
        total_multiplier: total_multiplier,
        free_times: free_times,
        jackpot_count: jackpot_count,
        win_amount: win_amount
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("计算游戏统计失败: #{inspect(error)}")
        {:error, "计算游戏统计失败"}
    end
  end

  # 验证图标矩阵的有效性
  @spec valid_icon_matrix?([[integer()]]) :: boolean()
  defp valid_icon_matrix?(icons) do
    length(icons) == Constants.icon_rows() and
      Enum.all?(icons, fn row ->
        length(row) == Constants.icon_cols() and
          Enum.all?(row, &Constants.valid_icon?/1)
      end)
  end

  # 优化的特定图标计数 (避免重复扁平化)
  @spec count_specific_icon([integer()], integer()) :: integer()
  defp count_specific_icon(flat_icons, target_icon) do
    Enum.count(flat_icons, &(&1 == target_icon))
  end

  # 生成图标矩阵 (3x5 - 3行5列，与前端一致)
  @spec generate_icons(integer(), boolean(), map() | nil) :: [[integer()]]
  defp generate_icons(control, is_free_game, game_config \\ nil) do
    # 根据控制类型调整概率
    icon_weights = get_icon_weights(control, is_free_game, game_config)

    # 生成 3x5 的图标矩阵 (3行5列，与前端ROW_SHOW=3, COL_SHOW=5一致)
    for _row <- 1..Constants.icon_rows() do
      for _col <- 1..Constants.icon_cols() do
        generate_random_icon(icon_weights)
      end
    end
  end

  # 获取图标权重配置 (使用配置化权重)
  @spec get_icon_weights(integer(), boolean(), map() | nil) :: map()
  defp get_icon_weights(control, is_free_game, game_config \\ nil) do
    # 从动态配置或静态配置中获取基础权重
    base_weights =
      case game_config do
        %{win_rate: %{icon_weights: weights}} when is_map(weights) ->
          weights

        _ ->
          # 回退到静态配置
          alias Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatConfig
          SlotCatConfig.get_icon_weights()
      end

    # 免费游戏期间不出现 FREE 和 JACKPOT 符号
    special_weights =
      if is_free_game do
        %{
          # 免费游戏期间不出现FREE符号
          Constants.icon_9() => 0,
          # 免费游戏期间不出现JACKPOT符号
          Constants.icon_10() => 0
        }
      else
        %{
          # 使用配置中的权重
          Constants.icon_9() => base_weights[Constants.icon_9()],
          # 使用配置中的权重
          Constants.icon_10() => base_weights[Constants.icon_10()]
        }
      end

    # 根据控制类型调整权重 (基于配置权重进行比例调整)
    adjusted_weights =
      case control do
        # control_kill - 强杀模式
        -1 ->
          # 降低所有高倍率图标的概率 (保持相对比例，但整体降低)
          Map.merge(base_weights, %{
            Constants.icon_wild() => max(1, div(base_weights[Constants.icon_wild()], 3)),
            Constants.icon_6() => max(1, div(base_weights[Constants.icon_6()] * 2, 3)),
            Constants.icon_7() => max(1, div(base_weights[Constants.icon_7()], 2)),
            Constants.icon_8() => max(1, div(base_weights[Constants.icon_8()], 2))
          })

        # control_release - 放分模式
        1 ->
          # 提高高倍率图标的概率 (基于配置权重按比例提升)
          Map.merge(base_weights, %{
            Constants.icon_wild() => base_weights[Constants.icon_wild()] * 2,
            Constants.icon_6() =>
              base_weights[Constants.icon_6()] + div(base_weights[Constants.icon_6()], 2),
            Constants.icon_7() =>
              base_weights[Constants.icon_7()] + div(base_weights[Constants.icon_7()], 2),
            Constants.icon_8() => base_weights[Constants.icon_8()] * 2
          })

        # control_normal - 正常模式
        _ ->
          base_weights
      end

    # 合并特殊符号权重
    final_weights = Map.merge(adjusted_weights, special_weights)

    # 在免费游戏期间，适当提高普通图标的权重以补偿特殊符号的缺失
    if is_free_game do
      # 基于配置权重按比例提升，而不是硬编码数值
      # 10% 的权重补偿
      compensation_factor = 0.1

      Map.merge(final_weights, %{
        Constants.icon_1() =>
          final_weights[Constants.icon_1()] +
            round(final_weights[Constants.icon_1()] * compensation_factor),
        Constants.icon_2() =>
          final_weights[Constants.icon_2()] +
            round(final_weights[Constants.icon_2()] * compensation_factor),
        Constants.icon_3() =>
          final_weights[Constants.icon_3()] +
            round(final_weights[Constants.icon_3()] * compensation_factor)
      })
    else
      final_weights
    end
  end

  # 根据权重生成随机图标
  @spec generate_random_icon(map()) :: integer()
  defp generate_random_icon(weights) do
    total_weight = Enum.sum(Map.values(weights))
    random_value = :rand.uniform(total_weight)

    {icon, _} =
      Enum.reduce_while(weights, {0, 0}, fn {icon, weight}, {_, acc} ->
        new_acc = acc + weight

        if random_value <= new_acc do
          {:halt, {icon, new_acc}}
        else
          {:cont, {icon, new_acc}}
        end
      end)

    icon
  end

  # 计算所有中奖线
  @spec calculate_win_lines([[integer()]]) :: [map()]
  defp calculate_win_lines(icons) do
    Constants.win_lines_config()
    |> Enum.with_index(1)
    |> Enum.map(fn {line_indices, line_id} ->
      calculate_single_win_line(icons, line_indices, line_id)
    end)
    |> Enum.filter(fn line -> line.count >= 3 end)
  end

  # 计算单条中奖线
  @spec calculate_single_win_line([[integer()]], [integer()], integer()) :: map()
  defp calculate_single_win_line(icons, line_indices, line_id) do
    # 将1-15索引转换为坐标并获取图标序列
    line_icons =
      Enum.map(line_indices, fn index ->
        {row, col} = Constants.index_to_position(index)
        # 安全地获取图标值，防止 nil 错误
        case Enum.at(icons, row) do
          nil ->
            Logger.error("🎰 [SLOTCAT_LOGIC] 图标矩阵行 #{row} 为 nil，索引: #{index}")
            # 默认图标
            1

          row_data ->
            case Enum.at(row_data, col) do
              nil ->
                Logger.error("🎰 [SLOTCAT_LOGIC] 图标矩阵行 #{row} 列 #{col} 为 nil，索引: #{index}")
                # 默认图标
                1

              icon ->
                icon
            end
        end
      end)

    # 计算连续相同图标数量
    {first_icon, count} = calculate_consecutive_count(line_icons)

    # 计算倍率 (需要传递配置参数，暂时使用静态配置)
    multiplier =
      if count >= 3 do
        Constants.get_icon_multiplier(first_icon, count)
      else
        0
      end

    %{
      line_id: line_id,
      icon_id: first_icon,
      count: count,
      multiplier: multiplier,
      positions: Enum.take(line_indices, count)
    }
  end

  # 计算连续相同图标数量 (支持 WILD 替换，但WILD不能替换免费和Jackpot符号)
  @spec calculate_consecutive_count([integer()]) :: {integer(), integer()}
  # 处理空列表情况
  defp calculate_consecutive_count([]), do: {1, 0}

  defp calculate_consecutive_count([first_icon | rest]) do
    # 如果第一个是 WILD，找到第一个非 WILD 且可被替换的图标作为基准
    base_icon =
      if Constants.is_wild_icon?(first_icon) do
        Enum.find(rest, first_icon, fn icon ->
          not Constants.is_wild_icon?(icon) and can_wild_substitute?(icon)
        end)
      else
        first_icon
      end

    # 只有当基准图标可以被WILD替换时，才进行替换计算
    if can_wild_substitute?(base_icon) do
      count =
        Enum.reduce_while([first_icon | rest], 0, fn icon, acc ->
          if icon == base_icon or
               (Constants.is_wild_icon?(icon) and can_wild_substitute?(base_icon)) do
            {:cont, acc + 1}
          else
            {:halt, acc}
          end
        end)

      {base_icon, count}
    else
      # 如果基准图标不能被WILD替换（如免费或Jackpot符号），则只计算完全相同的图标
      count =
        Enum.reduce_while([first_icon | rest], 0, fn icon, acc ->
          if icon == base_icon do
            {:cont, acc + 1}
          else
            {:halt, acc}
          end
        end)

      {base_icon, count}
    end
  end

  # 检查图标是否可以被WILD替换
  @spec can_wild_substitute?(integer()) :: boolean()
  defp can_wild_substitute?(icon) do
    # WILD不能替换免费游戏符号和Jackpot符号
    not (Constants.is_free_icon?(icon) or Constants.is_jackpot_icon?(icon))
  end

  # 计算总倍率
  @spec calculate_total_multiplier([map()]) :: integer()
  defp calculate_total_multiplier(win_lines) do
    Enum.reduce(win_lines, 0, fn line, acc ->
      acc + line.multiplier
    end)
  end

  # 计算免费游戏次数
  @spec calculate_free_times([[integer()]]) :: integer()
  defp calculate_free_times(icons) do
    free_count = count_icon_in_matrix(icons, Constants.icon_9())
    Constants.get_free_times(free_count)
  end

  # 计算 Jackpot 图标数量
  @spec calculate_jackpot_count([[integer()]]) :: integer()
  defp calculate_jackpot_count(icons) do
    count_icon_in_matrix(icons, Constants.icon_10())
  end

  # 计算矩阵中指定图标的数量
  @spec count_icon_in_matrix([[integer()]], integer()) :: integer()
  defp count_icon_in_matrix(icons, target_icon) do
    icons
    |> List.flatten()
    |> Enum.count(fn icon -> icon == target_icon end)
  end

  # 计算中奖金额
  # 注意：这里的 bet_amount 现在是单线下注金额，不需要再除以线数
  @spec calculate_win_amount(integer(), integer()) :: integer()
  defp calculate_win_amount(single_line_bet, total_multiplier) do
    # 直接使用单线下注金额乘以总倍率
    single_line_bet * total_multiplier
  end

  @doc """
  验证游戏结果是否符合控制要求
  """
  @spec validate_result(game_result(), integer(), integer()) :: boolean()
  def validate_result(result, control, _bet_amount) do
    case control do
      # control_kill
      -1 ->
        # 强杀模式：不能有大奖
        result.win_amount == 0 and result.free_times == 0 and result.jackpot_count < 3

      # control_release
      1 ->
        # 放分模式：应该有一定收益
        result.win_amount > 0 or result.free_times > 0

      # control_normal
      _ ->
        # 正常模式：任何结果都可以
        true
    end
  end

  @doc """
  格式化游戏结果为前端格式 (优化版本，包含错误处理)
  """
  @spec format_result_for_client(game_result()) :: {:ok, map()} | {:error, String.t()}
  def format_result_for_client(result) do
    try do
      # 验证输入结果
      with :ok <- validate_game_result(result),
           {:ok, icon_result} <- format_icon_result(result.icons),
           {:ok, line_result} <- format_line_result(result.win_lines) do
        formatted_result = %{
          "iconresult" => icon_result,
          "lineresult" => line_result,
          "linecount" => length(result.win_lines),
          "totalmult" => result.total_multiplier,
          "freetimes" => result.free_times,
          "jackpotnum" => result.jackpot_count,
          "winmoney" => result.win_amount,
          # 玩家的输赢分
          "changemoney" => result.win_amount,
          # 中Jackpot获得的金额(在房间中处理)
          "jackpotcash" => 0,
          # 转盘彩金
          "luckyjackpot" => 0
        }

        {:ok, formatted_result}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("格式化游戏结果失败: #{inspect(error)}")
        {:error, "格式化游戏结果失败"}
    end
  end

  # 验证游戏结果的有效性
  @spec validate_game_result(game_result()) :: :ok | {:error, String.t()}
  defp validate_game_result(result) do
    cond do
      is_nil(result) -> {:error, "游戏结果为空"}
      is_nil(result.icons) -> {:error, "图标矩阵为空"}
      is_nil(result.win_lines) -> {:error, "中奖线为空"}
      not is_integer(result.total_multiplier) -> {:error, "总倍率无效"}
      not is_integer(result.free_times) -> {:error, "免费次数无效"}
      not is_integer(result.jackpot_count) -> {:error, "Jackpot数量无效"}
      not is_number(result.win_amount) -> {:error, "中奖金额无效"}
      true -> :ok
    end
  end

  # 格式化图标结果
  @spec format_icon_result([[integer()]]) :: {:ok, map()} | {:error, String.t()}
  defp format_icon_result(icons) do
    try do
      # 将 3x5 矩阵转换为前端期望的 1-15 索引格式
      icon_result =
        icons
        |> Enum.with_index()
        |> Enum.flat_map(fn {row, row_index} ->
          Enum.with_index(row)
          |> Enum.map(fn {icon, col_index} ->
            index = row_index * 5 + col_index + 1
            {index, icon}
          end)
        end)
        |> Enum.into(%{})

      {:ok, icon_result}
    rescue
      error ->
        Logger.error("格式化图标结果失败: #{inspect(error)}")
        {:error, "格式化图标结果失败"}
    end
  end

  # 格式化中奖线结果
  @spec format_line_result([map()]) :: {:ok, map()} | {:error, String.t()}
  defp format_line_result(win_lines) do
    try do
      line_result =
        win_lines
        |> Enum.with_index(1)
        |> Enum.into(%{}, fn {line, index} ->
          {index,
           %{
             "line" => line.line_id,
             "num" => line.count
           }}
        end)

      {:ok, line_result}
    rescue
      error ->
        Logger.error("格式化中奖线结果失败: #{inspect(error)}")
        {:error, "格式化中奖线结果失败"}
    end
  end

  @doc """
  性能监控包装器 - 监控游戏结果生成的性能
  """
  @spec generate_game_result_with_monitoring(
          integer(),
          integer(),
          integer(),
          boolean(),
          map() | nil
        ) ::
          {:ok, game_result(), map()} | {:error, String.t()}
  def generate_game_result_with_monitoring(
        player_id,
        single_line_bet,
        control \\ 0,
        is_free_game \\ false,
        game_config \\ nil
      ) do
    start_time = System.monotonic_time(:microsecond)

    result = generate_game_result(player_id, single_line_bet, control, is_free_game, game_config)

    end_time = System.monotonic_time(:microsecond)
    duration = end_time - start_time

    performance_stats = %{
      duration_microseconds: duration,
      duration_milliseconds: duration / 1000,
      player_id: player_id,
      single_line_bet: single_line_bet,
      control: control,
      is_free_game: is_free_game
    }

    # 记录性能警告
    # 超过10ms
    if duration > 10_000 do
      Logger.warning("🎰 [PERFORMANCE] SlotCat游戏结果生成耗时过长: #{duration / 1000}ms, 玩家: #{player_id}")
    end

    case result do
      {:ok, game_result} -> {:ok, game_result, performance_stats}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  错误恢复机制 - 当游戏结果生成失败时的备用方案
  """
  @spec generate_fallback_result(integer(), integer()) :: game_result()
  def generate_fallback_result(single_line_bet, _control) do
    Logger.warning("🎰 [FALLBACK] 使用备用游戏结果生成机制")

    # 生成一个安全的默认结果（无中奖）
    fallback_icons = [
      [1, 2, 3, 4, 5],
      [2, 3, 4, 5, 1],
      [3, 4, 5, 1, 2]
    ]

    %__MODULE__{
      icons: fallback_icons,
      win_lines: [],
      total_multiplier: 0,
      free_times: 0,
      jackpot_count: 0,
      win_amount: 0,
      is_free_game: false
    }
  end
end
