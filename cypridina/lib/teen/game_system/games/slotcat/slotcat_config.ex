defmodule Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatConfig do
  @moduledoc """
  SlotCat 游戏配置管理

  统一管理 SlotCat 游戏的所有配置，包括：
  - 分账配置（抽水、奖池贡献）
  - 游戏配置（下注限制、RTP等）
  - 奖池配置（三个奖池的权重和优先级）
  - 房间配置（玩家数量、机器人等）
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatConstants

  @doc """
  获取默认配置
  """
  def get_default_config do
    %{
      # ===== 分账配置 =====
      ## 设置抽水比例
      # 3% 平台抽水
      rake_percentage: 0.03,

      # ===== Jackpot 配置 =====
      jackpot: %{
        # 总奖池贡献率
        # 2% 总奖池贡献率
        contribution_rate: 0.02,

        # 三个奖池的配置
        pools: [
          %{
            identifier: :left,
            weight: 2,
            # 优先级最低
            priority: 3,
            # 增加到 ₹12,500
            base_amount: 1_250_000,
            # base_amount ÷ 20
            min_amount: 62500,
            # base_amount × 20
            max_amount: 25_000_000
          },
          %{
            identifier: :right,
            weight: 3,
            # 优先级中等
            priority: 2,
            # 增加到 ₹25,000
            base_amount: 2_500_000,
            # base_amount ÷ 20
            min_amount: 125_000,
            # base_amount × 20
            max_amount: 50_000_000
          },
          %{
            identifier: :center,
            weight: 5,
            # 优先级最高
            priority: 1,
            # 增加到 ₹50,000，支持 ₹35,000 的70%奖金
            base_amount: 5_000_000,
            # base_amount ÷ 20
            min_amount: 250_000,
            # base_amount × 20
            max_amount: 100_000_000
          }
        ]
      },

      # ===== 游戏配置 =====
      game: %{
        # 下注限制
        min_bet: 10,
        max_bet: 10000,

        # 下注倍率配置
        # 可配置的下注倍率
        bet_multipliers: [0.2, 1, 2, 10, 20, 100, 200],

        # 游戏基础配置
        # 底分
        difen: 100,
        # 下注固定倍率
        bet_rate_num: 9,
        # 金币比例
        score_rate: 1,
        # 返还率
        rtp: 96.5,

        # 转轮配置
        # 转轮数量 (列数)
        reels: 5,
        # 图标行数
        rows: 3,
        # 支付线数量
        paylines: 9,

        # 图标配置
        icons: %{
          # WILD符号
          wild: 0,
          # 香蕉
          banana: 1,
          # 西瓜
          watermelon: 2,
          # 草莓
          strawberry: 3,
          # 葡萄
          grape: 4,
          # 芒果
          mango: 5,
          # 榴莲
          durian: 6,
          # 山竹
          mangosteen: 7,
          # BAR符号
          bar: 8,
          # 苹果(免费游戏)
          apple_free: 9,
          # 数字7(Jackpot)
          seven_jackpot: 10
        }
      },

      # ===== 权重配比配置 =====
      win_rate: %{
        # 整体返还率 (RTP - Return to Player)
        rtp: 96.5,

        # 图标权重配置 (数字越大出现概率越高)
        icon_weights: %{
          # WILD符号 - 适中概率 (0.8%)
          0 => 8,
          # 香蕉 - 最低倍率，最高概率 (12%)
          1 => 120,
          # 西瓜 - 最低倍率，最高概率 (11.5%)
          2 => 115,
          # 草莓 - 中低倍率 (8.5%)
          3 => 85,
          # 葡萄 - 中倍率 (7.5%)
          4 => 75,
          # 芒果 - 中倍率 (6.5%)
          5 => 65,
          # 榴莲 - 中高倍率 (5.5%)
          6 => 55,
          # 山竹 - 高倍率 (4.5%)
          7 => 45,
          # BAR - 最高倍率，最低概率 (2.5%)
          8 => 25,
          # 苹果(免费游戏) - 低频 (3.5%)
          9 => 35,
          # 7(Jackpot) - 极低频 (1.5%)
          10 => 15
        },

        # 大奖触发概率控制
        big_win_control: %{
          # Jackpot触发概率 (万分之一)
          jackpot_probability: 0.02,
          # 免费游戏触发概率控制
          free_game_probability: 0.08,
          # 连续大奖间隔控制 (防止连续大奖)
          big_win_cooldown: 50
        }
      },

      # ===== 赔率表配置 =====
      payout: %{
        # 赔率表 (图标类型 => {连击数 => 赔率})
        payout_table: %{
          # WILD (替代其他图标，无独立赔率)
          0 => %{},
          # 香蕉
          1 => %{2 => 1, 3 => 3, 4 => 10, 5 => 75},
          # 西瓜
          2 => %{3 => 3, 4 => 10, 5 => 85},
          # 草莓
          3 => %{3 => 15, 4 => 40, 5 => 250},
          # 葡萄
          4 => %{3 => 25, 4 => 50, 5 => 400},
          # 芒果
          5 => %{3 => 30, 4 => 70, 5 => 550},
          # 榴莲
          6 => %{3 => 35, 4 => 80, 5 => 650},
          # 山竹
          7 => %{3 => 45, 4 => 100, 5 => 800},
          # BAR
          8 => %{3 => 75, 4 => 175, 5 => 1250},
          # 苹果(免费游戏)
          9 => %{3 => 25, 4 => 40, 5 => 400},
          # 7(Jackpot)
          10 => %{3 => 100, 4 => 200, 5 => 1750}
        }
      },

      # ===== 免费游戏配置 =====
      free_game: %{
        # 触发免费游戏需要的苹果数量和对应次数
        trigger_table: %{
          # 5个苹果 = 15次免费
          5 => 15,
          # 4个苹果 = 10次免费
          4 => 10,
          # 3个苹果 = 5次免费
          3 => 5
        },
        # 免费游戏倍率
        multiplier: 2.0,
        # 免费游戏中的特殊权重 (可选)
        special_weights: nil
      },

      # ===== 任务系统配置 =====
      task_system: %{
        default_task: %{
          icon_id: 1,
          # 需要完成的次数
          total_count: 10,
          # 奖励金额
          reward_amount: 1000,
          # 最小下注要求
          min_bet_required: 180
        }
      },

      # ===== 房间配置 =====
      # 最大玩家数
      max_players: 1,
      # 最小玩家数
      min_players: 1,
      # 自动开始延迟
      auto_start_delay: 1000,
      # 是否启用机器人
      enable_robots: false,
      # 机器人数量
      robot_count: 0
    }
  end

  @doc """
  获取当前配置（目前返回默认配置）
  """
  def get_current_config() do
    Logger.info("🎰 [SLOTCAT_CONFIG] 返回默认配置")
    get_default_config()
  end

  @doc """
  获取合并后的配置
  接收 RoomManager 传入的合并后配置，进行最终处理
  """
  def get_merged_config(room_config) when is_map(room_config) do
    Logger.info("🎰 [SLOTCAT_CONFIG] 使用合并后的房间配置")

    # 获取默认配置作为基础
    default_config = get_default_config()

    # 与房间配置进行最终合并，确保配置完整性
    final_config = deep_merge_configs(default_config, room_config)

    Logger.info("🎰 [SLOTCAT_CONFIG] 配置合并完成，配置项数: #{map_size(final_config)}")
    final_config
  end

  @doc """
  获取游戏配置
  """
  def get_game_config do
    config = get_current_config()
    config.game
  end

  @doc """
  获取奖池配置
  """
  def get_jackpot_config do
    config = get_current_config()
    config.jackpot
  end

  @doc """
  获取分账配置
  """
  def get_allocation_config do
    config = get_current_config()

    %{
      rake: config.rake,
      contribution_rate: get_in(config, [:jackpot, :contribution_rate])
    }
  end

  @doc """
  获取房间配置
  """
  def get_room_config do
    config = get_current_config()
    config.room
  end

  @doc """
  获取权重配比配置
  """
  def get_win_rate_config do
    config = get_current_config()
    config.win_rate
  end

  @doc """
  获取图标权重配置
  """
  def get_icon_weights do
    config = get_current_config()
    config.win_rate.icon_weights
  end

  @doc """
  获取赔率表配置
  """
  def get_payout_config do
    config = get_current_config()
    config.payout
  end

  @doc """
  获取免费游戏配置
  """
  def get_free_game_config do
    config = get_current_config()
    config.free_game
  end

  @doc """
  获取大奖控制配置
  """
  def get_big_win_control do
    config = get_current_config()
    config.win_rate.big_win_control
  end

  # 深度合并配置
  defp deep_merge_configs(default, override) when is_map(default) and is_map(override) do
    Map.merge(default, override, fn
      _key, default_val, override_val when is_map(default_val) and is_map(override_val) ->
        deep_merge_configs(default_val, override_val)

      _key, _default_val, override_val ->
        override_val
    end)
  end

  defp deep_merge_configs(default, _override), do: default
end
