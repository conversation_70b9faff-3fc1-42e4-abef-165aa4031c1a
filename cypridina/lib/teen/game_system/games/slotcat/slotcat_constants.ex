defmodule <PERSON>pridina.Teen.GameSystem.Games.SlotCat.SlotCatConstants do
  @moduledoc """
  SlotCat 游戏常量定义

  基于 C++ 版本的 SlotCat 游戏常量，包括：
  - 游戏基础配置
  - 图标类型定义
  - 支付线配置
  - 协议定义
  """

  # 游戏基础配置
  # 图标行数 (ICON_ROW)
  @icon_rows 3
  # 图标列数 (ICON_COL)
  @icon_cols 5
  # 支付线数 (WIN_LINES)
  @win_lines 9
  # 最大图标数量
  @max_icons 11
  # 下注固定倍率
  @bet_rate_num 9

  # 图标类型枚举 (对应 EM_SLOTCAT_ICONTYPE)
  # WILD 图标
  @icon_wild 0
  # 香蕉
  @icon_1 1
  # 西瓜
  @icon_2 2
  # 草莓
  @icon_3 3
  # 葡萄
  @icon_4 4
  # 芒果
  @icon_5 5
  # 榴莲
  @icon_6 6
  # 山竹
  @icon_7 7
  # BAR
  @icon_8 8
  # 苹果 (FREE)
  @icon_9 9
  # 7 (JACKPOT)
  @icon_10 10

  # 协议定义
  # 游戏开始
  @cs_slotcat_gamestart_p 1000
  # 游戏开始结果
  @sc_slotcat_gamestart_p 1001
  # 免费游戏结果
  @sc_slotcat_freegame_p 1002
  # Jackpot中奖列表
  @cs_slotcat_jplist_p 1003
  # Jackpot中奖列表
  @sc_slotcat_jplist_p 1004
  # 同步Jackpot分数
  @sc_slotcat_jackpot_p 1005
  # Jackpot中奖通知
  @sc_slotcat_jpaward_p 1006
  # 广播桌面玩家列表
  @sc_slotcat_playerlist_p 1007
  # 广播桌面玩家游戏结果
  @sc_slotcat_gameresult_p 1008
  # 通知任务信息
  @sc_slotcat_taskinfo_p 1009
  # 领取任务奖励
  @cs_slotcat_gettask_p 1010
  # 领取任务奖励
  @sc_slotcat_gettask_p 1011
  # 切换下注
  @cs_slots_switch_bet_p 1012

  # 支付线配置 (根据中奖规则图片，按3行5列的1-15索引)
  #
  # 🎰 中奖规则说明 (基于提供的图片)：
  # 1. "On the winning line, the same symbol in the first reel from left to right can be won"
  #    - 在中奖线上，从左到右第一个转轮开始的相同符号可以中奖
  #    - 必须从第一个位置开始连续匹配，不能跳过
  #
  # 2. "WILD symbol can substitute for any symbols"
  #    - WILD符号可以替代任何符号
  #    - 但WILD不能替代免费游戏符号和Jackpot符号
  #
  # 3. "Bet on 1 line and only get 1 line reward"
  #    - 每条线独立计算奖励
  #
  # 4. 中奖示例：
  #    WIN:  [金币袋, 金币袋, 金币袋, WILD, 不同符号] → 中奖 (前3个相同 + WILD)
  #    LOSE: [不同符号, 金币袋, 金币袋, WILD, 金币袋] → 不中奖 (第1个位置不匹配)
  #
  # 索引布局：
  #  1  2  3  4  5   (第1行)
  #  6  7  8  9 10   (第2行)
  # 11 12 13 14 15   (第3行)
  @win_lines_config [
    # 第1条线: 中间行 (图片第1个) - 水平中线
    [6, 7, 8, 9, 10],
    # 第2条线: 上行 (图片第2个) - 水平上线
    [1, 2, 3, 4, 5],
    # 第3条线: 下行 (图片第3个) - 水平下线
    [11, 12, 13, 14, 15],
    # 第4条线: V形 (图片第4个) - 从上到下再到上
    [1, 7, 13, 9, 5],
    # 第5条线: 倒V形 (图片第5个) - 从下到上再到下
    [11, 7, 3, 9, 15],
    # 第6条线: 上升路径 (图片第6个) - 从左上经过中间到右下
    [1, 2, 8, 14, 15],
    # 第7条线: 下降路径 (图片第7个) - 从左下经过中间到右上
    [11, 12, 8, 4, 5],
    # 第8条线: M形 (图片第8个) - 中-下-中-上-中
    [6, 12, 8, 4, 10],
    # 第9条线: W形 (图片第9个) - 中-上-中-下-中
    [6, 2, 8, 14, 10]
  ]

  # 支付线坐标映射 (1-15索引对应的行列坐标)
  @line_pos_mapping %{
    1 => {0, 0},
    2 => {0, 1},
    3 => {0, 2},
    4 => {0, 3},
    5 => {0, 4},
    6 => {1, 0},
    7 => {1, 1},
    8 => {1, 2},
    9 => {1, 3},
    10 => {1, 4},
    11 => {2, 0},
    12 => {2, 1},
    13 => {2, 2},
    14 => {2, 3},
    15 => {2, 4}
  }

  # 图标赔率配置 (根据游戏设计图片中的赔率表)
  # 注意：WILD符号不直接产生赔率，而是替换其他符号
  @icon_multipliers %{
    # WILD - 可替代任何符号（除免费和累积奖池）
    @icon_wild => %{three: 0, four: 0, five: 0},
    # 香蕉 (金币袋)
    @icon_1 => %{three: 3, four: 10, five: 75},
    # 西瓜 (火球)
    @icon_2 => %{three: 3, four: 10, five: 85},
    # 草莓 (蓝色宝石)
    @icon_3 => %{three: 15, four: 40, five: 250},
    # 葡萄 (钱袋)
    @icon_4 => %{three: 25, four: 50, five: 400},
    # 芒果 (粉色猫咪)
    @icon_5 => %{three: 30, four: 70, five: 550},
    # 榴莲 (蓝色宝石)
    @icon_6 => %{three: 35, four: 80, five: 650},
    # 山竹 (绿色宝石)
    @icon_7 => %{three: 45, four: 100, five: 800},
    # BAR (紫色猫咪)
    @icon_8 => %{three: 75, four: 175, five: 1250},
    # 苹果(FREE) - 免费游戏符号
    @icon_9 => %{three: 25, four: 40, five: 400},
    # 数字7(JACKPOT) - 累积奖池符号
    @icon_10 => %{three: 100, four: 200, five: 1750}
  }

  # 免费游戏次数配置
  @free_times_config %{
    # 3个FREE图标 = 5次免费游戏
    3 => 5,
    # 4个FREE图标 = 10次免费游戏
    4 => 10,
    # 5个FREE图标 = 15次免费游戏
    5 => 15
  }

  # Jackpot 奖励比例配置 (根据总下注金额分档，避免"有动画无奖金"的用户体验问题)
  # 格式: %{总下注金额 => %{minor: MINOR奖池比例, major: MAJOR奖池比例, grand: GRAND奖池比例}}
  # 根据三种Jackpot类型：
  # - MINOR: 3个JACKPOT符号 -> 左边奖池
  # - MAJOR: 4个JACKPOT符号 -> 右边奖池
  # - GRAND: 5个及以上JACKPOT符号 -> 中间奖池
  # 注意：总投注必须达到₹90 (9000分) 才能触发Jackpot
  @jackpot_cash_config %{
    # 最低档位 (总下注₹90) - 刚好满足Jackpot要求，给予基础奖金避免用户困惑
    # 基础比例：3%, 5%, 10%
    9000 => %{minor: 3, major: 5, grand: 10},
    # 中低档位 (总下注₹180) - 稍高奖金，鼓励提升下注
    # 进阶比例：5%, 8%, 15%
    18000 => %{minor: 5, major: 8, grand: 15},
    # 中高档位 (总下注₹900) - 高额下注，丰厚奖金
    # 高级比例：15%, 25%, 50%
    90000 => %{minor: 15, major: 25, grand: 50},
    # 最高档位 (总下注₹1800) - 顶级下注，最高奖金
    # 顶级比例：20%, 35%, 70%
    180_000 => %{minor: 20, major: 35, grand: 70}
  }

  # 下注倍率配置 (对应前端 BET_NUM_CONFIG_LIST，与 odds 配置保持一致)
  @bet_multipliers [0.2, 1, 2, 10, 20, 100, 200]

  # 桌面最大玩家数 (对应前端 DESK_PLAYERS_MAX_NUM)
  @desk_players_max 1

  # Jackpot 广播间隔时间 (毫秒)
  @jackpot_broadcast_interval 30_000

  # 游戏控制类型
  # 强杀
  @control_kill -1
  # 正常
  @control_normal 0
  # 放分
  @control_release 1

  # 公开访问函数
  def icon_rows, do: @icon_rows
  def icon_cols, do: @icon_cols
  def win_lines, do: @win_lines
  def max_icons, do: @max_icons
  def bet_rate_num, do: @bet_rate_num

  def icon_wild, do: @icon_wild
  def icon_1, do: @icon_1
  def icon_2, do: @icon_2
  def icon_3, do: @icon_3
  def icon_4, do: @icon_4
  def icon_5, do: @icon_5
  def icon_6, do: @icon_6
  def icon_7, do: @icon_7
  def icon_8, do: @icon_8
  def icon_9, do: @icon_9
  def icon_10, do: @icon_10

  def cs_slotcat_gamestart_p, do: @cs_slotcat_gamestart_p
  def sc_slotcat_gamestart_p, do: @sc_slotcat_gamestart_p
  def sc_slotcat_freegame_p, do: @sc_slotcat_freegame_p
  def cs_slotcat_jplist_p, do: @cs_slotcat_jplist_p
  def sc_slotcat_jplist_p, do: @sc_slotcat_jplist_p
  def sc_slotcat_jackpot_p, do: @sc_slotcat_jackpot_p
  def sc_slotcat_jpaward_p, do: @sc_slotcat_jpaward_p
  def sc_slotcat_playerlist_p, do: @sc_slotcat_playerlist_p
  def sc_slotcat_gameresult_p, do: @sc_slotcat_gameresult_p
  def sc_slotcat_taskinfo_p, do: @sc_slotcat_taskinfo_p
  def cs_slotcat_gettask_p, do: @cs_slotcat_gettask_p
  def sc_slotcat_gettask_p, do: @sc_slotcat_gettask_p
  def cs_slots_switch_bet_p, do: @cs_slots_switch_bet_p

  def win_lines_config, do: @win_lines_config
  def line_pos_mapping, do: @line_pos_mapping
  def icon_multipliers, do: @icon_multipliers
  def free_times_config, do: @free_times_config
  def jackpot_cash_config, do: @jackpot_cash_config
  def bet_multipliers, do: @bet_multipliers
  def desk_players_max, do: @desk_players_max
  def jackpot_broadcast_interval, do: @jackpot_broadcast_interval

  def control_kill, do: @control_kill
  def control_normal, do: @control_normal
  def control_release, do: @control_release

  @doc """
  检查图标是否为特殊图标
  """
  def is_free_icon?(icon_id), do: icon_id == @icon_9
  def is_jackpot_icon?(icon_id), do: icon_id == @icon_10
  def is_wild_icon?(icon_id), do: icon_id == @icon_wild

  @doc """
  获取图标赔率
  """
  def get_icon_multiplier(icon_id, count) do
    case Map.get(@icon_multipliers, icon_id) do
      nil ->
        0

      multipliers ->
        case count do
          3 -> multipliers.three
          4 -> multipliers.four
          5 -> multipliers.five
          _ -> 0
        end
    end
  end

  @doc """
  获取免费游戏次数
  """
  def get_free_times(free_count) do
    Map.get(@free_times_config, free_count, 0)
  end

  @doc """
  获取 Jackpot 奖励比例 (根据单线下注金额和 Jackpot 数量，支持三个独立奖池)

  ## 参数
  - single_line_bet: 单线下注金额
  - jackpot_count: Jackpot 数量 (3=左边奖池, 4=右边奖池, 5=中间奖池)

  ## 返回值
  - 奖励比例 (百分比，如 8 表示 8%)

  ## Jackpot奖池设计 (根据图片规则)
  - MINOR (3个JACKPOT): 最容易触发的奖池
  - MAJOR (4个JACKPOT): 中等难度的奖池
  - GRAND (5个JACKPOT): 最难触发但奖金最高的奖池
  - 触发条件：总投注达到₹90 (9000分) 且相同符号是jackpot
  """
  def get_jackpot_cash_percentage(single_line_bet, jackpot_count, total_bet) do
    # 检查总投注是否达到₹90 (9000分)
    if total_bet < 9000 do
      # 不满足最低投注要求，无Jackpot奖励
      0
    else
      # 找到最接近的总下注档位
      bet_tier = find_closest_total_bet_tier(total_bet)

      case Map.get(@jackpot_cash_config, bet_tier) do
        nil ->
          0

        tier_config ->
          case jackpot_count do
            # MINOR奖池 (3个JACKPOT)
            3 -> tier_config.minor
            # MAJOR奖池 (4个JACKPOT)
            4 -> tier_config.major
            # GRAND奖池 (5个及以上JACKPOT)
            n when n >= 5 -> tier_config.grand
            _ -> 0
          end
      end
    end
  end

  @doc """
  获取 Jackpot 类型名称
  """
  def get_jackpot_type_name(jackpot_count) do
    case jackpot_count do
      3 -> "MINOR"
      4 -> "MAJOR"
      # 5个及以上都是GRAND
      n when n >= 5 -> "GRAND"
      _ -> "NONE"
    end
  end

  @doc """
  找到最接近的总下注档位 (新的基于总下注的匹配逻辑)
  """
  def find_closest_total_bet_tier(total_bet) do
    bet_tiers = Map.keys(@jackpot_cash_config) |> Enum.sort()

    # 找到小于等于当前总下注的最大档位
    Enum.reduce(bet_tiers, 9000, fn tier, acc ->
      if total_bet >= tier, do: tier, else: acc
    end)
  end

  @doc """
  找到最接近的下注档位 (保留原函数以兼容其他地方的调用)
  """
  def find_closest_bet_tier(single_line_bet) do
    # 为了向后兼容，将单线下注转换为总下注进行匹配
    # 假设9线
    total_bet = single_line_bet * 9
    find_closest_total_bet_tier(total_bet)
  end

  @doc """
  检查是否为有效的图标ID
  """
  def valid_icon?(icon_id) do
    icon_id >= @icon_wild and icon_id <= @icon_10
  end

  @doc """
  获取所有普通图标 (排除特殊图标)
  """
  def normal_icons do
    [@icon_1, @icon_2, @icon_3, @icon_4, @icon_5, @icon_6, @icon_7, @icon_8]
  end

  @doc """
  获取特殊图标
  """
  def special_icons do
    [@icon_wild, @icon_9, @icon_10]
  end

  @doc """
  获取奖池类型名称 (用于日志和调试)
  """
  def get_jackpot_pool_name(jackpot_count) do
    case jackpot_count do
      3 -> "左边奖池"
      4 -> "右边奖池"
      # 5个及以上都是中间奖池
      n when n >= 5 -> "中间奖池"
      _ -> "未知奖池"
    end
  end

  @doc """
  获取奖池类型原子 (用于内部处理)
  """
  def get_jackpot_pool_type(jackpot_count) do
    case jackpot_count do
      3 -> :left
      4 -> :right
      # 5个及以上都是中间奖池
      n when n >= 5 -> :center
      _ -> :unknown
    end
  end

  @doc """
  验证 Jackpot 数量是否有效
  3个=左边奖池, 4个=右边奖池, 5个及以上=中间奖池
  """
  def valid_jackpot_count?(jackpot_count) do
    is_integer(jackpot_count) and jackpot_count >= 3
  end

  @doc """
  获取所有有效的 Jackpot 数量
  """
  def valid_jackpot_counts, do: [3, 4, 5]

  @doc """
  计算预期奖金 (用于显示和预览)
  """
  def calculate_expected_jackpot_amount(single_line_bet, jackpot_count, pool_amount, total_bet) do
    percentage = get_jackpot_cash_percentage(single_line_bet, jackpot_count, total_bet)

    if percentage > 0 do
      div(pool_amount * percentage, 100)
    else
      0
    end
  end

  @doc """
  获取奖池配置信息 (根据图片规则更新)
  """
  def get_jackpot_pool_info do
    %{
      pools: %{
        minor: %{name: "MINOR", jackpot_count: 3, description: "3个JACKPOT触发"},
        major: %{name: "MAJOR", jackpot_count: 4, description: "4个JACKPOT触发"},
        grand: %{name: "GRAND", jackpot_count: 5, description: "5个JACKPOT触发"}
      },
      # ₹90 = 9000分
      minimum_bet_requirement: 9000,
      trigger_condition: "总投注达到₹90且相同符号是jackpot",
      bet_tiers: Map.keys(@jackpot_cash_config),
      config: @jackpot_cash_config
    }
  end

  @doc """
  将1-15索引转换为行列坐标
  """
  def index_to_position(index) do
    Map.get(@line_pos_mapping, index, {0, 0})
  end

  @doc """
  将行列坐标转换为1-15索引
  """
  def position_to_index(row, col) do
    row * 5 + col + 1
  end

  @doc """
  获取支付线的坐标列表
  """
  def get_line_positions(line_index) when line_index >= 1 and line_index <= 9 do
    line_config = Enum.at(@win_lines_config, line_index - 1)
    Enum.map(line_config, &index_to_position/1)
  end

  def get_line_positions(_), do: []
end
