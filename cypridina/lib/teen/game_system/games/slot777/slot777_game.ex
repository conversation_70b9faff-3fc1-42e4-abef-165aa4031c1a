defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Game do
  @moduledoc """
  Slot777老虎机游戏定义模块

  实现游戏工厂行为，定义Slot777游戏的基本信息和配置
  同时负责启动和管理Slot777全局管理器
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  use GenServer
  require <PERSON><PERSON>

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Config

  @impl true
  def game_type, do: :slot777
  @impl true
  def game_class_type, do: 1

  @impl true
  def game_name, do: "Slot777老虎机"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room

  @impl true
  def default_config do
    # 获取统一配置
    config = Slot777Config.get_current_config()

    %{
      # 房间配置
      max_players: config.room.max_players,
      min_players: config.room.min_players,
      auto_start_delay: config.room.auto_start_delay,
      enable_robots: config.room.enable_robots,
      robot_count: config.room.robot_count,

      # 下注配置
      min_bet: config.betting.min_bet,
      max_bet: config.betting.max_bet,
      odds_config: config.betting.odds_config,

      # 游戏配置 (保持兼容性)
      game_config: %{
        # 基础配置
        reels: config.game_basic.cols,
        rows: config.game_basic.rows,
        paylines: config.game_basic.max_lines,

        # 下注配置
        min_bet: config.betting.min_bet,
        max_bet: config.betting.max_bet,
        bet_multipliers: Map.values(config.betting.odds_config),

        # 中奖率配置
        rtp: config.win_rate.rtp,

        # Jackpot配置
        jackpot_config: %{
          base_amount: List.first(config.jackpot.pools).base_amount,
          contribution_rate: config.jackpot.contribution_rate,
          pools: config.jackpot.pools
        },

        # 免费游戏配置
        free_game_config: %{
          # 苹果触发
          scatter_count: 3,
          free_spins: config.free_game.trigger_table[3] || 4,
          multiplier: config.free_game.multiplier
        },

        # 图标配置
        symbol_config: %{
          normal_symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9],
          # WILD
          wild_symbol: 0,
          # 苹果(FREE)
          scatter_symbol: 9
        },

        # 支付表 (使用配置中的赔率表)
        paytable: config.payout.payout_table
      }
    }
  end

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [
      # Slot777 (统一使用的ID)
      40
    ]
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    # TODO: 统计信息将由新的统一系统提供
    get_default_stats()
  end

  @doc """
  获取当前活跃房间数
  """
  def get_total_rooms do
    # TODO: 房间统计将由新系统提供
    0
  end

  @doc """
  获取当前活跃玩家数
  """
  def get_active_players do
    # TODO: 玩家统计将由新系统提供
    0
  end

  # 获取默认统计信息（当全局管理器不可用时）
  defp get_default_stats do
    config = default_config()

    %{
      total_rooms: 0,
      active_players: 0,
      total_jackpot: config.game_config.jackpot_config.base_amount,
      total_contributions: 0,
      total_jackpots_won: 0
    }
  end

  @doc """
  获取当前总Jackpot金额
  """
  def get_total_jackpot do
    # TODO: 奖池金额将由新的统一奖池系统提供
    0
  end

  # TODO: 全局管理器功能将由新的统一系统实现

  @doc """
  验证游戏配置
  """
  def validate_config(config) do
    required_keys = [:max_players, :min_players, :game_config]

    case Enum.all?(required_keys, &Map.has_key?(config, &1)) do
      true -> {:ok, config}
      false -> {:error, "Missing required configuration keys"}
    end
  end

  @doc """
  获取游戏版本信息
  """
  def version_info do
    %{
      version: "2.1.0",
      build_date: "2024-12-16",
      features: [
        "基础老虎机游戏",
        "免费游戏",
        "Wild和Scatter图标",
        "多倍率下注",
        "准备接入新奖池系统"
      ]
    }
  end

  @doc """
  初始化Slot777游戏模块
  当游戏被注册到GameFactory时调用
  """
  def init_game_module do
    Logger.info("🎰 [SLOT777_GAME] 初始化Slot777游戏模块")
    Logger.info("🎰 [SLOT777_GAME] 游戏模块初始化完成")
    :ok
  end

  @doc """
  清理Slot777游戏模块
  当游戏被从GameFactory注销时调用
  """
  def cleanup_game_module do
    Logger.info("🎰 [SLOT777_GAME] 清理Slot777游戏模块")
    Logger.info("🎰 [SLOT777_GAME] 游戏模块清理完成")
    :ok
  end

  # 🔧 健康检查和维护功能

  @doc """
  执行健康检查
  """
  def health_check do
    %{
      game_module: :healthy,
      timestamp: DateTime.utc_now()
    }
  end

  # TODO: 管理器重启功能将由新系统实现
end
