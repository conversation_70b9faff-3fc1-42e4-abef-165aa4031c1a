defmodule Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiPlayer do
  @moduledoc """
  Teen Patti玩家管理模块

  负责管理玩家的所有数据和状态，包括：
  - 充值相关数据（充值次数、充值金额、充值余额）
  - 幸运值管理
  - 金币管理（免费金币、充值金币）
  - 玩家状态判断和分类
  - 幸运值调整规则
  """

  require Logger

  # 玩家类型定义
  @player_types %{
    # 免费玩家
    free: :free,
    # 新玩家
    new: :new,
    # 新玩家金币没有达80
    new_never_80: :new_never_80,
    # 待充值玩家（金币超过80的玩家）
    waiting_recharge: :waiting_recharge,
    # 回头玩家（超过3天未上线）
    returning: :returning,
    # 1充玩家
    charged_1: :charged_1,
    # 2充玩家
    charged_2: :charged_2,
    # 3充玩家
    charged_3: :charged_3,
    # 4充及以上玩家
    charged_4_plus: :charged_4_plus
  }

  # 玩家类型ID映射（整型到原子类型）
  @player_type_id_mapping %{
    # 免费玩家
    0 => :free,
    # 新玩家
    1 => :new,
    # 新玩家金币没有达80
    2 => :new_never_80,
    # 待充值玩家（金币超过80的玩家）
    3 => :waiting_recharge,
    # 回头玩家（超过3天未上线）
    4 => :returning,
    # 1充玩家
    5 => :charged_1,
    # 2充玩家
    6 => :charged_2,
    # 3充玩家
    7 => :charged_3,
    # 4充及以上玩家
    8 => :charged_4_plus
  }

  # 原子类型到玩家类型ID映射
  @player_type_to_id_mapping %{
    # 免费玩家
    :free => 0,
    # 新玩家
    :new => 1,
    # 新玩家金币没有达80
    :new_never_80 => 2,
    # 待充值玩家（金币超过80的玩家）
    :waiting_recharge => 3,
    # 回头玩家（超过3天未上线）
    :returning => 4,
    # 1充玩家
    :charged_1 => 5,
    # 2充玩家
    :charged_2 => 6,
    # 3充玩家
    :charged_3 => 7,
    # 4充及以上玩家
    :charged_4_plus => 8
  }

  # 玩家状态标签（根据幸运值）
  @luck_status_labels %{
    (850..1000) => "好运爆棚",
    (650..849) => "小有手气",
    (400..649) => "手气平稳",
    (200..399) => "有点背",
    (1..199) => "霉运缠身"
  }

  # 默认配置
  @default_config %{
    # 幸运值相关
    # 默认幸运值
    default_luck_value: 500,
    # 幸运值最小值
    luck_min: 0,
    # 幸运值最大值
    luck_max: 1000,
    # 每局自动衰减
    luck_decay_per_round: 1,
    # 幸运值阈值（边际递减触发点）
    luck_threshold: 500,

    # 充值相关
    # 充值后幸运值提升到
    recharge_luck_boost: 750,
    # 急救状态触发阈值
    emergency_rescue_threshold: 0.2,
    # 急救状态退出阈值
    emergency_rescue_exit: 0.3,

    # 玩家分类阈值
    # 金币达到80的阈值
    max_gold_threshold: 80,
    # 回头玩家天数阈值
    returning_days: 3
  }

  @doc """
  创建新玩家数据结构
  需要传入 state 和 player 参数以使用 room_base 的统一数据源
  """
  def create_player_data(player_id, opts \\ %{}) do
    # 🎯 重要：如果传入了 state 和 player，优先使用 room_base 的数据
    {recharge_count, current_coins} =
      case {Map.get(opts, :state), Map.get(opts, :player)} do
        {state, player} when not is_nil(state) and not is_nil(player) ->
          # 使用 room_base 的统一数据源
          rc = get_player_recharge_count_from_room_base(player)
          cc = get_player_points_from_room_base(state, player_id)

          Logger.info(
            "🎯 [CREATE_PLAYER_DATA] 使用room_base数据 - 玩家#{player_id}, 充值次数:#{rc}, 积分:#{cc}"
          )

          {rc, cc}

        _ ->
          # 兼容旧的调用方式（但会记录警告）
          Logger.warning("🎯 [CREATE_PLAYER_DATA] 使用旧方式创建玩家数据，建议传入state和player参数")
          {get_recharge_count(opts), Map.get(opts, :total_coins, 5_000_000)}
      end

    # 先创建基础玩家数据
    base_data = %{
      player_id: player_id,
      # 数字ID，默认使用player_id
      numeric_id: Map.get(opts, :numeric_id, player_id),

      # 玩家类型字段（整型）
      # 玩家类型ID，0=免费玩家，1=新玩家，2=新玩家金币没有达80，3=待充值（金币超过80），4=回头玩家，5=1充，6=2充，7=3充，8=4充及以上
      player_type_id: Map.get(opts, :player_type_id, 0),

      # 🎯 统一：使用 room_base 的充值次数
      recharge_count: recharge_count,
      # 充值总金额，默认0
      recharge_total_amount: Map.get(opts, :recharge_total_amount, 0),
      # 充值余额，默认0
      recharge_balance: Map.get(opts, :recharge_balance, 0),

      # 🎯 重构：使用专用函数获取幸运值
      # 幸运值，默认500
      luck_value: get_luck_value(opts),

      # 金币字段
      # 免费金币，默认0
      free_coins: Map.get(opts, :free_coins, 0),
      # 总金币（显示用）
      total_coins: Map.get(opts, :total_coins, 0),

      # 统计字段
      # 历史最高金币
      max_coins_reached: Map.get(opts, :max_coins_reached, 0),
      # 提款总金额
      total_withdraw_amount: Map.get(opts, :total_withdraw_amount, 0),

      # 充值提现记录字段（临时实现，后续会有专门的数据库表）
      # 充值记录列表
      recharge_records: Map.get(opts, :recharge_records, []),
      # 提现记录列表
      withdraw_records: Map.get(opts, :withdraw_records, []),

      # 状态字段
      # 急救状态：0=没有，1=拥有一次，2=正在使用
      emergency_rescue_status: Map.get(opts, :emergency_rescue_status, 0),
      # 幸运值调整标记数组：[1充1.55, 1充1.98, 2充1.75, 2充2.22, 3充2.0, 3充2.6]
      luck_adjustment_flags:
        Map.get(opts, :luck_adjustment_flags, [false, false, false, false, false, false]),

      # 🎯 新增：幸运值调整优先级控制
      # 当局是否已由提充比调整幸运值（优先级最高）
      withdraw_ratio_luck_adjusted: Map.get(opts, :withdraw_ratio_luck_adjusted, false),

      # 🎯 新增：连续输赢记录（数字化方式）
      consecutive_streak: Map.get(opts, :consecutive_streak, 0), # 连续输赢：负数=连续输，正数=连续赢，0=重置

      last_online_time: Map.get(opts, :last_online_time, DateTime.utc_now()), # 最后在线时间
      first_play_time: Map.get(opts, :first_play_time, DateTime.utc_now()), # 首次游戏时间

      # 游戏统计
      total_games: Map.get(opts, :total_games, 0),            # 总游戏局数

      created_at: DateTime.utc_now()
    }

    # 🎯 关键修复：先尝试 set_player_type 的测试逻辑，然后计算玩家类型
    # 先用默认类型调用 set_player_type，让测试逻辑有机会执行
    temp_data = set_player_type(base_data, :new)

    # 然后基于可能更新的数据重新计算玩家类型
    # 🎯 统一：如果有 state 和 player，使用 room_base 数据计算类型
    player_type =
      case {Map.get(opts, :state), Map.get(opts, :player)} do
        {state, player} when not is_nil(state) and not is_nil(player) ->
          # 使用 room_base 数据计算类型
          calculate_player_type_by_coins(
            recharge_count,
            current_coins,
            DateTime.utc_now(),
            temp_data
          )

        _ ->
          # 兼容旧方式
          calculate_player_type_dynamically(temp_data)
      end

    # 最后用正确的类型再次调用 set_player_type
    final_data = set_player_type(temp_data, player_type)

    Logger.info(
      "🎯 [CREATE_PLAYER_DATA] 玩家 #{player_id} 创建完成 - 类型: #{player_type}, 充值次数: #{Map.get(final_data, :recharge_count)}, 幸运值: #{Map.get(final_data, :luck_value)}"
    )

    final_data
  end

  @doc """
  获取玩家充值金币数量
  玩家的金币 - 玩家的免费金币 = 玩家充值金币
  """
  def get_charged_coins(player_data) do
    total_coins = Map.get(player_data, :total_coins, 0)
    free_coins = Map.get(player_data, :free_coins, 0)
    max(0, total_coins - free_coins)
  end

  @doc """
  获取玩家类型（优先使用player_type_id字段，如果没有则动态计算）
  """
  def get_player_type(player_data) do
    # 优先使用player_type_id字段
    case Map.get(player_data, :player_type_id) do
      nil ->
        # 如果没有player_type_id字段，使用旧的动态计算方式
        calculate_player_type_dynamically(player_data)

      type_id when is_integer(type_id) ->
        # 从整型ID获取玩家类型
        get_player_type_from_id(type_id)

      _ ->
        # 无效的player_type_id，使用动态计算
        calculate_player_type_dynamically(player_data)
    end
  end

  @doc """
  从整型ID获取玩家类型
  """
  def get_player_type_from_id(type_id) do
    Map.get(@player_type_id_mapping, type_id, :free)
  end

  @doc """
  从原子类型获取玩家类型ID
  """
  def get_player_type_id(player_type_atom) do
    Map.get(@player_type_to_id_mapping, player_type_atom, 0)
  end

  @doc """
  动态计算玩家类型（基于现有余额的新逻辑）
  支持两种调用方式：
  1. calculate_player_type_dynamically(player_data) - 从player_data中获取积分
  2. calculate_player_type_dynamically(state, player_id) - 使用get_player_points获取实时积分
  """
  def calculate_player_type_dynamically(player_data) when is_map(player_data) do
    # 🎯 重构：使用专用函数获取充值次数
    recharge_count = get_recharge_count(player_data)

    # 🎯 修改：使用正确的方式获取玩家当前金币/积分
    current_coins = get_player_current_coins(player_data)
    last_online_time = Map.get(player_data, :last_online_time, DateTime.utc_now())

    calculate_player_type_by_coins(recharge_count, current_coins, last_online_time, player_data)
  end

  @doc """
  从 room_base 获取数据计算玩家类型（新版本，推荐使用）
  """
  def calculate_player_type_from_room_base(state, player_id) do
    # 🎯 使用 room_base 的函数获取数据
    recharge_count =
      case Map.get(state.players, player_id) do
        nil ->
          0

        player ->
          # 🎯 这里应该调用 room_base 的函数，但需要在 teen_patti_room.ex 中调用
          Map.get(player, :recharge_count, 0)
      end

    current_coins =
      case Map.get(state.players, player_id) do
        nil -> 0
        player -> get_player_current_coins(player)
      end

    last_online_time = DateTime.utc_now()

    player_data = Map.get(state.players, player_id, %{numeric_id: player_id})
    calculate_player_type_by_coins(recharge_count, current_coins, last_online_time, player_data)
  end

  @doc """
  直接使用参数计算玩家类型（供缓存机制使用）
  """
  def calculate_player_type_by_coins_direct(
        recharge_count,
        current_coins,
        last_online_time,
        player_data
      ) do
    calculate_player_type_by_coins(recharge_count, current_coins, last_online_time, player_data)
  end

  # 私有函数：根据金币数量计算玩家类型（简化逻辑）
  defp calculate_player_type_by_coins(
         recharge_count,
         current_coins,
         last_online_time,
         player_data
       ) do
    player_id = Map.get(player_data, :numeric_id, "未知")

    Logger.debug(
      "🎯 [PLAYER_TYPE_DEBUG] 玩家#{player_id} - 充值次数:#{recharge_count}, 当前金币:#{current_coins}"
    )

    result =
      cond do
        # 🎯 简化：充值玩家直接通过充值次数判断
        recharge_count >= 4 ->
          @player_types.charged_4_plus

        recharge_count == 3 ->
          @player_types.charged_3

        recharge_count == 2 ->
          @player_types.charged_2

        recharge_count == 1 ->
          @player_types.charged_1

        # 🎯 简化：免费玩家（充值次数为0）通过金币和其他条件判断
        recharge_count == 0 ->
          is_new = is_new_player?(player_data)
          total_games = Map.get(player_data, :total_games, 0)

          Logger.debug(
            "🎯 [PLAYER_TYPE_DEBUG] 玩家#{player_id} - 是否新玩家:#{is_new}, 总游戏次数:#{total_games}"
          )

          cond do
            # 新玩家：指玩家设备第一次TP游戏
            is_new ->
              Logger.debug("🎯 [PLAYER_TYPE_DEBUG] 玩家#{player_id} - 判断为新玩家")
              @player_types.new

            # 新玩家金币没有达80（基于现有余额）
            current_coins < @default_config.max_gold_threshold ->
              Logger.debug(
                "🎯 [PLAYER_TYPE_DEBUG] 玩家#{player_id} - 金币#{current_coins} < 80，判断为new_never_80"
              )

              @player_types.new_never_80

            # 待充值玩家：指玩家金币超过80的玩家（基于现有余额）
            current_coins >= @default_config.max_gold_threshold ->
              Logger.debug(
                "🎯 [PLAYER_TYPE_DEBUG] 玩家#{player_id} - 金币#{current_coins} >= 80，判断为waiting_recharge"
              )

              @player_types.waiting_recharge

            # 回头玩家：超过3天未上线的玩家
            is_returning_player?(last_online_time) ->
              @player_types.returning

            true ->
              @player_types.free
          end

        # 默认情况
        true ->
          @player_types.free
      end

    Logger.debug("🎯 [PLAYER_TYPE_DEBUG] 玩家#{player_id} - 最终类型:#{result}")
    result
  end

  @doc """
  设置玩家类型（同时更新player_type_id字段和相关数据）
  """
  def set_player_type(player_data, player_type_atom) do
    type_id = get_player_type_id(player_type_atom)

    player_data
    |> Map.put(:player_type_id, type_id)
  end

  @doc """
  获取玩家状态标签（根据幸运值）
  """
  def get_luck_status_label(luck_value) do
    Enum.find_value(@luck_status_labels, "未知状态", fn {range, label} ->
      if luck_value in range, do: label
    end)
  end

  @doc """
  计算提充比
  总提充比：玩家提款总金额+身上的余额 / 玩家充值总金额
  """
  def calculate_withdraw_ratio(player_data) do
    total_withdraw = Map.get(player_data, :total_withdraw_amount, 0)
    current_balance = Map.get(player_data, :recharge_balance, 0)
    total_recharge = Map.get(player_data, :recharge_total_amount, 0)

    if total_recharge > 0 do
      (total_withdraw + current_balance) / total_recharge
    else
      0.0
    end
  end

  @doc """
  判断是否为新玩家
  """
  def is_new_player?(player_data) do
    # 暂定为代码配置，后续可以通过玩家数据获取
    total_games = Map.get(player_data, :total_games, 0)
    total_games == 0
  end

  @doc """
  判断是否为回头玩家
  """
  def is_returning_player?(last_online_time) do
    now = DateTime.utc_now()
    diff_days = DateTime.diff(now, last_online_time, :day)
    diff_days > @default_config.returning_days
  end

  @doc """
  更新玩家金币
  """
  def update_coins(player_data, coin_change, is_win \\ false) do
    current_total = Map.get(player_data, :total_coins, 0)
    current_free = Map.get(player_data, :free_coins, 0)
    current_charged = get_charged_coins(player_data)

    new_total = max(0, current_total + coin_change)

    {new_free, new_charged} =
      if coin_change < 0 do
        # 输钱：优先输充值部分的金币
        lose_amount = abs(coin_change)

        if current_charged >= lose_amount do
          # 充值金币足够扣除
          {current_free, current_charged - lose_amount}
        else
          # 充值金币不够，扣除免费金币
          remaining_lose = lose_amount - current_charged
          {max(0, current_free - remaining_lose), 0}
        end
      else
        # 赢钱：根据对手类型决定增加到哪部分
        win_amount = coin_change

        if is_charged_player?(player_data) and is_win do
          # 充值玩家赢钱，增加到充值部分
          {current_free, current_charged + win_amount}
        else
          # 免费玩家或其他情况，增加到免费部分
          {current_free + win_amount, current_charged}
        end
      end

    # 更新最高金币记录
    new_max_coins = max(Map.get(player_data, :max_coins_reached, 0), new_total)

    player_data
    |> Map.put(:total_coins, new_total)
    |> Map.put(:free_coins, new_free)
    |> Map.put(:recharge_balance, new_charged)
    |> Map.put(:max_coins_reached, new_max_coins)
  end

  @doc """
  判断是否为充值玩家
  """
  def is_charged_player?(player_data) do
    # 🎯 重构：使用专用函数获取充值次数
    get_recharge_count(player_data) > 0
  end

  @doc """
  生成记录ID（临时实现）
  """
  defp generate_record_id do
    "rec_#{System.unique_integer([:positive])}_#{DateTime.utc_now() |> DateTime.to_unix()}"
  end

  @doc """
  计算结算时的幸运值调整（纯计算函数，返回调整结果）
  优先级：提充比调整 > 极端爆炸局 > 连续输赢 > 普通输赢倍数调整

  返回: %{
    new_luck_value: 新幸运值,
    new_consecutive_streak: 新连续状态,
    adjustment_reason: 调整原因,
    should_reset_flags: 是否重置标记
  }
  """
  def calculate_luck_adjustment_on_settlement(current_luck, consecutive_streak, win_lose_multiplier, is_win, player_data, base_bet \\ 1) do
    player_id = Map.get(player_data, :player_id, "unknown")
    Logger.info("🎯 [LUCK_CALC] 开始计算幸运值调整 - 玩家: #{player_id}, 输赢: #{if is_win, do: "赢", else: "输"}#{win_lose_multiplier}")

    # 🎯 优先级1：如果当局已由提充比调整幸运值，不再进行其他调整
    if Map.get(player_data, :withdraw_ratio_luck_adjusted, false) do
      Logger.info("🎯 [LUCK_PRIORITY] 玩家 #{player_id} 当局已由提充比调整幸运值，跳过其他调整")

      # 🎯 使用Calculator模块计算连续输赢状态
      new_consecutive_streak = TeenPattiCalculator.calculate_consecutive_streak(consecutive_streak, is_win)

      %{
        new_luck_value: current_luck,
        new_consecutive_streak: new_consecutive_streak,
        adjustment_reason: "提充比已调整，跳过",
        should_reset_flags: true
      }
    else
      # 只对充值玩家调整幸运值
      if not is_charged_player?(player_data) do
        Logger.info("🆓 [FREE_PLAYER] 玩家 #{player_id} 免费玩家，只更新连续输赢记录")

        # 🎯 使用Calculator模块计算连续输赢状态
        new_consecutive_streak = TeenPattiCalculator.calculate_consecutive_streak(consecutive_streak, is_win)

        %{
          new_luck_value: current_luck,
          new_consecutive_streak: new_consecutive_streak,
          adjustment_reason: "免费玩家，不调整幸运值",
          should_reset_flags: false
        }
      else
        Logger.info("💰 [CHARGED_PLAYER] 玩家 #{player_id} 充值玩家，进行完整幸运值调整")

        # 🎯 使用Calculator模块进行完整的幸运值调整计算
        TeenPattiCalculator.calculate_settlement_adjustment(
          current_luck,
          consecutive_streak,
          win_lose_multiplier,
          is_win,
          1, # base_bet
          true, # is_charged_player
          false # withdraw_ratio_adjusted
        )
      end
    end
  end

  @doc """
  执行幸运值调整的核心逻辑
  """
  defp perform_luck_adjustment(player_data, win_lose_multiplier, is_win, base_bet) do
    current_luck = Map.get(player_data, :luck_value, @default_config.default_luck_value)
    player_id = Map.get(player_data, :player_id, "unknown")

    Logger.info("🎯 [LUCK_ADJUSTMENT] 玩家 #{player_id} 当前幸运值: #{current_luck}")

    # 计算实际倍数（输赢金额 / 底分）
    actual_multiplier = win_lose_multiplier / base_bet

    # 🎯 优先级2：极端爆炸局判断（>300倍）
    # 🎯 使用Calculator模块检查极端爆炸局
    extreme_adjustment = TeenPattiCalculator.check_extreme_explosion(actual_multiplier, is_win)

    if extreme_adjustment do
      {new_luck, reason} = extreme_adjustment

      Logger.info(
        "💥 [EXTREME_EXPLOSION] 玩家 #{player_data.player_id} #{reason}，幸运值直接设为 #{new_luck}"
      )

      # 极端爆炸局后不重置连续输赢记录
      player_data
      |> Map.put(:luck_value, new_luck)
      |> Map.put(:withdraw_ratio_luck_adjusted, false)
    else
      # 🎯 优先级3：更新连续输赢记录并检查连续条件
      # 🎯 使用Calculator模块计算连续状态
      current_streak = Map.get(player_data, :consecutive_streak, 0)
      new_streak = TeenPattiCalculator.calculate_consecutive_streak(current_streak, is_win)
      updated_player = Map.put(player_data, :consecutive_streak, new_streak)
      # 🎯 使用Calculator模块检查连续条件
      consecutive_adjustment = TeenPattiCalculator.check_consecutive_luck_adjustment(new_streak, Map.get(updated_player, :luck_value, 500))

      if consecutive_adjustment do
        {new_luck, reason} = consecutive_adjustment
        Logger.info("🔄 [CONSECUTIVE] 玩家 #{player_data.player_id} #{reason}，幸运值调整为 #{new_luck}")

        # 连续条件触发后重置记录
        updated_player
        |> Map.put(:luck_value, new_luck)
        |> Map.put(:consecutive_streak, 0)
        |> Map.put(:withdraw_ratio_luck_adjusted, false)
      else
        # 🎯 优先级4：普通输赢倍数调整
        # 🎯 使用Calculator模块计算普通调整
        adjustment = TeenPattiCalculator.calculate_normal_luck_adjustment(win_lose_multiplier, is_win)

        new_luck =
          if is_win do
            max(@default_config.luck_min, current_luck - adjustment)
          else
            min(@default_config.luck_max, current_luck + adjustment)
          end

        # 每局自动衰减1点（向500靠拢）
        # 🎯 使用Calculator模块应用衰减
        new_luck = TeenPattiCalculator.apply_luck_decay(new_luck)

        Logger.info(
          "� [NORMAL_ADJUSTMENT] 玩家 #{player_data.player_id} 幸运值调整: #{current_luck} -> #{new_luck} (#{if is_win, do: "赢", else: "输"}#{win_lose_multiplier}倍)"
        )

        updated_player
        |> Map.put(:luck_value, new_luck)
        |> Map.put(:withdraw_ratio_luck_adjusted, false)
      end
    end
  end

  @doc """
  处理极端大牌（拿到3条）的幸运值调整
  """
  def handle_extreme_card_adjustment(player_data, card_type) do
    # 🎯 优先级检查：如果当局已由提充比调整幸运值，不再进行其他调整
    if Map.get(player_data, :withdraw_ratio_luck_adjusted, false) do
      Logger.info("🎯 [LUCK_PRIORITY] 玩家 #{player_data.player_id} 当局已由提充比调整幸运值，跳过极端大牌调整")
      player_data
    else
      # 只对充值玩家调整幸运值
      if not is_charged_player?(player_data) do
        player_data
      else
        current_luck = Map.get(player_data, :luck_value, @default_config.default_luck_value)

        new_luck =
          case card_type do
            # 极端大牌：拿到3条，幸运值 (-5) ~ (-10)
            :trail ->
              adjustment = Enum.random(5..10)
              adjusted_luck = max(@default_config.luck_min, current_luck - adjustment)

              Logger.info(
                "🃏 [EXTREME_CARD] 玩家 #{player_data.player_id} 拿到3条，幸运值调整: #{current_luck} -> #{adjusted_luck} (-#{adjustment})"
              )

              adjusted_luck

            _ ->
              current_luck
          end

        player_data
        |> Map.put(:luck_value, new_luck)
      end
    end
  end

  # 🎯 连续输赢计算已移动到TeenPattiCalculator模块，删除重复代码

  @doc """
  检查极端爆炸局（>300倍）
  """
  defp check_extreme_explosion(actual_multiplier, is_win) do
    cond do
      # 极端爆炸局：单局输倍数超过300倍，幸运值直接=900
      not is_win and actual_multiplier > 300 ->
        {900, "单局输#{round(actual_multiplier)}倍(>300倍)"}

      # 极端爆炸局：单局赢倍数超过300倍，幸运值直接=150
      is_win and actual_multiplier > 300 ->
        {150, "单局赢#{round(actual_multiplier)}倍(>300倍)"}

      true ->
        nil
    end
  end

  @doc """
  检查连续输赢条件（数字化方式）
  """
  defp check_consecutive_conditions(player_data) do
    streak = Map.get(player_data, :consecutive_streak, 0)

    cond do
      # 连续输5局：幸运值 (+3) ~ (+5)
      streak <= -5 ->
        current_luck = Map.get(player_data, :luck_value, @default_config.default_luck_value)
        adjustment = Enum.random(3..5)
        new_luck = min(@default_config.luck_max, current_luck + adjustment)
        {new_luck, "连续输5局(+#{adjustment})"}

      # 连续赢3局：幸运值 (-5) ~ (-10)
      streak >= 3 ->
        current_luck = Map.get(player_data, :luck_value, @default_config.default_luck_value)
        adjustment = Enum.random(5..10)
        new_luck = max(@default_config.luck_min, current_luck - adjustment)
        {new_luck, "连续赢3局(-#{adjustment})"}

      true ->
        nil
    end
  end

  @doc """
  根据输赢倍数计算幸运值调整值
  """
  defp calculate_luck_adjustment(multiplier, is_win) do
    base_adjustment =
      cond do
        multiplier >= 1 and multiplier <= 5 ->
          if is_win, do: Enum.random(1..2), else: Enum.random(1..2)

        multiplier >= 6 and multiplier <= 10 ->
          if is_win, do: Enum.random(3..5), else: Enum.random(4..6)

        multiplier >= 11 and multiplier <= 20 ->
          if is_win, do: Enum.random(6..10), else: Enum.random(8..12)

        multiplier >= 21 and multiplier <= 40 ->
          if is_win, do: Enum.random(10..20), else: Enum.random(15..25)

        multiplier >= 41 and multiplier <= 70 ->
          if is_win, do: Enum.random(15..30), else: Enum.random(25..35)

        multiplier >= 71 and multiplier <= 100 ->
          if is_win, do: Enum.random(30..50), else: Enum.random(40..55)

        multiplier >= 101 and multiplier <= 200 ->
          if is_win, do: Enum.random(40..60), else: Enum.random(55..70)

        multiplier >= 201 ->
          if is_win, do: Enum.random(50..80), else: Enum.random(75..100)

        true ->
          1
      end

    base_adjustment
  end

  @doc """
  应用幸运值自然衰减（每局向500靠拢）
  """
  defp apply_luck_decay(luck_value) do
    threshold = @default_config.luck_threshold
    decay = @default_config.luck_decay_per_round

    cond do
      luck_value > threshold -> max(threshold, luck_value - decay)
      luck_value < threshold -> min(threshold, luck_value + decay)
      true -> luck_value
    end
  end

  @doc """
  检查并更新玩家状态（提充比相关）
  简化版本：使用数组存储6个改动标记
  """
  def check_and_update_player_status(player_data) do
    player_type = get_player_type(player_data)
    withdraw_ratio = calculate_withdraw_ratio(player_data)
    current_luck = Map.get(player_data, :luck_value, @default_config.default_luck_value)
    current_emergency_status = Map.get(player_data, :emergency_rescue_status, 0)

    adjustment_flags =
      Map.get(player_data, :luck_adjustment_flags, [false, false, false, false, false, false])

    # 只对1、2、3充玩家进行幸运值调整
    {new_luck, new_flags, new_emergency_status} =
      case player_type do
        :charged_1 ->
          apply_simple_luck_adjustment(
            player_data,
            :charged_1,
            withdraw_ratio,
            current_luck,
            adjustment_flags,
            current_emergency_status
          )

        :charged_2 ->
          apply_simple_luck_adjustment(
            player_data,
            :charged_2,
            withdraw_ratio,
            current_luck,
            adjustment_flags,
            current_emergency_status
          )

        :charged_3 ->
          apply_simple_luck_adjustment(
            player_data,
            :charged_3,
            withdraw_ratio,
            current_luck,
            adjustment_flags,
            current_emergency_status
          )

        _ ->
          # 非1、2、3充玩家不调整幸运值
          {current_luck, adjustment_flags, current_emergency_status}
      end

    # 🎯 如果幸运值有变化，设置提充比调整标记（优先级最高）
    withdraw_ratio_adjusted = new_luck != current_luck

    # 🎯 修复：使用 Map.put 安全设置字段，避免 badkey 错误
    updated_data =
      player_data
      |> Map.put(:luck_value, new_luck)
      |> Map.put(:luck_adjustment_flags, new_flags)
      |> Map.put(:emergency_rescue_status, new_emergency_status)
      |> Map.put(:withdraw_ratio_luck_adjusted, withdraw_ratio_adjusted)

    if new_luck != current_luck do
      Logger.info(
        "📊 [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} (#{player_type}) 幸运值调整: #{current_luck} -> #{new_luck} (提充比: #{Float.round(withdraw_ratio, 2)})"
      )
    end

    updated_data
  end

  @doc """
  简化的幸运值调整规则
  数组索引：[0]1充1.55, [1]1充1.98, [2]2充1.75, [3]2充2.22, [4]3充2.0, [5]3充2.6
  """
  defp apply_simple_luck_adjustment(
         player_data,
         player_type,
         withdraw_ratio,
         current_luck,
         adjustment_flags,
         current_emergency_status
       ) do
    # 检查急救状态逻辑
    {new_luck, new_flags, new_emergency_status} =
      cond do
        # 急救状态触发：提充比 < 0.2 且拥有急救状态
        withdraw_ratio < @default_config.emergency_rescue_threshold and
            current_emergency_status == 1 ->
          Logger.info("🚨 [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 触发急救状态，幸运值设为750")
          # 进入急救状态（正在使用）
          {750, adjustment_flags, 2}

        # 急救状态退出：正在使用急救状态 && 提充比 >= 0.3
        current_emergency_status == 2 and withdraw_ratio >= @default_config.emergency_rescue_exit ->
          Logger.info("✅ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 退出急救状态，幸运值恢复默认")
          # 退出急救状态，幸运值改为默认
          {@default_config.default_luck_value, adjustment_flags, 0}

        # 1充玩家提充比阈值检查
        player_type == :charged_1 and withdraw_ratio >= 1.98 and not Enum.at(adjustment_flags, 1) ->
          new_flags = List.replace_at(adjustment_flags, 1, true)
          Logger.info("⚠️ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 1充达到1.98阈值，幸运值设为100")

          # 🎯 创建数据库记录
          record =
            create_luck_adjustment_record(
              player_data,
              "withdraw_ratio_trigger",
              current_luck,
              100,
              withdraw_ratio,
              1
            )

          save_luck_adjustment_record(record)

          {100, new_flags, current_emergency_status}

        player_type == :charged_1 and withdraw_ratio >= 1.55 and not Enum.at(adjustment_flags, 0) ->
          new_flags = List.replace_at(adjustment_flags, 0, true)
          Logger.info("⚠️ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 1充达到1.55阈值，幸运值设为300")

          # 🎯 创建数据库记录
          record =
            create_luck_adjustment_record(
              player_data,
              "withdraw_ratio_trigger",
              current_luck,
              300,
              withdraw_ratio,
              0
            )

          save_luck_adjustment_record(record)

          {300, new_flags, current_emergency_status}

        # 2充玩家提充比阈值检查
        player_type == :charged_2 and withdraw_ratio >= 2.22 and not Enum.at(adjustment_flags, 3) ->
          new_flags = List.replace_at(adjustment_flags, 3, true)
          Logger.info("⚠️ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 2充达到2.22阈值，幸运值设为100")

          # 🎯 创建数据库记录
          record =
            create_luck_adjustment_record(
              player_data,
              "withdraw_ratio_trigger",
              current_luck,
              100,
              withdraw_ratio,
              3
            )

          save_luck_adjustment_record(record)

          {100, new_flags, current_emergency_status}

        player_type == :charged_2 and withdraw_ratio >= 1.75 and not Enum.at(adjustment_flags, 2) ->
          new_flags = List.replace_at(adjustment_flags, 2, true)
          Logger.info("⚠️ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 2充达到1.75阈值，幸运值设为300")

          # 🎯 创建数据库记录
          record =
            create_luck_adjustment_record(
              player_data,
              "withdraw_ratio_trigger",
              current_luck,
              300,
              withdraw_ratio,
              2
            )

          save_luck_adjustment_record(record)

          {300, new_flags, current_emergency_status}

        # 3充玩家提充比阈值检查
        player_type == :charged_3 and withdraw_ratio >= 2.6 and not Enum.at(adjustment_flags, 5) ->
          new_flags = List.replace_at(adjustment_flags, 5, true)
          Logger.info("⚠️ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 3充达到2.6阈值，幸运值设为100")

          # 🎯 创建数据库记录
          record =
            create_luck_adjustment_record(
              player_data,
              "withdraw_ratio_trigger",
              current_luck,
              100,
              withdraw_ratio,
              5
            )

          save_luck_adjustment_record(record)

          {100, new_flags, current_emergency_status}

        player_type == :charged_3 and withdraw_ratio >= 2.0 and not Enum.at(adjustment_flags, 4) ->
          new_flags = List.replace_at(adjustment_flags, 4, true)
          Logger.info("⚠️ [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 3充达到2.0阈值，幸运值设为300")

          # 🎯 创建数据库记录
          record =
            create_luck_adjustment_record(
              player_data,
              "withdraw_ratio_trigger",
              current_luck,
              300,
              withdraw_ratio,
              4
            )

          save_luck_adjustment_record(record)

          {300, new_flags, current_emergency_status}

        true ->
          {current_luck, adjustment_flags, current_emergency_status}
      end

    {new_luck, new_flags, new_emergency_status}
  end

  @doc """
  获取玩家配置信息（用于游戏逻辑）
  """
  def get_player_config(player_data) do
    player_type = get_player_type(player_data)
    # 🎯 重构：使用专用函数获取幸运值
    luck_value = get_luck_value(player_data)
    emergency_status = Map.get(player_data, :emergency_rescue_status, 0)

    %{
      player_type: player_type,
      luck_value: luck_value,
      luck_status: get_luck_status_label(luck_value),
      is_charged: is_charged_player?(player_data),
      emergency_rescue_status: emergency_status,
      withdraw_ratio: calculate_withdraw_ratio(player_data),
      charged_coins: get_charged_coins(player_data),
      free_coins: Map.get(player_data, :free_coins, 0),
      total_coins: Map.get(player_data, :total_coins, 0)
    }
  end

  @doc """
  发牌前检查和更新玩家状态
  根据新规则，在发牌前计算123充玩家的提充比进行判断，修改123充玩家的幸运值
  """
  def check_player_status_before_dealing(player_data) do
    player_type = get_player_type(player_data)

    # 只对1、2、3充玩家进行检查
    if player_type in [:charged_1, :charged_2, :charged_3] do
      check_and_update_player_status(player_data)
    else
      player_data
    end
  end

  @doc """
  批量检查玩家状态（发牌前）
  """
  def batch_check_players_before_dealing(players) when is_map(players) do
    Enum.reduce(players, %{}, fn {player_id, player_data}, acc ->
      updated_player_data = check_player_status_before_dealing(player_data)
      Map.put(acc, player_id, updated_player_data)
    end)
  end

  @doc """
  获取默认配置
  """
  def get_default_config, do: @default_config

  @doc """
  获取玩家类型定义
  """
  def get_player_types, do: @player_types

  @doc """
  判断玩家是否应该使用幸运值发牌
  根据新规则：幸运值仅针对充值玩家有效，免费玩家不用幸运值数值根据策略发牌
  """
  def should_use_luck_value?(player_data) do
    is_charged_player?(player_data)
  end

  @doc """
  获取玩家发牌用的幸运值
  如果是充值玩家，返回幸运值；如果是免费玩家，返回nil表示使用策略发牌
  """
  def get_dealing_luck_value(player_data) do
    if should_use_luck_value?(player_data) do
      # 🎯 重构：使用专用函数获取幸运值
      get_luck_value(player_data)
    else
      # 免费玩家不使用幸运值
      nil
    end
  end

  @doc """
  获取玩家充值记录
  """
  def get_recharge_records(player_data, limit \\ 10) do
    records = Map.get(player_data, :recharge_records, [])
    Enum.take(records, limit)
  end

  @doc """
  获取玩家提现记录
  """
  def get_withdraw_records(player_data, limit \\ 10) do
    records = Map.get(player_data, :withdraw_records, [])
    Enum.take(records, limit)
  end

  @doc """
  检查幸运值调整标记状态
  数组索引：[0]1充1.55, [1]1充1.98, [2]2充1.75, [3]2充2.22, [4]3充2.0, [5]3充2.6
  """
  def check_adjustment_flag(player_data, index) when index >= 0 and index <= 5 do
    adjustment_flags =
      Map.get(player_data, :luck_adjustment_flags, [false, false, false, false, false, false])

    Enum.at(adjustment_flags, index, false)
  end

  @doc """
  设置幸运值调整标记
  """
  def set_adjustment_flag(player_data, index, value \\ true) when index >= 0 and index <= 5 do
    current_flags =
      Map.get(player_data, :luck_adjustment_flags, [false, false, false, false, false, false])

    new_flags = List.replace_at(current_flags, index, value)

    player_data
    |> Map.put(:luck_adjustment_flags, new_flags)
  end

  @doc """
  处理玩家充值后的状态更新
  重新充值只更新急救状态，不重置提充比变动的幸运值标记
  """
  def handle_player_recharge(player_data, recharge_amount) do
    current_recharge_count = Map.get(player_data, :recharge_count, 0)
    current_total_amount = Map.get(player_data, :recharge_total_amount, 0)
    current_balance = Map.get(player_data, :recharge_balance, 0)
    current_emergency_status = Map.get(player_data, :emergency_rescue_status, 0)

    # 更新充值相关数据
    new_recharge_count = current_recharge_count + 1
    new_total_amount = current_total_amount + recharge_amount
    new_balance = current_balance + recharge_amount

    # 🎯 重要：重新充值时给予急救状态（如果没有的话）
    new_emergency_status =
      if current_emergency_status == 0 do
        # 给予一次急救状态
        1
      else
        # 保持现有状态
        current_emergency_status
      end

    # 🎯 关键：充值后幸运值设为750，但不重置提充比调整标记
    updated_data =
      player_data
      |> Map.put(:recharge_count, new_recharge_count)
      |> Map.put(:recharge_total_amount, new_total_amount)
      |> Map.put(:recharge_balance, new_balance)
      # 充值后幸运值750
      |> Map.put(:luck_value, @default_config.recharge_luck_boost)
      |> Map.put(:emergency_rescue_status, new_emergency_status)

    # 🎯 重要：不重置 luck_adjustment_flags，保持提充比调整记录

    Logger.info(
      "💰 [TEEN_PATTI_PLAYER] 玩家 #{player_data.player_id} 充值 #{recharge_amount}，充值次数: #{current_recharge_count} -> #{new_recharge_count}，急救状态: #{current_emergency_status} -> #{new_emergency_status}"
    )

    updated_data
  end

  @doc """
  重置幸运值调整标记（仅用于特殊情况，如数据修复）
  """
  def reset_adjustment_flags(player_data) do
    player_data
    |> Map.put(:luck_adjustment_flags, [false, false, false, false, false, false])
  end

  @doc """
  获取调整标记的含义说明
  """
  def get_adjustment_flag_meanings do
    [
      # 索引0
      "1充玩家达到1.55阈值",
      # 索引1
      "1充玩家达到1.98阈值",
      # 索引2
      "2充玩家达到1.75阈值",
      # 索引3
      "2充玩家达到2.22阈值",
      # 索引4
      "3充玩家达到2.0阈值",
      # 索引5
      "3充玩家达到2.6阈值"
    ]
  end

  @doc """
  创建幸运值调整记录（用于数据库存储）
  当由提充比变动导致幸运值调整时，创建记录保存到数据库
  """
  def create_luck_adjustment_record(
        player_data,
        adjustment_type,
        old_luck,
        new_luck,
        withdraw_ratio,
        trigger_index
      ) do
    meanings = get_adjustment_flag_meanings()
    trigger_description = Enum.at(meanings, trigger_index, "未知阈值")

    %{
      player_id: Map.get(player_data, :player_id),
      # "withdraw_ratio_trigger" 表示由提充比触发
      adjustment_type: adjustment_type,
      trigger_description: trigger_description,
      trigger_index: trigger_index,
      old_luck_value: old_luck,
      new_luck_value: new_luck,
      withdraw_ratio: Float.round(withdraw_ratio, 4),
      player_type: get_player_type(player_data),
      recharge_count: Map.get(player_data, :recharge_count, 0),
      recharge_total_amount: Map.get(player_data, :recharge_total_amount, 0),
      total_withdraw_amount: Map.get(player_data, :total_withdraw_amount, 0),
      recharge_balance: Map.get(player_data, :recharge_balance, 0),
      adjustment_flags_before:
        Map.get(player_data, :luck_adjustment_flags, [false, false, false, false, false, false]),
      created_at: DateTime.utc_now(),
      # 标记是在发牌时触发的
      game_context: "teen_patti_dealing"
    }
  end

  @doc """
  保存幸运值调整记录到数据库（待实现）
  """
  def save_luck_adjustment_record(record) do
    # TODO: 实现数据库保存逻辑
    # 这里可以调用数据库模块保存记录
    Logger.info("💾 [LUCK_ADJUSTMENT_DB] 保存幸运值调整记录: #{inspect(record)}")

    # 临时实现：可以保存到文件或发送到外部服务
    # 例如：
    # Cypridina.Database.LuckAdjustmentRecord.create(record)
    # 或者：
    # Cypridina.ExternalAPI.save_luck_adjustment(record)

    {:ok, record}
  end

  @doc """
  查询玩家的幸运值调整历史（待实现）
  """
  def get_player_luck_adjustment_history(player_id, limit \\ 50) do
    # TODO: 实现数据库查询逻辑
    Logger.info("🔍 [LUCK_ADJUSTMENT_DB] 查询玩家 #{player_id} 的幸运值调整历史")

    # 临时返回空列表
    []
  end

  @doc """
  统计幸运值调整数据（待实现）
  """
  def get_luck_adjustment_statistics(date_range \\ nil) do
    # TODO: 实现统计查询逻辑
    Logger.info("📊 [LUCK_ADJUSTMENT_DB] 查询幸运值调整统计数据")

    # 临时返回空统计
    %{
      total_adjustments: 0,
      by_player_type: %{},
      by_trigger_type: %{},
      average_withdraw_ratio: 0.0
    }
  end

  @doc """
  获取连续输赢记录的可读描述（数字化方式）
  """
  def get_consecutive_results_description(player_data) do
    streak = Map.get(player_data, :consecutive_streak, 0)

    cond do
      streak > 0 -> "连续赢#{streak}局"
      streak < 0 -> "连续输#{abs(streak)}局"
      true -> "重置状态"
    end
  end

  @doc """
  重置连续输赢记录（数字化方式）
  """
  def reset_consecutive_results(player_data) do
    # 🎯 修复：使用 Map.put 确保字段存在
    player_data
    |> Map.put(:consecutive_streak, 0)
  end

  @doc """
  检查是否满足连续条件（数字化方式）
  """
  def check_consecutive_trigger(player_data) do
    streak = Map.get(player_data, :consecutive_streak, 0)

    cond do
      # 连续输5局
      streak <= -5 ->
        {:consecutive_lose, abs(streak)}

      # 连续赢3局
      streak >= 3 ->
        {:consecutive_win, streak}

      true ->
        nil
    end
  end

  @doc """
  计算玩家幸运值

  基于玩家的充值状态、提款比例等因素计算幸运值
  """
  def calculate_luck_value(player_data) do
    base_luck = @default_config.default_luck_value

    # 基于充值次数的奖励
    recharge_bonus = min(player_data.recharge_count * 50, 200)

    # 基于充值总额的奖励
    amount_bonus = min(div(player_data.recharge_total_amount, 1000) * 10, 100)

    # 基于提款比例的惩罚（如果有的话）
    withdraw_penalty = Map.get(player_data, :withdraw_ratio, 0) * 100

    # 计算最终幸运值
    final_luck = base_luck + recharge_bonus + amount_bonus - withdraw_penalty

    # 确保幸运值在有效范围内
    max(0, min(1000, round(final_luck)))
  end

  @doc """
  更新玩家数据
  """
  def update_player_data(player_data, updates) do
    Map.merge(player_data, updates)
  end

  @doc """
  更新玩家游戏统计信息
  """
  def update_game_stats(player, win_amount \\ 0) do
    # 获取玩家当前的统计信息
    current_stats = Map.get(player.user, :game_stats, %{})

    # 更新统计信息
    updated_stats = %{
      total_games: Map.get(current_stats, :total_games, 0) + 1,
      total_wins: Map.get(current_stats, :total_wins, 0) + if(win_amount > 0, do: 1, else: 0),
      total_losses: Map.get(current_stats, :total_losses, 0) + if(win_amount < 0, do: 1, else: 0),
      max_coins_reached:
        max(Map.get(current_stats, :max_coins_reached, 0), get_player_points(player)),
      last_game_at: DateTime.utc_now()
    }

    # 更新玩家的用户数据
    updated_user = Map.put(player.user, :game_stats, updated_stats)
    updated_player = %{player | user: updated_user}

    Logger.info(
      "🃏 [TEEN_PATTI_PLAYER] 玩家 #{player.numeric_id} 统计更新: 总局数=#{updated_stats.total_games}, 胜局=#{updated_stats.total_wins}, 败局=#{updated_stats.total_losses}"
    )

    updated_player
  end

  @doc """
  批量更新多个玩家的游戏统计信息
  需要传入 state 参数以获取正确的游戏配置
  """
  def update_players_game_stats(players, settlement_result, state \\ nil) when is_map(players) do
    Logger.info("🃏 [TEEN_PATTI_PLAYER] 开始批量更新玩家游戏统计")
    Logger.info("🎯 [DEBUG] 参与玩家: #{inspect(Map.keys(players))}")
    Logger.info("🎯 [DEBUG] 结算变化: #{inspect(settlement_result.player_changes)}")

    # 🎯 修复：从游戏配置获取正确的底分
    base_bet =
      case state do
        %{game_data: %{config: %{base_bet: bet}}} -> bet
        # 默认底分，但会记录警告
        _ -> 10
      end

    if state == nil do
      Logger.warning("🎯 [SETTLEMENT] 未传入state参数，使用默认底分#{base_bet}，建议传入state获取正确配置")
    else
      Logger.info("🎯 [SETTLEMENT] 从游戏配置获取底分: #{base_bet}")
    end

    # 更新每个玩家的统计信息
    updated_players =
      Enum.reduce(players, %{}, fn {player_id, player}, acc ->
        # 获取玩家在本局的输赢情况
        player_change = Map.get(settlement_result.player_changes, player_id, 0)
        Logger.info("🎯 [DEBUG] 玩家 #{player_id} 金币变化: #{player_change}")

        # 🎯 新增：结算时调用幸运值调整
        player_with_luck_adjustment =
          if player_change != 0 do
            Logger.info("🎯 [SETTLEMENT] 玩家 #{player_id} 有输赢变化: #{player_change}")

            # 计算输赢倍数和是否获胜
            is_win = player_change > 0

            # 🎯 修复：直接使用变化金额除以底分计算倍数（前端已处理/100逻辑）
            win_lose_multiplier = abs(player_change) / base_bet

            Logger.info(
              "🎯 [SETTLEMENT] 倍数计算: 变化金额#{abs(player_change)} ÷ 底分#{base_bet} = #{Float.round(win_lose_multiplier, 2)}倍"
            )

            # 🎯 修复：安全获取玩家数据，确保包含所有必要字段
            player_data = get_player_data_safe(player, %{})

            Logger.info(
              "🎯 [SETTLEMENT] 调用幸运值调整 - 玩家: #{Map.get(player_data, :player_id, "unknown")}"
            )

        # 🎯 使用Calculator模块计算幸运值调整
        calculation_result = calculate_luck_adjustment_on_settlement(
          Map.get(player_data, :luck_value, 500),
          Map.get(player_data, :consecutive_streak, 0),
          win_lose_multiplier,
          is_win,
          player_data,
          base_bet
        )

        # 应用计算结果
        updated_player_data = %{
          player_data |
          luck_value: calculation_result.new_luck_value,
          consecutive_streak: calculation_result.new_consecutive_streak
        }

            # 更新玩家的用户数据
            updated_user = Map.merge(player.user || %{}, updated_player_data)
            %{player | user: updated_user}
          else
            Logger.info("🎯 [SETTLEMENT] 玩家 #{player_id} 无输赢变化，跳过幸运值调整")
            player
          end

        # 更新单个玩家统计
        updated_player = update_game_stats(player_with_luck_adjustment, player_change)

        Map.put(acc, player_id, updated_player)
      end)

    Logger.info("🃏 [TEEN_PATTI_PLAYER] 完成批量更新 #{map_size(updated_players)} 个玩家的游戏统计")
    updated_players
  end

  @doc """
  安全获取玩家数据，确保包含所有必要字段
  """
  defp get_player_data_safe(player, game_context) do
    # 先获取基础数据
    base_data = get_player_data(player, game_context)

    # 🎯 修复：确保 player_id 正确获取
    player_id =
      cond do
        Map.has_key?(base_data, :player_id) and base_data.player_id != nil -> base_data.player_id
        Map.has_key?(base_data, :numeric_id) -> to_string(base_data.numeric_id)
        Map.has_key?(player, :numeric_id) -> to_string(player.numeric_id)
        true -> "unknown"
      end

    # 🎯 修复：确保包含所有新增字段，避免 badkey 错误
    safe_data =
      Map.merge(
        %{
          # 基础字段
          player_id: player_id,
          numeric_id: Map.get(base_data, :numeric_id, Map.get(player, :numeric_id, 0)),
          recharge_count: Map.get(base_data, :recharge_count, 0),
          luck_value: Map.get(base_data, :luck_value, @default_config.default_luck_value),

      # 🎯 新增字段的默认值
      withdraw_ratio_luck_adjusted: false,
      consecutive_streak: 0,

          # 其他字段
          emergency_rescue_status: Map.get(base_data, :emergency_rescue_status, 0),
          luck_adjustment_flags:
            Map.get(base_data, :luck_adjustment_flags, [false, false, false, false, false, false])
        },
        base_data
      )

    Logger.info(
      "🎯 [DEBUG_SAFE_DATA] 玩家 #{player_id} 安全数据: recharge_count=#{Map.get(safe_data, :recharge_count)}, luck_value=#{Map.get(safe_data, :luck_value)}"
    )

    safe_data
  end

  @doc """
  获取玩家积分（兼容函数）
  """
  defp get_player_points(player) do
    if player && Map.has_key?(player, :user) do
      Map.get(player.user, :points, 0)
    else
      0
    end
  end

  @doc """
  判断是否为新玩家首局（基于游戏统计）
  """
  def is_new_player_first_game?(player_data) do
    # 从玩家的游戏统计中获取总局数
    # 支持两种数据结构：直接在player_data中或在player_data.user中
    game_stats =
      case Map.get(player_data, :game_stats) do
        nil ->
          # 如果直接没有game_stats，尝试从user中获取
          user_data = Map.get(player_data, :user, %{})
          Map.get(user_data, :game_stats, %{})

        stats ->
          stats
      end

    total_games = Map.get(game_stats, :total_games, 0)
    total_games == 0
  end

  @doc """
  获取玩家数据（从 TeenPattiDealer 移动过来）
  支持 PlayerData 结构体和普通 Map
  """
  def get_player_data(player, _game_context \\ %{}) do
    # 安全地获取游戏统计数据
    game_stats = get_game_stats_safely(player)

    %{
      total_games: Map.get(game_stats, :total_games, 0),
      max_coins_reached: Map.get(game_stats, :max_coins_reached, 0),
      # 🎯 重构：使用专用函数获取充值次数和幸运值
      recharge_count: get_recharge_count(player),
      recharge_amount: get_field_safely(player, :recharge_amount, 0),
      last_online_time: get_field_safely(player, :last_online_time, DateTime.utc_now()),
      total_coins: get_player_current_coins(player),

      # 🎯 重构：使用专用函数获取幸运值
      luck_value: get_luck_value(player),

      # 🎯 添加其他重要字段
      recharge_total_amount: get_field_safely(player, :recharge_total_amount, 0),
      total_withdraw_amount: get_field_safely(player, :total_withdraw_amount, 0),
      emergency_rescue_status: get_field_safely(player, :emergency_rescue_status, 0),
      luck_adjustment_flags: get_field_safely(player, :luck_adjustment_flags, [false, false, false, false, false, false]),
      withdraw_ratio_luck_adjusted: get_field_safely(player, :withdraw_ratio_luck_adjusted, false),
      consecutive_streak: get_field_safely(player, :consecutive_streak, 0),

      # 添加游戏统计信息，方便调试
      game_stats: game_stats
    }
  end

  @doc """
  安全地获取游戏统计数据
  支持 PlayerData 结构体和普通 Map
  """
  defp get_game_stats_safely(player) do
    cond do
      # 如果是 PlayerData 结构体
      is_struct(player, Cypridina.Teen.GameSystem.PlayerData) ->
        case player.user do
          %{game_stats: game_stats} when is_map(game_stats) -> game_stats
          _ -> %{}
        end

      # 如果是普通 Map 且有 user.game_stats
      is_map(player) ->
        case Map.get(player, :user) do
          %{game_stats: game_stats} when is_map(game_stats) -> game_stats
          _ -> Map.get(player, :game_stats, %{})
        end

      # 其他情况
      true ->
        %{}
    end
  end

  @doc """
  获取玩家当前金币/积分
  支持多种数据结构：PlayerData结构体、state.players中的玩家数据、普通Map
  """
  def get_player_current_coins(player_data) do
    result =
      cond do
        # 如果是 PlayerData 结构体，直接使用 user.total_coins
        is_struct(player_data, Cypridina.Teen.GameSystem.PlayerData) ->
          coins = Map.get(player_data.user, :total_coins, 0)
          Logger.debug("🎯 [GET_COINS] PlayerData结构体 - total_coins: #{coins}")
          coins

        # 如果是普通 Map，优先检查 user.total_coins（state.players中的数据结构）
        is_map(player_data) ->
          cond do
            # 检查是否有 user.total_coins（state.players中的玩家数据）
            Map.has_key?(player_data, :user) and is_map(player_data.user) ->
              coins = Map.get(player_data.user, :total_coins, 0)
              Logger.debug("🎯 [GET_COINS] 普通Map(有user) - user.total_coins: #{coins}")
              coins

            # 检查是否有 points 字段（其他Map数据）
            Map.has_key?(player_data, :points) ->
              coins = Map.get(player_data, :points, 0)
              Logger.debug("🎯 [GET_COINS] 普通Map(有points) - points: #{coins}")
              coins

            # 检查是否有 total_coins 字段（create_player_data创建的数据）
            Map.has_key?(player_data, :total_coins) ->
              coins = Map.get(player_data, :total_coins, 0)
              Logger.debug("🎯 [GET_COINS] 普通Map(有total_coins) - total_coins: #{coins}")
              coins

            # 其他情况
            true ->
              Logger.debug("🎯 [GET_COINS] 普通Map(无相关字段) - 返回0")
              0
          end

        # 其他情况
        true ->
          Logger.debug("🎯 [GET_COINS] 其他情况 - 返回0")
          0
      end

    Logger.debug("🎯 [GET_COINS] 最终返回: #{result}")
    result
  end

  # ==================== 统一字段获取函数 ====================

  @doc """
  获取玩家幸运值
  支持多种数据结构：PlayerData结构体、普通Map等

  ## 示例
  ```elixir
  # 替代 Map.get(player_data, :luck_value, 500)
  luck_value = get_luck_value(player_data)
  ```
  """
  def get_luck_value(data) do
    cond do
      # 如果是 PlayerData 结构体，从 user 字段获取
      is_struct(data, Cypridina.Teen.GameSystem.PlayerData) ->
        Map.get(data.user || %{}, :luck_value, @default_config.default_luck_value)

      # 如果是普通 Map，直接获取
      is_map(data) ->
        Map.get(data, :luck_value, @default_config.default_luck_value)

      # 其他情况
      true ->
        @default_config.default_luck_value
    end
  end

  @doc """
  获取玩家充值次数
  支持多种数据结构：PlayerData结构体、普通Map等

  ## 示例
  ```elixir
  # 替代 Map.get(player_data, :recharge_count, 0)
  recharge_count = get_recharge_count(player_data)
  ```
  """
  def get_recharge_count(data) do
    cond do
      # 如果是 PlayerData 结构体，从 user 字段获取
      is_struct(data, Cypridina.Teen.GameSystem.PlayerData) ->
        Map.get(data.user || %{}, :recharge_count, 0)

      # 如果是普通 Map，直接获取
      is_map(data) ->
        Map.get(data, :recharge_count, 0)

      # 其他情况
      true ->
        0
    end
  end

  @doc """
  从 room_base 获取玩家充值次数（统一数据源）
  """
  def get_player_recharge_count_from_room_base(player) do
    # 调用 room_base 中的私有函数（通过模块调用）
    # 注意：这里需要确保 room_base 的函数是公开的或者通过其他方式访问
    # 🎯 与 room_base.ex 第718行保持一致
    Map.get(player, :recharge_count, 1)
  end

  @doc """
  从 room_base 获取玩家积分（统一数据源）
  """
  def get_player_points_from_room_base(state, player_id) do
    # 这里应该调用 room_base 的 get_player_points 函数
    # 但由于模块结构限制，我们直接实现相同逻辑
    case Map.get(state.players, player_id) do
      nil -> 0
      player -> Map.get(player.user || %{}, :total_coins, 5_000_000)
    end
  end

  # 保留原有的 get_field_safely 函数以兼容现有代码
  defp get_field_safely(player, field, default) do
    cond do
      # 如果是 PlayerData 结构体，从 user 字段获取
      is_struct(player, Cypridina.Teen.GameSystem.PlayerData) ->
        Map.get(player.user || %{}, field, default)

      # 如果是普通 Map，直接获取
      is_map(player) ->
        Map.get(player, field, default)

      # 其他情况
      true ->
        default
    end
  end

  @doc """
  判断是否为新玩家金币没有达80（基于现有余额）
  支持两种调用方式：
  """
  def is_new_player_never_80?(player_data) when is_map(player_data) do
    # 🎯 修改：使用正确的方式获取玩家当前金币
    current_coins = get_player_current_coins(player_data)
    current_coins < @default_config.max_gold_threshold
  end
end
