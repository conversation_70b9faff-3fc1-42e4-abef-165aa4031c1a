defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiDealer do
  @moduledoc """
  Teen Patti发牌模块

  负责智能发牌逻辑，包括：
  - 冤家牌模式：发放容易让用户下大注的牌型组合
  - 收分模式：给机器人发最大牌，玩家发第二大牌
  - 放分模式：给玩家发最大牌，机器人发较小牌
  - 根据库存控制和玩家状态智能调整发牌策略
  """

  require Logger

  alias Cypridina.Teen.GameSystem.Games.TeenPatti.{
    TeenPattiConfig,
    TeenPattiPlayer,
    TeenPattiLogic
  }

  # 发牌模式定义
  @dealing_modes %{
    # 正常发牌
    normal: :normal,
    # 冤家牌模式
    rival_cards: :rival_cards,
    # 收分模式
    collect: :collect,
    # 放分模式
    release: :release
  }

  # 特殊事件配置
  @special_events_config %{
    # 机器人充值等待触发概率
    # 1%概率
    robot_recharge_trigger: 1
  }

  # 冤家牌组合定义
  @rival_card_combinations [
    # 顺子对顺子
    {:sequence, :sequence},
    # 顺子对同花顺
    {:sequence, :pure_sequence},
    # 同花顺对顺子
    {:pure_sequence, :sequence},
    # 同花顺对同花顺
    {:pure_sequence, :pure_sequence},
    # 三条对顺子
    {:trail, :sequence},
    # 三条对同花顺
    {:trail, :pure_sequence},
    # 三条对同花
    {:trail, :color}
  ]

  # 发牌模式定义
  @dealing_modes [:normal, :rival_cards, :collect, :release]

  @doc """
  主发牌函数 - 根据玩家类型和状态智能发牌
  """
  def deal_cards(players, game_context) do
    Logger.info("🎴 [DEALER] 开始发牌 - 玩家数:#{length(players)}")

    # 🎯 重构：发牌模块只负责发牌逻辑，幸运值检查已在 room 层面完成
    # 直接使用传入的玩家数据进行发牌

    # 🎯 关键逻辑：发牌前确定玩家类型，免费玩家必然和机器人匹配
    real_players = Enum.reject(players, fn player -> Map.get(player, :is_robot, false) end)
    robots = Enum.filter(players, fn player -> Map.get(player, :is_robot, false) end)

    Logger.info("🎴 [DEALER] 真实玩家:#{length(real_players)} 机器人:#{length(robots)}")

    # 为每个真实玩家确定类型并发牌
    initial_cards =
      Enum.reduce(real_players, %{}, fn real_player, acc ->
        player_type = get_player_dealing_type(real_player, game_context)

        Logger.info(
          "🎴 [DEALER] 玩家#{real_player.numeric_id} 类型:#{get_player_type_name(player_type)}"
        )

        # 根据玩家类型为玩家和对应的机器人发牌
        room_cards =
          deal_cards_for_player_and_robots(real_player, robots, player_type, game_context)

        Map.merge(acc, room_cards)
      end)

    # 如果没有真实玩家，按默认逻辑发牌
    base_cards =
      if length(real_players) == 0 do
        deal_cards_for_robots_only(robots, game_context)
      else
        initial_cards
      end

    # 🎯 关键修复：应用换牌策略
    Logger.info("🔄 [DEALER] 开始应用换牌策略")
    final_cards = apply_card_exchange_strategy(base_cards, players, game_context)

    Logger.info("🔄 [DEALER] 换牌策略应用完成")
    final_cards
  end

  # 🎯 重构：幸运值检查逻辑已移至 teen_patti_room.ex
  # 发牌模块现在只专注于发牌逻辑

  @doc """
  为玩家和机器人发牌（核心逻辑）
  """
  defp deal_cards_for_player_and_robots(real_player, robots, player_type, game_context) do
    # 生成一副完整的牌并洗牌
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()

    # 获取玩家的概率配置
    player_probabilities = get_card_probabilities(player_type, false)
    robot_probabilities = get_card_probabilities(player_type, true)

    # 为玩家生成牌型
    {player_cards, remaining_deck} = generate_cards_by_probability(player_probabilities, deck)

    Logger.info(
      "🎴 [DEALER] 玩家#{real_player.numeric_id} 生成牌型:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(player_cards))}"
    )

    # 为机器人生成牌型
    {robot_cards_map, _final_deck} =
      Enum.reduce(robots, {%{}, remaining_deck}, fn robot, {acc, current_deck} ->
        {robot_cards, updated_deck} =
          generate_cards_by_probability(robot_probabilities, current_deck)

        Logger.info(
          "🎴 [DEALER] 机器人#{robot.numeric_id} 生成牌型:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(robot_cards))}"
        )

        {Map.put(acc, robot.numeric_id, robot_cards), updated_deck}
      end)

    # 合并玩家和机器人的牌
    Map.put(robot_cards_map, real_player.numeric_id, player_cards)
  end

  @doc """
  只为机器人发牌
  """
  defp deal_cards_for_robots_only(robots, game_context) do
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()
    robot_probabilities = get_card_probabilities(:default, true)

    {robot_cards_map, _} =
      Enum.reduce(robots, {%{}, deck}, fn robot, {acc, current_deck} ->
        {robot_cards, updated_deck} =
          generate_cards_by_probability(robot_probabilities, current_deck)

        {Map.put(acc, robot.numeric_id, robot_cards), updated_deck}
      end)

    robot_cards_map
  end

  @doc """
  中途充值时重新生成机器人牌（玩家牌不动）
  这是处理特殊情况的核心函数：玩家在游戏中充值，机器人牌按概率重新生成
  """
  def recharge_adjust_robot_cards(current_cards, players, recharged_player_id, game_context) do
    Logger.info("🎴 [RECHARGE_ADJUST] 玩家#{recharged_player_id} 中途充值，开始处理")

    # 获取充值玩家和机器人列表
    recharged_player = Enum.find(players, fn p -> p.numeric_id == recharged_player_id end)
    robots = Enum.filter(players, fn player -> Map.get(player, :is_robot, false) end)

    # 重新确定玩家类型（充值后从待充值变为其他类型）
    old_player_type =
      get_player_dealing_type(recharged_player, Map.put(game_context, :ignore_recharge, true))

    new_player_type = get_player_dealing_type(recharged_player, game_context)

    Logger.info(
      "🎴 [RECHARGE_ADJUST] 玩家#{recharged_player_id} 类型变化: #{get_player_type_name(old_player_type)} → #{get_player_type_name(new_player_type)}"
    )

    # 玩家牌保持不变，重新生成机器人牌
    updated_cards =
      regenerate_robot_cards_only(current_cards, robots, new_player_type, recharged_player_id)

    # 处理待充值玩家的特殊事件
    if old_player_type == :waiting_recharge do
      Logger.info("🎴 [RECHARGE_ADJUST] 触发待充值玩家特殊事件处理")
      handle_waiting_recharge_special_event(updated_cards, recharged_player, robots, game_context)
    else
      updated_cards
    end
  end

  @doc """
  只重新生成机器人的牌
  """
  defp regenerate_robot_cards_only(current_cards, robots, player_type, player_id) do
    # 生成新的牌堆（排除玩家已有的牌）
    player_cards = Map.get(current_cards, player_id, [])
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()
    available_deck = remove_cards_from_deck(deck, player_cards)

    # 获取机器人概率配置
    robot_probabilities = get_card_probabilities(player_type, true)

    # 重新生成机器人牌
    {new_robot_cards, _} =
      Enum.reduce(robots, {%{}, available_deck}, fn robot, {acc, current_deck} ->
        {robot_cards, updated_deck} =
          generate_cards_by_probability(robot_probabilities, current_deck)

        Logger.info(
          "🎴 [RECHARGE_ADJUST] 重新生成机器人#{robot.numeric_id} 牌型:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(robot_cards))}"
        )

        {Map.put(acc, robot.numeric_id, robot_cards), updated_deck}
      end)

    # 合并玩家原有牌和机器人新牌
    Map.merge(current_cards, new_robot_cards)
  end

  @doc """
  处理待充值玩家的特殊事件
  """
  defp handle_waiting_recharge_special_event(player_cards, recharged_player, robots, game_context) do
    player_id = recharged_player.numeric_id
    player_cards_list = Map.get(player_cards, player_id, [])
    card_type = TeenPattiLogic.calculate_card_type(player_cards_list)

    Logger.info(
      "🎴 [WAITING_RECHARGE_EVENT] 玩家#{player_id} 牌型:#{TeenPattiLogic.get_card_type_name(card_type)}"
    )

    # 检查是否触发冤家牌事件：当玩家拿到同花或顺子牌时，使用配置的概率给机器人发冤家牌
    if card_type in [:color, :sequence] do
      trigger_probability = TeenPattiConfig.get_rival_cards_trigger_probability()

      if :rand.uniform(100) <= trigger_probability do
        Logger.info(
          "🎴 [WAITING_RECHARGE_EVENT] 玩家#{player_id} 触发冤家牌事件(#{trigger_probability}%概率)"
        )

        # 给机器人发冤家牌，然后根据充值状态处理结果
        rival_cards = generate_rival_cards_for_robots(robots, card_type)
        updated_cards = Map.merge(player_cards, rival_cards)

        # 处理充值玩家的冤家牌事件
        handle_recharged_player_rival_cards(recharged_player, updated_cards, game_context)
      else
        Logger.info(
          "🎴 [WAITING_RECHARGE_EVENT] 玩家#{player_id} 未触发冤家牌事件(#{100 - trigger_probability}%概率)"
        )

        player_cards
      end
    else
      Logger.info("🎴 [WAITING_RECHARGE_EVENT] 玩家#{player_id} 牌型不符合冤家牌触发条件")
      player_cards
    end
  end

  @doc """
  为机器人生成冤家牌
  """
  defp generate_rival_cards_for_robots(robots, player_card_type) do
    # 根据玩家牌型选择对应的冤家牌组合
    rival_combinations =
      case player_card_type do
        :color ->
          [
            # 同花对顺子
            {:color, :sequence},
            # 同花对同花顺
            {:color, :pure_sequence},
            # 同花对三条
            {:color, :trail}
          ]

        :sequence ->
          [
            # 顺子对顺子
            {:sequence, :sequence},
            # 顺子对同花顺
            {:sequence, :pure_sequence},
            # 顺子对三条
            {:sequence, :trail}
          ]

        _ ->
          @rival_card_combinations
      end

    {_, robot_card_type} = Enum.random(rival_combinations)

    Logger.info("🎴 [RIVAL_CARDS] 为机器人生成冤家牌型:#{robot_card_type}")

    # 为每个机器人生成对应的冤家牌型
    Enum.reduce(robots, %{}, fn robot, acc ->
      robot_cards = generate_specific_card_type(robot_card_type, TeenPattiLogic.generate_deck())

      Logger.info(
        "🎴 [RIVAL_CARDS] 机器人#{robot.numeric_id} 生成冤家牌:#{TeenPattiLogic.get_card_type_name(robot_card_type)}"
      )

      Map.put(acc, robot.numeric_id, robot_cards)
    end)
  end

  @doc """
  检查玩家是否已经看牌
  """
  defp player_has_looked_cards?(player_id, game_context) do
    looked_players = Map.get(game_context, :looked_players, [])
    player_id in looked_players
  end

  @doc """
  根据玩家类型生成初始牌型
  """
  defp generate_cards_by_player_type(players, game_context) do
    # 生成一副完整的牌并洗牌
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()

    # 为每个玩家生成牌型
    {player_cards, _remaining_deck} =
      Enum.reduce(players, {%{}, deck}, fn player, {acc, current_deck} ->
        player_type = get_player_dealing_type(player, game_context)
        is_robot = Map.get(player, :is_robot, false)

        # 获取对应的概率配置
        probabilities = get_card_probabilities(player_type, is_robot)

        # 根据概率生成牌型
        {cards, updated_deck} = generate_cards_by_probability(probabilities, current_deck)

        Logger.info(
          "🎴 [DEALER] 玩家#{player.numeric_id}(#{if is_robot, do: "机器人", else: get_player_type_name(player_type)}) 生成牌型:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(cards))}"
        )

        {Map.put(acc, player.numeric_id, cards), updated_deck}
      end)

    player_cards
  end

  @doc """
  获取玩家的发牌类型（使用缓存的玩家类型，同一局游戏内保持不变）
  """
  defp get_player_dealing_type(player, game_context) do
    if Map.get(player, :is_robot, false) do
      :robot
    else
      # 🎯 优化：使用缓存的玩家类型（同一局游戏内保持不变）
      cached_types = Map.get(game_context, :cached_player_types, %{})
      player_type = Map.get(cached_types, player.numeric_id)

      # 如果没有缓存，则动态计算（兜底逻辑）
      player_type =
        if player_type do
          player_type
        else
          Logger.warning("🎴 [DEALER_TYPE] 玩家#{player.numeric_id} 没有缓存类型，动态计算")
          player_data = TeenPattiPlayer.get_player_data(player, game_context)
          TeenPattiPlayer.get_player_type(player_data)
        end

      # 🎯 修复：从房间状态获取幸运值，而不是从PlayerData
      luck_value = get_player_luck_from_room(player, game_context)

      Logger.debug(
        "🎴 [DEALER_TYPE] 玩家#{player.numeric_id} 类型: #{player_type}, 幸运值: #{luck_value}"
      )

      # 将玩家类型映射到发牌类型
      case player_type do
        :new -> :new_player_first_game
        :new_never_80 -> :new_player_never_80
        :waiting_recharge -> :waiting_recharge
        :returning -> :returning_player
        :charged_1 -> :charged_player
        :charged_2 -> :charged_player
        :charged_3 -> :charged_player
        :charged_4_plus -> :charged_player
        :free -> :free_player
        # 默认为免费玩家
        _ -> :free_player
      end
    end
  end

  @doc """
  获取牌型概率配置（统一支持所有玩家类型）
  """
  defp get_card_probabilities(player_type, is_robot) do
    # 根据玩家类型获取对应的配置
    type_config =
      case player_type do
        :new_player_first_game ->
          TeenPattiConfig.get_new_player_first_game_probabilities()

        :new_player_never_80 ->
          TeenPattiConfig.get_new_player_never_80_probabilities()

        :waiting_recharge ->
          TeenPattiConfig.get_waiting_recharge_probabilities()

        # 回头玩家按新玩家处理
        :returning_player ->
          TeenPattiConfig.get_new_player_first_game_probabilities()

        # 充值玩家配置
        :charged_player ->
          TeenPattiConfig.get_charged_player_probabilities()

        # 免费玩家配置
        :free_player ->
          TeenPattiConfig.get_free_player_probabilities()

        # 机器人配置
        :robot ->
          TeenPattiConfig.get_robot_probabilities()

        _ ->
          # 🎯 修复：移除默认配置，所有类型都应该有明确的配置
          Logger.warning("🎴 [DEALER] 未知玩家类型: #{player_type}，使用免费玩家配置")
          TeenPattiConfig.get_free_player_probabilities()
      end

    if is_robot do
      type_config.robot
    else
      type_config.player
    end
  end

  # 使用 TeenPattiPlayer 模块的函数，避免重复代码
  defp is_new_player_first_game?(player_data) do
    TeenPattiPlayer.is_new_player_first_game?(player_data)
  end

  @doc """
  判断是否为回头玩家
  """
  defp is_returning_player?(player_data) do
    last_online_time = Map.get(player_data, :last_online_time, DateTime.utc_now())
    days_offline = DateTime.diff(DateTime.utc_now(), last_online_time, :day)
    days_offline > 3
  end

  @doc """
  获取玩家类型名称（支持所有类型）
  """
  defp get_player_type_name(player_type) do
    case player_type do
      :new_player_first_game -> "新玩家首局"
      :new_player_never_80 -> "新手玩家"
      :waiting_recharge -> "待充值玩家"
      :returning_player -> "回头玩家"
      :charged_player -> "充值玩家"
      :free_player -> "免费玩家"
      :robot -> "机器人"
      _ -> "未知类型(#{player_type})"
    end
  end

  @doc """
  从房间状态获取玩家幸运值
  🎯 修复：确保获取的是房间中实时更新的幸运值
  """
  defp get_player_luck_from_room(player, game_context) do
    if Map.get(player, :is_robot, false) do
      500  # 机器人默认幸运值
    else
      # 从房间状态的teen_patti_players中获取幸运值
      teen_patti_players = Map.get(game_context, :teen_patti_players, %{})
      player_data = Map.get(teen_patti_players, player.numeric_id, %{})
      Map.get(player_data, :luck_value, 500)
    end
  end

  @doc """
  根据概率生成牌型
  """
  defp generate_cards_by_probability(probabilities, deck) do
    # 生成随机数决定牌型
    random_value = :rand.uniform(100)

    # 累积概率计算
    card_type = determine_card_type_by_probability(random_value, probabilities)

    # 生成对应牌型的牌
    cards = generate_specific_card_type(card_type, deck)

    # 从牌堆中移除已使用的牌
    remaining_deck = remove_cards_from_deck(deck, cards)

    {cards, remaining_deck}
  end

  @doc """
  根据概率确定牌型
  """
  defp determine_card_type_by_probability(random_value, probabilities) do
    # 将所有权重转换为整数（乘以10来处理0.5这样的小数）
    scale_factor = 10

    scaled_weights = %{
      high_card: trunc(probabilities.high_card * scale_factor),
      pair: trunc(probabilities.pair * scale_factor),
      color: trunc(probabilities.color * scale_factor),
      sequence: trunc(probabilities.sequence * scale_factor),
      pure_sequence: trunc(probabilities.pure_sequence * scale_factor),
      trail: trunc(probabilities.trail * scale_factor)
    }

    # 计算总权重
    total_weight =
      scaled_weights.high_card + scaled_weights.pair + scaled_weights.color +
        scaled_weights.sequence + scaled_weights.pure_sequence + scaled_weights.trail

    # 生成1到总权重的随机数
    adjusted_random = :rand.uniform(total_weight)

    # 累积概率计算
    card_types_with_weights = [
      {:high_card, scaled_weights.high_card},
      {:pair, scaled_weights.pair},
      {:color, scaled_weights.color},
      {:sequence, scaled_weights.sequence},
      {:pure_sequence, scaled_weights.pure_sequence},
      {:trail, scaled_weights.trail}
    ]

    {result, _} =
      Enum.reduce_while(card_types_with_weights, {nil, 0}, fn {card_type, weight},
                                                              {_acc, cumulative} ->
        new_cumulative = cumulative + weight

        if adjusted_random <= new_cumulative do
          {:halt, {card_type, new_cumulative}}
        else
          {:cont, {nil, new_cumulative}}
        end
      end)

    final_result = result || :high_card
    Logger.info("🎲 [PROBABILITY_DEBUG] 🎯 最终牌型: #{final_result}")
    final_result
  end

  @doc """
  生成特定牌型的牌
  """
  defp generate_specific_card_type(card_type, deck) do
    case card_type do
      :trail -> generate_trail_cards_from_deck(deck)
      :pure_sequence -> generate_pure_sequence_cards_from_deck(deck)
      :sequence -> generate_sequence_cards_from_deck(deck)
      :color -> generate_color_cards_from_deck(deck)
      :pair -> generate_pair_cards_from_deck(deck)
      :high_card -> generate_high_card_cards_from_deck(deck)
    end
  end

  @doc """
  从牌堆生成三条牌
  """
  defp generate_trail_cards_from_deck(deck) do
    # 打乱牌堆，增加随机性
    shuffled_deck = Enum.shuffle(deck)
    cards_by_value = Enum.group_by(shuffled_deck, & &1.value)

    # 找到有至少3张的点数
    trail_values = Enum.filter(cards_by_value, fn {_, cards} -> length(cards) >= 3 end)

    if length(trail_values) > 0 do
      # 随机选择一个三条点数
      {value, available_cards} = Enum.random(trail_values)
      # 随机选择3张牌
      selected_cards = Enum.shuffle(available_cards) |> Enum.take(3)
      Logger.info("🎲 [TRAIL_DEBUG] 生成三条: #{value}")
      selected_cards
    else
      # 如果没有三条，生成高牌
      generate_high_card_cards_from_deck(deck)
    end
  end

  @doc """
  从牌堆生成同花顺牌
  """
  defp generate_pure_sequence_cards_from_deck(deck) do
    # 打乱牌堆，增加随机性
    shuffled_deck = Enum.shuffle(deck)
    cards_by_suit = Enum.group_by(shuffled_deck, & &1.suit)

    # 在每个花色中寻找顺子
    result =
      Enum.find_value(cards_by_suit, fn {suit, suit_cards} ->
        if length(suit_cards) >= 3 do
          # 按点数分组
          cards_by_value = Enum.group_by(suit_cards, & &1.value)
          available_values = Map.keys(cards_by_value) |> Enum.sort()

          # 寻找同花顺组合
          find_pure_sequence_combination(cards_by_value, available_values, suit)
        else
          nil
        end
      end)

    result || generate_sequence_cards_from_deck(deck)
  end

  @doc """
  寻找同花顺组合
  """
  defp find_pure_sequence_combination(cards_by_value, available_values, suit) do
    # 定义所有可能的顺子组合
    possible_sequences = [
      # A-2-3
      [14, 2, 3],
      # 2-3-4
      [2, 3, 4],
      # 3-4-5
      [3, 4, 5],
      # 4-5-6
      [4, 5, 6],
      # 5-6-7
      [5, 6, 7],
      # 6-7-8
      [6, 7, 8],
      # 7-8-9
      [7, 8, 9],
      # 8-9-10
      [8, 9, 10],
      # 9-10-J
      [9, 10, 11],
      # 10-J-Q
      [10, 11, 12],
      # J-Q-K
      [11, 12, 13],
      # Q-K-A
      [12, 13, 14]
    ]

    # 过滤出可用的顺子组合
    valid_sequences =
      Enum.filter(possible_sequences, fn sequence ->
        Enum.all?(sequence, fn value -> Map.has_key?(cards_by_value, value) end)
      end)

    if length(valid_sequences) > 0 do
      # 随机选择一个顺子组合
      selected_sequence = Enum.random(valid_sequences)

      # 为每个点数选择一张牌
      sequence_cards =
        Enum.map(selected_sequence, fn value ->
          available_cards = Map.get(cards_by_value, value, [])
          Enum.random(available_cards)
        end)

      sequence_cards
    else
      nil
    end
  end

  @doc """
  从牌堆生成顺子牌
  """
  defp generate_sequence_cards_from_deck(deck) do
    # 打乱牌堆，增加随机性
    shuffled_deck = Enum.shuffle(deck)

    # 按点数分组，每个点数保留所有牌
    cards_by_value = Enum.group_by(shuffled_deck, & &1.value)
    available_values = Map.keys(cards_by_value) |> Enum.sort()

    # 随机选择一个起始点数来构建顺子
    result = find_random_sequence_combination(cards_by_value, available_values)

    if result do
      result
    else
      generate_high_card_cards_from_deck(deck)
    end
  end

  @doc """
  强制生成顺子牌（当牌堆中没有现成顺子时）
  """
  defp force_generate_sequence_cards(deck) do
    # 定义所有可能的顺子组合（A-2-3, 2-3-4, ..., Q-K-A）
    sequence_patterns = [
      # A-2-3
      [14, 2, 3],
      # 2-3-4
      [2, 3, 4],
      # 3-4-5
      [3, 4, 5],
      # 4-5-6
      [4, 5, 6],
      # 5-6-7
      [5, 6, 7],
      # 6-7-8
      [6, 7, 8],
      # 7-8-9
      [7, 8, 9],
      # 8-9-10
      [8, 9, 10],
      # 9-10-J
      [9, 10, 11],
      # 10-J-Q
      [10, 11, 12],
      # J-Q-K
      [11, 12, 13],
      # Q-K-A
      [12, 13, 14]
    ]

    # 随机选择一个顺子模式
    pattern = Enum.random(sequence_patterns)

    # 为每个点数随机选择花色
    sequence_cards =
      Enum.map(pattern, fn value ->
        # 随机花色
        suit = Enum.random([3, 4, 5, 6])
        %{"color" => suit, "number" => value}
      end)

    Logger.info("🎲 [FORCE_SEQUENCE] 强制生成顺子: #{inspect(pattern)}")
    sequence_cards
  end

  @doc """
  从牌堆生成同花牌
  """
  defp generate_color_cards_from_deck(deck) do
    # 打乱牌堆，增加随机性
    shuffled_deck = Enum.shuffle(deck)
    cards_by_suit = Enum.group_by(shuffled_deck, & &1.suit)

    # 找到有至少3张的花色
    color_suits = Enum.filter(cards_by_suit, fn {_, cards} -> length(cards) >= 3 end)

    if length(color_suits) > 0 do
      # 随机选择一个花色
      {suit, available_cards} = Enum.random(color_suits)

      # 多次尝试生成非顺子的同花
      attempt_generate_non_sequence_color(available_cards, suit, 5)
    else
      generate_high_card_cards_from_deck(deck)
    end
  end

  @doc """
  尝试生成非顺子的同花牌
  """
  defp attempt_generate_non_sequence_color(available_cards, suit, attempts) when attempts > 0 do
    # 随机选择3张牌
    selected_cards = Enum.shuffle(available_cards) |> Enum.take(3)
    values = Enum.map(selected_cards, & &1.value) |> Enum.sort()

    # 检查是否是顺子
    if is_sequence_values?(values) do
      Logger.info("🎲 [COLOR_DEBUG] 检测到同花顺，重新选择 (剩余尝试: #{attempts - 1})")
      # 如果是顺子，重新尝试
      attempt_generate_non_sequence_color(available_cards, suit, attempts - 1)
    else
      Logger.info("🎲 [COLOR_DEBUG] 生成同花: #{suit} - #{inspect(values)}")
      selected_cards
    end
  end

  defp attempt_generate_non_sequence_color(available_cards, _suit, 0) do
    # 如果多次尝试都失败，直接返回3张牌
    Logger.info("🎲 [COLOR_DEBUG] 尝试次数用完，返回随机同花")
    Enum.shuffle(available_cards) |> Enum.take(3)
  end

  @doc """
  从牌堆生成对子牌
  """
  defp generate_pair_cards_from_deck(deck) do
    # 打乱牌堆，增加随机性
    shuffled_deck = Enum.shuffle(deck)
    cards_by_value = Enum.group_by(shuffled_deck, & &1.value)

    # 找到有至少2张的点数
    pair_values = Enum.filter(cards_by_value, fn {_, cards} -> length(cards) >= 2 end)

    if length(pair_values) > 0 do
      # 随机选择一个对子点数
      {pair_value, pair_cards} = Enum.random(pair_values)
      # 随机选择2张作为对子
      pair_selected = Enum.shuffle(pair_cards) |> Enum.take(2)

      # 选择一张不同点数的牌作为第三张
      other_cards = Enum.reject(shuffled_deck, fn card -> card.value == pair_value end)

      if length(other_cards) > 0 do
        third_card = Enum.random(other_cards)
        Logger.info("🎲 [PAIR_DEBUG] 生成对子: #{pair_value}对 + #{third_card.value}")
        pair_selected ++ [third_card]
      else
        pair_selected ++ [Enum.random(shuffled_deck)]
      end
    else
      generate_high_card_cards_from_deck(deck)
    end
  end

  @doc """
  从牌堆生成高牌
  """
  defp generate_high_card_cards_from_deck(deck) do
    # 多次尝试生成真正的高牌（不是对子、顺子、同花等）
    attempt_generate_high_card(deck, 10)
  end

  @doc """
  尝试生成高牌
  """
  defp attempt_generate_high_card(deck, attempts) when attempts > 0 do
    # 随机选择3张牌
    shuffled_deck = Enum.shuffle(deck)
    selected_cards = Enum.take(shuffled_deck, 3)

    values = Enum.map(selected_cards, & &1.value)
    suits = Enum.map(selected_cards, & &1.suit)

    cond do
      # 检查是否有重复点数（对子或三条）
      length(Enum.uniq(values)) != 3 ->
        Logger.info("🎲 [HIGH_CARD_DEBUG] 检测到重复点数，重新选择 (剩余尝试: #{attempts - 1})")
        attempt_generate_high_card(deck, attempts - 1)

      # 检查是否是同花
      length(Enum.uniq(suits)) == 1 ->
        Logger.info("🎲 [HIGH_CARD_DEBUG] 检测到同花，重新选择 (剩余尝试: #{attempts - 1})")
        attempt_generate_high_card(deck, attempts - 1)

      # 检查是否是顺子
      is_sequence_values?(values) ->
        Logger.info("🎲 [HIGH_CARD_DEBUG] 检测到顺子，重新选择 (剩余尝试: #{attempts - 1})")
        attempt_generate_high_card(deck, attempts - 1)

      # 符合高牌条件
      true ->
        Logger.info("🎲 [HIGH_CARD_DEBUG] 生成高牌: #{inspect(Enum.sort(values))}")
        selected_cards
    end
  end

  defp attempt_generate_high_card(deck, 0) do
    # 如果多次尝试都失败，直接返回3张牌
    Logger.info("🎲 [HIGH_CARD_DEBUG] 尝试次数用完，返回随机牌")
    Enum.shuffle(deck) |> Enum.take(3)
  end

  @doc """
  随机寻找顺子组合
  """
  defp find_random_sequence_combination(cards_by_value, available_values) do
    # 定义所有可能的顺子组合
    possible_sequences = [
      # A-2-3
      [14, 2, 3],
      # 2-3-4
      [2, 3, 4],
      # 3-4-5
      [3, 4, 5],
      # 4-5-6
      [4, 5, 6],
      # 5-6-7
      [5, 6, 7],
      # 6-7-8
      [6, 7, 8],
      # 7-8-9
      [7, 8, 9],
      # 8-9-10
      [8, 9, 10],
      # 9-10-J
      [9, 10, 11],
      # 10-J-Q
      [10, 11, 12],
      # J-Q-K
      [11, 12, 13],
      # Q-K-A
      [12, 13, 14]
    ]

    # 过滤出可用的顺子组合（所有点数都在牌堆中）
    valid_sequences =
      Enum.filter(possible_sequences, fn sequence ->
        Enum.all?(sequence, fn value -> Map.has_key?(cards_by_value, value) end)
      end)

    if length(valid_sequences) > 0 do
      # 随机选择一个顺子组合
      selected_sequence = Enum.random(valid_sequences)

      # 为每个点数随机选择一张牌，确保不是同花顺
      build_sequence_cards(selected_sequence, cards_by_value)
    else
      nil
    end
  end

  @doc """
  构建顺子牌，确保不是同花顺
  """
  defp build_sequence_cards(sequence_values, cards_by_value) do
    # 为每个点数选择一张牌
    selected_cards =
      Enum.map(sequence_values, fn value ->
        available_cards = Map.get(cards_by_value, value, [])

        if length(available_cards) > 0 do
          Enum.random(available_cards)
        else
          nil
        end
      end)

    # 检查是否所有牌都选中了
    if Enum.all?(selected_cards, &(&1 != nil)) do
      # 检查是否是同花顺，如果是则重新选择
      if is_pure_sequence?(selected_cards) do
        # 重新选择，确保至少有一张牌的花色不同
        rebuild_sequence_avoid_flush(sequence_values, cards_by_value)
      else
        selected_cards
      end
    else
      nil
    end
  end

  @doc """
  重新构建顺子，避免同花顺
  """
  defp rebuild_sequence_avoid_flush(sequence_values, cards_by_value) do
    # 先选择第一张牌
    [first_value | rest_values] = sequence_values
    first_cards = Map.get(cards_by_value, first_value, [])
    first_card = Enum.random(first_cards)
    first_suit = first_card.suit

    # 为剩余点数选择不同花色的牌
    rest_cards =
      Enum.map(rest_values, fn value ->
        available_cards = Map.get(cards_by_value, value, [])
        # 优先选择不同花色的牌
        different_suit_cards =
          Enum.filter(available_cards, fn card -> card.suit != first_suit end)

        if length(different_suit_cards) > 0 do
          Enum.random(different_suit_cards)
        else
          # 如果没有不同花色的牌，随机选择一张
          Enum.random(available_cards)
        end
      end)

    result = [first_card | rest_cards]

    # 最终检查
    if Enum.all?(result, &(&1 != nil)) do
      result
    else
      nil
    end
  end

  @doc """
  检查数值是否连续
  """
  defp is_consecutive?(values) do
    sorted_values = Enum.sort(values)

    Enum.zip(sorted_values, tl(sorted_values))
    |> Enum.all?(fn {a, b} -> b - a == 1 end)
  end

  @doc """
  检查是否是同花顺
  """
  defp is_pure_sequence?(cards) do
    # 检查是否所有牌都是同一花色
    suits = Enum.map(cards, & &1.suit) |> Enum.uniq()
    length(suits) == 1
  end

  @doc """
  检查数值序列是否连续
  """
  defp is_consecutive_values?(values) when length(values) < 2, do: true

  defp is_consecutive_values?(values) do
    sorted_values = Enum.sort(values)

    Enum.zip(sorted_values, tl(sorted_values))
    |> Enum.all?(fn {a, b} -> b - a == 1 end)
  end

  @doc """
  检查数值是否构成顺子（包括A-2-3的特殊情况）
  """
  defp is_sequence_values?(values) when length(values) != 3, do: false

  defp is_sequence_values?(values) do
    sorted_values = Enum.sort(values)

    # 检查普通顺子
    # 检查A-2-3的特殊情况
    is_consecutive_values?(sorted_values) or
      sorted_values == [2, 3, 14]
  end

  @doc """
  简化：直接标记最强机器人不能弃牌
  """
  defp mark_strongest_robot_no_fold_simple(player_cards, player_id, game_context) do
    # 分析所有机器人的牌力
    robots = get_robots_from_context(game_context)

    robot_analysis =
      Enum.map(robots, fn robot ->
        robot_id = robot.numeric_id
        cards = Map.get(player_cards, robot_id, [])

        if length(cards) == 3 do
          card_type = TeenPattiLogic.calculate_card_type(cards)
          card_strength = TeenPattiLogic.calculate_hand_strength(cards)
          {robot_id, cards, card_type, card_strength}
        else
          {robot_id, [], :high_card, 0}
        end
      end)
      |> Enum.sort_by(fn {_, _, _, strength} -> strength end, :desc)

    # 找到最强的机器人并直接设置no_fold字段
    case List.first(robot_analysis) do
      {strongest_robot_id, _, card_type, strength} when strength > 0 ->
        Logger.info(
          "🎴 [NO_FOLD] 标记最强机器人#{strongest_robot_id}不能弃牌，牌型:#{TeenPattiLogic.get_card_type_name(card_type)} 强度:#{strength}"
        )

        # 🎯 简化：直接通过消息设置机器人的no_fold字段
        room_pid = Map.get(game_context, :room_pid)

        if room_pid do
          send(room_pid, {:set_robot_no_fold, strongest_robot_id, true})
          Logger.info("🎴 [NO_FOLD] 已设置机器人#{strongest_robot_id}的no_fold=true")
        end

      _ ->
        Logger.warning("🎴 [NO_FOLD] 没有找到有效的机器人牌")
    end
  end

  @doc """
  从游戏上下文获取机器人列表
  """
  defp get_robots_from_context(game_context) do
    players = Map.get(game_context, :players, [])
    Enum.filter(players, fn player -> Map.get(player, :is_robot, false) end)
  end

  @doc """
  检查牌是否构成顺子
  """
  defp is_sequence_cards?(cards) do
    values = Enum.map(cards, & &1.value) |> Enum.sort()
    is_consecutive?(values)
  end

  @doc """
  应用换牌策略 - 根据玩家类型执行相应的换牌逻辑
  """
  defp apply_card_exchange_strategy(initial_cards, players, game_context) do
    Logger.info("🔄 [EXCHANGE] 开始应用换牌策略")

    # 分析所有玩家的牌型和强度
    card_analysis = analyze_all_cards(initial_cards, players)
    Logger.info("🔄 [EXCHANGE] 牌型分析完成，共#{length(card_analysis)}个玩家")

    # 获取真实玩家
    real_players = Enum.reject(players, fn player -> Map.get(player, :is_robot, false) end)

    # 为每个真实玩家应用换牌策略
    final_cards =
      Enum.reduce(real_players, initial_cards, fn real_player, current_cards ->
        player_type = get_player_dealing_type(real_player, game_context)

        Logger.info(
          "🔄 [EXCHANGE] 为玩家#{real_player.numeric_id}(#{get_player_type_name(player_type)}) 应用换牌策略"
        )

        apply_player_exchange_strategy(
          current_cards,
          real_player,
          player_type,
          card_analysis,
          game_context
        )
      end)

    # 检查是否有中途充值的玩家需要特殊处理
    recharged_players = get_recharged_players_in_game(game_context)

    if length(recharged_players) > 0 do
      Logger.info("🔄 [EXCHANGE] 发现中途充值玩家，应用特殊换牌策略")
      apply_recharge_exchange_strategy(final_cards, recharged_players, players, game_context)
    else
      final_cards
    end
  end

  @doc """
  分析所有玩家的牌型和强度
  """
  defp analyze_all_cards(player_cards, players) do
    players
    |> Enum.map(fn player ->
      player_id = player.numeric_id
      cards = Map.get(player_cards, player_id, [])

      if length(cards) == 3 do
        card_type = TeenPattiLogic.calculate_card_type(cards)
        card_strength = TeenPattiLogic.calculate_hand_strength(cards)
        {player_id, cards, card_type, card_strength}
      else
        {player_id, [], :high_card, 0}
      end
    end)
    |> Enum.sort_by(fn {_, _, _, strength} -> strength end, :desc)
  end

  @doc """
  获取游戏中充值的玩家
  """
  defp get_recharged_players_in_game(game_context) do
    Map.get(game_context, :recharged_players, [])
  end

  @doc """
  应用中途充值的换牌策略
  """
  defp apply_recharge_exchange_strategy(
         current_cards,
         recharged_players,
         all_players,
         game_context
       ) do
    Enum.reduce(recharged_players, current_cards, fn recharged_player_id, cards ->
      # 检查玩家是否已看牌
      if player_has_looked_cards?(recharged_player_id, game_context) do
        Logger.info("🔄 [RECHARGE_EXCHANGE] 玩家#{recharged_player_id} 已看牌，只调整机器人牌")
        recharge_adjust_robot_cards(cards, all_players, recharged_player_id, game_context)
      else
        Logger.info("🔄 [RECHARGE_EXCHANGE] 玩家#{recharged_player_id} 未看牌，可以调整所有牌")
        # 如果玩家未看牌，可以重新发牌
        recharged_player = Enum.find(all_players, fn p -> p.numeric_id == recharged_player_id end)
        robots = Enum.filter(all_players, fn p -> Map.get(p, :is_robot, false) end)
        new_player_type = get_player_dealing_type(recharged_player, game_context)

        # 重新为这个玩家和机器人发牌
        new_room_cards =
          deal_cards_for_player_and_robots(
            recharged_player,
            robots,
            new_player_type,
            game_context
          )

        Map.merge(cards, new_room_cards)
      end
    end)
  end

  @doc """
  分析牌型强度
  """
  defp analyze_card_strength(player_cards) do
    Enum.map(player_cards, fn {player_id, cards} ->
      card_type = TeenPattiLogic.calculate_card_type(cards)
      strength = get_card_type_strength(card_type)
      {player_id, cards, card_type, strength}
    end)
    |> Enum.sort_by(fn {_, _, _, strength} -> strength end, :desc)
  end

  @doc """
  获取牌型强度值
  """
  defp get_card_type_strength(card_type) do
    case card_type do
      :trail -> 6
      :pure_sequence -> 5
      :sequence -> 4
      :color -> 3
      :pair -> 2
      :high_card -> 1
    end
  end

  @doc """
  为玩家应用换牌策略
  """
  defp apply_player_exchange_strategy(
         player_cards,
         player,
         player_type,
         card_analysis,
         game_context
       ) do
    player_id = player.numeric_id
    player_cards_info = Enum.find(card_analysis, fn {id, _, _, _} -> id == player_id end)

    # 根据玩家类型执行换牌策略（看牌检查在各策略函数内部进行）
    case player_type do
      :new_player_first_game ->
        apply_new_player_first_game_strategy(
          player_cards,
          player_id,
          player_cards_info,
          card_analysis,
          game_context
        )

      :new_player_never_80 ->
        apply_new_player_never_80_strategy(
          player_cards,
          player_id,
          player_cards_info,
          card_analysis,
          game_context
        )

      :waiting_recharge ->
        apply_waiting_recharge_strategy(
          player_cards,
          player_id,
          player_cards_info,
          card_analysis,
          game_context
        )

      :returning_player ->
        apply_returning_player_strategy(
          player_cards,
          player_id,
          player_cards_info,
          card_analysis,
          game_context
        )

      _ ->
        # 其他类型玩家不执行换牌策略
        Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 类型#{player_type}不执行换牌策略")
        player_cards
    end
  end

  @doc """
  新玩家第一局换牌策略
  """
  defp apply_new_player_first_game_strategy(
         player_cards,
         player_id,
         {_, player_hand, player_type, player_strength},
         card_analysis,
         game_context
       ) do
    # 检查玩家是否已经看牌
    if player_has_received_cards?(player_id, game_context) do
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 已看牌，改为调整机器人牌")
      # 如果玩家已看牌，改为重新生成机器人牌来调整输赢
      apply_robot_card_adjustment_strategy(
        player_cards,
        %{numeric_id: player_id},
        :new_player_first_game,
        card_analysis,
        game_context
      )
    else
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 未看牌，执行新玩家首局换牌策略")

      # 获取配置化的概率
      probabilities = TeenPattiConfig.get_new_player_first_game_exchange_probabilities()
      others_analysis = Enum.reject(card_analysis, fn {id, _, _, _} -> id == player_id end)
      strongest_other = List.first(others_analysis)

      cond do
        # 如果玩家拿到的牌是非顺子，并且本局有其他玩家拿到的是顺子、同花顺或三条
        player_type not in [:sequence, :pure_sequence, :trail] and
            has_strong_cards?(others_analysis) ->
          if :rand.uniform(100) <= probabilities.strong_card_exchange do
            Logger.info(
              "🎴 [EXCHANGE] 玩家#{player_id} 非顺子遇强牌，#{probabilities.strong_card_exchange}%概率换最大牌"
            )

            exchange_cards(player_cards, player_id, strongest_other)
          else
            Logger.info(
              "🎴 [EXCHANGE] 玩家#{player_id} 非顺子遇强牌，#{100 - probabilities.strong_card_exchange}%概率不换牌"
            )

            player_cards
          end

        # 如果玩家拿到的是顺子，并且本局有其他玩家拿到的是同花顺或三条
        player_type == :sequence and has_stronger_than_sequence?(others_analysis) ->
          if :rand.uniform(100) <= probabilities.strong_card_exchange do
            Logger.info(
              "🎴 [EXCHANGE] 玩家#{player_id} 顺子遇更强牌，#{probabilities.strong_card_exchange}%概率换最大牌"
            )

            exchange_cards(player_cards, player_id, strongest_other)
          else
            Logger.info(
              "🎴 [EXCHANGE] 玩家#{player_id} 顺子遇更强牌，#{100 - probabilities.strong_card_exchange}%概率不换牌"
            )

            player_cards
          end

        # 如果玩家拿到的是顺子，并且本局有其他玩家拿到更大的顺子，但是没有人拿到同花顺或三条
        player_type == :sequence and has_bigger_sequence_only?(others_analysis, player_strength) ->
          if :rand.uniform(100) <= probabilities.sequence_vs_bigger_sequence do
            Logger.info(
              "🎴 [EXCHANGE] 玩家#{player_id} 顺子遇更大顺子，#{probabilities.sequence_vs_bigger_sequence}%概率换牌"
            )

            exchange_cards(player_cards, player_id, strongest_other)
          else
            Logger.info(
              "🎴 [EXCHANGE] 玩家#{player_id} 顺子遇更大顺子，#{100 - probabilities.sequence_vs_bigger_sequence}%概率不换牌"
            )

            player_cards
          end

        true ->
          Logger.info("🎴 [EXCHANGE] 玩家#{player_id} 不满足换牌条件")
          player_cards
      end
    end
  end

  @doc """
  新玩家金币从来没有达到过80换牌策略
  """
  defp apply_new_player_never_80_strategy(
         player_cards,
         player_id,
         {_, _, player_type, player_strength},
         card_analysis,
         game_context
       ) do
    # 检查玩家是否已经看牌
    if player_has_received_cards?(player_id, game_context) do
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 已看牌，改为调整机器人牌")
      # 如果玩家已看牌，改为重新生成机器人牌来调整输赢
      apply_robot_card_adjustment_strategy(
        player_cards,
        %{numeric_id: player_id},
        :new_player_never_80,
        card_analysis,
        game_context
      )
    else
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 未看牌，执行新玩家从未达到80换牌策略")

      # 获取配置化的概率
      probabilities = TeenPattiConfig.get_new_player_never_80_probabilities()
      strongest_other = List.first(card_analysis)

      # 如果玩家拿到的是顺子及以上的牌，但是不是场上最大的牌
      if player_type in [:sequence, :pure_sequence, :trail, :color] and
           player_strength < elem(strongest_other, 3) do
        if :rand.uniform(100) <= probabilities.strong_but_not_strongest do
          Logger.info(
            "🎴 [EXCHANGE] 玩家#{player_id} 强牌但非最强，#{probabilities.strong_but_not_strongest}%概率换最大牌"
          )

          exchange_cards(player_cards, player_id, strongest_other)
        else
          Logger.info(
            "🎴 [EXCHANGE] 玩家#{player_id} 强牌但非最强，#{100 - probabilities.strong_but_not_strongest}%概率不换牌"
          )

          player_cards
        end
      else
        Logger.info("🎴 [EXCHANGE] 玩家#{player_id} 不满足换牌条件")
        player_cards
      end
    end
  end

  @doc """
  待充值玩家换牌策略
  """
  defp apply_waiting_recharge_strategy(
         player_cards,
         player_id,
         {_, _, _, player_strength},
         card_analysis,
         game_context
       ) do
    # 检查玩家是否已经看牌
    if player_has_received_cards?(player_id, game_context) do
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 已看牌，改为调整机器人牌")
      # 如果玩家已看牌，改为重新生成机器人牌来调整输赢
      apply_robot_card_adjustment_strategy(
        player_cards,
        %{numeric_id: player_id},
        :waiting_recharge,
        card_analysis,
        game_context
      )
    else
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 未看牌，执行待充值玩家换牌策略")

      # 获取配置化的概率
      probabilities = TeenPattiConfig.get_waiting_recharge_probabilities_exchange()
      strongest_card = List.first(card_analysis)

      # 如果玩家拿到的是全场最大的牌
      if elem(strongest_card, 0) == player_id do
        second_strongest = Enum.at(card_analysis, 1)

        if :rand.uniform(100) <= probabilities.strongest_card_exchange do
          Logger.info(
            "🎴 [EXCHANGE] 玩家#{player_id} 最强牌，#{probabilities.strongest_card_exchange}%概率与第二强换牌"
          )

          exchange_cards(player_cards, player_id, second_strongest)
        else
          Logger.info(
            "🎴 [EXCHANGE] 玩家#{player_id} 最强牌，#{100 - probabilities.strongest_card_exchange}%概率不换牌"
          )

          player_cards
        end
      else
        Logger.info("🎴 [EXCHANGE] 玩家#{player_id} 非最强牌，不换牌")
        player_cards
      end
    end
  end

  @doc """
  回头玩家换牌策略（使用与新玩家首局相同的策略）
  """
  defp apply_returning_player_strategy(
         player_cards,
         player_id,
         player_cards_info,
         card_analysis,
         game_context
       ) do
    # 检查玩家是否已经看牌
    if player_has_received_cards?(player_id, game_context) do
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 已看牌，改为调整机器人牌")
      # 如果玩家已看牌，改为重新生成机器人牌来调整输赢
      apply_robot_card_adjustment_strategy(
        player_cards,
        %{numeric_id: player_id},
        :returning_player,
        card_analysis,
        game_context
      )
    else
      Logger.info("🔄 [EXCHANGE] 玩家#{player_id} 未看牌，执行回头玩家换牌策略（同新玩家首局）")
      # 回头玩家使用与新玩家首局相同的策略
      apply_new_player_first_game_strategy(
        player_cards,
        player_id,
        player_cards_info,
        card_analysis,
        game_context
      )
    end
  end

  @doc """
  当玩家已看牌时，通过重新生成机器人牌来调整输赢
  """
  defp apply_robot_card_adjustment_strategy(
         player_cards,
         player,
         player_type,
         card_analysis,
         game_context
       ) do
    player_id = player.numeric_id
    player_cards_list = Map.get(player_cards, player_id, [])
    player_card_type = TeenPattiLogic.calculate_card_type(player_cards_list)
    player_strength = get_card_type_strength(player_card_type)

    Logger.info("🎴 [ROBOT_ADJUST] 玩家#{player_id} 已看牌，重新生成机器人牌调整输赢")

    Logger.info(
      "🎴 [ROBOT_ADJUST] 玩家牌型:#{TeenPattiLogic.get_card_type_name(player_card_type)} 强度:#{player_strength}"
    )

    # 根据玩家类型决定调整策略（使用配置化概率）
    win_probability = TeenPattiConfig.get_robot_adjustment_probability(player_type)
    should_player_win = :rand.uniform(100) <= win_probability

    Logger.info("🎴 [ROBOT_ADJUST] 玩家类型#{player_type}，让玩家赢的概率#{win_probability}%")

    if should_player_win do
      Logger.info("🎴 [ROBOT_ADJUST] 决定让玩家#{player_id}赢，重新生成弱于玩家的机器人牌")
      regenerate_robot_cards_for_player_win(player_cards, player_id, game_context)
    else
      Logger.info("🎴 [ROBOT_ADJUST] 决定让玩家#{player_id}输，重新生成强于玩家的机器人牌")

      updated_cards =
        regenerate_robot_cards_for_player_lose(player_cards, player_id, game_context)

      # 🎯 简化：直接标记最强机器人不能弃牌
      mark_strongest_robot_no_fold_simple(updated_cards, player_id, game_context)

      updated_cards
    end
  end

  @doc """
  检查是否有强牌（顺子、同花顺或三条）
  """
  defp has_strong_cards?(card_analysis) do
    Enum.any?(card_analysis, fn {_, _, card_type, _} ->
      card_type in [:sequence, :pure_sequence, :trail]
    end)
  end

  @doc """
  检查是否有比顺子更强的牌
  """
  defp has_stronger_than_sequence?(card_analysis) do
    Enum.any?(card_analysis, fn {_, _, card_type, _} ->
      card_type in [:pure_sequence, :trail]
    end)
  end

  @doc """
  检查是否只有更大的顺子，没有同花顺或三条
  """
  defp has_bigger_sequence_only?(card_analysis, player_strength) do
    has_bigger_sequence =
      Enum.any?(card_analysis, fn {_, _, card_type, strength} ->
        card_type == :sequence and strength > player_strength
      end)

    no_stronger_types =
      not Enum.any?(card_analysis, fn {_, _, card_type, _} ->
        card_type in [:pure_sequence, :trail]
      end)

    has_bigger_sequence and no_stronger_types
  end

  @doc """
  执行换牌操作
  """
  defp exchange_cards(player_cards, player_id, {target_id, target_cards, _, _}) do
    player_cards_current = Map.get(player_cards, player_id)

    player_cards
    |> Map.put(player_id, target_cards)
    |> Map.put(target_id, player_cards_current)
  end

  @doc """
  从牌堆中移除已使用的牌
  """
  defp remove_cards_from_deck(deck, used_cards) do
    Enum.reject(deck, fn card ->
      Enum.any?(used_cards, fn used_card ->
        card.suit == used_card.suit and card.value == used_card.value
      end)
    end)
  end

  @doc """
  处理特殊事件
  """
  defp process_special_events(player_cards, players, game_context) do
    # 检查机器人充值等待触发
    check_robot_recharge_trigger(players, player_cards, game_context)

    # 检查冤家牌触发
    check_rival_cards_trigger(players, player_cards, game_context)

    player_cards
  end

  @doc """
  检查机器人充值等待触发
  """
  defp check_robot_recharge_trigger(players, player_cards, game_context) do
    bet_rounds = Map.get(game_context, :bet_rounds, 0)

    if bet_rounds > 6 do
      Enum.each(players, fn player ->
        if Map.get(player, :is_robot, false) do
          player_cards_list = Map.get(player_cards, player.numeric_id, [])
          card_type = TeenPattiLogic.calculate_card_type(player_cards_list)

          # 检查是否为10以上的顺子
          if is_high_sequence?(player_cards_list, card_type) do
            if :rand.uniform(100) <= @special_events_config.robot_recharge_trigger do
              Logger.info("🎴 [SPECIAL_EVENT] 机器人#{player.numeric_id} 触发充值等待事件")
              trigger_robot_recharge_waiting(player.numeric_id)
            end
          end
        end
      end)
    end
  end

  @doc """
  检查冤家牌触发
  """
  defp check_rival_cards_trigger(players, player_cards, game_context) do
    real_players = Enum.reject(players, fn player -> Map.get(player, :is_robot, false) end)

    Enum.each(real_players, fn player ->
      player_cards_list = Map.get(player_cards, player.numeric_id, [])
      card_type = TeenPattiLogic.calculate_card_type(player_cards_list)

      # 当玩家拿到同花或顺子牌时，40%概率给机器人发冤家牌
      if card_type in [:color, :sequence] do
        if :rand.uniform(100) <= @special_events_config.rival_cards_trigger do
          Logger.info("🎴 [SPECIAL_EVENT] 玩家#{player.numeric_id} 触发冤家牌事件")
          handle_rival_cards_event(player, player_cards, players, game_context)
        end
      end
    end)
  end

  @doc """
  处理冤家牌事件
  """
  defp handle_rival_cards_event(player, player_cards, players, game_context) do
    # 这里实现冤家牌的具体逻辑
    # 根据玩家是否充值来决定最终结果
    player_data = TeenPattiPlayer.get_player_data(player, game_context)
    has_recharged = Map.get(player_data, :recharge_count, 0) > 0

    if has_recharged do
      handle_recharged_player_rival_cards(player, player_cards, game_context)
    else
      handle_non_recharged_player_rival_cards(player, player_cards, game_context)
    end
  end

  @doc """
  检查是否为10以上的顺子
  """
  defp is_high_sequence?(cards, card_type) do
    if card_type == :sequence do
      min_value = Enum.min_by(cards, & &1.value).value
      min_value >= 10
    else
      false
    end
  end

  @doc """
  触发机器人充值等待
  """
  defp trigger_robot_recharge_waiting(robot_id) do
    # 这里可以发送消息给游戏系统，让机器人进入充值等待状态
    Logger.info("🎴 [ROBOT_EVENT] 机器人#{robot_id} 进入充值等待状态")
  end

  @doc """
  处理充值玩家的冤家牌事件
  """
  defp handle_recharged_player_rival_cards(player, player_cards, game_context) do
    player_id = player.numeric_id
    player_data = TeenPattiPlayer.get_player_data(player, game_context)

    # 获取充值金额和本局预计赢的金额
    recharge_amount = Map.get(player_data, :recharge_amount, 0)
    estimated_win_amount = calculate_estimated_win_amount(player_id, player_cards, game_context)

    Logger.info(
      "🎴 [RIVAL_CARDS] 充值玩家#{player_id} - 充值金额:#{recharge_amount} 预计赢金额:#{estimated_win_amount}"
    )

    # 根据赢的金额和充值金额比例决定结果，使用配置的概率
    win_rates = TeenPattiConfig.get_recharged_player_win_rates()

    win_probability =
      cond do
        # 如果本局玩家赢的金额小于玩家充值金额，就让玩家赢
        estimated_win_amount < recharge_amount ->
          Logger.info("🎴 [RIVAL_CARDS] 赢金额 < 充值金额，#{win_rates.under_recharge_amount}%让玩家赢")
          win_rates.under_recharge_amount

        # 如果本局玩家赢的金额大于等于玩家充值金额，并且小于玩家充值金额的2倍
        estimated_win_amount >= recharge_amount and estimated_win_amount < recharge_amount * 2 ->
          Logger.info("🎴 [RIVAL_CARDS] 充值金额 ≤ 赢金额 < 2倍充值金额，#{win_rates.one_to_two_times}%让玩家赢")
          win_rates.one_to_two_times

        # 如果本局玩家赢的金额大于等于玩家充值金额的2倍以上
        estimated_win_amount >= recharge_amount * 2 ->
          Logger.info("🎴 [RIVAL_CARDS] 赢金额 ≥ 2倍充值金额，#{win_rates.over_two_times}%让玩家赢")
          win_rates.over_two_times

        true ->
          # 默认50%
          50
      end

    # 根据概率决定是否让玩家赢
    if :rand.uniform(100) <= win_probability do
      Logger.info("🎴 [RIVAL_CARDS] 充值玩家#{player_id} 执行让玩家赢的策略")
      adjust_cards_for_player_win(player_cards, player_id, game_context)
    else
      Logger.info("🎴 [RIVAL_CARDS] 充值玩家#{player_id} 执行让玩家输的策略")
      adjust_cards_for_player_lose(player_cards, player_id, game_context)
    end
  end

  @doc """
  处理未充值玩家的冤家牌事件
  """
  defp handle_non_recharged_player_rival_cards(player, player_cards, game_context) do
    player_id = player.numeric_id

    # 检查玩家牌是否是最大的
    card_analysis = analyze_card_strength(player_cards)
    strongest_card = List.first(card_analysis)

    if elem(strongest_card, 0) == player_id do
      # 玩家牌是最大，计算预计赢的金额
      estimated_win_amount = calculate_estimated_win_amount(player_id, player_cards, game_context)

      Logger.info("🎴 [RIVAL_CARDS] 未充值玩家#{player_id} 最大牌，预计赢金额:#{estimated_win_amount}")

      # 使用配置模块的参数
      win_amount_threshold = TeenPattiConfig.get_win_amount_threshold()
      exchange_probability = TeenPattiConfig.get_exchange_probability()

      if estimated_win_amount > win_amount_threshold do
        # 如果本局赢的金额超过阈值，使用配置的概率让玩家输
        if :rand.uniform(100) <= exchange_probability do
          Logger.info(
            "🎴 [RIVAL_CARDS] 未充值玩家#{player_id} 赢金额>#{win_amount_threshold}，#{exchange_probability}%概率让玩家输"
          )

          # 检查玩家是否已看牌
          if player_has_received_cards?(player_id, game_context) do
            Logger.info("🎴 [RIVAL_CARDS] 玩家#{player_id} 已看牌，重新生成机器人牌让玩家输")
            regenerate_robot_cards_for_player_lose(player_cards, player_id, game_context)
          else
            Logger.info("🎴 [RIVAL_CARDS] 玩家#{player_id} 未看牌，直接换牌让玩家输")
            # 找到第二强的机器人牌进行对调
            second_strongest = Enum.at(card_analysis, 1)

            if second_strongest do
              exchange_cards(player_cards, player_id, second_strongest)
            else
              player_cards
            end
          end
        else
          Logger.info(
            "🎴 [RIVAL_CARDS] 未充值玩家#{player_id} 赢金额>#{win_amount_threshold}，#{100 - exchange_probability}%概率不换牌"
          )

          player_cards
        end
      else
        Logger.info("🎴 [RIVAL_CARDS] 未充值玩家#{player_id} 赢金额≤#{win_amount_threshold}，不换牌")
        player_cards
      end
    else
      Logger.info("🎴 [RIVAL_CARDS] 未充值玩家#{player_id} 非最大牌，不换牌")
      player_cards
    end
  end

  @doc """
  计算预计赢的金额（总下注额-个人下注额）
  """
  defp calculate_estimated_win_amount(player_id, player_cards, game_context) do
    # 获取总下注额和玩家个人下注额
    total_bet_amount = Map.get(game_context, :total_bet_amount, 0)
    player_bet_amount = Map.get(game_context, :player_bet_amounts, %{}) |> Map.get(player_id, 0)

    estimated_win = total_bet_amount - player_bet_amount
    # 确保不为负数
    max(estimated_win, 0)
  end

  @doc """
  调整牌型让玩家赢 - 通过重新生成机器人牌实现
  """
  defp adjust_cards_for_player_win(player_cards, player_id, game_context) do
    Logger.info("🎴 [ADJUST_WIN] ========== 开始调整让玩家#{player_id}赢 ==========")

    # 检查玩家是否已收到牌数据
    has_received = player_has_received_cards?(player_id, game_context)
    Logger.info("🎴 [ADJUST_WIN] 玩家#{player_id} 是否已收到牌数据: #{has_received}")

    result =
      if has_received do
        Logger.info("🎴 [ADJUST_WIN] 策略: 玩家牌保持不变，重新生成机器人牌")
        regenerate_robot_cards_for_player_win(player_cards, player_id, game_context)
      else
        Logger.info("🎴 [ADJUST_WIN] 策略: 直接交换玩家和机器人牌")
        exchange_cards_for_player_win(player_cards, player_id)
      end

    Logger.info("🎴 [ADJUST_WIN] ========== 调整完成 ==========")
    result
  end

  @doc """
  调整牌型让玩家输 - 通过重新生成机器人牌实现
  """
  defp adjust_cards_for_player_lose(player_cards, player_id, game_context) do
    # 检查玩家是否已收到牌数据
    if player_has_received_cards?(player_id, game_context) do
      Logger.info("🎴 [ADJUST_LOSE] 玩家#{player_id} 已收到牌数据，只调整机器人牌")
      regenerate_robot_cards_for_player_lose(player_cards, player_id, game_context)
    else
      Logger.info("🎴 [ADJUST_LOSE] 玩家#{player_id} 未收到牌数据，可以直接换牌")
      # 如果玩家还没收到牌数据，可以直接交换
      exchange_cards_for_player_lose(player_cards, player_id)
    end
  end

  @doc """
  检查玩家是否已收到牌数据（通过看牌状态判断）
  """
  defp player_has_received_cards?(player_id, game_context) do
    # 使用游戏中的看牌状态来判断玩家是否已经收到牌
    player_seen_status = Map.get(game_context, :player_seen_status, %{})
    Map.has_key?(player_seen_status, player_id)
  end

  @doc """
  重新生成机器人牌让玩家赢
  优先生成同类型但比玩家弱的牌，如果没有同类型就生成比玩家弱的其他牌型
  """
  defp regenerate_robot_cards_for_player_win(player_cards, player_id, game_context) do
    player_cards_list = Map.get(player_cards, player_id, [])
    player_card_type = TeenPattiLogic.calculate_card_type(player_cards_list)
    player_strength = get_card_type_strength(player_card_type)

    Logger.info(
      "🎴 [REGEN_WIN] 玩家#{player_id} 牌型:#{TeenPattiLogic.get_card_type_name(player_card_type)} 强度:#{player_strength}"
    )

    # 获取机器人列表
    robot_ids = get_robot_ids_from_cards(player_cards, player_id)

    # 收集所有机器人的旧牌，准备放回牌堆
    old_robot_cards =
      Enum.flat_map(robot_ids, fn robot_id ->
        Map.get(player_cards, robot_id, [])
      end)

    # 生成可用牌堆（排除玩家的牌，但包含机器人的旧牌）
    available_deck = generate_available_deck_with_old_cards(player_cards_list, old_robot_cards)

    Logger.info("🎴 [REGEN_WIN] 可用牌堆大小: #{length(available_deck)}")

    # 为每个机器人生成比玩家弱的牌
    {new_robot_cards, _} =
      Enum.reduce(robot_ids, {%{}, available_deck}, fn robot_id, {acc, current_deck} ->
        {weaker_cards, updated_deck} =
          generate_weaker_cards_than_player_smart(player_card_type, player_strength, current_deck)

        Logger.info(
          "🎴 [REGEN_WIN] 机器人#{robot_id} 生成弱牌:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(weaker_cards))}"
        )

        {Map.put(acc, robot_id, weaker_cards), updated_deck}
      end)

    # 合并玩家原有牌和机器人新牌
    Map.merge(player_cards, new_robot_cards)
  end

  @doc """
  重新生成机器人牌让玩家输
  优先生成同类型但比玩家强的牌，如果没有同类型就生成比玩家强的其他牌型
  """
  defp regenerate_robot_cards_for_player_lose(player_cards, player_id, game_context) do
    player_cards_list = Map.get(player_cards, player_id, [])
    player_card_type = TeenPattiLogic.calculate_card_type(player_cards_list)
    player_strength = get_card_type_strength(player_card_type)

    Logger.info(
      "🎴 [REGEN_LOSE] 玩家#{player_id} 牌型:#{TeenPattiLogic.get_card_type_name(player_card_type)} 强度:#{player_strength}"
    )

    # 获取机器人列表
    robot_ids = get_robot_ids_from_cards(player_cards, player_id)

    # 收集所有机器人的旧牌，准备放回牌堆
    old_robot_cards =
      Enum.flat_map(robot_ids, fn robot_id ->
        Map.get(player_cards, robot_id, [])
      end)

    # 生成可用牌堆（排除玩家的牌，但包含机器人的旧牌）
    available_deck = generate_available_deck_with_old_cards(player_cards_list, old_robot_cards)

    Logger.info("🎴 [REGEN_LOSE] 可用牌堆大小: #{length(available_deck)}")

    # 为至少一个机器人生成比玩家强的牌
    {new_robot_cards, _} =
      Enum.reduce(robot_ids, {%{}, available_deck}, fn robot_id, {acc, current_deck} ->
        # 第一个机器人生成强牌，其他机器人生成正常牌
        if map_size(acc) == 0 do
          {stronger_cards, updated_deck} =
            generate_stronger_cards_than_player_smart(
              player_card_type,
              player_strength,
              current_deck
            )

          Logger.info(
            "🎴 [REGEN_LOSE] 机器人#{robot_id} 生成强牌:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(stronger_cards))}"
          )

          {Map.put(acc, robot_id, stronger_cards), updated_deck}
        else
          {normal_cards, updated_deck} = generate_normal_robot_cards(current_deck)

          Logger.info(
            "🎴 [REGEN_LOSE] 机器人#{robot_id} 生成正常牌:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(normal_cards))}"
          )

          {Map.put(acc, robot_id, normal_cards), updated_deck}
        end
      end)

    # 合并玩家原有牌和机器人新牌
    Map.merge(player_cards, new_robot_cards)
  end

  @doc """
  获取机器人ID列表
  """
  defp get_robot_ids_from_cards(player_cards, exclude_player_id) do
    Map.keys(player_cards) |> Enum.reject(fn id -> id == exclude_player_id end)
  end

  @doc """
  生成可用牌堆（排除已使用的牌）
  """
  defp generate_available_deck(used_cards) do
    full_deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()
    remove_cards_from_deck(full_deck, used_cards)
  end

  @doc """
  生成可用牌堆（排除玩家牌，但包含机器人旧牌）
  这样可以实现把旧牌放回牌堆的效果
  """
  defp generate_available_deck_with_old_cards(player_cards, old_robot_cards) do
    full_deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()
    # 只排除玩家的牌，机器人的旧牌会自然包含在牌堆中
    remove_cards_from_deck(full_deck, player_cards)
  end

  @doc """
  生成比玩家弱的牌
  """
  defp generate_weaker_cards_than_player(player_strength, available_deck) do
    # 根据玩家强度选择更弱的牌型
    target_card_types =
      case player_strength do
        # 玩家三条，机器人可以是其他任何牌型
        6 -> [:pure_sequence, :sequence, :color, :pair, :high_card]
        # 玩家同花顺，机器人顺子以下
        5 -> [:sequence, :color, :pair, :high_card]
        # 玩家顺子，机器人同花以下
        4 -> [:color, :pair, :high_card]
        # 玩家同花，机器人对子以下
        3 -> [:pair, :high_card]
        # 玩家对子，机器人高牌
        2 -> [:high_card]
        # 默认高牌
        _ -> [:high_card]
      end

    # 随机选择一个较弱的牌型
    target_type = Enum.random(target_card_types)

    Logger.info("🎴 [WEAKER_CARDS] 玩家强度:#{player_strength} 生成目标牌型:#{target_type}")

    # 生成对应牌型
    cards = generate_specific_card_type(target_type, available_deck)
    remaining_deck = remove_cards_from_deck(available_deck, cards)

    {cards, remaining_deck}
  end

  @doc """
  智能生成比玩家弱的牌（优先同类型）
  """
  defp generate_weaker_cards_than_player_smart(player_card_type, player_strength, available_deck) do
    Logger.info("🎴 [WEAKER_SMART] 玩家牌型:#{player_card_type} 强度:#{player_strength}")

    # 首先尝试生成同类型但更弱的牌
    {cards, remaining_deck} =
      case player_card_type do
        :trail ->
          # 玩家是三条，尝试生成更小的三条，如果不行就生成同花顺
          try_generate_weaker_same_type(:trail, player_strength, available_deck) ||
            {generate_specific_card_type(:pure_sequence, available_deck), available_deck}

        :pure_sequence ->
          # 玩家是同花顺，尝试生成更小的同花顺，如果不行就生成同花
          try_generate_weaker_same_type(:pure_sequence, player_strength, available_deck) ||
            {generate_specific_card_type(:color, available_deck), available_deck}

        :color ->
          # 玩家是同花，尝试生成更小的同花，如果不行就生成顺子
          try_generate_weaker_same_type(:color, player_strength, available_deck) ||
            {generate_specific_card_type(:sequence, available_deck), available_deck}

        :sequence ->
          # 玩家是顺子，尝试生成更小的顺子，如果不行就生成对子
          try_generate_weaker_same_type(:sequence, player_strength, available_deck) ||
            {generate_specific_card_type(:pair, available_deck), available_deck}

        :pair ->
          # 玩家是对子，尝试生成更小的对子，如果不行就生成高牌
          try_generate_weaker_same_type(:pair, player_strength, available_deck) ||
            {generate_specific_card_type(:high_card, available_deck), available_deck}

        :high_card ->
          # 玩家是高牌，生成更小的高牌
          try_generate_weaker_same_type(:high_card, player_strength, available_deck) ||
            {generate_specific_card_type(:high_card, available_deck), available_deck}

        _ ->
          # 默认生成高牌
          {generate_specific_card_type(:high_card, available_deck), available_deck}
      end

    final_cards = if is_tuple(cards), do: elem(cards, 0), else: cards

    final_deck =
      if is_tuple(cards),
        do: elem(cards, 1),
        else: remove_cards_from_deck(available_deck, final_cards)

    Logger.info(
      "🎴 [WEAKER_SMART] 生成牌型:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(final_cards))}"
    )

    {final_cards, final_deck}
  end

  @doc """
  生成比玩家强的牌
  """
  defp generate_stronger_cards_than_player(player_strength, available_deck) do
    # 根据玩家强度选择更强的牌型
    target_card_types =
      case player_strength do
        # 玩家高牌，机器人可以是其他任何牌型
        1 -> [:trail, :pure_sequence, :sequence, :color, :pair]
        # 玩家对子，机器人同花以上
        2 -> [:trail, :pure_sequence, :sequence, :color]
        # 玩家同花，机器人顺子以上
        3 -> [:trail, :pure_sequence, :sequence]
        # 玩家顺子，机器人同花顺以上
        4 -> [:trail, :pure_sequence]
        # 玩家同花顺，机器人三条
        5 -> [:trail]
        # 默认三条
        _ -> [:trail]
      end

    # 随机选择一个较强的牌型
    target_type = Enum.random(target_card_types)

    Logger.info("🎴 [STRONGER_CARDS] 玩家强度:#{player_strength} 生成目标牌型:#{target_type}")

    # 生成对应牌型
    cards = generate_specific_card_type(target_type, available_deck)
    remaining_deck = remove_cards_from_deck(available_deck, cards)

    {cards, remaining_deck}
  end

  @doc """
  智能生成比玩家强的牌（优先同类型）
  """
  defp generate_stronger_cards_than_player_smart(
         player_card_type,
         player_strength,
         available_deck
       ) do
    Logger.info("🎴 [STRONGER_SMART] 玩家牌型:#{player_card_type} 强度:#{player_strength}")

    # 首先尝试生成同类型但更强的牌
    {cards, remaining_deck} =
      case player_card_type do
        :high_card ->
          # 玩家是高牌，尝试生成更大的高牌，如果不行就生成对子
          try_generate_stronger_same_type(:high_card, player_strength, available_deck) ||
            {generate_specific_card_type(:pair, available_deck), available_deck}

        :pair ->
          # 玩家是对子，尝试生成更大的对子，如果不行就生成同花
          try_generate_stronger_same_type(:pair, player_strength, available_deck) ||
            {generate_specific_card_type(:color, available_deck), available_deck}

        :color ->
          # 玩家是同花，尝试生成更大的同花，如果不行就生成顺子
          try_generate_stronger_same_type(:color, player_strength, available_deck) ||
            {generate_specific_card_type(:sequence, available_deck), available_deck}

        :sequence ->
          # 玩家是顺子，尝试生成更大的顺子，如果不行就生成同花顺
          try_generate_stronger_same_type(:sequence, player_strength, available_deck) ||
            {generate_specific_card_type(:pure_sequence, available_deck), available_deck}

        :pure_sequence ->
          # 玩家是同花顺，尝试生成更大的同花顺，如果不行就生成三条
          try_generate_stronger_same_type(:pure_sequence, player_strength, available_deck) ||
            {generate_specific_card_type(:trail, available_deck), available_deck}

        :trail ->
          # 玩家是三条，尝试生成更大的三条
          try_generate_stronger_same_type(:trail, player_strength, available_deck) ||
            {generate_specific_card_type(:trail, available_deck), available_deck}

        _ ->
          # 默认生成三条
          {generate_specific_card_type(:trail, available_deck), available_deck}
      end

    final_cards = if is_tuple(cards), do: elem(cards, 0), else: cards

    final_deck =
      if is_tuple(cards),
        do: elem(cards, 1),
        else: remove_cards_from_deck(available_deck, final_cards)

    Logger.info(
      "🎴 [STRONGER_SMART] 生成牌型:#{TeenPattiLogic.get_card_type_name(TeenPattiLogic.calculate_card_type(final_cards))}"
    )

    {final_cards, final_deck}
  end

  @doc """
  尝试生成同类型但更弱的牌
  """
  defp try_generate_weaker_same_type(card_type, player_strength, available_deck) do
    # 这里可以实现更精细的同类型牌生成逻辑
    # 暂时返回 nil，表示无法生成同类型的更弱牌
    nil
  end

  @doc """
  尝试生成同类型但更强的牌
  """
  defp try_generate_stronger_same_type(card_type, player_strength, available_deck) do
    # 这里可以实现更精细的同类型牌生成逻辑
    # 暂时返回 nil，表示无法生成同类型的更强牌
    nil
  end

  @doc """
  生成正常机器人牌
  """
  defp generate_normal_robot_cards(available_deck) do
    # 使用默认机器人概率生成牌
    robot_probabilities = get_card_probabilities(:default, true)
    generate_cards_by_probability(robot_probabilities, available_deck)
  end

  @doc """
  直接交换牌让玩家赢（玩家未收到牌数据时使用）
  """
  defp exchange_cards_for_player_win(player_cards, player_id) do
    card_analysis = analyze_card_strength(player_cards)
    player_card_info = Enum.find(card_analysis, fn {id, _, _, _} -> id == player_id end)

    if player_card_info do
      {_, _, _, player_strength} = player_card_info

      # 找到比玩家强的牌
      stronger_cards =
        Enum.filter(card_analysis, fn {id, _, _, strength} ->
          id != player_id and strength > player_strength
        end)

      if length(stronger_cards) > 0 do
        strongest_robot = List.first(stronger_cards)
        Logger.info("🎴 [EXCHANGE_WIN] 玩家#{player_id} 与机器人#{elem(strongest_robot, 0)} 换牌")
        exchange_cards(player_cards, player_id, strongest_robot)
      else
        Logger.info("🎴 [EXCHANGE_WIN] 玩家#{player_id} 已是最强牌，无需调整")
        player_cards
      end
    else
      player_cards
    end
  end

  @doc """
  直接交换牌让玩家输（玩家未收到牌数据时使用）
  """
  defp exchange_cards_for_player_lose(player_cards, player_id) do
    card_analysis = analyze_card_strength(player_cards)
    player_card_info = Enum.find(card_analysis, fn {id, _, _, _} -> id == player_id end)

    if player_card_info do
      {_, _, _, player_strength} = player_card_info

      # 找到比玩家弱的牌
      weaker_cards =
        Enum.filter(card_analysis, fn {id, _, _, strength} ->
          id != player_id and strength < player_strength
        end)

      if length(weaker_cards) > 0 do
        weakest_robot =
          List.last(Enum.sort_by(weaker_cards, fn {_, _, _, strength} -> strength end))

        Logger.info("🎴 [EXCHANGE_LOSE] 玩家#{player_id} 与机器人#{elem(weakest_robot, 0)} 换牌")
        exchange_cards(player_cards, player_id, weakest_robot)
      else
        Logger.info("🎴 [EXCHANGE_LOSE] 玩家#{player_id} 已是最弱牌，无需调整")
        player_cards
      end
    else
      player_cards
    end
  end

  @doc """
  确定发牌模式
  """
  defp determine_dealing_mode(players, game_context) do
    stock_status = Map.get(game_context, :stock_status, :normal)
    stock_limits = Map.get(game_context, :stock_limits, %{})
    current_stock = Map.get(game_context, :current_stock, 0)

    # 检查是否在安全区间
    if TeenPattiConfig.in_safe_zone?(current_stock, stock_limits) do
      # 在安全区间，检查是否触发特殊模式
      cond do
        TeenPattiConfig.should_trigger_rival_cards?() -> :rival_cards
        has_special_card_trigger?(players, game_context) -> :rival_cards
        true -> :normal
      end
    else
      # 不在安全区间，根据库存状态决定
      cond do
        TeenPattiConfig.should_trigger_collect_mode?(current_stock, stock_limits) -> :collect
        TeenPattiConfig.should_trigger_release_mode?(current_stock, stock_limits) -> :release
        true -> :normal
      end
    end
  end

  @doc """
  检查是否有特殊牌型触发条件
  """
  defp has_special_card_trigger?(players, game_context) do
    # 检查玩家是否拿到同花或顺子，触发冤家牌
    player_cards = Map.get(game_context, :player_cards, %{})

    Enum.any?(players, fn player ->
      if not Map.get(player, :is_robot, false) do
        cards = Map.get(player_cards, player.numeric_id, [])

        if length(cards) > 0 do
          card_type = TeenPattiLogic.calculate_card_type(cards)
          card_type in [:sequence, :color, :pure_sequence]
        else
          false
        end
      else
        false
      end
    end)
  end

  @doc """
  正常发牌 - 根据玩家幸运值和类型发牌
  """
  defp deal_normal_cards(players, game_context) do
    Logger.info("🎴 [TEEN_PATTI_DEALER] 执行正常发牌，玩家数量: #{length(players)}")

    # 生成一副完整的牌并洗牌
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()

    # 🎯 修复：使用同一副牌为所有玩家发牌，确保不重复
    {player_cards, _remaining_deck} =
      Enum.reduce(players, {%{}, deck}, fn player, {acc, current_deck} ->
        # 根据玩家类型和幸运值决定发牌策略
        {cards, updated_deck} =
          if Map.get(player, :is_robot, false) do
            # 机器人使用默认概率发牌
            deal_cards_from_deck(current_deck, 500)
          else
            # 真实玩家根据其状态和幸运值生成牌
            player_data = TeenPattiPlayer.get_player_data(player, game_context)
            player_config = TeenPattiPlayer.get_player_config(player_data)
            luck_value = Map.get(player_config, :luck_value, 500)

            # 🎯 新规则：根据是否应该使用幸运值来决定发牌方式
            if TeenPattiPlayer.should_use_luck_value?(player_data) do
              # 充值玩家：使用幸运值发牌
              deal_cards_from_deck(current_deck, luck_value)
            else
              # 免费玩家：根据策略发牌
              case player_config.player_type do
                :new -> deal_new_player_cards(current_deck)
                :new_never_80 -> deal_new_player_never_80_cards(current_deck)
                :waiting_recharge -> deal_waiting_recharge_cards(current_deck)
                :returning -> deal_new_player_cards(current_deck)
                # 默认免费玩家策略
                _ -> deal_free_player_cards(current_deck)
              end
            end
          end

        {Map.put(acc, player.numeric_id, cards), updated_deck}
      end)

    player_cards
  end

  @doc """
  冤家牌发牌 - 发放容易让用户下大注的牌型组合
  """
  defp deal_rival_cards(players, game_context) do
    Logger.info("🎴 [TEEN_PATTI_DEALER] 执行冤家牌发牌")

    # 选择冤家牌组合
    {player_card_type, robot_card_type} = Enum.random(@rival_card_combinations)

    # 分离真实玩家和机器人
    {real_players, robots} =
      Enum.split_with(players, fn player ->
        not Map.get(player, :is_robot, false)
      end)

    player_cards = %{}

    # 为真实玩家生成指定牌型
    player_cards =
      Enum.reduce(real_players, player_cards, fn player, acc ->
        cards = generate_specific_card_type(player_card_type, game_context)

        Logger.info(
          "🎴 [TEEN_PATTI_DEALER] 玩家 #{player.numeric_id} 冤家牌: #{inspect(player_card_type)}"
        )

        Map.put(acc, player.numeric_id, cards)
      end)

    # 为机器人生成对应的冤家牌型
    player_cards =
      Enum.reduce(robots, player_cards, fn robot, acc ->
        cards = generate_specific_card_type(robot_card_type, game_context)

        Logger.info(
          "🎴 [TEEN_PATTI_DEALER] 机器人 #{robot.numeric_id} 冤家牌: #{inspect(robot_card_type)}"
        )

        Map.put(acc, robot.numeric_id, cards)
      end)

    player_cards
  end

  @doc """
  收分模式发牌 - 给机器人发最大牌，玩家发第二大牌
  """
  defp deal_collect_mode_cards(players, game_context) do
    Logger.info("🎴 [TEEN_PATTI_DEALER] 执行收分模式发牌")

    # 分离真实玩家和机器人
    {real_players, robots} =
      Enum.split_with(players, fn player ->
        not Map.get(player, :is_robot, false)
      end)

    player_cards = %{}

    # 为机器人生成最强牌型
    player_cards =
      Enum.reduce(robots, player_cards, fn robot, acc ->
        cards = generate_strong_cards_for_collect_mode(game_context)
        Logger.info("🎴 [TEEN_PATTI_DEALER] 机器人 #{robot.numeric_id} 收分模式强牌")
        Map.put(acc, robot.numeric_id, cards)
      end)

    # 为真实玩家生成次强牌型
    player_cards =
      Enum.reduce(real_players, player_cards, fn player, acc ->
        cards = generate_second_strong_cards_for_collect_mode(game_context)
        Logger.info("🎴 [TEEN_PATTI_DEALER] 玩家 #{player.numeric_id} 收分模式次强牌")
        Map.put(acc, player.numeric_id, cards)
      end)

    player_cards
  end

  @doc """
  放分模式发牌 - 给玩家发最大牌，机器人发较小牌
  """
  defp deal_release_mode_cards(players, game_context) do
    Logger.info("🎴 [TEEN_PATTI_DEALER] 执行放分模式发牌")

    # 分离真实玩家和机器人
    {real_players, robots} =
      Enum.split_with(players, fn player ->
        not Map.get(player, :is_robot, false)
      end)

    player_cards = %{}

    # 为真实玩家生成最强牌型
    player_cards =
      Enum.reduce(real_players, player_cards, fn player, acc ->
        cards = generate_strong_cards_for_release_mode(game_context)
        Logger.info("🎴 [TEEN_PATTI_DEALER] 玩家 #{player.numeric_id} 放分模式强牌")
        Map.put(acc, player.numeric_id, cards)
      end)

    # 为机器人生成较弱牌型
    player_cards =
      Enum.reduce(robots, player_cards, fn robot, acc ->
        cards = generate_weak_cards_for_release_mode(game_context)
        Logger.info("🎴 [TEEN_PATTI_DEALER] 机器人 #{robot.numeric_id} 放分模式弱牌")
        Map.put(acc, robot.numeric_id, cards)
      end)

    player_cards
  end

  @doc """
  根据玩家状态生成牌
  """
  defp generate_cards_for_player(player, game_context, deck) do
    if Map.get(player, :is_robot, false) do
      # 机器人使用默认概率（幸运值500）
      generate_cards_by_probability(500, deck)
    else
      # 真实玩家根据其状态和幸运值生成牌
      # 优先从player.user中获取已更新的数据
      player_data =
        case Map.get(player, :user) do
          nil -> TeenPattiPlayer.get_player_data(player, game_context)
          user_data -> user_data
        end

      player_config = TeenPattiPlayer.get_player_config(player_data)

      # 🎯 新规则：根据是否应该使用幸运值来决定发牌方式
      if TeenPattiPlayer.should_use_luck_value?(player_data) do
        # 充值玩家：使用幸运值发牌
        generate_cards_by_probability(player_config.luck_value, deck)
      else
        # 免费玩家：根据策略发牌
        case player_config.player_type do
          :new -> generate_new_player_first_game_cards(deck)
          :new_never_80 -> generate_new_player_never_80_cards(deck)
          :waiting_recharge -> generate_waiting_recharge_cards(deck)
          # 按新玩家处理
          :returning -> generate_new_player_first_game_cards(deck)
          # 默认免费玩家策略
          _ -> generate_free_player_cards(deck)
        end
      end
    end
  end

  @doc """
  生成免费玩家牌型（使用策略概率）
  """
  defp generate_free_player_cards(deck) do
    # 获取免费玩家概率配置
    probabilities = TeenPattiConfig.get_free_player_probabilities().player
    generate_cards_by_probability_config(probabilities, deck)
  end

  @doc """
  根据概率配置生成牌型（用于策略发牌）
  """
  defp generate_cards_by_probability_config(probabilities, deck) do
    # 计算总权重
    total_weight = Enum.sum(Map.values(probabilities))
    random_value = :rand.uniform(total_weight)

    # 确定牌型
    card_type = determine_card_type_by_weight(probabilities, random_value, 0)

    # 从牌堆中取出符合牌型的牌
    case find_cards_of_type(deck, card_type) do
      {cards, remaining_deck} when length(cards) == 3 ->
        {cards, remaining_deck}

      _ ->
        # 如果找不到合适的牌型，随机取3张
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  @doc """
  生成新玩家第一局牌
  """
  defp generate_new_player_first_game_cards(deck) do
    probabilities = TeenPattiConfig.get_new_player_first_game_probabilities().player
    generate_cards_by_specific_probability(probabilities, deck)
  end

  @doc """
  生成新玩家从未达到80金币的牌
  """
  defp generate_new_player_never_80_cards(deck) do
    probabilities = TeenPattiConfig.get_new_player_never_80_probabilities().player
    generate_cards_by_specific_probability(probabilities, deck)
  end

  @doc """
  生成待充值玩家的牌
  """
  defp generate_waiting_recharge_cards(deck) do
    probabilities = TeenPattiConfig.get_waiting_recharge_probabilities().player
    generate_cards_by_specific_probability(probabilities, deck)
  end

  @doc """
  根据具体概率生成牌
  """
  defp generate_cards_by_specific_probability(probabilities, deck) do
    # 计算总权重
    total_weight = Enum.sum(Map.values(probabilities))

    # 随机选择牌型
    random_value = :rand.uniform(total_weight)

    card_type = determine_card_type_by_weight(probabilities, random_value, 0)

    # 生成对应牌型的牌
    generate_specific_card_type(card_type, %{deck: deck})
  end

  @doc """
  根据权重确定牌型
  """
  defp determine_card_type_by_weight(probabilities, target_value, current_sum) do
    Enum.find_value(probabilities, :high_card, fn {card_type, weight} ->
      new_sum = current_sum + weight
      if target_value <= new_sum, do: card_type
    end)
  end

  @doc """
  确保是高牌（不是对子或顺子）
  """
  defp ensure_high_card_values(values) do
    sorted_values = Enum.sort(values)

    cond do
      # 检查是否有重复值（对子）
      length(Enum.uniq(sorted_values)) != 3 ->
        # 重新生成不重复的值
        Enum.shuffle(2..14) |> Enum.take(3) |> Enum.uniq() |> Enum.take(3)

      # 检查是否是顺子
      is_sequence?(sorted_values) ->
        # 打乱顺序，确保不是顺子
        [Enum.at(sorted_values, 0), Enum.at(sorted_values, 1), Enum.at(sorted_values, 2) + 2]

      true ->
        values
    end
  end

  @doc """
  检查是否是顺子
  """
  defp is_sequence?(values) when length(values) == 3 do
    sorted = Enum.sort(values)

    Enum.at(sorted, 1) == Enum.at(sorted, 0) + 1 and
      Enum.at(sorted, 2) == Enum.at(sorted, 1) + 1
  end

  defp is_sequence?(_), do: false

  @doc """
  为收分模式生成强牌
  """
  defp generate_strong_cards_for_collect_mode(_context) do
    # 收分模式：机器人获得强牌（三条、同花顺、顺子）
    strong_types = [:trail, :pure_sequence, :sequence]
    card_type = Enum.random(strong_types)
    generate_specific_card_type(card_type, %{})
  end

  @doc """
  为收分模式生成次强牌
  """
  defp generate_second_strong_cards_for_collect_mode(_context) do
    # 收分模式：玩家获得次强牌（同花、对子、高牌）
    second_strong_types = [:color, :pair, :high_card]
    card_type = Enum.random(second_strong_types)
    generate_specific_card_type(card_type, %{})
  end

  @doc """
  为放分模式生成强牌
  """
  defp generate_strong_cards_for_release_mode(_context) do
    # 放分模式：玩家获得强牌
    strong_types = [:trail, :pure_sequence, :sequence, :color]
    card_type = Enum.random(strong_types)
    generate_specific_card_type(card_type, %{})
  end

  @doc """
  为放分模式生成弱牌
  """
  defp generate_weak_cards_for_release_mode(_context) do
    # 放分模式：机器人获得弱牌（高牌为主，偶尔对子）
    if :rand.uniform() < 0.8 do
      generate_specific_card_type(:high_card, %{})
    else
      generate_specific_card_type(:pair, %{})
    end
  end

  @doc """
  从牌堆中发牌（确保不重复）
  充值玩家使用幸运值发牌
  """
  defp deal_cards_from_deck(deck, luck_value) do
    # 根据幸运值获取概率配置
    probabilities = TeenPattiConfig.get_card_probabilities_by_luck(luck_value)

    # 计算总权重
    total_weight = Enum.sum(Map.values(probabilities))
    random_value = :rand.uniform(total_weight)

    # 确定牌型
    card_type = determine_card_type_by_weight(probabilities, random_value, 0)

    # 从牌堆中取出符合牌型的牌
    case find_cards_of_type(deck, card_type) do
      {cards, remaining_deck} when length(cards) == 3 ->
        {cards, remaining_deck}

      _ ->
        # 如果找不到合适的牌型，随机取3张
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  @doc """
  免费玩家发牌（使用策略概率，不使用幸运值）
  """
  defp deal_free_player_cards(deck) do
    # 获取免费玩家概率配置
    probabilities = TeenPattiConfig.get_free_player_probabilities().player

    # 计算总权重
    total_weight = Enum.sum(Map.values(probabilities))
    random_value = :rand.uniform(total_weight)

    # 确定牌型
    card_type = determine_card_type_by_weight(probabilities, random_value, 0)

    # 从牌堆中取出符合牌型的牌
    case find_cards_of_type(deck, card_type) do
      {cards, remaining_deck} when length(cards) == 3 ->
        {cards, remaining_deck}

      _ ->
        # 如果找不到合适的牌型，随机取3张
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  @doc """
  从牌堆中寻找指定牌型的牌
  """
  defp find_cards_of_type(deck, card_type) do
    case card_type do
      :trail -> find_trail_cards(deck)
      :pure_sequence -> find_pure_sequence_cards(deck)
      :sequence -> find_sequence_cards(deck)
      :color -> find_color_cards(deck)
      :pair -> find_pair_cards(deck)
      :high_card -> find_high_card_cards(deck)
      _ -> {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  # 简化的牌型查找函数（从牌堆中找到符合条件的牌）
  defp find_trail_cards(deck) do
    # 寻找三条：找到三张相同点数的牌
    deck_by_value = Enum.group_by(deck, fn card -> card.value end)

    case Enum.find(deck_by_value, fn {_value, cards} -> length(cards) >= 3 end) do
      {_value, cards} ->
        selected_cards = Enum.take(cards, 3)
        remaining_deck = deck -- selected_cards
        {selected_cards, remaining_deck}

      nil ->
        # 找不到三条，随机取3张
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  defp find_pair_cards(deck) do
    # 寻找对子：找到两张相同点数的牌，再加一张不同的
    deck_by_value = Enum.group_by(deck, fn card -> card.value end)

    case Enum.find(deck_by_value, fn {_value, cards} -> length(cards) >= 2 end) do
      {pair_value, pair_cards} ->
        pair = Enum.take(pair_cards, 2)
        remaining_after_pair = deck -- pair

        # 找一张不同点数的牌
        case Enum.find(remaining_after_pair, fn card -> card.value != pair_value end) do
          nil ->
            {Enum.take(deck, 3), Enum.drop(deck, 3)}

          third_card ->
            remaining_deck = remaining_after_pair -- [third_card]
            {pair ++ [third_card], remaining_deck}
        end

      nil ->
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  defp find_high_card_cards(deck) do
    # 高牌：随机取3张不同点数、不同花色的牌
    {Enum.take(deck, 3), Enum.drop(deck, 3)}
  end

  defp find_color_cards(deck) do
    # 同花：找到3张相同花色的牌
    deck_by_suit = Enum.group_by(deck, fn card -> card.suit end)

    case Enum.find(deck_by_suit, fn {_suit, cards} -> length(cards) >= 3 end) do
      {_suit, cards} ->
        selected_cards = Enum.take(cards, 3)
        remaining_deck = deck -- selected_cards
        {selected_cards, remaining_deck}

      nil ->
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  defp find_sequence_cards(deck) do
    # 顺子：找到连续的3张牌
    sorted_deck = Enum.sort_by(deck, fn card -> card.value end)

    case find_consecutive_cards(sorted_deck, 3) do
      nil ->
        {Enum.take(deck, 3), Enum.drop(deck, 3)}

      cards ->
        remaining_deck = deck -- cards
        {cards, remaining_deck}
    end
  end

  defp find_pure_sequence_cards(deck) do
    # 同花顺：找到连续且同花色的3张牌
    deck_by_suit = Enum.group_by(deck, fn card -> card.suit end)

    result =
      Enum.find_value(deck_by_suit, fn {_suit, cards} ->
        sorted_cards = Enum.sort_by(cards, fn card -> card.value end)
        find_consecutive_cards(sorted_cards, 3)
      end)

    case result do
      nil ->
        {Enum.take(deck, 3), Enum.drop(deck, 3)}

      cards ->
        remaining_deck = deck -- cards
        {cards, remaining_deck}
    end
  end

  defp find_consecutive_cards(cards, count) do
    cards
    |> Enum.chunk_every(count, 1, :discard)
    |> Enum.find(fn chunk ->
      chunk
      |> Enum.map(fn card -> card.value end)
      |> Enum.sort()
      |> is_consecutive?(count)
    end)
  end

  defp is_consecutive?(values, count) when length(values) == count do
    values
    |> Enum.with_index()
    |> Enum.all?(fn {value, index} -> value == Enum.at(values, 0) + index end)
  end

  defp is_consecutive?(_, _), do: false

  # 新玩家发牌函数
  defp deal_new_player_cards(deck) do
    probabilities = TeenPattiConfig.get_new_player_first_game_probabilities().player
    deal_cards_by_probabilities(deck, probabilities)
  end

  defp deal_new_player_never_80_cards(deck) do
    probabilities = TeenPattiConfig.get_new_player_never_80_probabilities().player
    deal_cards_by_probabilities(deck, probabilities)
  end

  defp deal_waiting_recharge_cards(deck) do
    probabilities = TeenPattiConfig.get_waiting_recharge_probabilities().player
    deal_cards_by_probabilities(deck, probabilities)
  end

  defp deal_cards_by_probabilities(deck, probabilities) do
    total_weight = Enum.sum(Map.values(probabilities))
    random_value = :rand.uniform(total_weight)
    card_type = determine_card_type_by_weight(probabilities, random_value, 0)

    case find_cards_of_type(deck, card_type) do
      {cards, remaining_deck} when length(cards) == 3 ->
        {cards, remaining_deck}

      _ ->
        {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end
end
