defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGameLogic do
  @moduledoc """
  Jhandi Munda 游戏逻辑引擎

  实现核心游戏算法，包括：
  - 骰子投掷算法
  - 中奖计算
  - 赔率计算
  - 游戏结果生成
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants

  @doc """
  投掷骰子，生成游戏结果

  返回格式：
  %{
    dice_results: [1, 2, 3, 4, 5, 6],  # 每个骰子的结果
    symbol_counts: %{1 => 2, 2 => 1, 3 => 1, 4 => 1, 5 => 1, 6 => 0},  # 每个符号出现的次数
    winning_symbols: [1, 2, 3, 4, 5]  # 有中奖的符号列表
  }
  """
  def roll_dice do
    # 生成6个骰子的结果，每个骰子随机选择1-6的符号
    dice_results =
      for _ <- 1..JhandiMundaConstants.dice_count() do
        Enum.random(JhandiMundaConstants.get_all_symbols())
      end

    # 统计每个符号出现的次数
    symbol_counts = count_symbols(dice_results)

    # 找出有中奖的符号（出现次数 > 0）
    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  @doc """
  根据控制参数生成特定结果的骰子

  control_type:
  - :random - 随机结果
  - :favor_player - 偏向玩家的结果
  - :favor_house - 偏向庄家的结果
  - :specific_symbol - 指定符号多出现
  """
  def roll_dice_with_control(control_type, opts \\ []) do
    case control_type do
      :random ->
        roll_dice()

      :favor_player ->
        generate_player_favorable_result(opts)

      :favor_house ->
        generate_house_favorable_result(opts)

      :specific_symbol ->
        target_symbol = Keyword.get(opts, :target_symbol, JhandiMundaConstants.symbol_clubs())
        target_count = Keyword.get(opts, :target_count, 3)
        generate_specific_symbol_result(target_symbol, target_count)

      _ ->
        roll_dice()
    end
  end

  @doc """
  计算玩家的中奖金额

  参数：
  - player_bets: %{symbol => bet_amount} 玩家在各符号的下注
  - game_result: 游戏结果

  返回：
  %{
    total_win: 总中奖金额,
    win_details: %{symbol => %{bet: 下注金额, count: 出现次数, odds: 赔率, win: 中奖金额}},
    total_bet: 总下注金额
  }
  """
  def calculate_player_winnings(player_bets, game_result) do
    symbol_counts = game_result.symbol_counts

    win_details =
      player_bets
      |> Enum.map(fn {symbol, bet_amount} ->
        count = Map.get(symbol_counts, symbol, 0)
        odds = JhandiMundaConstants.get_odds(count)
        win_amount = if count > 0, do: bet_amount * odds, else: 0

        {symbol,
         %{
           bet: bet_amount,
           count: count,
           odds: odds,
           win: win_amount
         }}
      end)
      |> Enum.into(%{})

    total_win =
      win_details
      |> Enum.reduce(0, fn {_symbol, details}, acc -> acc + details.win end)

    total_bet =
      player_bets
      |> Enum.reduce(0, fn {_symbol, bet_amount}, acc -> acc + bet_amount end)

    %{
      total_win: total_win,
      win_details: win_details,
      total_bet: total_bet,
      net_win: total_win - total_bet
    }
  end

  @doc """
  计算所有玩家的结算结果

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果

  返回：
  %{player_id => %{total_win: 总中奖金额, win_details: 详细信息, total_bet: 总下注金额, net_win: 净赢得}}
  """
  def calculate_settlements(all_player_bets, game_result) do
    all_player_bets
    |> Enum.reduce(%{}, fn {player_id, player_bets}, acc ->
      player_result = calculate_player_winnings(player_bets, game_result)
      Map.put(acc, player_id, player_result)
    end)
  end

  @doc """
  计算房间总的输赢情况

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果

  返回房间的盈亏情况
  """
  def calculate_room_profit(all_player_bets, game_result) do
    total_bets = 0
    total_wins = 0
    player_results = %{}

    {total_bets, total_wins, player_results} =
      all_player_bets
      |> Enum.reduce({0, 0, %{}}, fn {player_id, player_bets},
                                     {acc_bets, acc_wins, acc_results} ->
        player_result = calculate_player_winnings(player_bets, game_result)

        {
          acc_bets + player_result.total_bet,
          acc_wins + player_result.total_win,
          Map.put(acc_results, player_id, player_result)
        }
      end)

    %{
      total_bets: total_bets,
      total_wins: total_wins,
      house_profit: total_bets - total_wins,
      player_results: player_results
    }
  end

  @doc """
  验证下注是否有效
  """
  def validate_bet(symbol, amount) do
    cond do
      not JhandiMundaConstants.valid_symbol?(symbol) ->
        {:error, :invalid_symbol}

      amount < JhandiMundaConstants.min_bet() ->
        {:error, :bet_too_small}

      amount > JhandiMundaConstants.max_bet() ->
        {:error, :bet_too_large}

      not JhandiMundaConstants.valid_chip?(amount) ->
        {:error, :invalid_chip}

      true ->
        :ok
    end
  end

  @doc """
  生成游戏历史记录格式
  """
  def format_game_record(game_result, round_id, big_winners \\ []) do
    %{
      round_id: round_id,
      dice_results: game_result.dice_results,
      symbol_counts: game_result.symbol_counts,
      winning_symbols: game_result.winning_symbols,
      # 添加大赢家信息
      big_winners: big_winners,
      timestamp: DateTime.utc_now()
    }
  end

  # 私有函数

  defp count_symbols(dice_results) do
    # 初始化所有符号的计数为0
    initial_counts =
      JhandiMundaConstants.get_all_symbols()
      |> Enum.map(fn symbol -> {symbol, 0} end)
      |> Enum.into(%{})

    # 统计每个符号出现的次数
    dice_results
    |> Enum.reduce(initial_counts, fn symbol, acc ->
      Map.update(acc, symbol, 1, &(&1 + 1))
    end)
  end

  defp generate_player_favorable_result(_opts) do
    # 生成对玩家有利的结果（更多高赔率组合）
    # 随机选择一个符号，让它出现3-4次
    target_symbol = Enum.random(JhandiMundaConstants.get_all_symbols())
    target_count = Enum.random(3..4)

    generate_specific_symbol_result(target_symbol, target_count)
  end

  defp generate_house_favorable_result(_opts) do
    # 生成对庄家有利的结果（更分散的结果，低赔率）
    symbols = JhandiMundaConstants.get_all_symbols()

    # 尽量让每个符号都出现1次，剩余的随机分配
    dice_results =
      symbols ++
        for _ <- 1..(JhandiMundaConstants.dice_count() - length(symbols)) do
          Enum.random(symbols)
        end

    # 打乱顺序
    dice_results = Enum.shuffle(dice_results)

    symbol_counts = count_symbols(dice_results)

    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  defp generate_specific_symbol_result(target_symbol, target_count) do
    # 确保目标符号出现指定次数
    target_dice = for _ <- 1..target_count, do: target_symbol

    # 剩余骰子随机选择其他符号
    remaining_count = JhandiMundaConstants.dice_count() - target_count
    other_symbols = JhandiMundaConstants.get_all_symbols() -- [target_symbol]

    remaining_dice =
      for _ <- 1..remaining_count do
        Enum.random(other_symbols)
      end

    # 合并并打乱
    dice_results = Enum.shuffle(target_dice ++ remaining_dice)

    symbol_counts = count_symbols(dice_results)

    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_statistics(history_records) do
    if length(history_records) == 0 do
      %{
        total_rounds: 0,
        symbol_frequencies: %{},
        average_winning_symbols: 0
      }
    else
      symbol_frequencies =
        history_records
        |> Enum.reduce(%{}, fn record, acc ->
          record.symbol_counts
          |> Enum.reduce(acc, fn {symbol, count}, inner_acc ->
            Map.update(inner_acc, symbol, count, &(&1 + count))
          end)
        end)

      total_winning_symbols =
        history_records
        |> Enum.reduce(0, fn record, acc ->
          acc + length(record.winning_symbols)
        end)

      %{
        total_rounds: length(history_records),
        symbol_frequencies: symbol_frequencies,
        average_winning_symbols: total_winning_symbols / length(history_records)
      }
    end
  end
end
