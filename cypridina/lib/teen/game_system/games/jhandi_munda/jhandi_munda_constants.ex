defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants do
  @moduledoc """
  Jhandi Munda 游戏常量定义模块

  印度传统骰子游戏，使用6个骰子，每个骰子有6个面：
  - 梅花 (Clubs)
  - 皇冠 (Crown)
  - 黑桃 (Spades)
  - 方块 (Diamonds)
  - 国旗 (Flag)
  - 红桃 (Hearts)

  玩家可以在6个区域下注，根据骰子结果获得相应倍率的奖励
  """

  # 游戏基础常量
  # 下注区域数量
  @area_count 6
  # 骰子数量
  @dice_count 6
  # 符号数量
  @symbol_count 6

  # 积分倍率常量
  # 积分倍率，所有金额相关的值都要乘以此倍率
  @score_rate 100

  # 游戏符号定义 (对应前端 YXXPos)
  # 无效
  @symbol_none 0
  # 梅花
  @symbol_clubs 1
  # 皇冠
  @symbol_crown 2
  # 黑桃
  @symbol_spades 3
  # 方块
  @symbol_diamonds 4
  # 国旗
  @symbol_flag 5
  # 红桃
  @symbol_hearts 6

  # 游戏状态定义 (对应前端 EM_YXX_GAMESTATE)
  # 无状态
  @game_state_none 0
  # 下注阶段
  @game_state_betting 1
  # 亮牌阶段
  @game_state_revealing 2

  # 协议定义 (对应前端 YXXCMD)
  # SC_YXX_CONFIG_P - 发送配置
  @protocol_config 1000
  # SC_YXX_OPTTIME_P - 通知操作时间
  @protocol_opt_time 1001
  # CS_YXX_BUYHORSE_P - 请求下注
  @protocol_bet_request 1002
  # SC_YXX_OPER_ERROR_P - 服务端返回操作错误码
  @protocol_oper_error 1003
  # SC_YXX_BET_SUCCESS_P - 下注成功
  @protocol_bet_success 1004
  # SC_YXX_BET_SYNC_P - 筹码增量信息
  @protocol_bet_sync 1005
  # SC_YXX_SETTLEMENT_P - 结算
  @protocol_settlement 1006
  # CS_YXX_HISTORY_P - 请求历史信息
  @protocol_history_request 1007
  # SC_YXX_HISTORY_P - 返回历史信息
  @protocol_history_response 1008
  # CS_YXX_ALLLIST_P - 请求玩家列表
  @protocol_player_list_request 1009
  # SC_YXX_ALLLIST_P - 返回玩家列表
  @protocol_player_list_response 1010

  # 游戏错误码定义 (对应前端 YXX_GAME_ERROR)
  # 没有足够的金币
  @error_not_money 0
  # 下注上限
  @error_buy_limit 1
  # 当前不能换庄
  @error_not_round 2
  # 上庄金钱不足
  @error_zhuang_no_money 3
  # 下轮下庄
  @error_next_round 4
  # 无牛下庄
  @error_offzhuang_wuniu 5
  # 申请上庄成功
  @error_applyzhuang_ok 6
  # 金钱不足不能下注
  @error_not_money_to_bet 7
  # 没有续投的记录
  @error_follow_to_bet 8
  # 续投超出房间限制
  @error_follow_limit 9
  # 续投个人金钱不足
  @error_follow_not_money 10
  # 沉默次数太多
  @error_silence_too_many 11
  # 超过最大下注
  @error_bet_too_more 12
  # 不在下注阶段
  @error_state_error 13
  # 下注区域错误
  @error_buy_pos_error 14
  # 庄家下注
  @error_banker_bet 15
  # 非法筹码
  @error_illegal_bet 16
  # 下注太快
  @error_bet_too_fast 17

  # 赔率配置 - 适配前端 posnums[i] - 1 的逻辑
  # 前端会用 this.odds[posnums[i] - 1] 来获取赔率
  # 所以索引需要对应: [1个符号, 2个符号, 3个符号, 4个符号, 5个符号, 6个符号]
  # 标准规则：0或1个符号命中 = 输(0倍)，2个以上才有赔率
  @odds_table [0, 3, 5, 10, 20, 100]

  # 时间配置
  # 下注时间 (秒)
  @betting_time 10
  # 亮牌时间 (秒)
  @revealing_time 7

  # 下注配置 (所有金额都乘以 SCORE_RATE)
  # 最小下注金额
  @min_bet 10 * @score_rate
  # 最大下注金额
  @max_bet 600_000 * @score_rate
  # 可下注的筹码面额
  @bet_chips Enum.map([10, 50, 100, 1000, 5000, 10000], &(&1 * @score_rate))

  # 房间配置
  # 最大玩家数（匹配6个座位）
  @max_players 6
  # 最小玩家数
  @min_players 1
  # 最大沉默轮数
  @max_silence_rounds 10

  # 历史记录配置
  # 最大历史记录数
  @max_history_records 30
  # 玩家列表每页最大个数
  @player_list_limit 14

  # 公开访问函数
  def area_count, do: @area_count
  def dice_count, do: @dice_count
  def symbol_count, do: @symbol_count
  def score_rate, do: @score_rate

  # 符号定义
  def symbol_none, do: @symbol_none
  def symbol_clubs, do: @symbol_clubs
  def symbol_crown, do: @symbol_crown
  def symbol_spades, do: @symbol_spades
  def symbol_diamonds, do: @symbol_diamonds
  def symbol_flag, do: @symbol_flag
  def symbol_hearts, do: @symbol_hearts

  # 游戏状态
  def game_state_none, do: @game_state_none
  def game_state_betting, do: @game_state_betting
  def game_state_revealing, do: @game_state_revealing

  # 协议定义
  def protocol_config, do: @protocol_config
  def protocol_opt_time, do: @protocol_opt_time
  def protocol_bet_request, do: @protocol_bet_request
  def protocol_oper_error, do: @protocol_oper_error
  def protocol_bet_success, do: @protocol_bet_success
  def protocol_bet_sync, do: @protocol_bet_sync
  def protocol_settlement, do: @protocol_settlement
  def protocol_history_request, do: @protocol_history_request
  def protocol_history_response, do: @protocol_history_response
  def protocol_player_list_request, do: @protocol_player_list_request
  def protocol_player_list_response, do: @protocol_player_list_response

  # 错误码
  def error_not_money, do: @error_not_money
  def error_buy_limit, do: @error_buy_limit
  def error_not_round, do: @error_not_round
  def error_zhuang_no_money, do: @error_zhuang_no_money
  def error_next_round, do: @error_next_round
  def error_offzhuang_wuniu, do: @error_offzhuang_wuniu
  def error_applyzhuang_ok, do: @error_applyzhuang_ok
  def error_not_money_to_bet, do: @error_not_money_to_bet
  def error_follow_to_bet, do: @error_follow_to_bet
  def error_follow_limit, do: @error_follow_limit
  def error_follow_not_money, do: @error_follow_not_money
  def error_silence_too_many, do: @error_silence_too_many
  def error_bet_too_more, do: @error_bet_too_more
  def error_state_error, do: @error_state_error
  def error_buy_pos_error, do: @error_buy_pos_error
  def error_banker_bet, do: @error_banker_bet
  def error_illegal_bet, do: @error_illegal_bet
  def error_bet_too_fast, do: @error_bet_too_fast

  # 配置访问
  def odds_table, do: @odds_table
  def betting_time, do: @betting_time
  def revealing_time, do: @revealing_time
  def min_bet, do: @min_bet
  def max_bet, do: @max_bet
  def bet_chips, do: @bet_chips
  def max_players, do: @max_players
  def min_players, do: @min_players
  def max_silence_rounds, do: @max_silence_rounds
  def max_history_records, do: @max_history_records
  def player_list_limit, do: @player_list_limit

  @doc """
  验证符号是否有效
  """
  def valid_symbol?(symbol) do
    symbol in [
      @symbol_clubs,
      @symbol_crown,
      @symbol_spades,
      @symbol_diamonds,
      @symbol_flag,
      @symbol_hearts
    ]
  end

  @doc """
  验证下注区域是否有效
  """
  def valid_bet_area?(area) do
    area in 1..@area_count
  end

  @doc """
  验证筹码面额是否有效
  """
  def valid_chip?(chip) do
    chip in @bet_chips
  end

  @doc """
  根据相同符号数量获取赔率 - 适配前端逻辑
  前端使用 this.odds[posnums[i] - 1]，所以我们需要调整索引
  """
  def get_odds(count) when count >= 1 and count <= @dice_count do
    # 前端会减1，所以我们的索引需要对应：
    # count=1 -> index=0 (1个符号命中，赔率0)
    # count=2 -> index=1 (2个符号命中，赔率3)
    # count=3 -> index=2 (3个符号命中，赔率5)
    # 等等...
    Enum.at(@odds_table, count - 1, 0)
  end

  # 0个符号命中，赔率0
  def get_odds(0), do: 0
  def get_odds(_), do: 0

  @doc """
  获取符号名称
  """
  def get_symbol_name(symbol) do
    case symbol do
      @symbol_clubs -> "梅花"
      @symbol_crown -> "皇冠"
      @symbol_spades -> "黑桃"
      @symbol_diamonds -> "方块"
      @symbol_flag -> "国旗"
      @symbol_hearts -> "红桃"
      _ -> "未知"
    end
  end

  @doc """
  获取游戏状态名称
  """
  def get_game_state_name(state) do
    case state do
      @game_state_none -> "无状态"
      @game_state_betting -> "下注阶段"
      @game_state_revealing -> "亮牌阶段"
      _ -> "未知状态"
    end
  end

  @doc """
  获取所有有效符号列表
  """
  def get_all_symbols do
    [@symbol_clubs, @symbol_crown, @symbol_spades, @symbol_diamonds, @symbol_flag, @symbol_hearts]
  end

  @doc """
  获取符号到索引的映射
  返回格式: [{symbol, index}, ...]
  """
  def symbols do
    [
      {@symbol_clubs, 1},
      {@symbol_crown, 2},
      {@symbol_spades, 3},
      {@symbol_diamonds, 4},
      {@symbol_flag, 5},
      {@symbol_hearts, 6}
    ]
  end

  @doc """
  获取游戏配置信息
  """
  def get_game_config do
    %{
      area_count: @area_count,
      dice_count: @dice_count,
      symbol_count: @symbol_count,
      odds_table: @odds_table,
      betting_time: @betting_time,
      revealing_time: @revealing_time,
      min_bet: @min_bet,
      max_bet: @max_bet,
      bet_chips: @bet_chips,
      max_players: @max_players,
      min_players: @min_players
    }
  end

  # 添加缺失的函数
  def symbol_odds, do: @odds_table
  def bet_amounts, do: @bet_chips

  @doc """
  获取适配前端逻辑的完整赔率表
  前端使用 this.odds[posnums[i] - 1]，所以需要在索引0位置添加一个占位符
  返回格式: [0, 0, 3, 5, 10, 20, 100]
  对应: [占位符, 1个符号(0倍), 2个符号(3倍), 3个符号(5倍), 4个符号(10倍), 5个符号(20倍), 6个符号(100倍)]
  """
  def get_all_odds do
    # 在前面添加一个0作为占位符，适配前端的 posnums[i] - 1 逻辑
    [0] ++ @odds_table
  end
end
