defmodule Teen.Services.WithdrawalService do
  @moduledoc """
  提现服务

  处理用户提现申请，包括：
  - 流水验证
  - 支付网关集成
  - 提现审核流程
  - 费用计算
  """

  require Logger
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Teen.PaymentSystem.WithdrawalConfig
  alias Teen.PaymentSystem.GatewaySelector
  alias Teen.Services.TurnoverService
  alias Cypridina.Ledger
  alias Cypridina.Ledger.BalanceCache
  alias Cypridina.Ledger.AccountIdentifier

  @doc """
  创建提现申请
  """
  def create_withdrawal(user_id, params) do
    case WithdrawalRecord.create_withdrawal_request(user_id, params) do
      {:ok, withdrawal} ->
        Logger.info("创建提现申请成功: #{withdrawal.order_id}")
        {:ok, withdrawal}

      {:error, reason} ->
        Logger.error("创建提现申请失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理提现申请（审核通过后）
  """
  def process_withdrawal(withdrawal_id) do
    with {:ok, withdrawal} <- WithdrawalRecord.read(withdrawal_id),
         :ok <- validate_withdrawal_status(withdrawal),
         {:ok, gateway_config} <- select_withdrawal_gateway(withdrawal),
         {:ok, ledger_result} <- process_withdrawal_in_ledger(withdrawal),
         {:ok, payment_result} <- submit_to_payment_gateway(withdrawal, gateway_config) do
      # 更新提现记录状态
      update_withdrawal_progress(withdrawal, payment_result, ledger_result)
    else
      {:error, reason} ->
        # 如果有ledger操作，需要回滚
        Logger.error("提现处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end


  @doc """
  验证提现状态
  """
  defp validate_withdrawal_status(withdrawal) do
    cond do
      withdrawal.audit_status != 1 ->
        {:error, "提现申请未通过审核"}

      withdrawal.progress_status != 0 ->
        {:error, "提现申请已在处理中"}

      true ->
        :ok
    end
  end

  @doc """
  选择提现网关
  """
  defp select_withdrawal_gateway(withdrawal) do
    case GatewaySelector.select_withdraw_gateway(
           withdrawal.withdrawal_amount,
           "XAA",
           withdrawal.user_id
         ) do
      {:ok, gateway_config} ->
        {:ok, gateway_config}

      {:error, reason} ->
        Logger.error("选择提现网关失败: #{reason}")
        {:error, "暂无可用的提现渠道"}
    end
  end

  @doc """
  提交到支付网关
  """
  defp submit_to_payment_gateway(withdrawal, gateway_config) do
    # 构建支付网关请求参数
    params = build_gateway_params(withdrawal, gateway_config)

    # 调用支付网关API
    case call_payment_gateway(gateway_config, params) do
      {:ok, response} ->
        Logger.info("提现请求提交成功: #{withdrawal.order_id}")
        {:ok, response}

      {:error, reason} ->
        Logger.error("提现请求提交失败: #{withdrawal.order_id}, 原因: #{reason}")
        {:error, reason}
    end
  end

  @doc """
  构建支付网关参数
  """
  defp build_gateway_params(withdrawal, gateway_config) do
    %{
      "merchantId" => gateway_config.merchant_id,
      "orderId" => withdrawal.order_id,
      "amount" => Decimal.to_string(withdrawal.actual_amount),
      "currency" => "XAA",
      "channelId" => gateway_config.channel_id,
      "notifyUrl" => build_notify_url(withdrawal.order_id),
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond) |> to_string(),
      # 根据支付方式添加特定参数
      "paymentMethod" => withdrawal.payment_method,
      "paymentInfo" => get_payment_info(withdrawal)
    }
  end

  @doc """
  获取支付信息
  """
  defp get_payment_info(withdrawal) do
    case withdrawal.payment_method do
      "bank_card" -> withdrawal.bank_info
      "alipay" -> withdrawal.alipay_info
      "upi" -> withdrawal.upi_info
      _ -> ""
    end
  end

  @doc """
  调用支付网关API
  """
  defp call_payment_gateway(gateway_config, params) do
    # 生成签名
    sign = Teen.Services.MasterPayService.generate_payment_signature(params, gateway_config.secret_key)

    params_with_sign = Map.put(params, "sign", sign)

    # 发送HTTP请求
    url = gateway_config.gateway_url <> gateway_config.create_order_path
    headers = [{"Content-Type", "application/json"}]
    body = Jason.encode!(params_with_sign)

    # 暂时使用模拟响应，实际项目中需要配置HTTPoison依赖
    case simulate_payment_gateway_call(url, body, headers) do
      {:ok, response_body} ->
        case Jason.decode(response_body) do
          {:ok, response_data} ->
            if response_data["code"] == "200" do
              {:ok, response_data}
            else
              {:error, response_data["message"] || "支付网关返回错误"}
            end

          {:error, _} ->
            {:error, "支付网关响应格式错误"}
        end

      {:error, reason} ->
        {:error, "网络请求失败: #{reason}"}
    end
  end

  @doc """
  更新提现进度
  """
  defp update_withdrawal_progress(withdrawal, payment_result, ledger_result \\ nil) do
    update_params = %{
      # 处理中
      progress_status: 1,
      gateway_order_id: payment_result["orderId"],
      gateway_response: Jason.encode!(payment_result)
    }

    # 如果有ledger结果，也记录下来
    update_params =
      if ledger_result do
        Map.put(update_params, :ledger_transfer_id, ledger_result.transfer_id)
      else
        update_params
      end

    WithdrawalRecord.update_progress(withdrawal, update_params)
  end

  @doc """
  在ledger系统中处理提现（扣除用户余额）
  """
  defp process_withdrawal_in_ledger(withdrawal) do
    user_identifier = AccountIdentifier.user(withdrawal.user_id, :XAA)
    withdrawal_identifier = AccountIdentifier.system(:withdrawal_pending, :XAA)

    # 扣除用户余额到提现待处理账户
    case Ledger.transfer(
           user_identifier,
           withdrawal_identifier,
           Decimal.to_integer(withdrawal.withdrawal_amount),
           transaction_type: :withdrawal_request,
           description: "提现申请: #{withdrawal.order_id}",
           metadata: %{
             withdrawal_id: withdrawal.id,
             withdrawal_method: withdrawal.payment_method,
             order_id: withdrawal.order_id
           }
         ) do
      {:ok, transfer} ->
        {:ok, %{transfer_id: transfer.id, amount: withdrawal.withdrawal_amount}}

      {:error, reason} ->
        Logger.error("提现扣款失败: #{inspect(reason)}")
        {:error, "余额不足或系统错误"}
    end
  end

  @doc """
  构建回调URL
  """
  defp build_notify_url(order_id) do
    "#{Application.get_env(:cypridina, :base_url)}/api/withdrawal/callback/#{order_id}"
  end


  @doc """
  模拟支付网关调用（用于开发测试）
  """
  defp simulate_payment_gateway_call(_url, _body, _headers) do
    # 模拟成功响应
    response = %{
      "code" => "200",
      "message" => "success",
      "orderId" => ("GW" <> DateTime.utc_now()) |> DateTime.to_unix(:millisecond) |> to_string(),
      "status" => "processing"
    }

    {:ok, Jason.encode!(response)}
  end

  @doc """
  处理提现回调完成
  """
  def complete_withdrawal(order_id, callback_result) do
    with {:ok, withdrawal} <- WithdrawalRecord.get_by_order_id(order_id) do
      case callback_result.status do
        :success ->
          WithdrawalRecord.update_progress(withdrawal, %{
            # 支付成功
            progress_status: 2,
            # 成功
            result_status: 1,
            gateway_response: Jason.encode!(callback_result),
            completed_time: DateTime.utc_now()
          })

        :failed ->
          # 提现失败，退回余额
          refund_failed_withdrawal(withdrawal)

          WithdrawalRecord.update_progress(withdrawal, %{
            # 支付失败
            progress_status: 3,
            # 失败
            result_status: 2,
            gateway_response: Jason.encode!(callback_result),
            feedback: "Gateway payment failed"
          })

        _ ->
          {:ok, withdrawal}
      end
    end
  end

  @doc """
  处理提现失败
  """
  def fail_withdrawal(order_id, error_data) do
    with {:ok, withdrawal} <- WithdrawalRecord.get_by_order_id(order_id) do
      # 退回用户余额
      refund_failed_withdrawal(withdrawal)

      WithdrawalRecord.update_progress(withdrawal, %{
        # 支付失败
        progress_status: 3,
        # 失败
        result_status: 2,
        feedback: error_data[:error_message] || "Withdrawal failed"
      })
    end
  end

  defp refund_failed_withdrawal(withdrawal) do
    user_identifier = AccountIdentifier.user(withdrawal.user_id, :XAA)
    withdrawal_identifier = AccountIdentifier.system(:withdrawal_pending, :XAA)

    case Ledger.transfer(
           withdrawal_identifier,
           user_identifier,
           Decimal.to_integer(withdrawal.withdrawal_amount),
           transaction_type: :refund,
           description: "提现失败退款: #{withdrawal.order_id}"
         ) do
      {:ok, transfer} ->
        Logger.info("提现失败退款成功: #{withdrawal.order_id}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("提现失败退款失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
