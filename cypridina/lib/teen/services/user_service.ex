defmodule Teen.Services.UserService do
  @moduledoc """
  用户服务，展示如何与游戏事件中心交互

  这个服务处理用户相关的操作，
  通过事件中心来触发相关的任务和奖励逻辑。
  """

  require Logger

  alias Teen.Events.EventPublisher
  alias Teen.Accounts.User

  @doc """
  用户登录
  """
  def handle_user_login(username, password) do
    Logger.info("Processing login for user: #{username}")

    with {:ok, user} <- authenticate_user(username, password),
         {:ok, _session} <- create_user_session(user.id),
         {:ok, _record} <- record_login_activity(user.id) do
      # 通知事件中心用户登录 - 这里是关键！
      EventPublisher.handle_user_login(user.id, DateTime.utc_now())

      Logger.info("User #{user.id} logged in successfully")
      {:ok, %{user: user, login_time: DateTime.utc_now()}}
    else
      {:error, :invalid_credentials} ->
        Logger.warn("Invalid login attempt for username: #{username}")
        {:error, "用户名或密码错误"}

      {:error, reason} = error ->
        Logger.error("<PERSON><PERSON> failed for user #{username}: #{inspect(reason)}")
        error
    end
  end

  @doc """
  用户注册
  """
  def handle_user_register(user_params) do
    Logger.info("Processing registration for username: #{user_params.username}")

    with {:ok, user} <- create_user(user_params),
         {:ok, _account} <- initialize_user_account(user.id),
         {:ok, _profile} <- create_user_profile(user.id) do
      # 通知事件中心用户注册 - 这里是关键！
      EventPublisher.handle_user_register(user)

      Logger.info("User #{user.id} registered successfully")
      {:ok, user}
    else
      {:error, reason} = error ->
        Logger.error(
          "Registration failed for username #{user_params.username}: #{inspect(reason)}"
        )

        error
    end
  end

  @doc """
  用户登出
  """
  def handle_user_logout(user_id) do
    Logger.info("Processing logout for user: #{user_id}")

    with {:ok, _session} <- invalidate_user_session(user_id),
         {:ok, _record} <- record_logout_activity(user_id) do
      # 如果需要，也可以通知事件中心用户登出
      # EventPublisher.handle_user_logout(user_id, DateTime.utc_now())

      Logger.info("User #{user_id} logged out successfully")
      {:ok, %{status: :logged_out}}
    else
      {:error, reason} = error ->
        Logger.error("Logout failed for user #{user_id}: #{inspect(reason)}")
        error
    end
  end

  @doc """
  获取用户信息
  """
  def get_user_info(user_id) do
    case User.get_by_id(user_id) do
      {:ok, user} ->
        {:ok,
         %{
           id: user.id,
           username: user.username,
           email: user.email,
           created_at: user.inserted_at,
           last_login: get_last_login_time(user_id)
         }}

      {:error, reason} = error ->
        Logger.error("Failed to get user info for #{user_id}: #{inspect(reason)}")
        error
    end
  end

  @doc """
  更新用户资料
  """
  def update_user_profile(user_id, profile_params) do
    Logger.info("Updating profile for user: #{user_id}")

    case User.update(user_id, profile_params) do
      {:ok, user} ->
        Logger.info("Profile updated for user #{user_id}")
        {:ok, user}

      {:error, reason} = error ->
        Logger.error("Failed to update profile for user #{user_id}: #{inspect(reason)}")
        error
    end
  end

  # 私有函数

  defp authenticate_user(username, password) do
    case User.get_by_username(username) do
      {:ok, user} ->
        if verify_password(password, user.password_hash) do
          {:ok, user}
        else
          {:error, :invalid_credentials}
        end

      {:error, :not_found} ->
        {:error, :invalid_credentials}

      {:error, reason} = error ->
        error
    end
  end

  defp verify_password(password, password_hash) do
    # 这里应该使用真正的密码验证逻辑
    # 为了演示，我们简单比较
    password == password_hash
  end

  defp create_user_session(user_id) do
    # 这里应该创建用户会话
    Logger.info("Created session for user #{user_id}")
    {:ok, %{session_id: generate_session_id(), user_id: user_id}}
  end

  defp record_login_activity(user_id) do
    # 这里应该记录登录活动到数据库
    Logger.info("Recorded login activity for user #{user_id}")
    {:ok, %{status: :recorded}}
  end

  defp create_user(user_params) do
    # 这里应该创建用户记录
    user = %{
      id: generate_user_id(),
      username: user_params.username,
      email: user_params.email,
      # 实际应该加密
      password_hash: user_params.password,
      inserted_at: DateTime.utc_now()
    }

    Logger.info("Created user #{user.id}")
    {:ok, user}
  end

  defp initialize_user_account(user_id) do
    # 这里应该初始化用户账户
    Logger.info("Initialized account for user #{user_id}")
    {:ok, %{balance: Decimal.new(0)}}
  end

  defp create_user_profile(user_id) do
    # 这里应该创建用户资料
    Logger.info("Created profile for user #{user_id}")
    {:ok, %{user_id: user_id}}
  end

  defp invalidate_user_session(user_id) do
    # 这里应该使用户会话失效
    Logger.info("Invalidated session for user #{user_id}")
    {:ok, %{status: :invalidated}}
  end

  defp record_logout_activity(user_id) do
    # 这里应该记录登出活动到数据库
    Logger.info("Recorded logout activity for user #{user_id}")
    {:ok, %{status: :recorded}}
  end

  defp get_last_login_time(user_id) do
    # 这里应该从数据库获取最后登录时间
    # 为了演示，返回当前时间
    DateTime.utc_now()
  end

  defp generate_session_id do
    :crypto.strong_rand_bytes(32) |> Base.encode64()
  end

  defp generate_user_id do
    "user_" <> (:crypto.strong_rand_bytes(8) |> Base.encode16(case: :lower))
  end

  # 测试辅助函数

  @doc """
  模拟用户注册（用于测试）
  """
  def simulate_user_register(username, email \\ nil, password \\ "password123") do
    user_params = %{
      username: username,
      email: email || "#{username}@example.com",
      password: password
    }

    handle_user_register(user_params)
  end

  @doc """
  模拟用户登录（用于测试）
  """
  def simulate_user_login(username, password \\ "password123") do
    handle_user_login(username, password)
  end

  @doc """
  创建测试用户并登录（用于测试）
  """
  def create_and_login_test_user(username \\ nil) do
    username = username || "test_user_#{:rand.uniform(10000)}"

    with {:ok, user} <- simulate_user_register(username),
         {:ok, login_result} <- simulate_user_login(username) do
      Logger.info("Created and logged in test user: #{username}")
      {:ok, %{user: user, login: login_result}}
    end
  end
end
