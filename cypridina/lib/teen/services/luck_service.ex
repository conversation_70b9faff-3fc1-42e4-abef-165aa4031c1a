defmodule Teen.Services.LuckService do
  @moduledoc """
  幸运值服务 - 提供简单的获取和修改幸运值的接口
  """

  alias Teen.UserLuckValue
  import Ash.Query

  @doc """
  获取用户的幸运值
  """
  def get_user_luck(user_id, game_type \\ "teen_patti") do
    case UserLuckValue.get_luck(user_id, game_type) do
      {:ok, luck_record} -> {:ok, luck_record.current_luck}
      # 未找到记录时返回默认值 -1
      {:error, _} -> {:ok, -1}
    end
  end

  @doc """
  修改用户的幸运值
  """
  def update_luck(user_id, new_luck, game_type \\ "teen_patti") do
    case UserLuckValue.get_luck(user_id, game_type) do
      {:ok, luck_record} ->
        UserLuckValue.update_luck(luck_record, new_luck: new_luck)

      {:error, _} ->
        # 如果记录不存在，创建新记录
        create_luck_record(user_id, game_type, new_luck)
    end
  end

  @doc """
  管理员重置幸运值（用于后台管理）
  """
  def admin_reset_luck(user_id, new_value, game_type \\ "teen_patti") do
    case UserLuckValue.get_luck(user_id, game_type) do
      {:ok, luck_record} ->
        UserLuckValue.admin_reset(luck_record, new_value: new_value)

      {:error, _} ->
        # 如果记录不存在，创建新记录
        create_luck_record(user_id, game_type, new_value)
    end
  end

  @doc """
  确保所有用户都有幸运值记录，如果没有则创建默认记录
  """
  def ensure_all_users_have_luck_records(game_type \\ "teen_patti", _opts \\ []) do
    # 获取所有用户
    case Ash.read(Cypridina.Accounts.User) do
      {:ok, users} ->
        # 获取已有的幸运值记录
        case UserLuckValue |> Ash.Query.filter(game_type == "teen_patti") |> Ash.read() do
          {:ok, existing_records} ->
            existing_user_ids = MapSet.new(existing_records, & &1.user_id)

            # 找出没有幸运值记录的用户
            users_without_luck =
              users
              |> Enum.reject(&(&1.numeric_id in existing_user_ids))
              |> Enum.reject(&is_robot_user?/1)

            # 为没有记录的用户创建默认幸运值记录
            new_records =
              Enum.reduce(users_without_luck, [], fn user, acc ->
                case create_luck_record(user.numeric_id, game_type, -1) do
                  {:ok, record} -> [record | acc]
                  {:error, _} -> acc
                end
              end)

            # 返回所有记录
            all_records = existing_records ++ new_records
            {:ok, all_records}

          {:error, reason} ->
            {:error, reason}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取幸运值统计信息
  """
  def get_luck_statistics(game_type \\ "teen_patti") do
    case UserLuckValue |> Ash.Query.filter(game_type == "teen_patti") |> Ash.read() do
      {:ok, records} ->
        total_users = length(records)
        never_recharged = Enum.count(records, &(&1.current_luck == -1))
        active_users = total_users - never_recharged

        luck_values = Enum.map(records, & &1.current_luck) |> Enum.reject(&(&1 == -1))

        {average_luck, max_luck, min_luck} =
          if length(luck_values) > 0 do
            avg = luck_values |> Enum.sum() |> div(length(luck_values))
            max_val = Enum.max(luck_values)
            min_val = Enum.min(luck_values)
            {avg, max_val, min_val}
          else
            {0, 0, 0}
          end

        stats = %{
          total_users: total_users,
          never_recharged: never_recharged,
          active_users: active_users,
          average_luck: average_luck,
          max_luck: max_luck,
          min_luck: min_luck
        }

        {:ok, stats}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户充值次数
  """
  def get_user_recharge_count(user_id) do
    # 这里应该从充值记录表获取，暂时返回默认值
    {:ok, 0}
  end

  @doc """
  批量重置幸运值
  """
  def batch_reset_luck(user_ids, new_value, game_type \\ "teen_patti") do
    results =
      Enum.map(user_ids, fn user_id ->
        admin_reset_luck(user_id, new_value, game_type)
      end)

    {:ok, results}
  end

  # 判断是否为机器人用户
  defp is_robot_user?(user) do
    # 根据用户名判断是否为机器人
    username_str = to_string(user.username)

    username_str |> String.starts_with?("robot_") or
      username_str |> String.contains?("bot") or
      Map.get(user, :is_robot, false)
  end

  # 私有函数：创建新的幸运值记录
  defp create_luck_record(user_id, game_type, luck_value) do
    UserLuckValue
    |> Ash.Changeset.for_create(:create, %{
      user_id: user_id,
      game_type: game_type,
      current_luck: luck_value,
      last_updated_at: DateTime.utc_now()
    })
    |> Ash.create()
  end
end
