defmodule Teen.Services.GameService do
  @moduledoc """
  游戏服务 - 协调游戏流程和跨系统集成
  
  主要职责：
  - 游戏会话的统一入口
  - 协调游戏记录和事件发布
  - 处理复杂的游戏业务逻辑
  - 批量游戏操作（用于测试和模拟）
  """

  require Logger
  alias Teen.GameManagement.GameRecord

  @doc """
  开始游戏 - 便捷方法
  """
  def start_game(user_id, game_type, bet_amount) do
    GameRecord.start_game(user_id, game_type, bet_amount)
  end

  @doc """
  完成游戏 - 便捷方法
  """
  def complete_game(game_id, result_data) do
    GameRecord.complete_game_session(game_id, result_data)
  end

  @doc """
  获取用户游戏历史 - 便捷方法
  """
  def get_user_game_history(user_id, opts \\ []) do
    GameRecord.get_game_history(user_id, opts)
  end

  @doc """
  获取用户游戏统计 - 便捷方法
  """
  def get_user_game_stats(user_id, date_range \\ nil) do
    GameRecord.get_game_statistics(user_id, date_range)
  end

  @doc """
  处理游戏房间内的游戏流程
  这种涉及多个系统的复杂操作适合保留在服务层
  """
  def process_room_game(room_id, game_data) do
    # 这里可以处理房间内多个玩家的游戏逻辑
    # 包括匹配、下注同步、结果计算等
    Logger.info("Processing room game: #{room_id}")
    
    # 示例：处理房间内所有玩家的游戏结果
    with {:ok, room} <- get_room_info(room_id),
         {:ok, results} <- calculate_room_results(room, game_data),
         {:ok, _} <- distribute_room_winnings(room, results) do
      {:ok, results}
    end
  end

  @doc """
  处理锦标赛游戏
  涉及多个玩家和复杂的积分计算
  """
  def process_tournament_game(tournament_id, round_data) do
    Logger.info("Processing tournament game: #{tournament_id}")
    
    # 锦标赛涉及多轮游戏、积分排名、奖池分配等复杂逻辑
    # 这种跨多个资源的复杂业务逻辑适合在服务层处理
    {:ok, %{tournament_id: tournament_id, status: :processed}}
  end

  # 测试辅助函数

  @doc """
  模拟完整的游戏流程（用于测试）
  """
  def simulate_game(user_id, game_type, bet_amount, outcome \\ :win) do
    with {:ok, start_result} <- start_game(user_id, game_type, bet_amount),
         {:ok, game_result} <- complete_game(start_result.game_id, %{outcome: outcome}) do
      Logger.info("Simulated game completed for user #{user_id}: #{game_result.result_status}")
      {:ok, game_result}
    end
  end

  @doc """
  批量模拟游戏（用于测试任务系统）
  """
  def simulate_multiple_games(user_id, game_count, game_type \\ "teen_patti", bet_amount \\ 100) do
    results =
      1..game_count
      |> Enum.map(fn _i ->
        outcome = if :rand.uniform() > 0.5, do: :win, else: :lose
        simulate_game(user_id, game_type, bet_amount, outcome)
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)

    Logger.info("Simulated #{success_count}/#{game_count} games for user #{user_id}")
    {:ok, %{total: game_count, success: success_count, results: results}}
  end

  @doc """
  处理机器人游戏
  协调机器人服务和游戏记录
  """
  def process_robot_game(robot_id, game_type, strategy \\ :random) do
    # 机器人游戏涉及策略选择、行为模拟等
    # 需要与RobotService协调
    Logger.info("Processing robot game: #{robot_id}, strategy: #{strategy}")
    
    bet_amount = calculate_robot_bet(strategy)
    outcome = calculate_robot_outcome(strategy)
    
    simulate_game(robot_id, game_type, bet_amount, outcome)
  end

  # 私有辅助函数

  defp get_room_info(room_id) do
    # 获取房间信息的逻辑
    {:ok, %{room_id: room_id, players: []}}
  end

  defp calculate_room_results(room, game_data) do
    # 计算房间游戏结果的逻辑
    {:ok, %{winners: [], losers: []}}
  end

  defp distribute_room_winnings(room, results) do
    # 分配房间奖金的逻辑
    {:ok, results}
  end

  defp calculate_robot_bet(strategy) do
    case strategy do
      :conservative -> 50
      :aggressive -> 500
      _ -> 100
    end
  end

  defp calculate_robot_outcome(strategy) do
    case strategy do
      :conservative -> if(:rand.uniform() > 0.3, do: :win, else: :lose)
      :aggressive -> if(:rand.uniform() > 0.7, do: :win, else: :lose)
      _ -> if(:rand.uniform() > 0.5, do: :win, else: :lose)
    end
  end
end