defmodule Teen.Services.TurnoverService do
  @moduledoc """
  流水洗码服务

  实现流水洗码规则验证，包括：
  - 计算用户所需流水金额
  - 验证用户是否满足提现条件
  - 管理不同类型金币的流水倍数
  - 处理流水清零逻辑
  """

  require Logger
  alias Cypridina.Ledger
  alias Cypridina.Accounts.User

  # 流水倍数配置
  @turnover_multipliers %{
    # 充值本金
    "deposit" => Decimal.new("1"),
    # 存钱罐领取
    "piggy_bank" => Decimal.new("5"),
    # 流水返利
    "cash_back" => Decimal.new("5"),
    # VIP奖励
    "vip_reward" => Decimal.new("5"),
    # 每日任务奖励
    "daily_task" => Decimal.new("5"),
    # 注册赠送
    "register_bonus" => Decimal.new("5"),
    # 绑定手机赠送
    "phone_binding" => Decimal.new("5"),
    # 转盘奖励
    "lucky_spin" => Decimal.new("5"),
    # 7天登录奖励
    "login_reward" => Decimal.new("5"),
    # 月卡额外奖励
    "monthly_card" => Decimal.new("5"),
    # 周首充额外奖励
    "first_recharge" => Decimal.new("5"),
    # 破产充值额外奖励
    "bankruptcy_bonus" => Decimal.new("5"),
    # 兑换码
    "bonus_code" => Decimal.new("5")
  }

  @doc """
  验证用户是否满足提现条件
  """
  def validate_withdrawal_eligibility(user_id, withdrawal_amount) do
    with {:ok, user} <- User.get_by_id(user_id),
         {:ok, balance_info} <- get_user_balance_info(user_id),
         {:ok, turnover_info} <- get_user_turnover_info(user_id) do
      # 检查余额是否足够
      if Decimal.compare(balance_info.withdrawable_balance, withdrawal_amount) == :lt do
        {:error, "可提现余额不足"}
      else
        # 检查流水要求
        remaining_turnover =
          Decimal.sub(turnover_info.required_turnover, turnover_info.completed_turnover)

        if Decimal.compare(remaining_turnover, Decimal.new("0")) == :gt do
          {:error, "流水要求未满足，还需完成 #{remaining_turnover} 的有效投注"}
        else
          {:ok,
           %{
             withdrawable_amount: withdrawal_amount,
             current_balance: balance_info.total_balance,
             withdrawable_balance: balance_info.withdrawable_balance,
             required_turnover: turnover_info.required_turnover,
             completed_turnover: turnover_info.completed_turnover
           }}
        end
      end
    end
  end

  @doc """
  计算用户的流水要求
  """
  def calculate_turnover_requirement(user_id, bonus_type, amount) do
    Teen.UserSystem.add_turnover_requirement(user_id, amount, bonus_type)
  end

  @doc """
  更新用户完成的流水金额
  """
  def update_completed_turnover(user_id, bet_amount) do
    Teen.UserSystem.update_completed_turnover(user_id, bet_amount)
  end

  @doc """
  检查并在必要时清零流水
  """
  def check_and_reset_turnover_if_needed(user_id) do
    Teen.UserSystem.check_and_reset_turnover_if_needed(user_id)
  end

  @doc """
  获取用户余额信息
  """
  def get_user_balance_info(user_id) do
    try do
      # 获取用户总余额
      total_balance = Ledger.get_user_balance(user_id, "XAA")

      # 获取流水信息来计算可提现余额
      case get_user_turnover_info(user_id) do
        {:ok, turnover_info} ->
          remaining_turnover =
            Decimal.sub(turnover_info.required_turnover, turnover_info.completed_turnover)

          # 如果还有未完成的流水要求，可提现余额为0
          withdrawable_balance =
            if Decimal.compare(remaining_turnover, Decimal.new("0")) == :gt do
              Decimal.new("0")
            else
              total_balance
            end

          {:ok,
           %{
             total_balance: total_balance,
             withdrawable_balance: withdrawable_balance,
             locked_balance: Decimal.sub(total_balance, withdrawable_balance)
           }}

        {:error, _} ->
          # 没有流水要求，全部可提现
          {:ok,
           %{
             total_balance: total_balance,
             withdrawable_balance: total_balance,
             locked_balance: Decimal.new("0")
           }}
      end
    rescue
      error ->
        Logger.error("获取用户余额失败: #{inspect(error)}")
        {:error, "获取余额信息失败"}
    end
  end

  @doc """
  获取用户流水信息
  """
  def get_user_turnover_info(user_id) do
    Teen.UserSystem.get_user_turnover_info(user_id)
  end

  @doc """
  创建用户流水要求记录
  """
  defp create_user_turnover_requirement(
         user_id,
         required_turnover,
         completed_turnover \\ Decimal.new("0")
       ) do
    # 这里应该在数据库中创建流水记录
    # 暂时返回成功，后续需要实现真实的数据库操作
    Logger.info("为用户 #{user_id} 创建流水要求: 需要 #{required_turnover}, 已完成 #{completed_turnover}")
    {:ok, %{required_turnover: required_turnover, completed_turnover: completed_turnover}}
  end

  @doc """
  更新用户流水要求
  """
  defp update_user_turnover_requirement(user_id, new_required) do
    # 这里应该更新数据库中的流水要求
    Logger.info("更新用户 #{user_id} 流水要求为: #{new_required}")
    {:ok, new_required}
  end

  @doc """
  更新用户已完成流水
  """
  defp update_user_completed_turnover(user_id, new_completed) do
    # 这里应该更新数据库中的已完成流水
    Logger.info("更新用户 #{user_id} 已完成流水为: #{new_completed}")
    {:ok, new_completed}
  end

  @doc """
  重置用户流水要求
  """
  defp reset_user_turnover(user_id) do
    # 这里应该重置数据库中的流水记录
    Logger.info("重置用户 #{user_id} 的流水要求")
    {:ok, %{required_turnover: Decimal.new("0"), completed_turnover: Decimal.new("0")}}
  end

  @doc """
  获取流水倍数配置
  """
  def get_turnover_multiplier(bonus_type) do
    Map.get(@turnover_multipliers, bonus_type, Decimal.new("1"))
  end

  @doc """
  获取所有流水倍数配置
  """
  def get_all_turnover_multipliers do
    @turnover_multipliers
  end
end
