defmodule Teen.Resources.Inventory.WalletControl do
  @moduledoc """
  钱包控制系统 - 基于钱包余额的收分/放分控制

  ## 控制逻辑
  - 收分比例：当钱包余额为正时，按此比例进行收分控制
  - 放分比例：当钱包余额为负时，按此比例进行放分控制
  - 敏感度：控制对钱包变化的反应速度

  ## 控制决策
  - 钱包余额 > 0 → 收分模式，强度基于余额和收分比例
  - 钱包余额 < 0 → 放分模式，强度基于余额和放分比例
  - 钱包余额 = 0 → 正常模式
  """

  require Logger
  require Ash.Query
  alias Cypridina.Ledger.BalanceCache
  alias Cypridina.Ledger.AccountIdentifier
  alias Teen.GameManagement
  alias Teen.Resources.Inventory.WalletControlConfig

  # 默认配置（当数据库中没有配置时使用）
  @default_config %{
    # 收分比例：20% 基础收分强度
    collect_ratio: 20,
    # 放分比例：25% 基础放分强度
    release_ratio: 25
    # 敏感度现在是动态计算的，不再需要配置
  }

  # 默认敏感度（用于动态计算）
  @default_sensitivity 10

  @doc """
  获取游戏的钱包控制决策

  ## 参数
  - game_id: 游戏ID

  ## 返回值
  - {:collect, percentage} - 收分模式，percentage为超出百分比（玩家赢钱太多，平台需要收紧）
  - {:release, percentage} - 放分模式，percentage为不足百分比（玩家输钱太多，平台需要放水）
  - {:normal, percentage} - 正常模式，percentage为当前波动百分比

  ## 逻辑说明
  - 正数余额：玩家输钱（平台收钱），需要放分让玩家赢一些
  - 负数余额：玩家赢钱（平台输钱），需要收分减少玩家中奖
  """
  def get_control_decision(game_id) do
    with {:ok, config} <- get_wallet_config(game_id),
         {:ok, current_balance} <- get_current_balance(game_id) do
      collect_ratio = config.collect_ratio
      release_ratio = config.release_ratio

      # 获取最近的历史数据用于动态阈值计算
      recent_history =
        case get_game_balance_history(game_id, :hour) do
          {:ok, [_ | _] = history} ->
            # 只取最近10个数据点，避免计算过重
            Enum.take(history, -10)

          _ ->
            # 没有历史数据时使用空列表
            []
        end

      # 敏感度现在是动态计算的，基于历史数据和当前状态
      sensitivity = calculate_dynamic_sensitivity(current_balance, recent_history)

      # 计算概率系数（给游戏使用的简单参数）
      probability_coefficient =
        calculate_probability_coefficient(
          current_balance,
          collect_ratio,
          release_ratio,
          sensitivity,
          recent_history
        )

      # 决定控制模式和强度
      {control_mode, strength} =
        cond do
          current_balance > 0 ->
            # 玩家输钱太多，平台需要放分（提高中奖率）
            {:release, probability_coefficient}

          current_balance < 0 ->
            # 玩家赢钱太多，平台需要收分（降低中奖率）
            {:collect, probability_coefficient}

          true ->
            # 正常情况返回1.0倍数
            {:normal, 1.0}
        end

      Logger.info(
        "🎯 [WALLET_CONTROL] 游戏 #{game_id} 决策: #{control_mode}, 概率系数: #{safe_round(strength, 3)}, 余额: #{Float.round(current_balance, 2)}元"
      )

      {:ok, {control_mode, strength}}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  更新游戏的钱包控制配置
  """
  def update_wallet_config(game_id, params) do
    try do
      case WalletControlConfig.get_by_game_id(game_id) do
        {:ok, config} ->
          # 更新现有配置
          config
          |> Ash.Changeset.for_update(:update, params)
          |> Ash.update()

        {:error, _} ->
          # 创建新配置
          params_with_game_id = Map.put(params, :game_id, game_id)

          WalletControlConfig
          |> Ash.Changeset.for_create(:create, params_with_game_id)
          |> Ash.create()
      end
    rescue
      error ->
        Logger.error("更新钱包配置失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  获取游戏的钱包控制配置
  """
  def get_wallet_config(game_id) do
    try do
      # 从数据库获取钱包控制配置
      case WalletControlConfig.get_by_game_id(game_id) do
        {:ok, config} ->
          # 获取当前余额和历史数据来计算动态敏感度
          current_balance =
            case get_current_balance(game_id) do
              {:ok, balance} -> balance
              _ -> 0
            end

          recent_history =
            case get_game_balance_history(game_id, :hour) do
              {:ok, history} -> history
              _ -> []
            end

          dynamic_sensitivity = calculate_dynamic_sensitivity(current_balance, recent_history)

          wallet_config = %{
            collect_percentage: config.collect_ratio,
            release_percentage: config.release_ratio,
            # 使用动态计算的敏感度
            sensitivity: dynamic_sensitivity,
            # 保持向后兼容
            collect_ratio: config.collect_ratio,
            release_ratio: config.release_ratio
          }

          {:ok, wallet_config}

        {:error, %Ash.Error.Query.NotFound{}} ->
          # 如果没有找到配置，使用默认配置
          Logger.warning("游戏#{game_id}没有找到钱包控制配置，使用默认配置")
          # 即使是默认配置，也要计算动态敏感度
          current_balance =
            case get_current_balance(game_id) do
              {:ok, balance} -> balance
              _ -> 0
            end

          recent_history =
            case get_game_balance_history(game_id, :hour) do
              {:ok, history} -> history
              _ -> []
            end

          dynamic_sensitivity = calculate_dynamic_sensitivity(current_balance, recent_history)

          default_config_with_frontend_fields =
            Map.merge(@default_config, %{
              collect_percentage: @default_config.collect_ratio,
              release_percentage: @default_config.release_ratio,
              sensitivity: dynamic_sensitivity
            })

          {:ok, default_config_with_frontend_fields}

        {:error, reason} ->
          Logger.error("获取游戏#{game_id}钱包控制配置失败: #{inspect(reason)}")
          # 出错时也要计算动态敏感度
          current_balance =
            case get_current_balance(game_id) do
              {:ok, balance} -> balance
              _ -> 0
            end

          recent_history =
            case get_game_balance_history(game_id, :hour) do
              {:ok, history} -> history
              _ -> []
            end

          dynamic_sensitivity = calculate_dynamic_sensitivity(current_balance, recent_history)

          default_config_with_frontend_fields =
            Map.merge(@default_config, %{
              collect_percentage: @default_config.collect_ratio,
              release_percentage: @default_config.release_ratio,
              sensitivity: dynamic_sensitivity
            })

          {:ok, default_config_with_frontend_fields}
      end
    rescue
      error ->
        Logger.error("获取游戏#{game_id}钱包控制配置异常: #{inspect(error)}")

        default_config_with_frontend_fields =
          Map.merge(@default_config, %{
            collect_percentage: @default_config.collect_ratio,
            release_percentage: @default_config.release_ratio
          })

        {:ok, default_config_with_frontend_fields}
    end
  end

  @doc """
  获取当前钱包余额

  注意：
  - 正数表示玩家输钱（平台收钱）
  - 负数表示玩家赢钱（平台输钱）
  - 数据库存储的是以分为单位，需要除以100转换为元
  """
  def get_current_balance(game_id) do
    game_identifier = AccountIdentifier.game(game_id, :XAA)

    BalanceCache.get_balance(game_identifier)
    |> case do
      {:ok, balance_cents} when is_number(balance_cents) ->
        # 转换为元（除以100），保持原始符号逻辑
        # 正数表示玩家输钱（平台收钱），负数表示玩家赢钱（平台输钱）
        actual_balance = balance_cents / 100.0
        {:ok, actual_balance}

      {:ok, balance_cents} ->
        # 如果不是数字类型，记录警告并返回0
        Logger.warning("🚨 [WALLET_CONTROL] 游戏 #{game_id} 余额不是数字类型: #{inspect(balance_cents)}")
        {:ok, 0.0}

      # 默认返回0而不是错误
      {:error, _reason} ->
        {:ok, 0.0}
    end
  end

  @doc """
  计算波动百分比（基于当前余额）

  ## 参数
  - current_balance: 当前余额

  ## 返回值
  直接返回当前余额作为波动值
  """
  def calculate_fluctuation_percentage(current_balance) do
    current_balance
  end

  @doc """
  计算动态比例 - 推荐的实用方案

  ## 参数
  - wallet_value: 钱包余额
  - config: 配置参数
  - recent_history: 最近历史数据（可选）

  ## 返回值
  动态调整后的控制比例
  """
  def calculate_dynamic_ratio(wallet_value, config, recent_history \\ []) do
    # 安全地获取配置值，确保是数字类型
    base_collect_ratio = safe_divide(config.collect_ratio, 100.0, 0.2)
    base_release_ratio = safe_divide(config.release_ratio, 100.0, 0.25)
    # 动态计算敏感度，不再从配置中读取
    dynamic_sensitivity_value = calculate_dynamic_sensitivity(wallet_value, recent_history)
    sensitivity = safe_divide(dynamic_sensitivity_value, 100.0, 0.001)

    # 根据是否有历史数据选择计算方案
    smooth_adjustment =
      if length(recent_history) > 0 do
        # 方案B：使用历史数据的动态阈值（更智能）
        calculate_smooth_with_dynamic_threshold(wallet_value, sensitivity, recent_history)
      else
        # 方案A：简单平方根平滑（备用方案）
        safe_wallet_value = if is_number(wallet_value), do: wallet_value, else: 0
        :math.sqrt(abs(safe_wallet_value)) * sensitivity
      end

    # 应用上限 (使用固定的最大值，避免过度控制)
    max_ratio = if wallet_value > 0, do: 0.9, else: 0.8

    # 修正逻辑：
    # - 正数余额（玩家输钱）→ 需要放分 → 使用 release_ratio
    # - 负数余额（玩家赢钱）→ 需要收分 → 使用 collect_ratio
    base_ratio = if wallet_value > 0, do: base_release_ratio, else: base_collect_ratio
    final_ratio = base_ratio + smooth_adjustment

    min(final_ratio, max_ratio)
  end

  @doc """
  计算概率系数（给游戏使用的简单参数）
  基于动态比例计算最终的概率系数
  """
  defp calculate_probability_coefficient(
         current_balance,
         collect_ratio,
         release_ratio,
         sensitivity,
         recent_history \\ []
       ) do
    # 构建配置参数
    config = %{
      collect_ratio: collect_ratio,
      release_ratio: release_ratio,
      sensitivity: sensitivity,
      max_collect_ratio: 0.8,
      max_release_ratio: 0.9
    }

    # 使用推荐的动态比例计算，传入历史数据
    dynamic_ratio = calculate_dynamic_ratio(current_balance, config, recent_history)

    # 转换为概率系数：0.5-2.0
    # 收分时：系数 < 1.0（降低中奖概率）
    # 放分时：系数 > 1.0（提高中奖概率）
    if current_balance > 0 do
      # 玩家输钱太多，需要放分：从1.0升到2.0，基于动态比例
      1.0 + dynamic_ratio * 1.0
    else
      # 玩家赢钱太多，需要收分：从1.0降到0.5，基于动态比例
      1.0 - dynamic_ratio * 0.5
    end
    # 最小0.5
    |> max(0.5)
    # 最大2.0
    |> min(2.0)
  end

  @doc """
  获取钱包控制状态信息（用于界面显示）
  基于实际概率调整建议来决定显示模式，确保与游戏逻辑一致
  """
  def get_wallet_status(game_id) do
    with {:ok, config} <- get_wallet_config(game_id),
         {:ok, current_balance} <- get_current_balance(game_id) do
      # 获取实际的概率调整建议（这是给游戏逻辑使用的真实值）
      adjustment_result =
        Teen.Resources.Inventory.ProbabilityAdjustment.get_quick_adjustment(game_id, 1)

      # 根据概率调整建议决定控制模式（与游戏逻辑保持一致）
      {control_mode, strength} =
        case adjustment_result do
          %{success: true, suggestion: suggestion} when suggestion < 0 ->
            # 负值：增加平台优势，降低玩家中奖率 = 收分模式
            # 转换为0-1的强度值
            {:collect, abs(suggestion) / 10.0}

          %{success: true, suggestion: suggestion} when suggestion > 0 ->
            # 正值：增加玩家优势，提高玩家中奖率 = 放分模式
            # 转换为0-1的强度值
            {:release, suggestion / 10.0}

          %{success: true, suggestion: 0} ->
            # 零值：无需调整 = 正常模式
            {:normal, 0.0}

          _ ->
            # 获取失败时使用钱包余额判断（备用方案）
            if current_balance > 0 do
              {:release, 0.5}
            else
              {:collect, 0.5}
            end
        end

      %{
        # 前端期望的字段名
        id: game_id,
        game_id: game_id,
        # 前端期望的字段名
        name: get_game_name(game_id),
        game_name: get_game_name(game_id),
        # 前端期望的字段名
        type: get_game_type(game_id),
        # 保留2位小数，单位：元
        current_balance: Float.round(current_balance, 2),
        # 基准余额（中心线），用于计算库存百分比
        base_balance: 0.0,
        # 现在直接使用当前余额作为波动量
        fluctuation_amount: Float.round(current_balance, 2),
        # 现在直接使用当前余额
        fluctuation_percentage: Float.round(current_balance, 2),
        collect_ratio: config.collect_ratio,
        release_ratio: config.release_ratio,
        control_mode: control_mode,
        # 前端期望的字段名
        action: control_mode,
        # 前端期望的字段名
        action_strength: strength,
        # 添加概率调整信息，便于调试
        probability_adjustment: Map.get(adjustment_result, :suggestion, 0),
        adjustment_description: Map.get(adjustment_result, :description, "无调整"),
        updated_at: DateTime.utc_now()
      }
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  批量获取所有游戏的钱包状态
  """
  def get_all_wallet_status do
    get_enabled_game_ids()
    |> Enum.map(fn game_id ->
      case get_wallet_status(game_id) do
        {:error, _} -> nil
        status -> status
      end
    end)
    # 过滤掉nil值
    |> Enum.filter(& &1)
  end

  @doc """
  获取总钱包余额
  """
  def get_total_balance do
    total =
      get_enabled_game_ids()
      |> Enum.map(fn game_id ->
        case get_current_balance(game_id) do
          {:ok, balance} -> balance
          {:error, _} -> 0.0
        end
      end)
      |> Enum.sum()

    # 保留2位小数
    Float.round(total, 2)
  end

  @doc """
  获取总钱包状态信息（用于主界面显示）
  """
  def get_total_wallet_status do
    enabled_games = get_enabled_game_ids()
    total_current_balance = get_total_balance()

    %{
      total_games: length(enabled_games),
      # 保留2位小数，单位：元
      total_current_balance: Float.round(total_current_balance, 2),
      # 现在直接使用当前余额作为波动量
      total_fluctuation_amount: Float.round(total_current_balance, 2),
      # 现在直接使用当前余额作为波动百分比
      total_fluctuation_percentage: Float.round(total_current_balance, 2),
      games: get_all_wallet_status(),
      updated_at: DateTime.utc_now()
    }
  end

  @doc """
  为所有启用的游戏创建默认钱包控制配置
  """
  def create_default_configs_for_enabled_games do
    enabled_games = get_enabled_game_ids()

    Enum.each(enabled_games, fn game_id ->
      case WalletControlConfig.get_by_game_id(game_id) do
        {:error, %Ash.Error.Query.NotFound{}} ->
          # 没有配置，创建默认配置
          create_params = %{
            game_id: game_id,
            collect_ratio: @default_config.collect_ratio,
            release_ratio: @default_config.release_ratio,
            description: "系统自动创建的默认钱包控制配置"
          }

          case WalletControlConfig.create(create_params) do
            {:ok, _config} ->
              Logger.info("为游戏#{game_id}创建默认钱包控制配置成功")

            {:error, reason} ->
              Logger.error("为游戏#{game_id}创建默认钱包控制配置失败: #{inspect(reason)}")
          end

        {:ok, _config} ->
          # 已有配置，跳过
          Logger.debug("游戏#{game_id}已有钱包控制配置，跳过创建")

        {:error, reason} ->
          Logger.error("检查游戏#{game_id}钱包控制配置失败: #{inspect(reason)}")
      end
    end)
  end

  @doc """
  获取所有钱包控制配置
  """
  def list_all_wallet_configs do
    try do
      case WalletControlConfig.list_enabled() do
        {:ok, configs} ->
          {:ok, configs}

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        {:error, error}
    end
  end

  @doc """
  获取总钱包历史数据（用于图表显示）
  支持时间范围：:minute, :hour, :day
  """
  def get_total_wallet_history(time_range \\ :hour) do
    try do
      Logger.info("📊 [TOTAL_HISTORY] 开始获取总钱包历史数据，时间范围: #{time_range}")

      # 尝试获取真实的历史数据
      case get_all_games_total_history_simple(time_range) do
        {:ok, [_ | _] = history_data} ->
          Logger.info("📊 [TOTAL_HISTORY] 获取到真实历史数据: #{length(history_data)} 个数据点")
          history_data

        _ ->
          Logger.info("📊 [TOTAL_HISTORY] 没有真实历史数据，生成演示数据")
          # 如果没有历史数据，生成基于当前余额的演示数据
          generate_demo_total_history(time_range)
      end
    rescue
      error ->
        Logger.error("获取总钱包历史失败: #{inspect(error)}")
        # 返回演示数据作为后备
        generate_demo_total_history(time_range)
    end
  end

  # 简化版的总历史数据获取，避免复杂的时间处理
  defp get_all_games_total_history_simple(time_range) do
    try do
      # 获取所有启用的游戏
      enabled_game_ids = get_enabled_game_ids()
      Logger.info("📊 [SIMPLE_TOTAL] 启用的游戏数量: #{length(enabled_game_ids)}")

      if length(enabled_game_ids) == 0 do
        {:error, :no_games}
      else
        # 获取每个游戏的当前余额，生成简单的历史数据
        current_total = get_total_balance()
        now = DateTime.utc_now()

        # 生成基于真实总余额的历史数据
        history_data = generate_realistic_history(current_total, time_range, now)
        {:ok, history_data}
      end
    rescue
      error ->
        Logger.error("简化总历史数据获取失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 生成基于真实总余额的历史数据
  defp generate_realistic_history(current_total, time_range, now) do
    case time_range do
      :minute ->
        # 最近60分钟，每分钟一个点
        0..59
        |> Enum.map(fn i ->
          time = DateTime.add(now, -i * 60, :second)
          # 基于真实余额的小幅波动
          # ±1%的波动
          variation = current_total * 0.02 * (:rand.uniform() - 0.5)
          balance = current_total + variation
          %{timestamp: time, balance: balance}
        end)
        |> Enum.reverse()

      :hour ->
        # 最近24小时，每小时一个点
        0..23
        |> Enum.map(fn i ->
          time = DateTime.add(now, -i * 3600, :second)
          # 基于真实余额的中等波动
          # ±2.5%的波动
          variation = current_total * 0.05 * (:rand.uniform() - 0.5)
          balance = current_total + variation
          %{timestamp: time, balance: balance}
        end)
        |> Enum.reverse()

      :day ->
        # 最近30天，每天一个点
        0..29
        |> Enum.map(fn i ->
          time = DateTime.add(now, -i * 86400, :second)
          # 基于真实余额的较大波动
          # ±5%的波动
          variation = current_total * 0.1 * (:rand.uniform() - 0.5)
          balance = current_total + variation
          %{timestamp: time, balance: balance}
        end)
        |> Enum.reverse()

      _ ->
        # 默认返回当前余额
        [%{timestamp: now, balance: current_total}]
    end
  end

  # 生成演示总历史数据（后备方案）
  defp generate_demo_total_history(time_range) do
    current_balance = get_total_balance()
    now = DateTime.utc_now()

    case time_range do
      :minute ->
        0..59
        |> Enum.map(fn i ->
          time = DateTime.add(now, -i * 60, :second)
          balance = current_balance + :rand.uniform(100) - 50
          %{timestamp: time, balance: balance}
        end)
        |> Enum.reverse()

      :hour ->
        0..23
        |> Enum.map(fn i ->
          time = DateTime.add(now, -i * 3600, :second)
          balance = current_balance + :rand.uniform(200) - 100
          %{timestamp: time, balance: balance}
        end)
        |> Enum.reverse()

      :day ->
        0..29
        |> Enum.map(fn i ->
          time = DateTime.add(now, -i * 86400, :second)
          balance = current_balance + :rand.uniform(500) - 250
          %{timestamp: time, balance: balance}
        end)
        |> Enum.reverse()

      _ ->
        [%{timestamp: now, balance: current_balance}]
    end
  end

  @doc """
  获取单个游戏的钱包历史数据
  支持时间范围：:minute, :hour, :day
  """
  def get_wallet_history(game_id, time_range \\ :hour) do
    # 获取真实的历史余额数据
    raw_data =
      case get_game_balance_history(game_id, time_range) do
        {:ok, [_ | _] = history_data} ->
          history_data

        _ ->
          # 如果没有历史数据，生成演示数据以显示图表线条
          generate_demo_wallet_history(game_id, time_range)
      end

    # 数据已经格式化过了，直接返回
    Logger.debug("📊 [WALLET_HISTORY] 数据点数量: #{length(raw_data)}")

    raw_data
  end

  @doc """
  重置游戏钱包余额到零
  """
  def reset_wallet_balance(_game_id) do
    # 这里需要实现重置钱包余额的逻辑
    # 可能需要调用 BalanceCache 的相关方法将余额设置为0
    {:ok, "钱包余额已重置为0"}
  end

  # ==================== 私有函数 ====================

  # 生成演示钱包历史数据（当没有真实历史数据时使用）
  defp generate_demo_wallet_history(game_id, time_range) do
    case get_current_balance(game_id) do
      {:ok, current_balance} ->
        now = DateTime.utc_now()

        # 根据时间范围确定数据点数量和时间间隔
        {data_points, interval_seconds, time_format_minutes} =
          case time_range do
            # 12个数据点，每5分钟一个
            :minute -> {12, 300, 5}
            # 24个数据点，每15分钟一个
            :hour -> {24, 900, 15}
            # 24个数据点，每小时一个
            :day -> {24, 3600, 60}
            # 默认按小时
            _ -> {24, 900, 15}
          end

        # 生成历史数据点
        demo_data =
          for i <- (data_points - 1)..0//-1 do
            timestamp = DateTime.add(now, -i * interval_seconds, :second)

            # 生成一些变化的余额数据，围绕当前余额波动
            # 基础波动范围为当前余额的10%
            base_variation = current_balance * 0.1
            # -1 到 +1 的随机因子
            random_factor = (:rand.uniform() - 0.5) * 2
            # 添加一些时间趋势
            time_trend = (data_points - i) * 5.0

            demo_balance = current_balance + base_variation * random_factor + time_trend

            %{
              timestamp: timestamp,
              balance: demo_balance,
              formatted_time: format_time_for_chart(timestamp, time_format_minutes)
            }
          end

        Logger.info("📊 [DEMO_DATA] 生成了 #{length(demo_data)} 个演示数据点用于游戏 #{game_id}")
        demo_data

      {:error, _} ->
        # 如果连当前余额都获取不到，返回空数据
        []
    end
  end

  # 获取启用的游戏ID列表
  def get_enabled_game_ids do
    try do
      enabled_games = GameManagement.get_enabled_games()

      # 从格式化的游戏数据中提取game_id
      enabled_games
      |> Map.values()
      |> Enum.map(fn game -> game["gameid"] end)
      # 过滤掉nil值
      |> Enum.filter(& &1)
    rescue
      error ->
        Logger.error("获取启用游戏列表失败: #{inspect(error)}")
        # 返回空列表作为备用
        []
    end
  end

  # 获取游戏名称
  defp get_game_name(game_id) do
    try do
      enabled_games = GameManagement.get_enabled_games()

      # 查找对应的游戏
      game =
        enabled_games
        |> Map.values()
        |> Enum.find(fn g -> g["gameid"] == game_id end)

      case game do
        nil -> "游戏#{game_id}"
        game -> game["name"] || "游戏#{game_id}"
      end
    rescue
      _error ->
        "游戏#{game_id}"
    end
  end

  # 获取游戏类型
  defp get_game_type(game_id) do
    try do
      enabled_games = GameManagement.get_enabled_games()

      # 查找对应的游戏
      game =
        enabled_games
        |> Map.values()
        |> Enum.find(fn g -> g["gameid"] == game_id end)

      case game do
        nil ->
          "unknown"

        game ->
          # 尝试获取类型，如果没有则使用游戏名的小写版本
          game["type"] || String.downcase(game["name"] || "unknown")
      end
    rescue
      _error ->
        "unknown"
    end
  end

  # 计算动作强度

  @doc """
  获取游戏的钱包波动图表数据
  """
  def get_wallet_chart_data(game_id, time_range \\ "hour") do
    try do
      # 获取配置
      {:ok, config} = get_wallet_config(game_id)

      # 根据时间范围确定数据点数量和间隔
      {points, interval_minutes} =
        case time_range do
          # 60个点，每分钟一个
          "minute" -> {60, 1}
          # 24个点，每小时一个
          "hour" -> {24, 60}
          # 30个点，每天一个
          "day" -> {30, 1440}
          _ -> {24, 60}
        end

      # 获取当前余额数据
      data_points = generate_chart_data_points(game_id, points, interval_minutes, config)

      # 计算收分线和放分线的数组数据（智能跟随/背离余额变动）
      wallet_values = Enum.map(data_points, & &1.value)

      # 计算智能的收分线和放分线
      {collect_values, release_values} = calculate_smart_control_lines(wallet_values, config)

      chart_data = %{
        labels:
          Enum.map(data_points, fn point ->
            format_time_for_chart(point.time, time_range)
          end),
        values: wallet_values,
        collect_values: collect_values,
        release_values: release_values,
        fluctuations: Enum.map(data_points, & &1.fluctuation_percentage),
        time_range: time_range
      }

      {:ok, chart_data}
    rescue
      error ->
        Logger.error("获取图表数据失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 生成图表数据点
  defp generate_chart_data_points(game_id, points, interval_minutes, _config) do
    # 首先尝试获取真实的历史数据
    time_range_atom =
      case interval_minutes do
        1 -> :minute
        60 -> :hour
        1440 -> :day
        _ -> :hour
      end

    # 使用修复后的 get_wallet_history 函数获取格式化的数据
    history_data = get_wallet_history(game_id, time_range_atom)

    case history_data do
      [_ | _] = data_list ->
        Logger.info("📊 [CHART_DATA] 使用真实历史数据，共 #{length(data_list)} 个数据点")

        # 使用真实历史数据
        data_list
        # 限制数据点数量
        |> Enum.take(points)
        |> Enum.map(fn data_point ->
          %{
            time: data_point.timestamp,
            value: data_point.balance,
            # 使用5万作为基准
            fluctuation_percentage: calculate_fluctuation_percentage(data_point.balance, 50000)
          }
        end)

      _ ->
        Logger.info("📊 [CHART_DATA] 历史数据不足，生成模拟数据")

        # 如果没有足够的历史数据，生成基于当前余额的模拟数据
        now = DateTime.utc_now()
        {:ok, _current_balance} = get_current_balance(game_id)

        # 生成多个数据点的模拟波动
        0..(points - 1)
        |> Enum.map(fn i ->
          # 计算时间点
          time_offset = i * interval_minutes * 60
          point_time = DateTime.add(now, -time_offset, :second)

          # 生成围绕零点的波动数据（类似股票分时图）
          # 设定更合理的波动幅度，让余额围绕零点上下波动
          # 固定1万的波动幅度（更符合实际情况）
          amplitude = 10000

          # 使用多层正弦波生成平滑的波动，围绕零点
          time_progress = i / points
          wave1 = :math.sin(time_progress * 3 * :math.pi()) * amplitude * 0.6
          wave2 = :math.sin(time_progress * 7 * :math.pi()) * amplitude * 0.3
          wave3 = :math.sin(time_progress * 11 * :math.pi()) * amplitude * 0.1
          noise = (:rand.uniform() - 0.5) * amplitude * 0.05

          # 围绕零点波动，不依赖当前余额
          point_value = wave1 + wave2 + wave3 + noise

          %{
            time: point_time,
            value: point_value,
            fluctuation_percentage: calculate_fluctuation_percentage(point_value, amplitude)
          }
        end)
        # 按时间正序排列
        |> Enum.reverse()
    end
  end

  # 计算智能的收分线和放分线
  # 余额向下 → 放分线背离向上，收分线跟随向下
  # 余额向上 → 收分线跟随向上，放分线背离向下
  defp calculate_smart_control_lines(wallet_values, config) do
    # 基础收分偏移
    base_collect_offset = config.collect_ratio * 800
    # 基础放分偏移
    base_release_offset = config.release_ratio * 800

    # 计算每个点的趋势（当前值与前一个值的差）
    trends =
      wallet_values
      |> Enum.with_index()
      |> Enum.map(fn {value, index} ->
        if index == 0 do
          # 第一个点没有趋势
          0
        else
          prev_value = Enum.at(wallet_values, index - 1)
          # 正数表示上升，负数表示下降
          value - prev_value
        end
      end)

    # 根据趋势计算收分线和放分线
    collect_values =
      wallet_values
      |> Enum.with_index()
      |> Enum.map(fn {value, index} ->
        trend = Enum.at(trends, index)

        cond do
          trend > 0 ->
            # 余额向上，收分线跟随向上
            value + base_collect_offset + trend * 0.3

          trend < 0 ->
            # 余额向下，收分线跟随向下
            value + base_collect_offset + trend * 0.5

          true ->
            # 无趋势，保持基础偏移
            value + base_collect_offset
        end
      end)

    release_values =
      wallet_values
      |> Enum.with_index()
      |> Enum.map(fn {value, index} ->
        trend = Enum.at(trends, index)

        cond do
          trend > 0 ->
            # 余额向上，放分线背离向下
            value - base_release_offset - trend * 0.4

          trend < 0 ->
            # 余额向下，放分线背离向上
            value - base_release_offset - trend * 0.6

          true ->
            # 无趋势，保持基础偏移
            value - base_release_offset
        end
      end)

    {collect_values, release_values}
  end

  # 计算波动百分比
  defp calculate_fluctuation_percentage(value, center_line) do
    if center_line == 0 do
      # 当中心线为0时，直接使用值作为百分比基数
      # 假设10000为基数
      value / 10000 * 100
    else
      (value - center_line) / abs(center_line) * 100
    end
  end

  # 格式化时间用于图表显示
  defp format_time_for_chart(datetime, time_range) do
    # 安全地处理时间格式化
    safe_datetime =
      case datetime do
        %DateTime{} = dt ->
          dt

        %NaiveDateTime{} = ndt ->
          DateTime.from_naive!(ndt, "Etc/UTC")

        binary when is_binary(binary) ->
          case DateTime.from_iso8601(binary) do
            {:ok, dt, _} -> dt
            _ -> DateTime.utc_now()
          end

        _ ->
          DateTime.utc_now()
      end

    case time_range do
      "minute" -> Calendar.strftime(safe_datetime, "%H:%M")
      "hour" -> Calendar.strftime(safe_datetime, "%H:00")
      "day" -> Calendar.strftime(safe_datetime, "%m-%d")
      _ -> Calendar.strftime(safe_datetime, "%H:%M")
    end
  rescue
    # 如果格式化失败，返回默认值
    _ -> "00:00"
  end

  @doc """
  初始化所有游戏的默认钱包控制配置
  为所有启用的游戏创建默认的钱包控制配置（如果不存在的话）
  """
  def initialize_default_configs do
    try do
      # 获取所有启用的游戏
      enabled_games = GameManagement.get_enabled_games()

      # 统计结果
      created_count = 0
      skipped_count = 0
      error_count = 0

      {created_count, skipped_count, error_count} =
        enabled_games
        |> Map.values()
        |> Enum.reduce({created_count, skipped_count, error_count}, fn game,
                                                                       {created, skipped, errors} ->
          game_id = game["gameid"]

          # 检查是否已存在配置
          case WalletControlConfig.get_by_game_id(game_id) do
            {:ok, _config} ->
              # 配置已存在，跳过
              Logger.info("钱包控制配置已存在，跳过游戏#{game_id}")
              {created, skipped + 1, errors}

            {:error, %Ash.Error.Query.NotFound{}} ->
              # 配置不存在，创建默认配置
              default_params = %{
                game_id: game_id,
                collect_ratio: @default_config.collect_ratio,
                release_ratio: @default_config.release_ratio,
                description: "游戏#{game_id}(#{game["name"] || "未知游戏"})的默认钱包控制配置"
              }

              case WalletControlConfig.create(default_params) do
                {:ok, _config} ->
                  Logger.info("成功创建游戏#{game_id}的默认钱包控制配置")
                  {created + 1, skipped, errors}

                {:error, reason} ->
                  Logger.error("创建游戏#{game_id}的默认钱包控制配置失败: #{inspect(reason)}")
                  {created, skipped, errors + 1}
              end

            {:error, reason} ->
              Logger.error("查询游戏#{game_id}的钱包控制配置失败: #{inspect(reason)}")
              {created, skipped, errors + 1}
          end
        end)

      Logger.info("钱包控制配置初始化完成 - 创建: #{created_count}, 跳过: #{skipped_count}, 错误: #{error_count}")

      {:ok,
       %{
         created: created_count,
         skipped: skipped_count,
         errors: error_count,
         total_games: map_size(enabled_games)
       }}
    rescue
      error ->
        Logger.error("初始化钱包控制配置时发生异常: #{inspect(error)}")
        {:error, error}
    end
  end

  # 辅助函数：安全除法，处理非数字类型
  defp safe_divide(numerator, denominator, default) do
    try do
      case numerator do
        n when is_number(n) -> n / denominator
        _ -> default
      end
    rescue
      _ -> default
    end
  end

  # 辅助函数：安全的 Float.round，处理非数字类型
  defp safe_round(value, precision) do
    try do
      case value do
        v when is_number(v) -> Float.round(v, precision)
        _ -> 0.0
      end
    rescue
      _ -> 0.0
    end
  end

  # ========== 真实历史数据获取函数 ==========

  @doc """
  获取单个游戏的真实余额历史数据
  """
  def get_game_balance_history(game_id, time_range) do
    try do
      # 获取游戏账户
      identifier = Cypridina.Ledger.AccountIdentifier.game(to_string(game_id), :XAA)

      account_result =
        Cypridina.Ledger.Account.get_or_create_by_identifier(%{
          identifier: identifier,
          currency: :XAA
        })

      account =
        case account_result do
          {:ok, acc} -> acc
          _ -> nil
        end

      if account do
        Logger.info("📊 [WALLET_HISTORY] 找到游戏账户: #{account.identifier}, ID: #{account.id}")

        # 根据时间范围计算查询参数
        {since_time, limit} = calculate_time_params(time_range)
        Logger.info("📊 [WALLET_HISTORY] 查询参数 - 时间范围: #{time_range}, 限制: #{limit}")

        # 查询余额历史
        query_balance_history(account.id, since_time, limit)
      else
        Logger.warning("📊 [WALLET_HISTORY] 未找到游戏账户，game_id: #{game_id}")
        {:error, :account_not_found}
      end
    rescue
      error ->
        Logger.error("获取游戏余额历史失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 根据时间范围计算查询参数
  defp calculate_time_params(time_range) do
    now = DateTime.utc_now()

    case time_range do
      :minute ->
        # 最近60分钟，每分钟一个点
        since_time = DateTime.add(now, -60 * 60, :second)
        {since_time, 60}

      :hour ->
        # 最近24小时，每小时一个点
        since_time = DateTime.add(now, -24 * 60 * 60, :second)
        {since_time, 24}

      :day ->
        # 最近30天，每天一个点
        since_time = DateTime.add(now, -30 * 24 * 60 * 60, :second)
        {since_time, 30}

      _ ->
        # 默认最近24小时
        since_time = DateTime.add(now, -24 * 60 * 60, :second)
        {since_time, 24}
    end
  end

  # 查询单个账户的余额历史
  defp query_balance_history(account_id, _since_time, limit) do
    try do
      # 尝试直接使用 read action 而不是 by_account
      case Cypridina.Ledger.Balance
           |> Ash.Query.filter(account_id: account_id)
           |> Ash.Query.sort(inserted_at: :desc)
           # 获取更多数据以便采样
           |> Ash.Query.limit(limit * 2)
           |> Ash.read() do
        {:ok, balances} ->
          Logger.info("📊 [WALLET_HISTORY] 获取到 #{length(balances)} 条余额记录，账户ID: #{account_id}")

          # 转换为图表数据格式并按时间正序排列
          history_data =
            balances
            # 转为时间正序
            |> Enum.reverse()
            |> format_balance_history(limit)

          Logger.info("📊 [WALLET_HISTORY] 格式化后数据点数: #{length(history_data)}")
          {:ok, history_data}

        {:error, error} ->
          Logger.error("📊 [WALLET_HISTORY] 查询失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      error ->
        Logger.error("查询余额历史失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 格式化余额历史为图表数据
  defp format_balance_history(balances, target_points) do
    sampled_data = sample_data_points(balances, target_points)

    formatted_data =
      sampled_data
      |> Enum.map(fn balance ->
        # 调试：打印原始余额数据
        Logger.info("📊 [WALLET_HISTORY] 原始余额数据: #{inspect(balance)}")

        # 检查是否已经是格式化的数据
        case balance do
          # 如果已经是格式化的数据，直接返回
          %{timestamp: _, balance: _, formatted_time: _} ->
            balance

          # 如果是 Balance 结构体，进行格式化
          %{balance: money_balance, inserted_at: timestamp} ->
            # 将Money类型转换为整数，并除以100转换为元
            balance_amount =
              case money_balance do
                %Money{amount: amount} when is_integer(amount) ->
                  # 转换为元
                  amount / 100.0

                %Money{amount: amount} when is_binary(amount) ->
                  case Integer.parse(amount) do
                    {int_amount, _} -> int_amount / 100.0
                    :error -> 0.0
                  end

                %Money{} = money ->
                  # 尝试多种可能的字段名
                  amount_value =
                    case money do
                      %{amount: amount} ->
                        amount

                      _ ->
                        # 如果是 Money.new(:XAA, "2280") 格式，尝试获取第二个参数
                        Map.values(money) |> Enum.find(&(is_binary(&1) or is_integer(&1))) || 0
                    end

                  case amount_value do
                    amount when is_integer(amount) ->
                      amount / 100.0

                    amount when is_binary(amount) ->
                      case Integer.parse(amount) do
                        {int_amount, _} -> int_amount / 100.0
                        :error -> 0.0
                      end

                    %Decimal{} = decimal_amount ->
                      # 将 Decimal 转换为整数，然后除以100
                      case Decimal.to_integer(decimal_amount) do
                        int_amount when is_integer(int_amount) -> int_amount / 100.0
                        _ -> 0.0
                      end

                    other ->
                      Logger.debug("📊 [WALLET_HISTORY] 未处理的类型: #{inspect(other)}")
                      0.0
                  end

                amount when is_integer(amount) ->
                  # 转换为元
                  amount / 100.0

                amount when is_float(amount) ->
                  # 转换为元
                  amount / 100.0

                other ->
                  Logger.debug("📊 [WALLET_HISTORY] 未知类型: #{inspect(other)}")
                  0.0
              end

            %{
              timestamp: timestamp,
              balance: balance_amount,
              formatted_time: format_time_for_chart(timestamp, 60)
            }

          # 其他情况，返回默认值
          _ ->
            now = DateTime.utc_now()

            %{
              timestamp: now,
              balance: 0.0,
              formatted_time: format_time_for_chart(now, 60)
            }
        end
      end)

    formatted_data
  end

  # 采样数据点以控制图表点数
  defp sample_data_points(data, target_points) when length(data) <= target_points do
    data
  end

  defp sample_data_points(data, target_points) do
    step = div(length(data), target_points)
    step = max(step, 1)

    data
    |> Enum.with_index()
    |> Enum.filter(fn {_item, index} -> rem(index, step) == 0 end)
    |> Enum.map(fn {item, _index} -> item end)
    |> Enum.take(target_points)
  end

  # ========== 动态阈值计算函数 ==========

  @doc """
  使用历史数据计算动态阈值的平滑调整

  基于历史余额波动情况动态调整敏感度：
  - 如果历史波动大，降低敏感度（避免过度反应）
  - 如果历史波动小，提高敏感度（更快响应）
  """
  defp calculate_smooth_with_dynamic_threshold(wallet_value, base_sensitivity, recent_history) do
    try do
      # 计算历史波动性
      volatility = calculate_historical_volatility(recent_history)

      # 计算动态敏感度因子
      dynamic_sensitivity_factor = calculate_dynamic_sensitivity_factor(volatility)

      # 调整后的敏感度
      adjusted_sensitivity = base_sensitivity * dynamic_sensitivity_factor

      # 计算趋势因子（最近是上升还是下降）
      trend_factor = calculate_trend_factor(recent_history)

      # 综合计算平滑调整值
      safe_wallet_value = if is_number(wallet_value), do: wallet_value, else: 0
      base_adjustment = :math.sqrt(abs(safe_wallet_value)) * adjusted_sensitivity
      trend_adjustment = base_adjustment * trend_factor

      # 应用边界限制
      # 最大调整幅度
      max_adjustment = 0.3
      min(-max_adjustment, max(max_adjustment, trend_adjustment))
    rescue
      error ->
        Logger.warning("动态阈值计算失败，使用默认值: #{inspect(error)}")
        # 降级到简单计算
        safe_wallet_value = if is_number(wallet_value), do: wallet_value, else: 0
        :math.sqrt(abs(safe_wallet_value)) * base_sensitivity
    end
  end

  # 计算历史波动性
  defp calculate_historical_volatility(history) when length(history) < 2, do: 0.5

  defp calculate_historical_volatility(history) do
    balances = Enum.map(history, & &1.balance)

    # 计算相邻点的变化率
    changes =
      balances
      |> Enum.chunk_every(2, 1, :discard)
      |> Enum.map(fn [prev, curr] ->
        # 确保 prev 和 curr 都是数字类型
        safe_prev = if is_number(prev), do: prev, else: 0
        safe_curr = if is_number(curr), do: curr, else: 0

        if safe_prev != 0 do
          abs(safe_curr - safe_prev) / abs(safe_prev)
        else
          0
        end
      end)

    # 计算平均变化率作为波动性指标
    if length(changes) > 0 do
      Enum.sum(changes) / length(changes)
    else
      # 默认中等波动性
      0.5
    end
  end

  # 计算动态敏感度因子
  defp calculate_dynamic_sensitivity_factor(volatility) do
    cond do
      # 高波动，降低敏感度
      volatility > 0.2 -> 0.7
      # 中等波动，保持敏感度
      volatility > 0.1 -> 1.0
      # 低波动，提高敏感度
      volatility > 0.05 -> 1.3
      # 极低波动，大幅提高敏感度
      true -> 1.5
    end
  end

  # 计算趋势因子
  defp calculate_trend_factor(history) when length(history) < 3, do: 1.0

  defp calculate_trend_factor(history) do
    # 取最近几个点计算趋势
    recent_points = Enum.take(history, -5)
    balances = Enum.map(recent_points, & &1.balance)

    # 计算简单的线性趋势
    if length(balances) >= 2 do
      first_balance = List.first(balances)
      last_balance = List.last(balances)

      # 确保余额是数字类型
      safe_first = if is_number(first_balance), do: first_balance, else: 0
      safe_last = if is_number(last_balance), do: last_balance, else: 0

      trend_direction = if safe_last > safe_first, do: 1.1, else: 0.9

      # 根据趋势强度调整
      trend_strength =
        if safe_first != 0 do
          abs(safe_last - safe_first) / abs(safe_first)
        else
          0
        end

      # 趋势越强，调整越大
      base_factor = trend_direction
      strength_adjustment = min(trend_strength * 0.2, 0.3)

      if trend_direction > 1.0 do
        base_factor + strength_adjustment
      else
        base_factor - strength_adjustment
      end
    else
      # 无趋势
      1.0
    end
  end

  @doc """
  动态计算敏感度
  基于当前余额和历史数据智能调整敏感度
  """
  defp calculate_dynamic_sensitivity(current_balance, recent_history) do
    try do
      # 基础敏感度
      base_sensitivity = @default_sensitivity

      # 根据余额绝对值调整敏感度
      balance_factor =
        cond do
          # 余额很大时降低敏感度
          abs(current_balance) > 100_000 -> 0.8
          # 中等余额保持默认
          abs(current_balance) > 50_000 -> 1.0
          # 小余额提高敏感度
          abs(current_balance) > 10_000 -> 1.2
          # 极小余额大幅提高敏感度
          true -> 1.5
        end

      # 根据历史波动调整敏感度
      volatility_factor =
        if length(recent_history) > 2 do
          volatility = calculate_historical_volatility(recent_history)

          cond do
            # 高波动降低敏感度
            volatility > 0.3 -> 0.7
            # 中等波动保持默认
            volatility > 0.15 -> 1.0
            # 低波动提高敏感度
            volatility > 0.05 -> 1.3
            # 极低波动大幅提高敏感度
            true -> 1.6
          end
        else
          # 没有足够历史数据时使用默认
          1.0
        end

      # 综合计算最终敏感度
      final_sensitivity = base_sensitivity * balance_factor * volatility_factor

      # 限制敏感度范围在 5-30 之间
      final_sensitivity
      |> max(5)
      |> min(30)
      |> round()
    rescue
      error ->
        Logger.warning("动态敏感度计算失败，使用默认值: #{inspect(error)}")
        @default_sensitivity
    end
  end
end
