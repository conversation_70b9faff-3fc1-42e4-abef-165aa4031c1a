defmodule Teen.Resources.Inventory.WalletControlConfig do
  @moduledoc """
  钱包控制配置资源
  管理每个游戏的钱包控制参数：收分比例、放分比例、敏感度
  """
  use Ash.Resource,
    domain: Teen.GameManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "wallet_control_configs"
    repo Cypridina.Repo
  end

  code_interface do
    domain Teen.GameManagement
    define :create, action: :create
    define :update, action: :update
    define :enable, action: :enable
    define :disable, action: :disable
    define :destroy, action: :destroy
    define :get_by_id, action: :read, get_by: [:id]
    define :get_by_game_id, action: :get_by_game_id, args: [:game_id]
    define :list_enabled, action: :list_enabled
    define :list_by_game_ids, action: :list_by_game_ids
  end

  actions do
    defaults [:read]

    create :create do
      accept [
        :game_id,
        :collect_ratio,
        :release_ratio,
        :sensitivity,
        :is_enabled,
        :description,
        :created_by
      ]
    end

    update :update do
      accept [
        :collect_ratio,
        :release_ratio,
        :sensitivity,
        :is_enabled,
        :description,
        :updated_by
      ]
    end

    update :enable do
      accept []
      change set_attribute(:is_enabled, true)
      change set_attribute(:updated_by, arg(:actor_id))
    end

    update :disable do
      accept []
      change set_attribute(:is_enabled, false)
      change set_attribute(:updated_by, arg(:actor_id))
    end

    destroy :destroy

    read :get_by_game_id do
      argument :game_id, :integer, allow_nil?: false
      filter expr(game_id == ^arg(:game_id))
      get? true
    end

    read :list_enabled do
      filter expr(is_enabled == true)
    end

    read :list_by_game_ids do
      argument :game_ids, {:array, :integer}, allow_nil?: false
      filter expr(game_id in ^arg(:game_ids))
    end
  end

  preparations do
    prepare build(sort: [game_id: :asc])
  end

  validations do
    validate present([:game_id, :collect_ratio, :release_ratio])
    validate numericality(:collect_ratio, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
    validate numericality(:release_ratio, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
    validate numericality(:sensitivity, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
  end

  attributes do
    uuid_primary_key :id

    attribute :game_id, :integer do
      allow_nil? false
      description "游戏ID，关联game_configs表"
    end

    attribute :collect_ratio, :integer do
      allow_nil? false
      default 20
      description "收分比例：控制收分的激进程度（0-100，数值越大收分越激进）"
    end

    attribute :release_ratio, :integer do
      allow_nil? false
      default 25
      description "放分比例：控制放分的激进程度（0-100，数值越大放分越激进）"
    end

    attribute :sensitivity, :integer do
      allow_nil? false
      default 10
      description "敏感度：控制对钱包变化的反应速度（0-100，数值越大反应越敏感）"
    end

    attribute :is_enabled, :boolean do
      allow_nil? false
      default true
      description "是否启用钱包控制"
    end

    attribute :description, :string do
      description "配置说明"
    end

    attribute :created_by, :uuid do
      description "创建者用户ID"
    end

    attribute :updated_by, :uuid do
      description "更新者用户ID"
    end

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    belongs_to :game_config, Teen.GameManagement.ManageGameConfig do
      source_attribute :game_id
      destination_attribute :game_id
    end

    belongs_to :creator, Cypridina.Accounts.User do
      source_attribute :created_by
      destination_attribute :id
    end

    belongs_to :updater, Cypridina.Accounts.User do
      source_attribute :updated_by
      destination_attribute :id
    end
  end

  identities do
    identity :unique_game_id, [:game_id]
  end
end
