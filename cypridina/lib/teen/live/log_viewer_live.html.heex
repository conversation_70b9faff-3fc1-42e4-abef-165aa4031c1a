<div class="p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-base-content mb-2">系统日志查看器</h1>
    <p class="text-base-content/70">查看和监控系统日志文件</p>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
    <!-- 左侧文件列表 -->
    <div class="lg:col-span-1">
      <div class="bg-base-100 rounded-lg shadow-sm border border-base-300">
        <div class="p-4 border-b border-base-300">
          <h2 class="text-lg font-semibold text-base-content">日志文件</h2>
        </div>
        <div class="p-4">
          <div class="space-y-4 max-h-96 overflow-y-auto">
            <!-- 系统日志分类 -->
            <div>
              <h3 class="text-sm font-semibold text-base-content/80 mb-2 flex items-center">
                <.icon name="hero-server" class="w-4 h-4 mr-1" /> 系统日志
              </h3>
              <div class="space-y-2 ml-4">
                <%= for system_file <- @log_categories.system do %>
                  <div class={[
                    "group p-3 rounded-lg border transition-colors",
                    if(@selected_file == system_file.path,
                      do: "bg-primary/10 border-primary/30",
                      else: "bg-base-200 border-base-300 hover:bg-base-300"
                    )
                  ]}>
                    <div
                      class="cursor-pointer"
                      phx-click="select_file"
                      phx-value-file={system_file.path}
                    >
                      <div
                        class="font-medium text-sm text-base-content truncate"
                        title={system_file.name}
                      >
                        {system_file.name}
                      </div>
                      <div class="text-xs text-base-content/60 mt-1">
                        <div>{system_file.size}</div>
                        <div>{system_file.modified}</div>
                      </div>
                    </div>
                    <div class="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        phx-click="delete_file"
                        phx-value-file={system_file.path}
                        phx-confirm="确定要删除日志文件 #{system_file.name} 吗？此操作不可恢复。"
                        class="btn btn-xs btn-error btn-outline"
                      >
                        <.icon name="hero-trash" class="w-3 h-3 mr-1" /> 删除
                      </button>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
            
<!-- 游戏日志分类 -->
            <%= for {game_name, game_files} <- @log_categories.games do %>
              <div>
                <h3 class="text-sm font-semibold text-base-content/80 mb-2 flex items-center">
                  <.icon name="hero-puzzle-piece" class="w-4 h-4 mr-1" />
                  {game_name}
                </h3>
                <div class="space-y-2 ml-4">
                  <%= for game_file <- game_files do %>
                    <div class={[
                      "group p-3 rounded-lg border transition-colors",
                      if(@selected_file == game_file.path,
                        do: "bg-primary/10 border-primary/30",
                        else: "bg-base-200 border-base-300 hover:bg-base-300"
                      )
                    ]}>
                      <div
                        class="cursor-pointer"
                        phx-click="select_file"
                        phx-value-file={game_file.path}
                      >
                        <div
                          class="font-medium text-sm text-base-content truncate"
                          title={game_file.name}
                        >
                          {game_file.name}
                        </div>
                        <div class="text-xs text-base-content/60 mt-1">
                          <div>{game_file.size}</div>
                          <div>{game_file.modified}</div>
                        </div>
                      </div>
                      <div class="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          phx-click="delete_file"
                          phx-value-file={game_file.path}
                          phx-confirm="确定要删除日志文件 #{game_file.name} 吗？此操作不可恢复。"
                          class="btn btn-xs btn-error btn-outline"
                        >
                          <.icon name="hero-trash" class="w-3 h-3 mr-1" /> 删除
                        </button>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    
<!-- 右侧日志内容 -->
    <div class="lg:col-span-3">
      <div class="bg-base-100 rounded-lg shadow-sm border border-base-300">
        <!-- 工具栏 -->
        <div class="p-4 border-b border-base-300">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
              <h2 class="text-lg font-semibold text-base-content">
                {if @selected_file, do: @selected_file, else: "请选择日志文件"}
              </h2>

              <%= if @selected_file do %>
                <button
                  phx-click="refresh"
                  class="btn btn-sm btn-outline"
                >
                  <.icon name="hero-arrow-path" class="w-4 h-4 mr-1" /> 刷新
                </button>

                <button
                  phx-click="download"
                  class="btn btn-sm btn-outline"
                >
                  <.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-1" /> 下载
                </button>
              <% end %>
            </div>

            <div class="flex items-center space-x-4">
              <!-- 显示行数选择 -->
              <div class="flex items-center space-x-2">
                <label class="text-sm text-base-content/70">显示行数:</label>
                <.form for={%{}} as={:lines_form} phx-change="change_lines">
                  <select
                    name="lines"
                    class="select select-sm select-bordered w-20"
                  >
                    <option value="50" selected={@lines_to_show == 50}>50</option>
                    <option value="100" selected={@lines_to_show == 100}>100</option>
                    <option value="200" selected={@lines_to_show == 200}>200</option>
                    <option value="500" selected={@lines_to_show == 500}>500</option>
                    <option value="1000" selected={@lines_to_show == 1000}>1000</option>
                  </select>
                </.form>
              </div>

              <!-- 自动刷新开关 -->
              <div class="flex items-center space-x-2">
                <label class="text-sm text-base-content/70">自动刷新:</label>
                <input
                  type="checkbox"
                  class="toggle toggle-primary toggle-sm"
                  checked={@auto_refresh}
                  phx-click="toggle_auto_refresh"
                />
              </div>
            </div>
          </div>
          
<!-- 搜索框 -->
          <%= if @selected_file do %>
            <div class="mt-4">
              <.form
                for={%{}}
                as={:search}
                phx-change="search"
                class="flex items-center space-x-2"
              >
                <div class="flex-1">
                  <input
                    type="text"
                    name="term"
                    value={@search_term}
                    placeholder="搜索日志内容..."
                    class="input input-bordered w-full"
                  />
                </div>
              </.form>
            </div>
          <% end %>
        </div>

<!-- 日志内容区域 -->
        <div class="p-4">
          <%= if @selected_file do %>
            <!-- 颜色图例 -->
            <div class="mb-3 p-3 bg-base-200 rounded-lg border border-base-300">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-base-content/80">日志颜色说明</h4>
                <button
                  phx-click="toggle_color_legend"
                  class="btn btn-xs btn-ghost"
                >
                  {if @show_color_legend, do: "隐藏", else: "显示"}
                </button>
              </div>
              <%= if @show_color_legend do %>
                <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                  <div class="flex items-center space-x-2 p-2 rounded-lg bg-error/10 border border-error/20">
                    <span class="w-3 h-3 bg-error rounded"></span>
                    <span class="text-error font-medium">错误 (ERROR)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-2 rounded-lg bg-warning/10 border border-warning/20">
                    <span class="w-3 h-3 bg-warning rounded"></span>
                    <span class="text-warning font-medium">警告 (WARN)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-2 rounded-lg bg-info/10 border border-info/20">
                    <span class="w-3 h-3 bg-info rounded"></span>
                    <span class="text-info font-medium">信息 (INFO)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-2 rounded-lg bg-success/10 border border-success/20">
                    <span class="w-3 h-3 bg-success rounded"></span>
                    <span class="text-success font-medium">成功 (SUCCESS)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-2 rounded-lg bg-secondary/10 border border-secondary/20">
                    <span class="w-3 h-3 bg-secondary rounded"></span>
                    <span class="text-secondary font-medium">调试 (DEBUG)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-2 rounded-lg bg-neutral/10 border border-neutral/20">
                    <span class="w-3 h-3 bg-neutral rounded"></span>
                    <span class="text-neutral font-medium">其他内容</span>
                  </div>
                </div>
              <% end %>
            </div>

            <div
              class="bg-base-300 p-4 rounded-lg font-mono text-sm overflow-auto"
              style="max-height: 600px;"
            >
              <%= for {line, index} <- filter_content(@log_content, @search_term) |> String.split("\n") |> Enum.with_index() do %>
                <div
                  class={["whitespace-pre-wrap", get_log_line_color_class(line)]}
                  data-line={index}
                >
                  {line}
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <.icon name="hero-document-text" class="mx-auto h-12 w-12 text-base-content/40" />
              <h3 class="mt-2 text-sm font-medium text-base-content">未选择日志文件</h3>
              <p class="mt-1 text-sm text-base-content/60">请从左侧列表中选择一个日志文件查看内容</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  window.addEventListener("phx:download_file", (e) => {
    const link = document.createElement("a");
    link.href = e.detail.url;
    link.download = e.detail.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });
</script>
