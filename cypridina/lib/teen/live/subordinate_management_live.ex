defmodule Teen.Live.SubordinateManagementLive do
  @moduledoc """
  下线管理页面 - 移植自RacingGame.Live.AdminPanel.SubordinateManagementComponent

  提供下线管理功能，仅限代理使用
  """

  use CypridinaWeb, :live_view

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.{User, AgentRelationship}
  alias Cypridina.Utils.TimeHelper
  alias CypridinaWeb.Components.PointsHistoryComponent
  require Ash.Query

  def mount(_params, _session, socket) do
    # 检查用户是否有代理权限
    if is_agent?(socket.assigns.current_user) do
      socket =
        socket
        |> assign(:page_title, "下线管理")
        |> assign(:fluid?, true)
        |> assign_defaults()
        |> load_subordinates_data()

      {:ok, socket, layout: {Teen.Layouts, :admin}}
    else
      {:ok, socket |> put_flash(:error, "权限不足，仅限代理使用") |> redirect(to: "/")}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
    |> assign(:show_create_modal, false)
    |> assign(:show_reset_password_modal, false)
    |> assign(:show_transfer_modal, false)
    |> assign(:show_commission_modal, false)
    |> assign(:selected_subordinate, nil)
    |> assign(:create_form, %{})
    |> assign(:reset_password_form, %{})
    |> assign(:transfer_form, %{})
    |> assign(:commission_form, %{})
    |> assign(:pending_refund_requests_count, 0)
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_subordinates_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)

    socket =
      socket
      |> assign(:page, page)
      |> load_subordinates_data()

    {:noreply, socket}
  end

  def handle_event("show_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, true)
      |> assign(:create_form, %{
        "username" => "",
        "password" => "",
        "user_type" => "normal",
        "commission_rate" => "0.0",
        "initial_points" => "0"
      })

    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, false)
      |> assign(:create_form, %{})

    {:noreply, socket}
  end

  def handle_event("create_subordinate", %{"subordinate" => subordinate_params}, socket) do
    user = socket.assigns.current_user

    case create_new_subordinate(user.id, subordinate_params) do
      {:ok, _result} ->
        socket =
          socket
          |> assign(:show_create_modal, false)
          |> load_subordinates_data()
          |> put_flash(:info, "下线创建成功")

        {:noreply, socket}

      {:error, _reason} ->
        socket = put_flash(socket, :error, "下线创建失败，请检查输入信息")
        {:noreply, socket}
    end
  end

  # 重置密码相关事件
  def handle_event("show_reset_password_modal", %{"user_id" => user_id}, socket) do
    subordinate = Enum.find(socket.assigns.subordinates_data, &(&1.id == user_id))

    socket =
      socket
      |> assign(:show_reset_password_modal, true)
      |> assign(:selected_subordinate, subordinate)
      |> assign(:reset_password_form, %{
        "new_password" => "",
        "confirm_password" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_reset_password_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_reset_password_modal, false)
      |> assign(:selected_subordinate, nil)
      |> assign(:reset_password_form, %{})

    {:noreply, socket}
  end

  def handle_event("reset_password", %{"password" => password_params}, socket) do
    subordinate = socket.assigns.selected_subordinate

    case reset_subordinate_password(subordinate.id, password_params) do
      {:ok, _user} ->
        socket =
          socket
          |> assign(:show_reset_password_modal, false)
          |> put_flash(:info, "密码重置成功")

        {:noreply, socket}

      {:error, _reason} ->
        socket = put_flash(socket, :error, "密码重置失败")
        {:noreply, socket}
    end
  end

  # 转账相关事件
  def handle_event("show_transfer_modal", %{"user_id" => user_id}, socket) do
    subordinate = Enum.find(socket.assigns.subordinates_data, &(&1.id == user_id))

    socket =
      socket
      |> assign(:show_transfer_modal, true)
      |> assign(:selected_subordinate, subordinate)
      |> assign(:transfer_form, %{
        "amount" => "",
        "transfer_type" => "to_subordinate",
        "reason" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_transfer_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_transfer_modal, false)
      |> assign(:selected_subordinate, nil)
      |> assign(:transfer_form, %{})

    {:noreply, socket}
  end

  def handle_event("submit_transfer", %{"transfer" => transfer_params}, socket) do
    agent = socket.assigns.current_user
    subordinate = socket.assigns.selected_subordinate

    case process_transfer(agent.id, subordinate.id, transfer_params) do
      {:ok, _result} ->
        socket =
          socket
          |> assign(:show_transfer_modal, false)
          |> load_subordinates_data()
          |> put_flash(:info, "转账成功")

        {:noreply, socket}

      {:error, _reason} ->
        socket = put_flash(socket, :error, "转账失败，请检查余额和输入信息")
        {:noreply, socket}
    end
  end

  # 调整抽水相关事件
  def handle_event("show_commission_modal", %{"user_id" => user_id}, socket) do
    subordinate = Enum.find(socket.assigns.subordinates_data, &(&1.id == user_id))

    socket =
      socket
      |> assign(:show_commission_modal, true)
      |> assign(:selected_subordinate, subordinate)
      |> assign(:commission_form, %{
        "commission_rate" => to_string(subordinate.commission_rate || 0.0),
        "reason" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_commission_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_commission_modal, false)
      |> assign(:selected_subordinate, nil)
      |> assign(:commission_form, %{})

    {:noreply, socket}
  end

  def handle_event("update_commission", %{"commission" => commission_params}, socket) do
    subordinate = socket.assigns.selected_subordinate

    case update_subordinate_commission(subordinate.id, commission_params) do
      {:ok, _result} ->
        socket =
          socket
          |> assign(:show_commission_modal, false)
          |> load_subordinates_data()
          |> put_flash(:info, "抽水比例调整成功")

        {:noreply, socket}

      {:error, _reason} ->
        socket = put_flash(socket, :error, "抽水比例调整失败")
        {:noreply, socket}
    end
  end

  defp load_subordinates_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 获取代理关系，然后获取下线用户信息
    {subordinates, total_count} = get_agent_subordinates(user.id, search_query, page, per_page)

    socket
    |> assign(:subordinates_data, subordinates)
    |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})
    |> load_pending_refund_requests_count()
  end

  defp load_pending_refund_requests_count(socket) do
    # 这里需要实现获取待处理退款申请数量的逻辑
    assign(socket, :pending_refund_requests_count, 0)
  end

  defp get_agent_subordinates(agent_id, search_query, page, per_page) do
    offset = (page - 1) * per_page

    # 首先获取代理关系
    relationships_query =
      AgentRelationship
      |> Ash.Query.filter(agent_id == ^agent_id)
      |> Ash.Query.limit(per_page)
      |> Ash.Query.offset(offset)

    case Ash.read(relationships_query) do
      {:ok, relationships} ->
        user_ids = Enum.map(relationships, & &1.user_id)

        if length(user_ids) > 0 do
          users_query =
            User
            |> Ash.Query.filter(id in ^user_ids)

          users_query =
            if search_query != "" do
              Ash.Query.filter(users_query, contains(username, ^search_query))
            else
              users_query
            end

          case Ash.read(users_query) do
            {:ok, users} ->
              # 合并用户信息和代理关系信息
              subordinates =
                Enum.map(users, fn user ->
                  relationship = Enum.find(relationships, &(&1.user_id == user.id))

                  Map.merge(user, %{
                    commission_rate: relationship.commission_rate,
                    is_agent: user.agent_level && user.agent_level >= 0,
                    agent_level: user.agent_level,
                    # 需要从实际的积分系统获取
                    points: 0
                  })
                end)

              total_count = get_total_subordinates_count(agent_id, search_query)
              {subordinates, total_count}

            {:error, _} ->
              {[], 0}
          end
        else
          {[], 0}
        end

      {:error, _} ->
        {[], 0}
    end
  end

  defp get_total_subordinates_count(agent_id, search_query) do
    relationships_query =
      AgentRelationship
      |> Ash.Query.filter(agent_id == ^agent_id)

    case Ash.read(relationships_query) do
      {:ok, relationships} ->
        user_ids = Enum.map(relationships, & &1.user_id)

        if length(user_ids) > 0 do
          users_query = User |> Ash.Query.filter(id in ^user_ids)

          users_query =
            if search_query != "" do
              Ash.Query.filter(users_query, contains(username, ^search_query))
            else
              users_query
            end

          case Ash.count(users_query) do
            {:ok, count} -> count
            {:error, _} -> 0
          end
        else
          0
        end

      {:error, _} ->
        0
    end
  end

  defp is_agent?(user) do
    # 检查用户是否是代理
    user.agent_level && user.agent_level >= 0
  end

  defp create_new_subordinate(agent_id, subordinate_params) do
    # 这里需要实现创建下线的逻辑
    # 暂时返回成功，实际需要创建用户并建立代理关系
    {:ok, %{}}
  end

  defp reset_subordinate_password(user_id, password_params) do
    # 这里需要实现重置密码的逻辑
    # 暂时返回成功，实际需要调用User的密码重置action
    {:ok, %{}}
  end

  defp process_transfer(agent_id, subordinate_id, transfer_params) do
    # 这里需要实现转账的逻辑
    # 暂时返回成功，实际需要处理积分转账
    {:ok, %{}}
  end

  defp update_subordinate_commission(user_id, commission_params) do
    # 这里需要实现更新抽水比例的逻辑
    # 暂时返回成功，实际需要更新AgentRelationship
    {:ok, %{}}
  end
end
