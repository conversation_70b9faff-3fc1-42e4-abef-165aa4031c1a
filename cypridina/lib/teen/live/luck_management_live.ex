defmodule Teen.Live.LuckManagementLive do
  @moduledoc """
  幸运值管理后台界面
  """

  use <PERSON><PERSON><PERSON>ina<PERSON><PERSON>, :live_view
  require Logger

  alias Teen.Services.LuckService
  alias Teen.UserLuckValue

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 定期更新数据
      :timer.send_interval(10000, self(), :refresh_stats)
    end

    # 初始化默认统计数据以避免KeyError
    default_stats = %{
      total_users: 0,
      never_recharged: 0,
      active_users: 0,
      average_luck: 0,
      max_luck: 0,
      min_luck: 0
    }

    socket =
      socket
      |> assign(:page_title, "运气值管理")
      |> assign(:current_url, "/admin/luck-management")
      |> assign(:fluid?, true)
      |> assign(:luck_records, [])
      |> assign(:stats, default_stats)
      |> assign(:loading, true)
      |> assign(:selected_users, MapSet.new())
      |> assign(:search_query, "")
      |> assign(:filter_min_luck, nil)
      |> assign(:filter_max_luck, nil)
      |> assign(:game_type, "teen_patti")
      |> assign(:page, 1)
      |> assign(:per_page, 10)
      |> assign(:show_batch_form, false)
      |> assign(:batch_value, "")
      |> assign(:total_records, 0)
      |> load_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("search", %{"query" => query}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_luck", %{"min" => min_str, "max" => max_str}, socket) do
    min_luck =
      case Integer.parse(min_str) do
        {min, ""} -> min
        _ -> nil
      end

    max_luck =
      case Integer.parse(max_str) do
        {max, ""} -> max
        _ -> nil
      end

    socket =
      socket
      |> assign(:filter_min_luck, min_luck)
      |> assign(:filter_max_luck, max_luck)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_filters", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:filter_min_luck, nil)
      |> assign(:filter_max_luck, nil)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("select_user", %{"user_id" => user_id_str}, socket) do
    user_id = String.to_integer(user_id_str)
    selected = socket.assigns.selected_users

    new_selected =
      if MapSet.member?(selected, user_id) do
        MapSet.delete(selected, user_id)
      else
        MapSet.put(selected, user_id)
      end

    {:noreply, assign(socket, :selected_users, new_selected)}
  end

  @impl true
  def handle_event("select_all", _params, socket) do
    all_user_ids =
      socket.assigns.luck_records
      |> Enum.map(& &1.user_id)
      |> MapSet.new()

    {:noreply, assign(socket, :selected_users, all_user_ids)}
  end

  @impl true
  def handle_event("clear_selection", _params, socket) do
    {:noreply, assign(socket, :selected_users, MapSet.new())}
  end

  @impl true
  def handle_event("reset_single", %{"user_id" => user_id_str, "value" => value_str}, socket) do
    user_id = String.to_integer(user_id_str)

    case Integer.parse(value_str) do
      {value, ""} when value >= -1 and value <= 1000 ->
        case LuckService.admin_reset_luck(user_id, value) do
          {:ok, _} ->
            Logger.info("🍀 [ADMIN] 重置用户 #{user_id} 幸运值为 #{value}")

            socket =
              socket
              |> put_flash(:info, "用户 #{user_id} 幸运值已重置为 #{value}")
              |> load_data()

            {:noreply, socket}

          {:error, reason} ->
            Logger.error("🍀 [ADMIN] 重置用户 #{user_id} 幸运值失败: #{inspect(reason)}")
            {:noreply, put_flash(socket, :error, "重置失败: #{inspect(reason)}")}
        end

      _ ->
        {:noreply, put_flash(socket, :error, "幸运值必须是 -1 到 1000 之间的整数")}
    end
  end

  @impl true
  def handle_event("toggle_batch_form", _params, socket) do
    {:noreply, assign(socket, :show_batch_form, not socket.assigns.show_batch_form)}
  end

  @impl true
  def handle_event("batch_reset", %{"value" => value_str}, socket) do
    selected = MapSet.to_list(socket.assigns.selected_users)

    if length(selected) == 0 do
      {:noreply, put_flash(socket, :warning, "请先选择要操作的用户")}
    else
      case Integer.parse(value_str) do
        {value, ""} when value >= -1 and value <= 1000 ->
          Logger.info("🍀 [ADMIN] 批量重置 #{length(selected)} 个用户的幸运值为 #{value}")

          case LuckService.batch_reset_luck(selected, value) do
            {:ok, results} ->
              success_count =
                Enum.count(results, fn
                  {:ok, _} -> true
                  _ -> false
                end)

              Logger.info("🍀 [ADMIN] 批量重置完成: #{success_count}/#{length(selected)} 成功")

              socket =
                socket
                |> put_flash(:info, "批量重置完成: #{success_count}/#{length(selected)} 成功")
                |> assign(:selected_users, MapSet.new())
                |> assign(:show_batch_form, false)
                |> assign(:batch_value, "")
                |> load_data()

              {:noreply, socket}

            {:error, reason} ->
              Logger.error("🍀 [ADMIN] 批量重置失败: #{inspect(reason)}")
              {:noreply, put_flash(socket, :error, "批量重置失败: #{inspect(reason)}")}
          end

        _ ->
          {:noreply, put_flash(socket, :error, "幸运值必须是 -1 到 1000 之间的整数")}
      end
    end
  end

  @impl true
  def handle_event("change_page", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        socket =
          socket
          |> assign(:page, page)
          |> load_data()

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:refresh_stats, socket) do
    # 只更新统计数据，不更新列表
    case LuckService.get_luck_statistics(socket.assigns.game_type) do
      {:ok, stats} ->
        {:noreply, assign(socket, :stats, stats)}

      {:error, reason} ->
        Logger.error("🍀 [ADMIN] 刷新统计数据失败: #{inspect(reason)}")
        {:noreply, socket}
    end
  end

  # 加载数据
  defp load_data(socket) do
    try do
      # 获取统计数据
      stats =
        case LuckService.get_luck_statistics(socket.assigns.game_type) do
          {:ok, stats} ->
            stats

          {:error, reason} ->
            Logger.error("🍀 [ADMIN] 获取统计数据失败: #{inspect(reason)}")

            %{
              total_users: 0,
              never_recharged: 0,
              active_users: 0,
              average_luck: 0,
              max_luck: 0,
              min_luck: 0
            }
        end

      # 获取幸运值记录列表（确保所有用户都有记录）
      opts = build_filter_opts(socket)

      {all_records, total_count} =
        case LuckService.ensure_all_users_have_luck_records(socket.assigns.game_type, opts) do
          {:ok, records} ->
            {records, length(records)}

          {:error, reason} ->
            Logger.error("🍀 [ADMIN] 获取幸运值记录失败: #{inspect(reason)}")
            {[], 0}
        end

      # 应用搜索过滤
      filtered_records = apply_search_filter(all_records, socket.assigns.search_query)

      # 分页
      page = socket.assigns.page
      per_page = socket.assigns.per_page
      offset = (page - 1) * per_page
      paginated_records = Enum.slice(filtered_records, offset, per_page)

      # 为每个记录添加充值次数信息
      records_with_recharge_count =
        Enum.map(paginated_records, fn record ->
          {:ok, recharge_count} = LuckService.get_user_recharge_count(record.user_id)
          Map.put(record, :recharge_count, recharge_count)
        end)

      socket
      |> assign(:luck_records, records_with_recharge_count)
      |> assign(:stats, stats)
      |> assign(:total_records, length(filtered_records))
      |> assign(:loading, false)
    rescue
      error ->
        Logger.error("🍀 [ADMIN] 加载数据失败: #{inspect(error)}")

        # 设置默认统计数据以避免KeyError
        default_stats = %{
          total_users: 0,
          never_recharged: 0,
          active_users: 0,
          average_luck: 0,
          max_luck: 0,
          min_luck: 0
        }

        socket
        |> put_flash(:error, "加载数据失败: #{inspect(error)}")
        |> assign(:luck_records, [])
        |> assign(:stats, default_stats)
        |> assign(:total_records, 0)
        |> assign(:loading, false)
    end
  end

  defp build_filter_opts(socket) do
    opts = []

    opts =
      if socket.assigns.filter_min_luck,
        do: Keyword.put(opts, :min_luck, socket.assigns.filter_min_luck),
        else: opts

    opts =
      if socket.assigns.filter_max_luck,
        do: Keyword.put(opts, :max_luck, socket.assigns.filter_max_luck),
        else: opts

    opts
  end

  defp apply_search_filter(records, "") do
    records
  end

  defp apply_search_filter(records, query) do
    query_lower = String.downcase(query)

    Enum.filter(records, fn record ->
      user_id_str = Integer.to_string(record.user_id)
      String.contains?(user_id_str, query_lower)
    end)
  end

  # 格式化幸运值显示
  defp format_luck_value(-1), do: "未充值"
  defp format_luck_value(value), do: Integer.to_string(value)

  # 格式化充值次数显示
  defp format_recharge_count(0), do: "未充值"
  defp format_recharge_count(count), do: "#{count}次"

  # 幸运值颜色样式
  defp luck_value_class(-1), do: "text-gray-500"
  defp luck_value_class(value) when value >= 800, do: "text-green-600 font-bold"
  defp luck_value_class(value) when value >= 500, do: "text-blue-600"
  defp luck_value_class(value) when value >= 200, do: "text-yellow-600"
  defp luck_value_class(_), do: "text-red-600"

  # 格式化时间
  defp format_time(nil), do: "从未"

  defp format_time(datetime) do
    Calendar.strftime(datetime, "%Y-%m-%d %H:%M:%S")
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900">幸运值管理</h1>
          <p class="mt-2 text-gray-600">管理用户的 Teen Patti 游戏幸运值</p>
        </div>

    <!-- 统计卡片 -->
        <div class="mb-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                    <dd class="text-lg font-medium text-gray-900">{@stats.total_users || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">未充值用户</dt>
                    <dd class="text-lg font-medium text-gray-900">{@stats.never_recharged || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                    <dd class="text-lg font-medium text-gray-900">{@stats.active_users || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">平均幸运值</dt>
                    <dd class="text-lg font-medium text-gray-900">{@stats.average_luck || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

    <!-- 搜索和筛选 -->
        <div class="mb-6 bg-white shadow rounded-lg p-6">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <!-- 搜索用户ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700">搜索用户ID</label>
              <form phx-submit="search" class="flex items-end space-x-2">
                <div class="flex-1">
                  <input
                    type="text"
                    name="query"
                    value={@search_query}
                    placeholder="输入用户ID"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <button
                  type="submit"
                  class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  搜索
                </button>
              </form>
            </div>

    <!-- 幸运值范围筛选 -->
            <div class="col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">幸运值范围筛选</label>
              <form phx-submit="filter_luck" class="flex items-center space-x-2">
                <div class="flex-1">
                  <input
                    type="number"
                    name="min"
                    value={@filter_min_luck}
                    min="-1"
                    max="1000"
                    placeholder="最小值"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <span class="text-gray-500">至</span>
                <div class="flex-1">
                  <input
                    type="number"
                    name="max"
                    value={@filter_max_luck}
                    min="-1"
                    max="1000"
                    placeholder="最大值"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <button
                  type="submit"
                  class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  筛选
                </button>
              </form>
            </div>

            <div class="flex items-end">
              <button
                phx-click="clear_filters"
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                清除筛选
              </button>
            </div>
          </div>
        </div>

    <!-- 操作按钮 -->
        <div class="mb-6 bg-white shadow rounded-lg p-6">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex flex-wrap gap-2">
              <button
                phx-click="select_all"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                全选
              </button>

              <button
                phx-click="clear_selection"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                清除选择
              </button>
            </div>

            <div class="flex flex-wrap gap-2">
              <span class="text-sm text-gray-500">
                已选择 {MapSet.size(@selected_users)} 个用户
              </span>

              <button
                phx-click="toggle_batch_form"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                {if @show_batch_form, do: "取消批量操作", else: "批量重置幸运值"}
              </button>
            </div>
          </div>

    <!-- 批量操作表单 -->
          <%= if @show_batch_form do %>
            <div class="mt-6 border-t pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">批量重置幸运值</h3>

              <form phx-submit="batch_reset" class="flex items-center space-x-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">新幸运值</label>
                  <input
                    type="number"
                    name="value"
                    value={@batch_value}
                    min="-1"
                    max="1000"
                    placeholder="输入幸运值 (-1 到 1000)"
                    required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  <p class="mt-1 text-sm text-gray-500">-1: 未充值, 0-1000: 幸运值</p>
                </div>

                <div class="flex items-end">
                  <button
                    type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                  >
                    确认批量重置 ({MapSet.size(@selected_users)})
                  </button>
                </div>
              </form>
            </div>
          <% end %>
        </div>

    <!-- 幸运值列表 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
          <%= if @loading do %>
            <div class="p-8 text-center">
              <div class="inline-flex items-center">
                <svg
                  class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  >
                  </circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  >
                  </path>
                </svg>
                加载中...
              </div>
            </div>
          <% else %>
            <%= if length(@luck_records) > 0 do %>
              <ul class="divide-y divide-gray-200">
                <%= for record <- @luck_records do %>
                  <li class="px-6 py-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <input
                          type="checkbox"
                          phx-click="select_user"
                          phx-value-user_id={record.user_id}
                          checked={MapSet.member?(@selected_users, record.user_id)}
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />

                        <div class="ml-4">
                          <div class="flex items-center space-x-3">
                            <p class="text-sm font-medium text-gray-900">
                              用户ID: {record.user_id}
                            </p>
                            <span class={[
                              "text-lg font-semibold",
                              luck_value_class(record.current_luck)
                            ]}>
                              {format_luck_value(record.current_luck)}
                            </span>
                          </div>
                          <div class="flex items-center space-x-4 mt-1">
                            <p class="text-sm text-gray-500">
                              游戏类型: {record.game_type}
                            </p>
                            <p class="text-sm text-gray-500">
                              充值次数: {format_recharge_count(Map.get(record, :recharge_count, 0))}
                            </p>
                            <p class="text-sm text-gray-500">
                              最后更新: {format_time(record.last_updated_at)}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <!-- 单个重置表单 -->
                        <form phx-submit="reset_single">
                          <input type="hidden" name="user_id" value={record.user_id} />
                          <div class="flex items-center space-x-2">
                            <input
                              type="number"
                              name="value"
                              min="-1"
                              max="1000"
                              placeholder="新值"
                              class="w-20 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm"
                            />
                            <button
                              type="submit"
                              class="inline-flex items-center px-3 py-1 border border-transparent rounded text-xs font-medium text-white bg-blue-600 hover:bg-blue-700"
                            >
                              重置
                            </button>
                          </div>
                        </form>
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            <% else %>
              <div class="p-8 text-center">
                <p class="text-gray-500">暂无幸运值数据</p>
              </div>
            <% end %>
          <% end %>
        </div>

    <!-- 分页控制 -->
        <%= if assigns[:total_records] && @total_records > @per_page do %>
          <div class="mt-6 flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <button
                phx-click="change_page"
                phx-value-page={@page - 1}
                disabled={@page <= 1}
                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>

              <span class="px-3 py-2 text-sm text-gray-700">
                第 {@page} 页 / 共 {ceil(@total_records / @per_page)} 页
              </span>

              <button
                phx-click="change_page"
                phx-value-page={@page + 1}
                disabled={@page >= ceil(@total_records / @per_page)}
                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>

            <div class="text-sm text-gray-500">
              共 {@total_records} 条记录
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
