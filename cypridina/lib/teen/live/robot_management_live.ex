defmodule Teen.Live.RobotManagementLive do
  @moduledoc """
  机器人管理后台界面
  """

  use <PERSON><PERSON><PERSON>ina<PERSON><PERSON>, :live_view
  require Logger

  alias Teen.RobotManagement.{RobotEntity, SimpleRobotProvider, RobotStateManager}

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 定期更新数据
      :timer.send_interval(5000, self(), :update_stats)
    end

    socket =
      socket
      |> assign(:page_title, "机器人管理")
      |> assign(:current_url, "/admin/robot-management")
      |> assign(:fluid?, true)
      |> assign(:robots, [])
      |> assign(:stats, %{})
      |> assign(:loading, true)
      |> assign(:show_create_form, false)
      |> assign(:selected_robots, MapSet.new())
      |> assign(:filter_status, :all)
      |> assign(:filter_game_type, :all)
      |> assign(:filter_enabled, :all)
      |> assign(:page, 1)
      |> assign(:per_page, 20)
      |> load_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_create_form", _params, socket) do
    {:noreply, assign(socket, :show_create_form, not socket.assigns.show_create_form)}
  end

  @impl true
  def handle_event("create_robot", params, socket) do
    Logger.info("🤖 [ADMIN] 创建机器人请求: #{inspect(params)}")

    case SimpleRobotProvider.create_robot_manually(params) do
      {:ok, robot} ->
        Logger.info("🤖 [ADMIN] 机器人创建成功: #{robot.robot_id}")

        socket =
          socket
          |> put_flash(:info, "机器人创建成功！ID: #{robot.robot_id}")
          |> assign(:show_create_form, false)
          |> load_data()

        {:noreply, socket}

      {:error, error} ->
        Logger.error("🤖 [ADMIN] 机器人创建失败: #{inspect(error)}")

        {:noreply, put_flash(socket, :error, "机器人创建失败: #{inspect(error)}")}
    end
  end

  @impl true
  def handle_event("batch_create_robots", %{"count" => count_str, "game_type" => game_type}, socket) do
    case Integer.parse(count_str) do
      {count, ""} when count > 0 and count <= 50 ->
        Logger.info("🤖 [ADMIN] 批量创建 #{count} 个机器人")

        case SimpleRobotProvider.batch_create_robots(game_type, count) do
          {:ok, robots} ->
            Logger.info("🤖 [ADMIN] 批量创建成功: #{length(robots)} 个机器人")

            socket =
              socket
              |> put_flash(:info, "批量创建成功！创建了 #{length(robots)} 个机器人")
              |> load_data()

            {:noreply, socket}

          {:error, error} ->
            Logger.error("🤖 [ADMIN] 批量创建失败: #{inspect(error)}")
            {:noreply, put_flash(socket, :error, "批量创建失败: #{inspect(error)}")}
        end

      _ ->
        {:noreply, put_flash(socket, :error, "请输入有效的数量 (1-50)")}
    end
  end

  @impl true
  def handle_event("select_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    selected = socket.assigns.selected_robots

    new_selected =
      if MapSet.member?(selected, robot_id) do
        MapSet.delete(selected, robot_id)
      else
        MapSet.put(selected, robot_id)
      end

    {:noreply, assign(socket, :selected_robots, new_selected)}
  end

  @impl true
  def handle_event("select_all", _params, socket) do
    all_robot_ids =
      socket.assigns.robots
      |> Enum.map(& &1.robot_id)
      |> MapSet.new()

    {:noreply, assign(socket, :selected_robots, all_robot_ids)}
  end

  @impl true
  def handle_event("clear_selection", _params, socket) do
    {:noreply, assign(socket, :selected_robots, MapSet.new())}
  end

  @impl true
  def handle_event("recycle_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 回收选中的机器人: #{inspect(selected)}")

      Enum.each(selected, fn robot_id ->
        RobotStateManager.recycle_robot_by_admin(robot_id, "admin", true)
      end)

      socket =
        socket
        |> put_flash(:info, "成功回收 #{length(selected)} 个机器人")
        |> assign(:selected_robots, MapSet.new())
        |> load_data()

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :warning, "请先选择要回收的机器人")}
    end
  end

  @impl true
  def handle_event("release_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 释放选中的机器人: #{inspect(selected)}")

      SimpleRobotProvider.release_robots(selected)

      socket =
        socket
        |> put_flash(:info, "成功释放 #{length(selected)} 个机器人")
        |> assign(:selected_robots, MapSet.new())
        |> load_data()

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :warning, "请先选择要释放的机器人")}
    end
  end

  @impl true
  def handle_event("filter_status", %{"status" => status}, socket) do
    filter_status = String.to_existing_atom(status)

    socket =
      socket
      |> assign(:filter_status, filter_status)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_game_type", %{"game_type" => game_type}, socket) do
    filter_game_type = if game_type == "all", do: :all, else: game_type

    socket =
      socket
      |> assign(:filter_game_type, filter_game_type)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_enabled", %{"enabled" => enabled}, socket) do
    filter_enabled = String.to_existing_atom(enabled)

    socket =
      socket
      |> assign(:filter_enabled, filter_enabled)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("cleanup_game_robots", %{"game_type" => game_type}, socket) do
    Logger.info("🤖 [ADMIN] 清理游戏 #{game_type} 的机器人状态")

    case RobotStateManager.cleanup_robots_by_game(game_type) do
      {:ok, result} ->
        cleared_count = Map.get(result, :cleared, 0)
        total_count = Map.get(result, :total, 0)

        socket =
          socket
          |> put_flash(:info, "游戏 #{game_type} 清理完成：共检查 #{total_count} 个机器人，重置 #{cleared_count} 个")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 清理游戏机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "清理失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("cleanup_all_game_robots", _params, socket) do
    Logger.info("🤖 [ADMIN] 全局清理所有游戏的机器人状态")

    case RobotStateManager.cleanup_all_game_robots() do
      {:ok, result} ->
        total_cleared = Map.get(result, :total_cleared, 0)

        socket =
          socket
          |> put_flash(:info, "全局清理完成：总共重置了 #{total_cleared} 个机器人状态")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 全局清理失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "全局清理失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("recycle_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 回收单个机器人: #{robot_id}")

    case RobotStateManager.recycle_robot_by_admin(robot_id, "admin", true) do
      {:ok, _robot} ->
        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 回收成功")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 回收机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "回收失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("enable_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 启用机器人: #{robot_id}")

    case RobotStateManager.enable_robot(robot_id) do
      {:ok, _robot} ->
        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 启用成功")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 启用机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "启用失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("disable_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 禁用机器人: #{robot_id}")

    case RobotStateManager.disable_robot(robot_id) do
      {:ok, _robot} ->
        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 禁用成功")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 禁用机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "禁用失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("batch_enable_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 批量启用选中的机器人: #{inspect(selected)}")

      case RobotStateManager.batch_enable_robots(selected) do
        {:ok, result} ->
          success_count = Map.get(result, :success, 0)

          socket =
            socket
            |> put_flash(:info, "成功启用 #{success_count} 个机器人")
            |> assign(:selected_robots, MapSet.new())
            |> load_data()

          {:noreply, socket}

        {:error, reason} ->
          Logger.error("🤖 [ADMIN] 批量启用失败: #{inspect(reason)}")
          {:noreply, put_flash(socket, :error, "批量启用失败: #{inspect(reason)}")}
      end
    else
      {:noreply, put_flash(socket, :warning, "请先选择要启用的机器人")}
    end
  end

  @impl true
  def handle_event("batch_disable_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 批量禁用选中的机器人: #{inspect(selected)}")

      case RobotStateManager.batch_disable_robots(selected) do
        {:ok, result} ->
          success_count = Map.get(result, :success, 0)

          socket =
            socket
            |> put_flash(:info, "成功禁用 #{success_count} 个机器人")
            |> assign(:selected_robots, MapSet.new())
            |> load_data()

          {:noreply, socket}

        {:error, reason} ->
          Logger.error("🤖 [ADMIN] 批量禁用失败: #{inspect(reason)}")
          {:noreply, put_flash(socket, :error, "批量禁用失败: #{inspect(reason)}")}
      end
    else
      {:noreply, put_flash(socket, :warning, "请先选择要禁用的机器人")}
    end
  end

  @impl true
  def handle_event("enable_all_robots", _params, socket) do
    Logger.info("🤖 [ADMIN] 启用所有机器人")

    case RobotStateManager.enable_all_robots() do
      {:ok, result} ->
        success_count = Map.get(result, :success, 0)

        socket =
          socket
          |> put_flash(:info, "成功启用 #{success_count} 个机器人")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 启用所有机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "启用所有机器人失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("change_page", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        socket =
          socket
          |> assign(:page, page)
          |> load_data()

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:update_stats, socket) do
    {:noreply, load_data(socket)}
  end

  # 加载数据
  defp load_data(socket) do
    try do
      # 确保默认stats
      default_stats = %{
        total: 0,
        idle: 0,
        assigned: 0,
        in_game: 0,
        in_round: 0,
        recycling: 0,
        recycled: 0,
        insufficient_funds: 0,
        enabled: 0,
        disabled: 0
      }

      # 获取统计数据
      stats = try do
        RobotStateManager.get_robot_status_stats()
      rescue
        error ->
          Logger.error("🤖 [ADMIN] 获取统计数据失败: #{inspect(error)}")
          %{}
      end

      # 合并默认值，确保所有键都存在
      stats = Map.merge(default_stats, stats || %{})

      # 获取机器人列表（带过滤和分页）
      filter_status = socket.assigns.filter_status
      filter_game_type = socket.assigns.filter_game_type
      filter_enabled = socket.assigns.filter_enabled
      page = socket.assigns.page
      per_page = socket.assigns.per_page

      robots = try do
        Ash.read!(RobotEntity)
        |> filter_by_status(filter_status)
        |> filter_by_game_type(filter_game_type)
        |> filter_by_enabled(filter_enabled)
        |> Enum.sort_by(& &1.inserted_at, {:desc, DateTime})
        |> paginate(page, per_page)
      rescue
        error ->
          Logger.error("🤖 [ADMIN] 获取机器人列表失败: #{inspect(error)}")
          []
      end

      socket
      |> assign(:robots, robots)
      |> assign(:stats, stats)
      |> assign(:loading, false)
    rescue
      error ->
        Logger.error("🤖 [ADMIN] 加载数据失败: #{inspect(error)}")

        socket
        |> put_flash(:error, "加载数据失败")
        |> assign(:loading, false)
    end
  end

  # 按状态过滤
  defp filter_by_status(robots, :all), do: robots
  defp filter_by_status(robots, status), do: Enum.filter(robots, &(&1.status == status))

  # 按游戏类型过滤
  defp filter_by_game_type(robots, :all), do: robots
  defp filter_by_game_type(robots, game_type), do: Enum.filter(robots, &(&1.current_game_type == game_type))

  # 按启用状态过滤
  defp filter_by_enabled(robots, :all), do: robots
  defp filter_by_enabled(robots, :enabled), do: Enum.filter(robots, &(&1.is_enabled))
  defp filter_by_enabled(robots, :disabled), do: Enum.filter(robots, &(not &1.is_enabled))

  # 分页
  defp paginate(robots, page, per_page) do
    offset = (page - 1) * per_page
    Enum.slice(robots, offset, per_page)
  end

  # 状态显示
  defp status_badge(status) do
    case status do
      :idle -> "bg-green-100 text-green-800"
      :assigned -> "bg-blue-100 text-blue-800"
      :in_game -> "bg-purple-100 text-purple-800"
      :in_round -> "bg-orange-100 text-orange-800"
      :recycling -> "bg-amber-100 text-amber-800"
      :recycled -> "bg-gray-100 text-gray-800"
      :insufficient_funds -> "bg-yellow-100 text-yellow-800"
      :offline -> "bg-gray-100 text-gray-600"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  defp status_text(status) do
    case status do
      :idle -> "空闲"
      :assigned -> "已分配"
      :in_game -> "游戏中"
      :in_round -> "回合中"
      :recycling -> "回收中"
      :recycled -> "已回收"
      :insufficient_funds -> "积分不足"
      :offline -> "离线"
      _ -> "未知"
    end
  end

  # 格式化数字，添加千位分隔符
  defp format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.graphemes()
    |> Enum.reverse()
    |> Enum.chunk_every(3)
    |> Enum.join(",")
    |> String.reverse()
  end

  defp format_number(_), do: "0"

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900">机器人管理</h1>
          <p class="mt-2 text-gray-600">管理游戏机器人的创建、分配和状态</p>
        </div>

        <!-- 统计卡片 -->
        <div class="mb-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm"><%= @stats.total || 0 %></span>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">总计</dt>
                    <dd class="text-lg font-medium text-gray-900"><%= @stats.total || 0 %></dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm"><%= @stats.idle || 0 %></span>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">空闲</dt>
                    <dd class="text-lg font-medium text-gray-900"><%= @stats.idle || 0 %></dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm"><%= (@stats.assigned || 0) + (@stats.in_game || 0) + (@stats.in_round || 0) %></span>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">游戏中</dt>
                    <dd class="text-lg font-medium text-gray-900"><%= (@stats.assigned || 0) + (@stats.in_game || 0) + (@stats.in_round || 0) %></dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm"><%= (@stats.recycling || 0) + (@stats.insufficient_funds || 0) %></span>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">需处理</dt>
                    <dd class="text-lg font-medium text-gray-900"><%= (@stats.recycling || 0) + (@stats.insufficient_funds || 0) %></dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm"><%= @stats.disabled || 0 %></span>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">禁用</dt>
                    <dd class="text-lg font-medium text-gray-900"><%= @stats.disabled || 0 %></dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="mb-6 bg-white shadow rounded-lg p-6">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex flex-wrap gap-2">
              <button
                phx-click="toggle_create_form"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <%= if @show_create_form do %>
                  取消创建
                <% else %>
                  创建机器人
                <% end %>
              </button>

              <button
                phx-click="select_all"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                全选
              </button>

              <button
                phx-click="clear_selection"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                清除选择
              </button>
            </div>

            <div class="flex flex-wrap gap-2">
              <button
                phx-click="release_selected"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                释放选中 (<%= MapSet.size(@selected_robots) %>)
              </button>

              <button
                phx-click="recycle_selected"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                回收选中 (<%= MapSet.size(@selected_robots) %>)
              </button>

              <button
                phx-click="cleanup_all_game_robots"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                全局状态清理
              </button>

              <button
                phx-click="batch_enable_selected"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                启用选中 (<%= MapSet.size(@selected_robots) %>)
              </button>

              <button
                phx-click="batch_disable_selected"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                禁用选中 (<%= MapSet.size(@selected_robots) %>)
              </button>

              <button
                phx-click="enable_all_robots"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                启用全部
              </button>
            </div>
          </div>

          <!-- 游戏状态清理区域 -->
          <div class="mt-6 border-t pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">游戏状态清理</h3>
            <p class="text-sm text-gray-600 mb-4">清理特定游戏的机器人状态，用于解决服务器重启后机器人状态不同步的问题</p>

            <div class="flex flex-wrap gap-2">
              <%= for game_type <- ["jhandi_munda", "teen_patti", "pot_blind", "slot777", "slotcat", "slotniu"] do %>
                <button
                  phx-click="cleanup_game_robots"
                  phx-value-game_type={game_type}
                  class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  清理 <%= String.replace(game_type, "_", " ") |> String.upcase() %>
                </button>
              <% end %>
            </div>
          </div>

          <!-- 创建机器人表单 -->
          <%= if @show_create_form do %>
            <div class="mt-6 border-t pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">创建新机器人</h3>

              <form phx-submit="create_robot" class="space-y-4">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">昵称</label>
                    <input
                      type="text"
                      name="nickname"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="留空自动生成"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">游戏类型</label>
                    <select
                      name="game_type"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="jhandi_munda">Jhandi Munda</option>
                      <option value="longhu">龙虎</option>
                      <option value="potblind">Pot Blind</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">性格类型</label>
                    <select
                      name="personality_type"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="balanced">平衡型</option>
                      <option value="aggressive">激进型</option>
                      <option value="conservative">保守型</option>
                      <option value="trend_follower">趋势型</option>
                      <option value="contrarian">逆向型</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">初始积分</label>
                    <input
                      type="number"
                      name="initial_points"
                      value="100000"
                      min="1000"
                      max="1000000"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div class="flex items-center space-x-4">
                  <button
                    type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    创建机器人
                  </button>

                  <div class="border-l border-gray-300 pl-4">
                    <label class="text-sm font-medium text-gray-700">批量创建：</label>
                    <form phx-submit="batch_create_robots" class="inline-flex items-center space-x-2 ml-2">
                      <input
                        type="number"
                        name="count"
                        placeholder="数量"
                        min="1"
                        max="50"
                        class="w-20 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm"
                      />
                      <select
                        name="game_type"
                        class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm"
                      >
                        <option value="jhandi_munda">Jhandi Munda</option>
                        <option value="longhu">龙虎</option>
                        <option value="potblind">Pot Blind</option>
                      </select>
                      <button
                        type="submit"
                        class="inline-flex items-center px-3 py-1 border border-transparent rounded text-xs font-medium text-white bg-green-600 hover:bg-green-700"
                      >
                        批量创建
                      </button>
                    </form>
                  </div>
                </div>
              </form>
            </div>
          <% end %>
        </div>

        <!-- 过滤器 -->
        <div class="mb-6 bg-white shadow rounded-lg p-4">
          <div class="space-y-4">
            <!-- 状态过滤 -->
            <div class="flex items-center space-x-4">
              <span class="text-sm font-medium text-gray-700">状态过滤：</span>
              <div class="flex flex-wrap gap-2">
                <%= for {status, label} <- [
                  {:all, "全部"},
                  {:idle, "空闲"},
                  {:in_game, "游戏中"},
                  {:recycling, "回收中"},
                  {:insufficient_funds, "积分不足"}
                ] do %>
                  <button
                    phx-click="filter_status"
                    phx-value-status={status}
                    class={[
                      "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",
                      if(@filter_status == status,
                         do: "bg-blue-100 text-blue-800",
                         else: "bg-gray-100 text-gray-800 hover:bg-gray-200")
                    ]}
                  >
                    <%= label %>
                  </button>
                <% end %>
              </div>
            </div>

            <!-- 游戏类型过滤 -->
            <div class="flex items-center space-x-4">
              <span class="text-sm font-medium text-gray-700">游戏过滤：</span>
              <div class="flex flex-wrap gap-2">
                <%= for {game_type, label} <- [
                  {:all, "全部"},
                  {"jhandi_munda", "Jhandi Munda"},
                  {"teen_patti", "Teen Patti"},
                  {"pot_blind", "Pot Blind"},
                  {"slot777", "Slot777"},
                  {"slotcat", "SlotCat"},
                  {"slotniu", "SlotNiu"}
                ] do %>
                  <button
                    phx-click="filter_game_type"
                    phx-value-game_type={game_type}
                    class={[
                      "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",
                      if(@filter_game_type == game_type,
                         do: "bg-green-100 text-green-800",
                         else: "bg-gray-100 text-gray-800 hover:bg-gray-200")
                    ]}
                  >
                    <%= label %>
                  </button>
                <% end %>
              </div>
            </div>

            <!-- 启用状态过滤 -->
            <div class="flex items-center space-x-4">
              <span class="text-sm font-medium text-gray-700">启用状态：</span>
              <div class="flex flex-wrap gap-2">
                <%= for {enabled, label} <- [
                  {:all, "全部"},
                  {:enabled, "已启用"},
                  {:disabled, "已禁用"}
                ] do %>
                  <button
                    phx-click="filter_enabled"
                    phx-value-enabled={enabled}
                    class={[
                      "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",
                      if(@filter_enabled == enabled,
                         do: "bg-purple-100 text-purple-800",
                         else: "bg-gray-100 text-gray-800 hover:bg-gray-200")
                    ]}
                  >
                    <%= label %>
                  </button>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- 机器人列表 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
          <%= if @loading do %>
            <div class="p-8 text-center">
              <div class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                加载中...
              </div>
            </div>
          <% else %>
            <%= if length(@robots) > 0 do %>
              <ul class="divide-y divide-gray-200">
                <%= for robot <- @robots do %>
                  <li class="px-6 py-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <input
                          type="checkbox"
                          phx-click="select_robot"
                          phx-value-robot_id={robot.robot_id}
                          checked={MapSet.member?(@selected_robots, robot.robot_id)}
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div class="ml-4">
                          <div class="flex items-center space-x-3">
                            <p class="text-sm font-medium text-gray-900">
                              #<%= robot.robot_id %> - <%= robot.nickname %>
                            </p>
                            <span class={[
                              "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                              status_badge(robot.status)
                            ]}>
                              <%= status_text(robot.status) %>
                            </span>
                          </div>
                          <div class="flex items-center space-x-4 mt-1">
                            <p class="text-sm text-gray-500">
                              积分: <%= format_number(robot.current_points) %>
                            </p>
                            <%= if robot.current_game_type do %>
                              <p class="text-sm text-gray-500">
                                游戏: <%= robot.current_game_type %>
                              </p>
                            <% end %>
                            <%= if robot.current_room_id do %>
                              <p class="text-sm text-gray-500">
                                房间: <%= robot.current_room_id %>
                              </p>
                            <% end %>
                            <p class="text-sm text-gray-500">
                              更新: <%= Calendar.strftime(robot.status_changed_at, "%Y-%m-%d %H:%M:%S") %>
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <!-- 状态标签 -->
                        <%= if robot.is_in_round do %>
                          <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800">
                            回合中
                          </span>
                        <% end %>

                        <%= if robot.can_be_kicked do %>
                          <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                            可踢出
                          </span>
                        <% else %>
                          <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                            保护中
                          </span>
                        <% end %>

                        <!-- 启用状态 -->
                        <%= if robot.is_enabled do %>
                          <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            已启用
                          </span>
                        <% else %>
                          <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            已禁用
                          </span>
                        <% end %>

                        <!-- 操作按钮 -->
                        <div class="flex items-center space-x-1">
                          <%= if robot.is_enabled do %>
                            <button
                              phx-click="disable_robot"
                              phx-value-robot_id={robot.robot_id}
                              class="inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
                              title="禁用机器人"
                            >
                              禁用
                            </button>
                          <% else %>
                            <button
                              phx-click="enable_robot"
                              phx-value-robot_id={robot.robot_id}
                              class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500"
                              title="启用机器人"
                            >
                              启用
                            </button>
                          <% end %>

                          <button
                            phx-click="recycle_robot"
                            phx-value-robot_id={robot.robot_id}
                            class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="回收机器人"
                          >
                            回收
                          </button>
                        </div>
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            <% else %>
              <div class="p-8 text-center">
                <p class="text-gray-500">暂无机器人数据</p>
              </div>
            <% end %>
          <% end %>
        </div>

        <!-- 分页控制 -->
        <%= if length(@robots) >= @per_page do %>
          <div class="mt-6 flex items-center justify-center">
            <div class="flex items-center space-x-2">
              <button
                phx-click="change_page"
                phx-value-page={@page - 1}
                disabled={@page <= 1}
                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>

              <span class="px-3 py-2 text-sm text-gray-700">
                第 <%= @page %> 页
              </span>

              <button
                phx-click="change_page"
                phx-value-page={@page + 1}
                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                下一页
              </button>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
