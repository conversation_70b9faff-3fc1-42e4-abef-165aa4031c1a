defmodule Teen.Live.GameExpectation.GameWalletControlLive do
  use <PERSON><PERSON><PERSON>ina<PERSON>eb, :live_view
  require Logger

  alias Teen.Resources.Inventory.WalletControl
  alias Teen.GameManagement

  @impl true
  def mount(%{"game_id" => game_id}, _session, socket) do
    game_id = String.to_integer(game_id)

    # 获取游戏信息
    game_info = get_game_info(game_id)

    # 获取钱包状态
    wallet_status = WalletControl.get_wallet_status(game_id)

    # 获取钱包配置
    {:ok, wallet_config} = WalletControl.get_wallet_config(game_id)

    # 获取历史数据
    chart_data = get_chart_data(game_id, "hour")

    socket =
      socket
      |> assign(:game_id, game_id)
      |> assign(:game_info, game_info)
      |> assign(:wallet_status, wallet_status)
      |> assign(:wallet_config, wallet_config)
      |> assign(:chart_data, chart_data)
      |> assign(:time_range, "hour")
      |> assign(:editing_config, false)
      |> assign(:config_form, to_form(%{}))

    # 设置定时器进行实时更新 - 降低频率减少系统负载
    if connected?(socket) do
      # 改为30秒更新一次
      :timer.send_interval(30000, self(), :update_data)
    end

    {:ok, socket}
  end

  @impl true
  def handle_info(:update_data, socket) do
    game_id = socket.assigns.game_id
    time_range = socket.assigns.time_range

    # 更新钱包状态
    wallet_status = WalletControl.get_wallet_status(game_id)

    # 更新图表数据
    chart_data = get_chart_data(game_id, time_range)

    socket =
      socket
      |> assign(:wallet_status, wallet_status)
      |> assign(:chart_data, chart_data)
      |> push_event("update_chart", %{data: chart_data})

    {:noreply, socket}
  end

  @impl true
  def handle_event("change_time_range", %{"range" => range}, socket) do
    game_id = socket.assigns.game_id
    chart_data = get_chart_data(game_id, range)

    socket =
      socket
      |> assign(:time_range, range)
      |> assign(:chart_data, chart_data)
      |> push_event("update_chart", %{data: chart_data})

    {:noreply, socket}
  end

  @impl true
  def handle_event("edit_config", _params, socket) do
    wallet_config = socket.assigns.wallet_config

    form_data = %{
      "collect_percentage" => wallet_config.collect_percentage,
      "release_percentage" => wallet_config.release_percentage,
      "sensitivity" => wallet_config.sensitivity
    }

    socket =
      socket
      |> assign(:editing_config, true)
      |> assign(:config_form, to_form(form_data))

    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_edit", _params, socket) do
    socket = assign(socket, :editing_config, false)
    {:noreply, socket}
  end

  @impl true
  def handle_event("save_config", %{"config" => config_params}, socket) do
    game_id = socket.assigns.game_id

    try do
      # 安全地转换参数，提供具体的字段名称用于错误提示
      collect_percentage =
        safe_to_integer_with_field(config_params["collect_percentage"], "收分百分比")

      release_percentage =
        safe_to_integer_with_field(config_params["release_percentage"], "放分百分比")

      # 验证参数范围
      cond do
        collect_percentage < 0 or collect_percentage > 100 ->
          socket = put_flash(socket, :error, "收分百分比必须在0-100之间")
          {:noreply, socket}

        release_percentage < 0 or release_percentage > 100 ->
          socket = put_flash(socket, :error, "放分百分比必须在0-100之间")
          {:noreply, socket}

        true ->
          update_params = %{
            collect_ratio: collect_percentage,
            release_ratio: release_percentage
            # 敏感度由系统动态计算，不需要用户输入
            # 暂时不设置 updated_by，因为需要真实的用户UUID
          }

          case WalletControl.update_wallet_config(game_id, update_params) do
            {:ok, _updated_config} ->
              # 重新获取配置以确保字段名一致
              {:ok, wallet_config} = WalletControl.get_wallet_config(game_id)

              socket =
                socket
                |> assign(:wallet_config, wallet_config)
                |> assign(:editing_config, false)
                |> put_flash(:info, "配置更新成功")

              {:noreply, socket}

            {:error, reason} ->
              Logger.error("保存配置失败: #{inspect(reason)}")
              socket = put_flash(socket, :error, "配置更新失败: #{inspect(reason)}")
              {:noreply, socket}
          end
      end
    rescue
      error in ArgumentError ->
        Logger.error("保存配置参数错误: #{error.message}")
        socket = put_flash(socket, :error, error.message)
        {:noreply, socket}

      error ->
        Logger.error("保存配置异常: #{inspect(error)}")
        socket = put_flash(socket, :error, "配置保存失败，请检查输入参数")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("go_back", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin/inventory_control")}
  end

  # 私有辅助函数
  defp safe_to_integer_with_field(value, field_name) do
    try do
      safe_to_integer(value)
    rescue
      ArgumentError ->
        raise ArgumentError, "#{field_name}字段格式错误：请输入有效的数字"
    end
  end

  defp safe_to_integer(value) when is_binary(value) do
    case String.trim(value) do
      "" ->
        raise ArgumentError, "数字字段不能为空"

      trimmed_value ->
        case Integer.parse(trimmed_value) do
          {int, ""} -> int
          _ -> raise ArgumentError, "无效的数字格式: #{value}"
        end
    end
  end

  defp safe_to_integer(value) when is_integer(value), do: value

  defp safe_to_integer(nil) do
    raise ArgumentError, "数字字段不能为空"
  end

  defp safe_to_integer(value) do
    raise ArgumentError, "无效的数字类型: #{inspect(value)}"
  end

  # 获取游戏信息
  defp get_game_info(game_id) do
    try do
      enabled_games = GameManagement.get_enabled_games()

      game =
        enabled_games
        |> Map.values()
        |> Enum.find(fn g -> g["gameid"] == game_id end)

      case game do
        nil ->
          %{id: game_id, name: "游戏#{game_id}", type: "unknown"}

        game ->
          %{
            id: game_id,
            name: game["name"] || "游戏#{game_id}",
            type: String.downcase(game["name"] || "unknown")
          }
      end
    rescue
      _error -> %{id: game_id, name: "游戏#{game_id}", type: "unknown"}
    end
  end

  # 获取图表数据 - 优先使用真实历史数据
  defp get_chart_data(game_id, time_range) do
    try do
      # 将前端时间范围转换为后端格式
      time_range_atom =
        case time_range do
          "minute" -> :minute
          "hour" -> :hour
          "day" -> :day
          _ -> :hour
        end

      # 获取真实的钱包历史数据
      case WalletControl.get_wallet_history(game_id, time_range_atom) do
        history_data when is_list(history_data) and length(history_data) > 0 ->
          # 转换为前端图表格式
          convert_history_to_chart_data(history_data, time_range)

        _ ->
          # 如果没有历史数据，使用当前余额生成单点数据
          current_balance = WalletControl.get_current_balance(game_id)

          case current_balance do
            {:ok, balance} ->
              generate_single_point_data(balance, time_range)

            _ ->
              generate_mock_data(time_range)
          end
      end
    rescue
      error ->
        IO.puts("获取图表数据失败: #{inspect(error)}")
        generate_mock_data(time_range)
    end
  end

  # 将历史数据转换为图表格式 - 只显示真实余额波动
  defp convert_history_to_chart_data(history_data, _time_range) do
    # 提取钱包余额数据
    wallet_values = Enum.map(history_data, & &1.balance)

    # 生成时间标签
    time_labels =
      Enum.map(history_data, fn data_point ->
        DateTime.to_iso8601(data_point.timestamp)
      end)

    chart_data = %{
      labels: time_labels,
      values: wallet_values
    }

    chart_data
  end

  # 生成单点数据（当没有历史数据时）
  defp generate_single_point_data(current_balance, _time_range) do
    now = DateTime.utc_now()
    time_label = DateTime.to_iso8601(now)

    %{
      labels: [time_label],
      values: [current_balance]
    }
  end

  # 生成简洁的余额波动模拟数据
  defp generate_mock_data(time_range) do
    {points, interval} =
      case time_range do
        # 60个点，每分钟一个
        "minute" -> {60, 1}
        # 24个点，每小时一个
        "hour" -> {24, 60}
        # 30个点，每天一个
        "day" -> {30, 1440}
      end

    now = DateTime.utc_now()

    # 基础配置 - 围绕零点波动
    # 基础振幅 1万（更合理的波动范围）
    base_amplitude = 10000

    # 生成钱包余额波动线
    wallet_values =
      0..points
      |> Enum.map(fn i ->
        time_progress = i / points

        # 钱包余额线 - 围绕零点波动，多层次叠加
        wallet_main = :math.sin(time_progress * 3 * :math.pi()) * base_amplitude * 0.6
        wallet_secondary = :math.sin(time_progress * 7 * :math.pi()) * base_amplitude * 0.3
        wallet_tertiary = :math.sin(time_progress * 11 * :math.pi()) * base_amplitude * 0.1
        wallet_noise = (:rand.uniform() - 0.5) * base_amplitude * 0.05
        wallet_value = wallet_main + wallet_secondary + wallet_tertiary + wallet_noise

        trunc(wallet_value)
      end)

    # 生成时间标签
    time_labels =
      0..points
      |> Enum.map(fn i ->
        time = DateTime.add(now, -i * interval * 60, :second)
        DateTime.to_iso8601(time)
      end)
      |> Enum.reverse()

    %{
      labels: time_labels,
      values: Enum.reverse(wallet_values)
    }
  end
end
