defmodule Teen.Live.GameExpectation.GameRoomsOverviewLive do
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :live_view
  alias Teen.GameManagement.ManageGameConfig

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "游戏房间概览")
     |> assign(:current_url, "/admin/game-rooms")
     |> assign(:loading, true)
     |> assign(:games_with_rooms, [])
     |> assign(:search_query, "")
     |> assign(:selected_game, "all")
     |> assign(:fluid?, true)
     |> load_games_with_rooms(),
     layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("search", %{"query" => query}, socket) do
    {:noreply,
     socket
     |> assign(:search_query, query)
     |> assign(:loading, true)
     |> load_games_with_rooms()}
  end

  @impl true
  def handle_event("filter_game", %{"game" => game}, socket) do
    {:noreply,
     socket
     |> assign(:selected_game, game)
     |> assign(:loading, true)
     |> load_games_with_rooms()}
  end

  defp load_games_with_rooms(socket) do
    # 模拟数据加载延迟
    :timer.sleep(300)
    
    games_with_rooms = get_games_with_rooms()
    
    socket
    |> assign(:games_with_rooms, games_with_rooms)
    |> assign(:loading, false)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="admin-main-content p-4 lg:p-6 space-y-6">
      <!-- 页面头部 -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold text-base-content flex items-center gap-3">
            <.icon name="hero-building-office" class="size-8 text-error" />
            游戏房间概览
          </h1>
          <p class="text-base-content/60 mt-1">管理所有游戏的房间配置</p>
        </div>
        
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <!-- 搜索框 -->
          <div class="relative">
            <input 
              type="text" 
              placeholder="搜索游戏或房间..." 
              class="input input-sm input-bordered w-full max-w-xs pl-10"
              phx-change="search"
              name="query"
              value={@search_query}
            />
            <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-base-content/60" />
          </div>
          
          <!-- 游戏筛选器 -->
          <select 
            class="select select-sm select-bordered"
            phx-change="filter_game"
            name="game"
          >
            <option value="all" selected={@selected_game == "all"}>全部游戏</option>
            <option value="teen_patti" selected={@selected_game == "teen_patti"}>Teen Patti</option>
            <option value="poker" selected={@selected_game == "poker"}>Poker</option>
            <option value="rummy" selected={@selected_game == "rummy"}>Rummy</option>
          </select>
        </div>
      </div>

      <!-- 加载状态 -->
      <%= if @loading do %>
        <div class="flex items-center justify-center py-12">
          <div class="loading loading-spinner loading-lg text-primary"></div>
          <span class="ml-3 text-base-content/60">正在加载房间数据...</span>
        </div>
      <% else %>
        <!-- 游戏房间列表 -->
        <div class="grid grid-cols-1 gap-6">
          <%= for game <- @games_with_rooms do %>
            <div class="card bg-base-100 shadow-xl border border-base-300">
              <div class="card-body">
                <!-- 游戏标题 -->
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center gap-3">
                    <div class="avatar placeholder">
                      <div class="bg-error text-error-content rounded-lg w-12 h-12">
                        <span class="text-lg font-bold"><%= String.first(game.name) %></span>
                      </div>
                    </div>
                    <div>
                      <h2 class="card-title text-lg"><%= game.display_name %></h2>
                      <p class="text-sm text-base-content/60">游戏ID: <%= game.name %></p>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class={[
                      "badge",
                      game.is_enabled && "badge-success",
                      !game.is_enabled && "badge-error"
                    ]}>
                      <%= if game.is_enabled, do: "已启用", else: "已禁用" %>
                    </div>
                    <.link 
                      navigate={"/admin/games/#{game.id}/room_management"} 
                      class="btn btn-sm btn-primary gap-2"
                    >
                      <.icon name="hero-cog-6-tooth" class="size-4" />
                      管理房间
                    </.link>
                  </div>
                </div>

                <!-- 房间统计 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div class="stat bg-base-200 rounded-lg p-3">
                    <div class="stat-title text-xs">总房间数</div>
                    <div class="stat-value text-lg text-primary"><%= game.total_rooms %></div>
                  </div>
                  <div class="stat bg-base-200 rounded-lg p-3">
                    <div class="stat-title text-xs">活跃房间</div>
                    <div class="stat-value text-lg text-success"><%= game.active_rooms %></div>
                  </div>
                  <div class="stat bg-base-200 rounded-lg p-3">
                    <div class="stat-title text-xs">在线玩家</div>
                    <div class="stat-value text-lg text-info"><%= game.online_players %></div>
                  </div>
                  <div class="stat bg-base-200 rounded-lg p-3">
                    <div class="stat-title text-xs">今日收入</div>
                    <div class="stat-value text-lg text-warning">¥<%= format_currency(game.daily_revenue) %></div>
                  </div>
                </div>

                <!-- 房间列表 -->
                <div class="overflow-x-auto">
                  <table class="table table-zebra table-sm">
                    <thead>
                      <tr>
                        <th>房间名称</th>
                        <th>底注</th>
                        <th>在线人数</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <%= for room <- game.rooms do %>
                        <tr>
                          <td class="font-medium"><%= room.name %></td>
                          <td>¥<%= room.min_bet %> - ¥<%= room.max_bet %></td>
                          <td>
                            <div class="flex items-center gap-2">
                              <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                              <%= room.online_players %>/<%= room.max_players %>
                            </div>
                          </td>
                          <td>
                            <div class={[
                              "badge badge-sm",
                              room.is_enabled && "badge-success",
                              !room.is_enabled && "badge-error"
                            ]}>
                              <%= if room.is_enabled, do: "运行中", else: "已停用" %>
                            </div>
                          </td>
                          <td>
                            <div class="flex gap-1">
                              <.link 
                                navigate={"/admin/games/#{game.id}/room_management/#{room.id}/edit"} 
                                class="btn btn-xs btn-ghost"
                              >
                                <.icon name="hero-pencil" class="size-3" />
                              </.link>
                              <button class="btn btn-xs btn-ghost text-info">
                                <.icon name="hero-eye" class="size-3" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  # 辅助函数
  defp get_games_with_rooms do
    [
      %{
        id: 1,
        name: "teen_patti",
        display_name: "Teen Patti Classic",
        is_enabled: true,
        total_rooms: 8,
        active_rooms: 6,
        online_players: 234,
        daily_revenue: 45_678,
        rooms: [
          %{id: 1, name: "新手房", min_bet: 10, max_bet: 100, online_players: 45, max_players: 100, is_enabled: true},
          %{id: 2, name: "进阶房", min_bet: 100, max_bet: 1000, online_players: 78, max_players: 100, is_enabled: true},
          %{id: 3, name: "高级房", min_bet: 1000, max_bet: 10000, online_players: 23, max_players: 50, is_enabled: true},
          %{id: 4, name: "VIP房", min_bet: 10000, max_bet: 100000, online_players: 12, max_players: 20, is_enabled: true}
        ]
      },
      %{
        id: 2,
        name: "teen_patti_gold",
        display_name: "Teen Patti Gold",
        is_enabled: true,
        total_rooms: 6,
        active_rooms: 5,
        online_players: 156,
        daily_revenue: 32_456,
        rooms: [
          %{id: 5, name: "黄金新手房", min_bet: 20, max_bet: 200, online_players: 34, max_players: 100, is_enabled: true},
          %{id: 6, name: "黄金进阶房", min_bet: 200, max_bet: 2000, online_players: 56, max_players: 100, is_enabled: true},
          %{id: 7, name: "黄金高级房", min_bet: 2000, max_bet: 20000, online_players: 18, max_players: 50, is_enabled: true}
        ]
      },
      %{
        id: 3,
        name: "teen_patti_royal",
        display_name: "Teen Patti Royal",
        is_enabled: false,
        total_rooms: 4,
        active_rooms: 0,
        online_players: 0,
        daily_revenue: 0,
        rooms: [
          %{id: 8, name: "皇家房间", min_bet: 5000, max_bet: 50000, online_players: 0, max_players: 30, is_enabled: false}
        ]
      }
    ]
  end

  defp format_currency(amount) when is_integer(amount) do
    amount
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end
end
