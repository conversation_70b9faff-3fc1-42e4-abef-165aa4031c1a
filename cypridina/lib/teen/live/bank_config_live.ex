defmodule Teen.Live.BankConfigLive do
  @moduledoc """
  银行配置管理页面

  提供银行配置的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.BankConfig
      #
      #
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "银行配置"

  @impl Backpex.LiveResource
  def plural_name, do: "银行配置"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      name: %{
        module: Backpex.Fields.Text,
        label: "银行名称",
        searchable: true,
        help_text: "银行的完整名称"
      },
      bank_code: %{
        module: Backpex.Fields.Text,
        label: "银行代码",
        searchable: true,
        help_text: "银行的唯一标识代码"
      },
      icon_url: %{
        module: Backpex.Fields.URL,
        label: "银行图标",
        help_text: "银行图标的URL地址"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"禁用", 0},
          {"启用", 1}
        ],
        render: fn assigns ->
          status_class = if assigns.value == 1, do: "badge-success", else: "badge-error"
          status_text = if assigns.value == 1, do: "启用", else: "禁用"

          ~H"""
          <span class={"badge #{status_class}"}>{status_text}</span>
          """
        end
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序",
        help_text: "数字越小排序越靠前"
      },
      min_amount: %{
        module: Backpex.Fields.Number,
        label: "最小提现金额",
        help_text: "该银行的最小提现金额（分）"
      },
      max_amount: %{
        module: Backpex.Fields.Number,
        label: "最大提现金额",
        help_text: "该银行的最大提现金额（分）"
      },
      fee_rate: %{
        module: Backpex.Fields.Number,
        label: "手续费率",
        help_text: "提现手续费率（百分比）"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述",
        help_text: "银行的详细描述"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.StatusSelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      enable_bank: %{
        module: Teen.ResourceActions.EnableBank,
        label: "启用银行",
        icon: "hero-check-circle",
        confirm_label: "确认启用",
        confirm_text: "确定要启用选中的银行吗？",
        fields: []
      },
      disable_bank: %{
        module: Teen.ResourceActions.DisableBank,
        label: "禁用银行",
        icon: "hero-x-circle",
        confirm_label: "确认禁用",
        confirm_text: "确定要禁用选中的银行吗？",
        fields: []
      }
    ]
  end

  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">银行配置管理</h2>
          <p class="text-base-content/70 mt-1">管理支持的银行和提现配置</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总银行数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">启用中</div>
            <div class="stat-value text-success">0</div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>操作说明：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>银行代码必须唯一，用于系统内部识别</li>
          <li>图标URL用于在用户界面显示银行标识</li>
          <li>提现限额和手续费可以按银行单独设置</li>
          <li>禁用的银行不会在用户端显示</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :form_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>配置说明：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>银行代码建议使用标准的银行简称</li>
          <li>金额单位为分，100分=1元</li>
          <li>手续费率为百分比，2表示2%</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(_assigns, _slot), do: nil
end
