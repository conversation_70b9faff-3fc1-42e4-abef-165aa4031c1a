defmodule Teen.Live.ActivitySystem.EnhancedFreeBonusLive do
  @moduledoc """
  增强版免费积分任务管理界面

  提供更完善的任务管理功能：
  - 实时任务监控
  - 批量操作
  - 统计分析
  - 用户进度查看
  - A/B测试管理
  """

  use CypridinaWeb, :live_view

  alias Teen.ActivitySystem.{
    FreeBonusTask,
    EnhancedFreeBonusService,
    FreeBonusConfig
  }

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "免费积分任务管理")
      |> assign(:tasks, [])
      |> assign(:statistics, %{})
      |> assign(:selected_tasks, [])
      |> assign(:filter_options, %{
        status: "all",
        difficulty: "all",
        category: "all"
      })
      |> assign(:show_task_form, false)
      |> assign(:current_task, nil)
      |> assign(:user_search, "")
      |> assign(:user_progress, %{})
      |> load_tasks()
      |> load_statistics()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  @impl true
  def handle_event("filter_tasks", %{"filter" => filter_params}, socket) do
    socket =
      socket
      |> assign(:filter_options, filter_params)
      |> load_tasks()

    {:noreply, socket}
  end

  def handle_event("toggle_task_selection", %{"task_id" => task_id}, socket) do
    selected_tasks = socket.assigns.selected_tasks

    updated_selection = if task_id in selected_tasks do
      List.delete(selected_tasks, task_id)
    else
      [task_id | selected_tasks]
    end

    {:noreply, assign(socket, :selected_tasks, updated_selection)}
  end

  def handle_event("bulk_action", %{"action" => action}, socket) do
    case perform_bulk_action(action, socket.assigns.selected_tasks) do
      {:ok, message} ->
        socket =
          socket
          |> put_flash(:info, message)
          |> assign(:selected_tasks, [])
          |> load_tasks()
          |> load_statistics()

        {:noreply, socket}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, message)}
    end
  end

  def handle_event("search_user", %{"user_id" => user_id}, socket) do
    case String.trim(user_id) do
      "" ->
        {:noreply, assign(socket, :user_progress, %{})}

      uid ->
        case EnhancedFreeBonusService.get_enhanced_free_bonus_data(uid) do
          {:ok, progress_data} ->
            socket =
              socket
              |> assign(:user_search, uid)
              |> assign(:user_progress, progress_data)

            {:noreply, socket}

          {:error, _} ->
            {:noreply, put_flash(socket, :error, "用户不存在或数据获取失败")}
        end
    end
  end

  def handle_event("export_tasks", _params, socket) do
    case export_task_data(socket.assigns.tasks) do
      {:ok, filename} ->
        {:noreply, put_flash(socket, :info, "任务数据已导出: #{filename}")}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "导出失败: #{reason}")}
    end
  end

  def handle_event("show_task_form", params, socket) do
    task = case Map.get(params, "task_id") do
      nil ->
        # 新建任务
        %{
          title: "",
          share_count: 0,
          game_id: "",
          required_win_coins: "0",
          withdraw_count: 0,
          reward_amount: "0",
          status: :enabled
        }

      task_id ->
        # 编辑任务
        case FreeBonusTask.read(task_id) do
          {:ok, task} -> task
          _ -> nil
        end
    end

    socket =
      socket
      |> assign(:show_task_form, true)
      |> assign(:current_task, task)

    {:noreply, socket}
  end

  def handle_event("hide_task_form", _params, socket) do
    socket =
      socket
      |> assign(:show_task_form, false)
      |> assign(:current_task, nil)

    {:noreply, socket}
  end

  def handle_event("save_task", %{"task" => task_params}, socket) do
    case save_task(socket.assigns.current_task, task_params) do
      {:ok, _task} ->
        socket =
          socket
          |> put_flash(:info, "任务保存成功")
          |> assign(:show_task_form, false)
          |> assign(:current_task, nil)
          |> load_tasks()
          |> load_statistics()

        {:noreply, socket}

      {:error, changeset} ->
        errors = format_changeset_errors(changeset)
        {:noreply, put_flash(socket, :error, "保存失败: #{errors}")}
    end
  end

  def handle_event("delete_task", %{"task_id" => task_id}, socket) do
    case FreeBonusTask.read(task_id) do
      {:ok, task} ->
        case FreeBonusTask.destroy(task) do
          :ok ->
            socket =
              socket
              |> put_flash(:info, "任务删除成功")
              |> load_tasks()
              |> load_statistics()

            {:noreply, socket}

          {:error, _} ->
            {:noreply, put_flash(socket, :error, "删除失败")}
        end

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "任务不存在")}
    end
  end

  def handle_event("refresh_data", _params, socket) do
    socket =
      socket
      |> load_tasks()
      |> load_statistics()
      |> put_flash(:info, "数据已刷新")

    {:noreply, socket}
  end

  # 私有函数

  defp apply_action(socket, :index, _params) do
    socket
  end

  defp apply_action(socket, :analytics, _params) do
    socket
    |> load_detailed_analytics()
  end

  defp load_tasks(socket) do
    filter_options = socket.assigns.filter_options

    case FreeBonusTask.list_active_tasks() do
      {:ok, tasks} ->
        filtered_tasks = apply_filters(tasks, filter_options)
        assign(socket, :tasks, filtered_tasks)

      {:error, _} ->
        assign(socket, :tasks, [])
    end
  end

  defp load_statistics(socket) do
    statistics = calculate_task_statistics(socket.assigns.tasks)
    assign(socket, :statistics, statistics)
  end

  defp load_detailed_analytics(socket) do
    analytics = calculate_detailed_analytics()
    assign(socket, :analytics, analytics)
  end

  defp apply_filters(tasks, filter_options) do
    tasks
    |> filter_by_status(filter_options["status"])
    |> filter_by_difficulty(filter_options["difficulty"])
    |> filter_by_category(filter_options["category"])
  end

  defp filter_by_status(tasks, "all"), do: tasks
  defp filter_by_status(tasks, status) do
    status_atom = String.to_atom(status)
    Enum.filter(tasks, &(&1.status == status_atom))
  end

  defp filter_by_difficulty(tasks, "all"), do: tasks
  defp filter_by_difficulty(tasks, difficulty) do
    Enum.filter(tasks, fn task ->
      task_difficulty = calculate_task_difficulty(task)
      task_difficulty == difficulty
    end)
  end

  defp filter_by_category(tasks, "all"), do: tasks
  defp filter_by_category(tasks, category) do
    Enum.filter(tasks, fn task ->
      task_category = categorize_task(task)
      task_category == category
    end)
  end

  defp calculate_task_difficulty(task) do
    # 复用 FreeBonusConfig 中的逻辑
    cond do
      task.share_count <= 1 && Decimal.to_integer(task.required_win_coins) <= 500 -> "easy"
      task.share_count <= 3 && Decimal.to_integer(task.required_win_coins) <= 2000 -> "medium"
      task.share_count <= 5 && Decimal.to_integer(task.required_win_coins) <= 5000 -> "hard"
      true -> "expert"
    end
  end

  defp categorize_task(task) do
    cond do
      task.share_count > 0 && Decimal.to_integer(task.required_win_coins) == 0 -> "social"
      Decimal.to_integer(task.required_win_coins) > 0 && task.withdraw_count == 0 -> "gaming"
      task.withdraw_count > 0 -> "financial"
      true -> "comprehensive"
    end
  end

  defp calculate_task_statistics(tasks) do
    total_tasks = length(tasks)
    enabled_tasks = Enum.count(tasks, &(&1.status == :enabled))

    difficulty_distribution =
      tasks
      |> Enum.group_by(&calculate_task_difficulty/1)
      |> Enum.map(fn {difficulty, task_list} -> {difficulty, length(task_list)} end)
      |> Enum.into(%{})

    category_distribution =
      tasks
      |> Enum.group_by(&categorize_task/1)
      |> Enum.map(fn {category, task_list} -> {category, length(task_list)} end)
      |> Enum.into(%{})

    total_rewards =
      tasks
      |> Enum.map(&Decimal.to_integer(&1.reward_amount))
      |> Enum.sum()

    %{
      total_tasks: total_tasks,
      enabled_tasks: enabled_tasks,
      disabled_tasks: total_tasks - enabled_tasks,
      difficulty_distribution: difficulty_distribution,
      category_distribution: category_distribution,
      total_rewards: total_rewards,
      average_reward: if(total_tasks > 0, do: div(total_rewards, total_tasks), else: 0)
    }
  end

  defp calculate_detailed_analytics do
    # TODO: 实现详细的任务分析
    %{
      completion_rates: %{},
      user_engagement: %{},
      reward_effectiveness: %{},
      task_popularity: %{}
    }
  end

  defp perform_bulk_action("enable", task_ids) do
    case enable_tasks(task_ids) do
      :ok -> {:ok, "已启用 #{length(task_ids)} 个任务"}
      {:error, reason} -> {:error, "批量启用失败: #{reason}"}
    end
  end

  defp perform_bulk_action("disable", task_ids) do
    case disable_tasks(task_ids) do
      :ok -> {:ok, "已禁用 #{length(task_ids)} 个任务"}
      {:error, reason} -> {:error, "批量禁用失败: #{reason}"}
    end
  end

  defp perform_bulk_action("delete", task_ids) do
    case delete_tasks(task_ids) do
      :ok -> {:ok, "已删除 #{length(task_ids)} 个任务"}
      {:error, reason} -> {:error, "批量删除失败: #{reason}"}
    end
  end

  defp perform_bulk_action(_, _), do: {:error, "未知操作"}

  defp enable_tasks(task_ids) do
    try do
      Enum.each(task_ids, fn task_id ->
        case FreeBonusTask.read(task_id) do
          {:ok, task} -> FreeBonusTask.enable_task(task)
          _ -> :ok
        end
      end)
      :ok
    rescue
      _ -> {:error, "操作失败"}
    end
  end

  defp disable_tasks(task_ids) do
    try do
      Enum.each(task_ids, fn task_id ->
        case FreeBonusTask.read(task_id) do
          {:ok, task} -> FreeBonusTask.disable_task(task)
          _ -> :ok
        end
      end)
      :ok
    rescue
      _ -> {:error, "操作失败"}
    end
  end

  defp delete_tasks(task_ids) do
    try do
      Enum.each(task_ids, fn task_id ->
        case FreeBonusTask.read(task_id) do
          {:ok, task} -> FreeBonusTask.destroy(task)
          _ -> :ok
        end
      end)
      :ok
    rescue
      _ -> {:error, "操作失败"}
    end
  end

  defp save_task(nil, task_params) do
    # 新建任务
    FreeBonusTask.create(task_params)
  end

  defp save_task(current_task, task_params) do
    # 更新任务
    FreeBonusTask.update(current_task, task_params)
  end

  defp format_changeset_errors(changeset) do
    # TODO: 格式化错误信息
    "验证失败"
  end

  defp export_task_data(tasks) do
    # TODO: 实现任务数据导出
    filename = "free_bonus_tasks_#{DateTime.utc_now() |> DateTime.to_date()}.csv"
    {:ok, filename}
  end
end
