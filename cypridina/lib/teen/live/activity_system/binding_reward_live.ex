defmodule Teen.Live.ActivitySystem.BindingRewardLive do
  @moduledoc """
  绑定奖励管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.BindingReward
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "绑定奖励"

  @impl Backpex.LiveResource
  def plural_name, do: "绑定奖励"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      binding_type: %{
        module: Backpex.Fields.Select,
        label: "绑定类型",
        options: [
          {"手机号", "phone"},
          {"邮箱", "email"},
          {"银行卡", "bank_card"},
          {"身份证", "id_card"}
        ]
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"}
        ]
      },
      one_time_only: %{
        module: Backpex.Fields.Boolean,
        label: "仅限一次"
      },
      verification_required: %{
        module: Backpex.Fields.Boolean,
        label: "需要验证"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
