defmodule Teen.Live.ActivitySystem.WeeklyCardLive do
  @moduledoc """
  周卡活动管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.WeeklyCard
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      card_name: %{
        module: Backpex.Fields.Text,
        label: "卡片名称"
      },
      card_type: %{
        module: Backpex.Fields.Select,
        label: "卡片类型",
        options: [
          {"周卡", "weekly"},
          {"月卡", "monthly"},
          {"季卡", "quarterly"}
        ]
      },
      price: %{
        module: Backpex.Fields.Number,
        label: "价格"
      },
      daily_reward: %{
        module: Backpex.Fields.Number,
        label: "每日奖励"
      },
      total_days: %{
        module: Backpex.Fields.Number,
        label: "总天数"
      },
      bonus_reward: %{
        module: Backpex.Fields.Number,
        label: "额外奖励"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
