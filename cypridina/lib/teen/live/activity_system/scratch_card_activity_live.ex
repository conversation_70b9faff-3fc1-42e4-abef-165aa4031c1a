defmodule Teen.Live.ActivitySystem.ScratchCardActivityLive do
  @moduledoc """
  刮刮卡活动管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.ScratchCardActivity
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "刮刮卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "刮刮卡活动"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      card_name: %{
        module: Backpex.Fields.Text,
        label: "卡片名称"
      },
      card_type: %{
        module: Backpex.Fields.Select,
        label: "卡片类型",
        options: [
          {"普通卡", "normal"},
          {"高级卡", "premium"},
          {"特殊卡", "special"}
        ]
      },
      cost_amount: %{
        module: Backpex.Fields.Number,
        label: "购买费用"
      },
      min_reward: %{
        module: Backpex.Fields.Number,
        label: "最小奖励"
      },
      max_reward: %{
        module: Backpex.Fields.Number,
        label: "最大奖励"
      },
      win_probability: %{
        module: Backpex.Fields.Number,
        label: "中奖概率(%)"
      },
      daily_limit: %{
        module: Backpex.Fields.Number,
        label: "每日限制次数"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
