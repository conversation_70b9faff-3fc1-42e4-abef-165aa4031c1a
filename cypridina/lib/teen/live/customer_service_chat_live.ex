defmodule Teen.Live.CustomerServiceChatLive do
  @moduledoc """
  客服聊天系统 LiveView

  提供实时的客服聊天功能，支持：
  - 实时消息收发
  - 聊天记录查看
  - 用户状态显示
  - 敏感词过滤
  - 图片消息支持
  """

  use Phoenix.LiveView, layout: {CypridinaWeb.Layouts, :root}
  use Phoenix.Component
  require Logger

  import CypridinaWeb.Components.Chat
  import CypridinaWeb.Components.Avatar
  import CypridinaWeb.Components.Card
  import CypridinaWeb.CoreComponents

  alias CypridinaWeb.Components.Button
  alias Cypridina.Utils.TimeHelper
  alias Teen.UserPresence

  alias Teen.CustomerService
  alias Cypridina.Accounts.User

  @impl Phoenix.LiveView
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 订阅聊天更新
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "customer_service_chat")
      # 订阅用户在线状态变化
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "user_presence")
    end

    socket =
      socket
      |> assign(:page_title, "客服聊天系统")
      |> assign(:current_chat_user, nil)
      |> assign(:chat_messages, [])
      |> assign(:new_message, "")
      |> assign(:online_users, [])
      |> assign(:unread_count, 0)
      |> assign(:show_new_chat_modal, false)
      |> assign(:available_users, [])
      |> assign(:search_query, "")
      |> assign(:user_search_query, "")
      |> assign(:sending_message, false)
      |> assign(:typing_indicator, false)
      |> load_chat_data()
      |> load_available_users()

    {:ok, socket}
  end

  @impl Phoenix.LiveView
  def handle_params(params, _url, socket) do
    socket =
      case Map.get(params, "user_id") do
        nil ->
          socket

        user_id ->
          socket
          |> assign(:current_chat_user, user_id)
          |> load_chat_messages(user_id)
      end

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("send_message", params, socket) do
    message = String.trim(get_in(params, ["message"]) || "")

    if message == "" do
      {:noreply, socket}
    else
      case socket.assigns.current_chat_user do
        nil ->
          {:noreply, put_flash(socket, :error, "请先选择要回复的用户")}

        user_id ->
          socket = assign(socket, :sending_message, true)
          # 安全地转换user_id为整数类型
          user_id = parse_user_id(user_id)

          case send_customer_service_reply(user_id, message, socket) do
            {:ok, _chat} ->
              socket =
                socket
                |> assign(:new_message, "")
                |> assign(:sending_message, false)
                |> load_chat_messages(user_id)
                |> put_flash(:info, "消息发送成功")

              # 发送自动滚动事件
              send(self(), :scroll_to_bottom)

              {:noreply, socket}

            {:error, _reason} ->
              socket =
                socket
                |> assign(:sending_message, false)
                |> put_flash(:error, "消息发送失败")

              {:noreply, socket}
          end
      end
    end
  end

  @impl Phoenix.LiveView
  def handle_event("select_user", %{"user_id" => user_id}, socket) do
    # 安全地转换user_id为整数类型
    user_id = parse_user_id(user_id)

    socket =
      socket
      |> assign(:current_chat_user, user_id)
      |> load_chat_messages(user_id)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("search_chats", %{"value" => query}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> filter_chat_messages(query)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> load_chat_data()

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("update_message", params, socket) do
    message = get_in(params, ["value"]) || ""
    {:noreply, assign(socket, :new_message, message)}
  end

  @impl Phoenix.LiveView
  def handle_event("handle_keydown", %{"key" => "Enter", "shiftKey" => false}, socket) do
    # Enter键发送消息（不按Shift）
    handle_event("send_message", %{"message" => socket.assigns.new_message}, socket)
  end

  @impl Phoenix.LiveView
  def handle_event("handle_keydown", _params, socket) do
    # 其他按键不处理
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("scroll_to_bottom", _params, socket) do
    send(self(), :scroll_to_bottom)
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("show_new_chat_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_new_chat_modal, true)
      |> assign(:search_query, "")
      |> load_available_users()

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("hide_new_chat_modal", _params, socket) do
    socket = assign(socket, :show_new_chat_modal, false)
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("search_users", %{"value" => query}, socket) do
    socket = assign(socket, :user_search_query, query)
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("start_chat_with_user", %{"user_id" => user_id}, socket) do
    # 安全地转换user_id为整数类型
    user_id = parse_user_id(user_id)

    # 创建新的聊天会话
    case create_new_chat_session(user_id, socket) do
      {:ok, _chat} ->
        socket =
          socket
          |> assign(:show_new_chat_modal, false)
          |> assign(:current_chat_user, user_id)
          |> load_chat_data()
          |> load_chat_messages(user_id)
          |> put_flash(:info, "已创建新的聊天会话")

        {:noreply, socket}

      {:error, reason} ->
        socket = put_flash(socket, :error, "创建聊天会话失败: #{reason}")
        {:noreply, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_info({:customer_chat_created, chat}, socket) do
    socket =
      socket
      |> load_chat_data()
      |> maybe_update_current_chat(chat)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: "user_presence", event: "presence_diff"},
        socket
      ) do
    # 用户在线状态发生变化，重新加载数据
    socket =
      socket
      |> load_chat_data()
      |> load_available_users()

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info({:customer_chat_updated, chat}, socket) do
    socket =
      socket
      |> load_chat_data()
      |> maybe_update_current_chat(chat)

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info(:scroll_to_bottom, socket) do
    # 发送JavaScript事件来滚动到底部
    {:noreply, push_event(socket, "scroll_to_bottom", %{})}
  end

  # 自定义渲染函数
  @impl Phoenix.LiveView
  def render(assigns) do
    ~H"""
    <div class="h-screen flex bg-white">
      
    <!-- 左侧聊天列表 - Telegram风格，常驻显示 -->
      <div class="bg-white border-r border-gray-200 flex flex-col w-96 flex-shrink-0">
        <!-- 头部 - Telegram风格 -->
        <div class="bg-blue-600 text-white">
          <div class="p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold">客服聊天</h2>
                  <%= if @unread_count > 0 do %>
                    <div class="text-sm text-blue-200">{@unread_count} 条未读消息</div>
                  <% end %>
                </div>
              </div>
              <button
                phx-click="show_new_chat_modal"
                class="p-2 hover:bg-white/10 rounded-full transition-colors"
                title="新聊天"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
        
    <!-- 搜索框 -->
        <div class="p-3 border-b border-gray-200 bg-gray-50">
          <div class="relative">
            <input
              type="text"
              placeholder="搜索聊天记录..."
              value={@search_query || ""}
              phx-keyup="search_chats"
              phx-debounce="300"
              class="w-full pl-10 pr-4 py-2 bg-white border border-gray-300 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <%= if @search_query && String.length(@search_query) > 0 do %>
              <button
                phx-click="clear_search"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <svg
                  class="w-4 h-4 text-gray-400 hover:text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            <% end %>
          </div>
        </div>
        
    <!-- 聊天列表 - Telegram风格 -->
        <div class="flex-1 overflow-y-auto">
          <%= if length(@chat_messages) > 0 do %>
            <%= for user_chat <- @chat_messages do %>
              <div
                class={[
                  "flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors",
                  @current_chat_user == user_chat.user_id && "bg-blue-50 border-l-4 border-l-blue-500"
                ]}
                phx-click="select_user"
                phx-value-user_id={user_chat.user_id}
              >
                <!-- 头像 -->
                <div class="relative mr-3 flex-shrink-0">
                  <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                    {String.first(user_chat.user.profile.nickname || "用户")}
                  </div>
                  <!-- 在线状态 - 通过Presence检查 -->
                  <%= if is_user_online?(user_chat.user_id) do %>
                    <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 border-2 border-white rounded-full">
                    </div>
                  <% else %>
                    <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-gray-400 border-2 border-white rounded-full">
                    </div>
                  <% end %>
                </div>
                
    <!-- 聊天信息 -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between mb-1">
                    <h4 class="font-semibold text-gray-900 truncate">
                      {user_chat.user.profile.nickname || "用户#{user_chat.user_id}"}
                    </h4>
                    <div class="flex items-center gap-2">
                      <span class="text-xs text-gray-500">
                        {TimeHelper.format_telegram_time(user_chat.inserted_at)}
                      </span>
                      <%= if user_chat.status == 0 do %>
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span class="text-xs text-white font-bold">1</span>
                        </div>
                      <% end %>
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <p class="text-sm text-gray-600 truncate">
                      {String.slice(user_chat.question || "暂无消息", 0, 35)}
                    </p>
                    <%= if user_chat.status == 0 do %>
                      <svg
                        class="w-4 h-4 text-gray-400 ml-2 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    <% else %>
                      <svg
                        class="w-4 h-4 text-blue-500 ml-2 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          <% else %>
            <div class="flex flex-col items-center justify-center h-64 text-gray-400">
              <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-600 mb-2">暂无聊天记录</h3>
              <p class="text-sm text-gray-500 text-center mb-4">点击右上角按钮开始新的对话</p>
            </div>
          <% end %>
        </div>
      </div>
      
    <!-- 右侧聊天区域 - Telegram风格 -->
      <div class="flex-1 flex flex-col lg:relative">
        <%= if @current_chat_user do %>
          <!-- 聊天头部 - Telegram风格 -->
          <div class="bg-white border-b border-gray-200 shadow-sm">
            <div class="flex items-center justify-between p-4">
              <div class="flex items-center gap-3">
                <!-- 用户头像 -->
                <div class="relative">
                  <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                    {get_user_initial(@current_chat_user)}
                  </div>
                  <!-- 在线状态 - 通过Presence检查 -->
                  <%= if is_user_online?(@current_chat_user) do %>
                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full">
                    </div>
                  <% else %>
                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-gray-400 border-2 border-white rounded-full">
                    </div>
                  <% end %>
                </div>
                
    <!-- 用户信息 -->
                <div>
                  <h3 class="font-semibold text-gray-900">
                    {get_user_name(@current_chat_user)}
                  </h3>
                  <!-- 在线状态文字 - 通过Presence检查 -->
                  <%= if is_user_online?(@current_chat_user) do %>
                    <p class="text-sm text-green-600 flex items-center">
                      <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span> 在线
                    </p>
                  <% else %>
                    <p class="text-sm text-gray-500 flex items-center">
                      <span class="w-2 h-2 bg-gray-400 rounded-full mr-1.5"></span> 离线
                    </p>
                  <% end %>
                </div>
              </div>
              
    <!-- 聊天操作按钮 -->
              <div class="flex items-center gap-2">
                <button
                  phx-click="scroll_to_bottom"
                  class="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  title="滚动到底部"
                >
                  <svg
                    class="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                </button>
                <button
                  class="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  title="更多选项"
                >
                  <svg
                    class="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
    <!-- 聊天消息区域 - Telegram风格绿色渐变背景 -->
          <div
            id="chat-messages"
            phx-hook="ChatScroll"
            class="flex-1 overflow-y-auto p-4 space-y-3"
            style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 25%, #7fcdcd 50%, #74b9ff 75%, #6c5ce7 100%); scroll-behavior: smooth;"
          >
            {render_chat_messages(assigns)}
            <!-- 消息底部锚点 -->
            <div id="messages-bottom" class="h-1"></div>
          </div>
          
    <!-- 输入状态指示器 -->
          <div :if={@typing_indicator} class="px-4 py-2 bg-gray-50 border-t border-gray-100">
            <div class="flex items-center space-x-2 text-sm text-gray-500">
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0.1s"
                >
                </div>
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0.2s"
                >
                </div>
              </div>
              <span>对方正在输入...</span>
            </div>
          </div>
          
    <!-- 消息输入区域 - Telegram风格 -->
          <div class="p-4 bg-white border-t border-gray-200">
            <form phx-submit="send_message">
              <div class="flex items-end gap-3">
                <!-- 附件按钮 -->
                <button
                  type="button"
                  class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                  title="添加附件"
                >
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                    />
                  </svg>
                </button>
                
    <!-- 输入框 -->
                <div class="flex-1">
                  <div class="relative">
                    <input
                      id="message-input"
                      name="message"
                      value={@new_message}
                      placeholder="输入消息..."
                      phx-keyup="update_message"
                      phx-keydown="handle_keydown"
                      maxlength="1000"
                      autofocus
                      class="w-full px-4 py-3 pr-12 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      style="min-height: 44px;"
                    />
                    <!-- 表情按钮 -->
                    <button
                      type="button"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-500 hover:text-blue-600 rounded-full transition-colors"
                      title="表情"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </button>
                  </div>
                  <!-- 提示文字 -->
                  <div class="flex justify-between items-center mt-1 px-2">
                    <div class="text-xs text-gray-400">
                      按 Enter 发送
                    </div>
                    <div class="text-xs text-gray-400">
                      {String.length(@new_message || "")}/1000
                    </div>
                  </div>
                </div>
                
    <!-- 发送按钮 -->
                <button
                  type="submit"
                  disabled={String.trim(@new_message || "") == ""}
                  class={[
                    "p-3 rounded-full transition-all duration-200",
                    (String.trim(@new_message || "") == "" &&
                       "bg-gray-200 text-gray-400 cursor-not-allowed") ||
                      "bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl"
                  ]}
                  title="发送消息"
                >
                  <%= if @sending_message do %>
                    <svg
                      class="w-5 h-5 animate-spin"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                  <% else %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                      />
                    </svg>
                  <% end %>
                </button>
              </div>
            </form>
          </div>
        <% else %>
          <!-- 空状态 - Telegram风格 -->
          <div
            class="flex-1 flex items-center justify-center"
            style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 25%, #7fcdcd 50%, #74b9ff 75%, #6c5ce7 100%);"
          >
            <div class="text-center max-w-md mx-auto p-8">
              <div class="mx-auto mb-8">
                <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                  </svg>
                </div>
              </div>
              <h3 class="text-2xl font-semibold text-white mb-3">选择聊天开始对话</h3>
              <p class="text-white/80 leading-relaxed">从左侧列表中选择一个用户开始客服对话<br />或者点击左上角按钮创建新的聊天会话</p>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- 新聊天模态框 - Telegram风格 -->
    <%= if @show_new_chat_modal do %>
      <div class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-2xl w-96 max-h-[80vh] flex flex-col">
          <!-- 模态框头部 -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 class="text-xl font-semibold text-gray-900">发起新聊天</h3>
            <button
              phx-click="hide_new_chat_modal"
              class="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          
    <!-- 搜索框 -->
          <div class="p-4 border-b border-gray-200">
            <div class="relative">
              <input
                type="text"
                placeholder="搜索用户..."
                value={@user_search_query || ""}
                phx-keyup="search_users"
                phx-debounce="300"
                class="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg
                  class="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
          
    <!-- 用户列表 -->
          <div class="flex-1 overflow-y-auto">
            <%= if length(@available_users) > 0 do %>
              <%= for user <- filter_available_users(@available_users, @user_search_query) do %>
                <div
                  class="flex items-center p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors"
                  phx-click="start_chat_with_user"
                  phx-value-user_id={user.id}
                >
                  <!-- 用户头像 -->
                  <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-lg flex-shrink-0">
                    {String.first(get_user_display_name(user))}
                  </div>
                  
    <!-- 用户信息 -->
                  <div class="ml-3 flex-1">
                    <h4 class="font-semibold text-gray-900">
                      {get_user_display_name(user)}
                    </h4>
                    <div class="flex items-center justify-between mt-1">
                      <div class="flex items-center">
                        <!-- 在线状态 - 通过Presence检查 -->
                        <%= if is_user_online?(user.id) do %>
                          <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span class="text-sm text-green-600">在线</span>
                        <% else %>
                          <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                          <span class="text-sm text-gray-500">离线</span>
                        <% end %>
                      </div>
                      <!-- 聊天状态提示 -->
                      <%= if CustomerService.has_active_chat_session?(user.id) do %>
                        <span class="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full">
                          进行中
                        </span>
                      <% end %>
                    </div>
                  </div>
                  
    <!-- 箭头图标 -->
                  <div class="flex-shrink-0">
                    <svg
                      class="w-5 h-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              <% end %>
            <% else %>
              <div class="flex flex-col items-center justify-center py-12 text-gray-500">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                  </svg>
                </div>
                <%= if @user_search_query && @user_search_query != "" do %>
                  <p class="text-center">未找到匹配的用户</p>
                  <p class="text-sm text-center mt-1">尝试使用不同的关键词搜索</p>
                <% else %>
                  <p class="text-center">暂无可用用户</p>
                  <p class="text-sm text-center mt-1">当前没有在线用户</p>
                <% end %>
              </div>
            <% end %>
          </div>
          
    <!-- 模态框底部 -->
          <div class="p-4 border-t border-gray-200">
            <button
              phx-click="hide_new_chat_modal"
              class="w-full py-3 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full font-medium transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    <% end %>
    """
  end

  # 私有函数
  defp load_chat_data(socket) do
    # 加载最近的聊天记录
    chats = CustomerService.list_recent_customer_chats(limit: 50)
    unread_count = CustomerService.count_unread_chats()

    socket
    |> assign(:chat_messages, chats)
    |> assign(:unread_count, unread_count)
  end

  defp load_chat_messages(socket, user_id) do
    messages = CustomerService.list_user_chat_history(user_id)
    assign(socket, :current_chat_messages, messages)
  end

  defp send_customer_service_reply(user_id, message, socket) do
    # 获取当前客服用户ID，如果没有则使用nil
    current_user_id =
      case socket.assigns[:current_user] do
        %{id: id} when is_binary(id) -> id
        _ -> nil
      end

    # 记录调试信息
    Logger.info(
      "💬 [CHAT] 发送客服回复 - 用户: #{user_id}, 消息: #{String.slice(message, 0, 50)}, 客服: #{current_user_id}"
    )

    # 创建客服回复记录
    attrs = %{
      user_id: user_id,
      platform: "admin",
      # 标识这是客服回复
      question: "[客服回复]",
      reply_content: message,
      # 已处理状态
      status: 1,
      customer_service_id: current_user_id,
      processed_at: DateTime.utc_now()
    }

    case CustomerService.create_customer_chat(attrs) do
      {:ok, chat} ->
        Logger.info("💬 [CHAT] 客服回复创建成功 - ID: #{chat.id}")

        # 广播聊天更新事件
        Phoenix.PubSub.broadcast(
          Cypridina.PubSub,
          "customer_service_chat",
          {:customer_chat_created, chat}
        )

        {:ok, chat}

      {:error, reason} ->
        Logger.error("💬 [CHAT] 客服回复创建失败 - 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp maybe_update_current_chat(socket, chat) do
    if socket.assigns.current_chat_user == chat.user_id do
      load_chat_messages(socket, chat.user_id)
    else
      socket
    end
  end

  defp format_time(datetime) do
    Calendar.strftime(datetime, "%m-%d %H:%M")
  end

  defp get_user_initial(user_id) do
    # 这里应该从数据库获取用户信息
    "U"
  end

  defp get_user_name(user_id) do
    # 这里应该从数据库获取用户信息
    "用户#{user_id}"
  end

  # 加载可用用户列表
  defp load_available_users(socket) do
    # 获取所有用户（不再依赖数据库的在线状态字段）
    all_users =
      User
      # 加载用户资料
      |> Ash.Query.load(:profile)
      # 增加限制数量，因为我们需要过滤
      |> Ash.Query.limit(100)
      |> Ash.read!()

    # 过滤掉已有活跃聊天会话的用户
    available_users =
      all_users
      |> Enum.reject(fn user ->
        CustomerService.has_active_chat_session?(user.id)
      end)

    # 可以选择只显示在线用户
    # |> Enum.filter(&is_user_online?(&1.id))

    Logger.info("💬 [CHAT] 加载可用用户 - 总数: #{length(all_users)}, 可用: #{length(available_users)}")
    assign(socket, :available_users, available_users)
  end

  # 过滤聊天记录
  defp filter_chat_messages(socket, query) when query == "" or is_nil(query) do
    load_chat_data(socket)
  end

  defp filter_chat_messages(socket, query) do
    query_lower = String.downcase(query)

    # 从数据库搜索聊天记录
    filtered_chats =
      CustomerService.list_customer_chats()
      |> Enum.filter(fn chat ->
        # 搜索用户昵称
        user_name = chat.user.profile.nickname || "用户#{chat.user_id}"
        name_match = String.contains?(String.downcase(user_name), query_lower)

        # 搜索消息内容
        content_match =
          (chat.question && String.contains?(String.downcase(chat.question), query_lower)) ||
            (chat.reply_content &&
               String.contains?(String.downcase(chat.reply_content), query_lower))

        name_match || content_match
      end)

    assign(socket, :chat_messages, filtered_chats)
  end

  # 过滤可用用户
  defp filter_available_users(users, query) when query == "" or is_nil(query) do
    users
  end

  defp filter_available_users(users, query) do
    query_lower = String.downcase(query)

    Enum.filter(users, fn user ->
      display_name = get_user_display_name(user)
      String.contains?(String.downcase(display_name), query_lower)
    end)
  end

  # 创建新的聊天会话
  defp create_new_chat_session(user_id, socket) do
    current_user_id =
      case socket.assigns[:current_user] do
        %{id: id} when is_binary(id) -> id
        _ -> nil
      end

    Logger.info("💬 [CHAT] 尝试创建新聊天会话 - 用户: #{user_id}, 客服: #{current_user_id}")

    # 使用带重复检查的创建函数
    attrs = %{
      user_id: user_id,
      platform: "admin",
      question: "客服主动发起的聊天",
      reply_content: "",
      # 未处理状态
      status: 0,
      customer_service_id: current_user_id,
      processed_at: nil
    }

    case CustomerService.create_customer_chat_with_check(attrs) do
      {:ok, chat} ->
        Logger.info("💬 [CHAT] 新聊天会话创建成功 - ID: #{chat.id}")
        {:ok, chat}

      {:error, {:already_exists, existing_chat}} ->
        Logger.info("💬 [CHAT] 用户已有活跃聊天会话 - 会话ID: #{existing_chat.id}")
        {:error, "该用户已有进行中的聊天会话，请在现有会话中继续对话"}

      {:error, reason} ->
        Logger.error("💬 [CHAT] 创建聊天会话失败 - 原因: #{inspect(reason)}")
        {:error, "创建聊天会话失败"}
    end
  end

  # 安全地解析用户ID
  defp parse_user_id(user_id) when is_binary(user_id) do
    case Integer.parse(user_id) do
      {id, ""} -> id
      # 如果解析失败，返回原值
      _ -> user_id
    end
  end

  defp parse_user_id(user_id), do: user_id

  # 检查用户是否在线 - 通过Phoenix Presence
  defp is_user_online?(user_id) when is_nil(user_id), do: false

  defp is_user_online?(user_id) do
    UserPresence.user_online?(user_id)
  end

  # 获取用户显示名称
  defp get_user_display_name(user) do
    cond do
      user.profile && user.profile.nickname && user.profile.nickname != "" ->
        user.profile.nickname

      user.username && user.username != "" ->
        user.username

      true ->
        "用户#{user.numeric_id || user.id}"
    end
  end

  defp render_chat_messages(assigns) do
    ~H"""
    <%= if length(@current_chat_messages || []) > 0 do %>
      <%= for message <- @current_chat_messages do %>
        <%= if message.question && message.question != "" do %>
          <!-- 用户消息 - Telegram风格 -->
          <div class="flex items-end gap-2 mb-4">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
              {String.first(message.user.profile.nickname || "U")}
            </div>
            <div class="flex flex-col max-w-xs lg:max-w-md">
              <div class="bg-white rounded-2xl rounded-bl-md shadow-lg px-4 py-3 border border-gray-100">
                <div class="text-xs text-blue-600 font-medium mb-1">
                  {message.user.profile.nickname || "用户"}
                </div>
                <p class="text-gray-900 leading-relaxed text-sm">{message.question}</p>
              </div>
              <div class="text-xs text-gray-500 mt-1 ml-3">
                {TimeHelper.time_ago(message.inserted_at)}
              </div>
            </div>
          </div>
        <% end %>

        <%= if message.reply_content && message.reply_content != "" do %>
          <!-- 客服回复 - Telegram风格 -->
          <div class="flex items-end gap-2 mb-4 justify-end">
            <div class="flex flex-col max-w-xs lg:max-w-md">
              <div class="bg-green-500 rounded-2xl rounded-br-md shadow-lg px-4 py-3">
                <div class="text-xs text-green-100 font-medium mb-1">
                  客服
                </div>
                <p class="text-white leading-relaxed text-sm">{message.reply_content}</p>
              </div>
              <div class="flex items-center justify-end gap-1 text-xs text-gray-500 mt-1 mr-3">
                <span>{TimeHelper.time_ago(message.processed_at || message.updated_at)}</span>
                <!-- 消息状态指示 - 双勾表示已读 -->
                <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                <svg class="w-3 h-3 text-green-600 -ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
              CS
            </div>
          </div>
        <% end %>
      <% end %>
    <% else %>
      <div class="flex flex-col items-center justify-center h-64 text-gray-500">
        <svg
          class="w-16 h-16 mb-4 text-gray-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        <p class="text-lg font-medium mb-2">暂无聊天记录</p>
        <p class="text-sm">开始对话吧！</p>
      </div>
    <% end %>
    """
  end
end
