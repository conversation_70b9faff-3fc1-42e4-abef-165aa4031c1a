defmodule Teen.Live.LogViewerLive do
  @moduledoc """
  日志查看器页面

  提供查看logs目录下各种日志文件的功能
  """

  use CypridinaWeb, :live_view

  @logs_dir "logs"
  @max_lines 1000
  @default_lines 100

  def mount(_params, _session, socket) do
    log_categories = get_categorized_log_files()

    socket =
      socket
      |> assign(:log_categories, log_categories)
      |> assign(:selected_file, nil)
      |> assign(:log_content, "")
      |> assign(:lines_to_show, @default_lines)
      |> assign(:search_term, "")
      |> assign(:auto_refresh, false)
      # 颜色图例显示状态
      |> assign(:show_color_legend, false)
      |> assign(:page_title, "系统日志")

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  def handle_event("select_file", %{"file" => file_path}, socket) do
    content = read_log_file(file_path, socket.assigns.lines_to_show)

    socket =
      socket
      |> assign(:selected_file, file_path)
      |> assign(:log_content, content)
      |> assign(:search_term, "")

    {:noreply, socket}
  end

  def handle_event("refresh", _params, socket) do
    if socket.assigns.selected_file do
      content = read_log_file(socket.assigns.selected_file, socket.assigns.lines_to_show)
      socket = assign(socket, :log_content, content)
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  def handle_event("change_lines", %{"lines_form" => %{"lines" => lines_str}}, socket) do
    lines = String.to_integer(lines_str)
    lines = min(lines, @max_lines)

    content =
      if socket.assigns.selected_file do
        read_log_file(socket.assigns.selected_file, lines)
      else
        ""
      end

    socket =
      socket
      |> assign(:lines_to_show, lines)
      |> assign(:log_content, content)

    {:noreply, socket}
  end

  def handle_event("search", %{"term" => term}, socket) do
    socket = assign(socket, :search_term, term)
    {:noreply, socket}
  end

  def handle_event("toggle_auto_refresh", _params, socket) do
    auto_refresh = !socket.assigns.auto_refresh

    socket =
      socket
      |> assign(:auto_refresh, auto_refresh)
      |> maybe_schedule_refresh(auto_refresh)

    {:noreply, socket}
  end

  def handle_event("toggle_color_legend", _params, socket) do
    socket = assign(socket, :show_color_legend, !socket.assigns.show_color_legend)
    {:noreply, socket}
  end

  def handle_event("download", _params, socket) do
    if socket.assigns.selected_file do
      file_path = Path.join(@logs_dir, socket.assigns.selected_file)

      if File.exists?(file_path) do
        {:noreply,
         socket
         |> put_flash(:info, "开始下载日志文件...")
         |> push_event("download_file", %{
           url: "/admin/logs/download?file=#{URI.encode(socket.assigns.selected_file)}",
           filename: Path.basename(socket.assigns.selected_file)
         })}
      else
        {:noreply, put_flash(socket, :error, "文件不存在")}
      end
    else
      {:noreply, put_flash(socket, :error, "请先选择一个日志文件")}
    end
  end

  def handle_event("delete_file", %{"file" => file_path}, socket) do
    case delete_log_file(file_path) do
      :ok ->
        # 重新获取文件列表
        log_categories = get_categorized_log_files()

        # 如果删除的是当前选中的文件，清空选择
        {selected_file, log_content} =
          if socket.assigns.selected_file == file_path do
            {nil, ""}
          else
            {socket.assigns.selected_file, socket.assigns.log_content}
          end

        socket =
          socket
          |> assign(:log_categories, log_categories)
          |> assign(:selected_file, selected_file)
          |> assign(:log_content, log_content)
          |> put_flash(:info, "已删除日志文件: #{file_path}")

        {:noreply, socket}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "删除失败: #{reason}")}
    end
  end

  def handle_info(:refresh_log, socket) do
    if socket.assigns.auto_refresh && socket.assigns.selected_file do
      content = read_log_file(socket.assigns.selected_file, socket.assigns.lines_to_show)

      socket =
        socket
        |> assign(:log_content, content)
        |> maybe_schedule_refresh(true)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp get_categorized_log_files do
    %{
      system: get_system_log_files(),
      games: get_game_log_files_categorized()
    }
  end

  defp get_system_log_files do
    case File.ls(@logs_dir) do
      {:ok, files} ->
        files
        |> Enum.filter(&String.ends_with?(&1, ".log"))
        |> Enum.map(fn file ->
          file_path = Path.join(@logs_dir, file)
          stat = File.stat!(file_path)

          %{
            name: file,
            path: file,
            size: format_file_size(stat.size),
            modified: format_datetime(stat.mtime),
            mtime: stat.mtime
          }
        end)
        |> Enum.sort_by(& &1.mtime, :desc)

      {:error, _} ->
        []
    end
  end

  defp get_game_log_files_categorized do
    games_dir = Path.join(@logs_dir, "games")

    case File.ls(games_dir) do
      {:ok, files} ->
        files
        |> Enum.filter(&String.ends_with?(&1, ".log"))
        |> Enum.map(fn file ->
          file_path = Path.join(games_dir, file)
          stat = File.stat!(file_path)
          game_name = extract_game_name(file)

          %{
            name: file,
            path: "games/#{file}",
            size: format_file_size(stat.size),
            modified: format_datetime(stat.mtime),
            mtime: stat.mtime,
            game: game_name
          }
        end)
        |> Enum.sort_by(& &1.mtime, :desc)
        |> Enum.group_by(& &1.game)

      {:error, _} ->
        %{}
    end
  end

  defp extract_game_name(filename) do
    # 从文件名中提取游戏ID，格式为 game_{game_id}_{room_id}.log
    case Regex.run(~r/game_(\d+)_\d+\.log/, filename) do
      [_, game_id] ->
        get_game_name_by_id(game_id)

      _ ->
        "其他游戏"
    end
  end

  defp get_game_name_by_id(game_id) do
    case game_id do
      "1" -> "Teen Patti"
      "3" -> "Pot Blind"
      "21" -> "Jhandi Munda"
      "22" -> "龙虎斗"
      "23" -> "Crash"
      "40" -> "老虎机777"
      "41" -> "老虎机牛"
      "42" -> "老虎机猫"
      "54" -> "Safari of Wealth"
      _ -> "游戏#{game_id}"
    end
  end

  defp read_log_file(file_path, lines) do
    full_path = Path.join(@logs_dir, file_path)

    case File.exists?(full_path) do
      true ->
        case System.cmd("tail", ["-n", to_string(lines), full_path], stderr_to_stdout: true) do
          {content, 0} -> content
          {error, _} -> "读取文件失败: #{error}"
        end

      false ->
        "文件不存在: #{file_path}"
    end
  end

  defp format_file_size(size) when size < 1024, do: "#{size} B"
  defp format_file_size(size) when size < 1024 * 1024, do: "#{Float.round(size / 1024, 1)} KB"

  defp format_file_size(size) when size < 1024 * 1024 * 1024,
    do: "#{Float.round(size / (1024 * 1024), 1)} MB"

  defp format_file_size(size), do: "#{Float.round(size / (1024 * 1024 * 1024), 1)} GB"

  defp format_datetime({{year, month, day}, {hour, minute, second}}) do
    "#{year}-#{String.pad_leading(to_string(month), 2, "0")}-#{String.pad_leading(to_string(day), 2, "0")} " <>
      "#{String.pad_leading(to_string(hour), 2, "0")}:#{String.pad_leading(to_string(minute), 2, "0")}:#{String.pad_leading(to_string(second), 2, "0")}"
  end

  defp maybe_schedule_refresh(socket, true) do
    Process.send_after(self(), :refresh_log, 5000)
    socket
  end

  defp maybe_schedule_refresh(socket, false), do: socket

  # 删除日志文件
  defp delete_log_file(file_path) do
    full_path = Path.join(@logs_dir, file_path)

    case File.exists?(full_path) do
      true ->
        try do
          File.rm!(full_path)
          :ok
        rescue
          error ->
            {:error, "删除文件失败: #{inspect(error)}"}
        end

      false ->
        {:error, "文件不存在"}
    end
  end

  # 在模板中使用的公共函数
  def filter_content(content, search_term) when is_binary(content) and is_binary(search_term) do
    case search_term do
      "" ->
        content

      term ->
        content
        |> String.split("\n")
        |> Enum.filter(&String.contains?(String.downcase(&1), String.downcase(term)))
        |> Enum.join("\n")
    end
  end

  # 根据日志行内容的ASCII码计算颜色类
  def get_log_line_color_class(line) when is_binary(line) do
    case String.trim(line) do
      "" ->
        # 空行显示为灰色
        "text-gray-500"

      trimmed_line ->
        # 根据行内容的特征来分配颜色
        cond do
          # 错误日志 - 红色
          String.contains?(String.downcase(trimmed_line), [
            "error",
            "错误",
            "failed",
            "失败",
            "exception"
          ]) ->
            "text-red-400"

          # 警告日志 - 黄色
          String.contains?(String.downcase(trimmed_line), ["warn", "warning", "警告"]) ->
            "text-yellow-400"

          # 信息日志 - 蓝色
          String.contains?(String.downcase(trimmed_line), ["info", "信息", "information"]) ->
            "text-blue-400"

          # 调试日志 - 紫色
          String.contains?(String.downcase(trimmed_line), ["debug", "调试", "trace"]) ->
            "text-purple-400"

          # 成功日志 - 绿色
          String.contains?(String.downcase(trimmed_line), [
            "success",
            "成功",
            "ok",
            "completed",
            "完成"
          ]) ->
            "text-green-400"

          # 根据首字符ASCII码分配颜色
          true ->
            first_char = String.first(trimmed_line)
            ascii_value = if first_char, do: :binary.first(first_char), else: 0
            get_color_by_ascii(ascii_value)
        end
    end
  end

  # 根据ASCII码值分配颜色
  defp get_color_by_ascii(ascii_value) do
    # 使用ASCII值的模运算来分配不同颜色
    case rem(ascii_value, 8) do
      # 浅灰色
      0 -> "text-gray-300"
      # 青色
      1 -> "text-cyan-400"
      # 绿色
      2 -> "text-green-400"
      # 黄色
      3 -> "text-yellow-400"
      # 蓝色
      4 -> "text-blue-400"
      # 紫色
      5 -> "text-purple-400"
      # 粉色
      6 -> "text-pink-400"
      # 橙色
      7 -> "text-orange-400"
    end
  end
end
