defmodule Teen.Live.ItemActions.DisableChannelAction do
  @moduledoc """
  禁用渠道操作

  用于在渠道管理页面中禁用指定的渠道
  """

  use Backpex.ItemAction
  import Phoenix.LiveView

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "禁用"

  @impl Backpex.ItemAction
  def icon(_assigns, _item), do: "hero-x-circle"

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认禁用"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要禁用这个渠道吗？"

  @impl Backpex.ItemAction
  def fields(), do: []

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    socket =
      try do
        case items do
          [item] ->
            case Cypridina.Accounts.Channel.disable(item) do
              {:ok, _updated_item} ->
                socket
                |> put_flash(:info, "渠道已成功禁用")

              {:error, _changeset} ->
                socket
                |> put_flash(:error, "禁用渠道失败")
            end

          _ ->
            socket
            |> put_flash(:error, "只能对单个渠道执行禁用操作")
        end
      rescue
        error ->
          socket
          |> put_flash(:error, "操作失败: #{inspect(error)}")
      end

    {:noreply, socket}
  end
end
