defmodule Teen.Live.PaymentOrderLive do
  @moduledoc """
  支付订单管理页面

  提供支付订单的查看、管理和统计功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.PaymentOrder
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "支付订单"

  @impl Backpex.LiveResource
  def prepare_query(query, _context) do
    Ash.Query.load(query, [:user])
  end

  @impl Backpex.LiveResource
  def plural_name, do: "支付订单"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      # 订单通常不允许手动创建
      :new -> false
      # 允许修改订单状态等
      :edit -> true
      # 订单不允许删除
      :delete -> false
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        only: [:show],
        help_text: "系统生成的内部ID"
      },
      gateway_config_id: %{
        module: Backpex.Fields.Text,
        label: "网关配置ID",
        only: [:show],
        help_text: "支付网关配置的内部ID"
      },
      gateway_order_id: %{
        module: Backpex.Fields.Text,
        label: "网关订单ID",
        only: [:show],
        help_text: "支付网关生成的订单ID"
      },
      order_id: %{
        module: Backpex.Fields.Text,
        label: "订单号",
        searchable: true,
        readonly: true,
        help_text: "系统生成的唯一订单号"
      },
      user_id: %{
        module: Backpex.Fields.BelongsTo,
        label: "用户",
        display_field: :username,
        live_resource: Cypridina.Accounts.User,
        searchable: true,
        help_text: "下单用户"
      },
      amount: %{
        module: Backpex.Fields.Number,
        label: "订单金额",
        help_text: "支付金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end
      },
      currency: %{
        module: Backpex.Fields.Text,
        label: "货币类型",
        help_text: "支付货币类型"
      },
      channel_id: %{
        module: Backpex.Fields.Text,
        label: "支付渠道",
        searchable: true,
        help_text: "使用的支付渠道ID"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "订单状态",
        options: [
          {"待支付", "pending"},
          {"支付中", "processing"},
          {"支付成功", "success"},
          {"支付失败", "failed"},
          {"已取消", "cancelled"},
          {"已退款", "refunded"}
        ],
        render: fn assigns ->
          {status_class, status_text} =
            case assigns.value do
              "pending" -> {"badge-warning", "待支付"}
              "processing" -> {"badge-info", "支付中"}
              "success" -> {"badge-success", "支付成功"}
              "failed" -> {"badge-error", "支付失败"}
              "cancelled" -> {"badge-neutral", "已取消"}
              "refunded" -> {"badge-secondary", "已退款"}
              _ -> {"badge-ghost", assigns.value}
            end

          assigns =
            assigns
            |> assign(:status_class, status_class)
            |> assign(:status_text, status_text)

          ~H"""
          <span class={"badge #{@status_class}"}>{@status_text}</span>
          """
        end
      },
      payment_url: %{
        module: Backpex.Fields.URL,
        label: "支付链接",
        help_text: "第三方支付页面链接"
      },
      callback_data: %{
        module: Backpex.Fields.Textarea,
        label: "回调数据",
        help_text: "支付网关回调的数据",
        render: fn assigns ->
          data = assigns.value || %{}
          assigns = assign(assigns, :formatted_data, Jason.encode!(data, pretty: true))

          ~H"""
          <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto"><%= @formatted_data %></pre>
          """
        end
      },
      paid_at: %{
        module: Backpex.Fields.DateTime,
        label: "支付时间",
        help_text: "订单支付完成的时间"
      },
      created_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.PaymentStatusSelect
      },
      channel_id: %{
        module: Teen.Filters.PaymentChannelSelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      refresh_status: %{
        module: Teen.ResourceActions.RefreshPaymentStatus,
        label: "刷新状态",
        icon: "hero-arrow-path",
        confirm_label: "确认刷新",
        confirm_text: "确定要刷新选中订单的状态吗？",
        fields: []
      },
      export_orders: %{
        module: Teen.ResourceActions.ExportPaymentOrders,
        label: "导出订单",
        icon: "hero-document-arrow-down",
        confirm_label: "确认导出",
        confirm_text: "确定要导出选中的订单吗？",
        fields: []
      }
    ]
  end

  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">支付订单管理</h2>
          <p class="text-base-content/70 mt-1">查看和管理所有支付订单</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总订单数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">成功订单</div>
            <div class="stat-value text-success">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">待支付</div>
            <div class="stat-value text-warning">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">今日交易额</div>
            <div class="stat-value text-info">¥0</div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>操作说明：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>订单状态会自动通过支付网关回调更新</li>
          <li>可以手动刷新订单状态以获取最新信息</li>
          <li>支付链接仅在订单创建后短时间内有效</li>
          <li>回调数据包含支付网关返回的详细信息</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :form_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>注意事项：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>请谨慎修改订单状态，避免数据不一致</li>
          <li>订单金额修改可能影响财务对账</li>
          <li>建议通过支付网关查询确认订单真实状态</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(_assigns, _slot), do: nil
end
