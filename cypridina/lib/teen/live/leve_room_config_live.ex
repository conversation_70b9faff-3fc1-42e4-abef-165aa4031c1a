defmodule Teen.Live.LeveRoomConfigLive do
  @moduledoc """
  房间配置管理页面

  提供房间配置的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.GameManagement.LeveRoomConfig
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "房间配置"

  @impl Backpex.LiveResource
  def plural_name, do: "房间配置"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      game_config: %{
        module: Backpex.Fields.BelongsTo,
        label: "游戏配置",
        display_field: :display_name,
        live_resource: Teen.Live.GameManagement.ManageGameConfigLive,
        searchable: true,
        help_text: "关联的游戏配置"
      },
      game_id: %{
        module: Backpex.Fields.Number,
        label: "游戏ID",
        searchable: true,
        help_text: "游戏的唯一标识ID"
      },
      server_id: %{
        module: Backpex.Fields.Number,
        label: "服务器ID",
        searchable: true,
        help_text: "服务器的唯一标识ID"
      },
      server_ip: %{
        module: Backpex.Fields.Text,
        label: "服务器IP",
        searchable: true,
        help_text: "服务器的IP地址"
      },
      port: %{
        module: Backpex.Fields.Number,
        label: "端口",
        help_text: "服务器端口号"
      },
      order_id: %{
        module: Backpex.Fields.Number,
        label: "排序ID",
        help_text: "房间排序顺序"
      },
      # 隐藏最小下注字段 - 不在 fields 中定义即可隐藏
      # min_bet: %{
      #   module: Backpex.Fields.Number,
      #   label: "最小下注",
      #   help_text: "底分/最小下注金额"
      # },
      entry_fee: %{
        module: Backpex.Fields.Number,
        label: "入场费",
        help_text: "入场金币要求"
      },
      max_bet: %{
        module: Backpex.Fields.Number,
        label: "封顶金币",
        help_text: "封顶金币(0表示无限制)"
      },
      # 隐藏最大玩家数字段 - 不在 fields 中定义即可隐藏
      # max_players: %{
      #   module: Backpex.Fields.Number,
      #   label: "最大玩家数",
      #   help_text: "最大玩家数量"
      # },
      bundle_name: %{
        module: Backpex.Fields.Text,
        label: "游戏包名",
        searchable: true,
        help_text: "游戏包名，对应前端bundleName"
      },
      is_enabled: %{
        module: Backpex.Fields.Boolean,
        label: "启用状态",
        help_text: "是否启用该房间配置"
      },
      unified_config: %{
        module: Backpex.Fields.Textarea,
        label: "游戏配置",
        help_text: "统一配置JSON - 包含所有游戏配置（房间设置+玩法配置）",
        only: [:new, :edit, :show],
        render: fn assigns ->
          value =
            case assigns.value do
              nil -> ""
              map when is_map(map) -> Jason.encode!(map, pretty: true)
              str when is_binary(str) -> str
              _ -> ""
            end

          assigns = assign(assigns, :formatted_value, value)

          ~H"""
          <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto max-h-40"><%= @formatted_value %></pre>
          """
        end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      is_enabled: %{
        module: Teen.Filters.StatusSelect
      },
      game_id: %{
        module: Teen.Filters.GameIdFilter
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      enable_room: %{
        module: Teen.ResourceActions.EnableRoom,
        label: "启用房间",
        icon: "hero-check-circle",
        confirm_label: "确认启用",
        confirm_text: "确定要启用选中的房间吗？",
        fields: []
      },
      disable_room: %{
        module: Teen.ResourceActions.DisableRoom,
        label: "禁用房间",
        icon: "hero-x-circle",
        confirm_label: "确认禁用",
        confirm_text: "确定要禁用选中的房间吗？",
        fields: []
      }
    ]
  end

  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end
end
