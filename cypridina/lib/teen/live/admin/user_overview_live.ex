defmodule Teen.Live.Admin.UserOverviewLive do
  use <PERSON><PERSON><PERSON>inaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "用户概览")
     |> assign(:current_url, "/admin/user-overview")
     |> assign(:loading, false)
     |> assign(:search_query, "")
     |> assign(:selected_filter, "all")
     |> assign(:fluid?, true)
     |> load_user_data(),
     layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("search", %{"query" => query}, socket) do
    {:noreply,
     socket
     |> assign(:search_query, query)
     |> assign(:loading, true)
     |> load_user_data()}
  end

  @impl true
  def handle_event("filter_users", %{"filter" => filter}, socket) do
    {:noreply,
     socket
     |> assign(:selected_filter, filter)
     |> assign(:loading, true)
     |> load_user_data()}
  end

  defp load_user_data(socket) do
    # 模拟数据加载
    :timer.sleep(500) # 模拟网络延迟

    socket
    |> assign(:user_stats, get_user_stats())
    |> assign(:recent_users, get_recent_users())
    |> assign(:user_distribution, get_user_distribution())
    |> assign(:loading, false)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="admin-main-content p-4 lg:p-6 space-y-6">
      <!-- 页面头部 -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold text-base-content flex items-center gap-3">
            <.icon name="hero-users" class="size-8 text-primary" />
            用户管理概览
          </h1>
          <p class="text-base-content/60 mt-1">管理和监控用户活动</p>
        </div>

        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <!-- 搜索框 -->
          <div class="relative">
            <input
              type="text"
              placeholder="搜索用户..."
              class="input input-sm input-bordered w-full max-w-xs pl-10"
              phx-change="search"
              name="query"
              value={@search_query}
            />
            <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-base-content/60" />
          </div>

          <!-- 筛选器 -->
          <select
            class="select select-sm select-bordered"
            phx-change="filter_users"
            name="filter"
          >
            <option value="all" selected={@selected_filter == "all"}>全部用户</option>
            <option value="active" selected={@selected_filter == "active"}>活跃用户</option>
            <option value="new" selected={@selected_filter == "new"}>新用户</option>
            <option value="vip" selected={@selected_filter == "vip"}>VIP用户</option>
          </select>

          <!-- 操作按钮 -->
          <.link navigate="/admin/users" class="btn btn-sm btn-primary gap-2">
            <.icon name="hero-plus" class="size-4" />
            添加用户
          </.link>
        </div>
      </div>

      <!-- 用户统计卡片 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <div class="stat-card bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200">
          <div class="stat-figure text-blue-600">
            <.icon name="hero-user-group" class="size-8" />
          </div>
          <div class="stat-title text-blue-800">总用户数</div>
          <div class="stat-value text-blue-600 text-2xl lg:text-3xl">
            <%= format_number(@user_stats.total_users) %>
          </div>
          <div class="stat-desc text-blue-600/70">
            注册用户总数
          </div>
        </div>

        <div class="stat-card bg-gradient-to-br from-green-50 to-green-100 border border-green-200">
          <div class="stat-figure text-green-600">
            <.icon name="hero-signal" class="size-8" />
          </div>
          <div class="stat-title text-green-800">活跃用户</div>
          <div class="stat-value text-green-600 text-2xl lg:text-3xl">
            <%= format_number(@user_stats.active_users) %>
          </div>
          <div class="stat-desc text-green-600/70">
            本月活跃用户
          </div>
        </div>

        <div class="stat-card bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200">
          <div class="stat-figure text-yellow-600">
            <.icon name="hero-user-plus" class="size-8" />
          </div>
          <div class="stat-title text-yellow-800">新增用户</div>
          <div class="stat-value text-yellow-600 text-2xl lg:text-3xl">
            <%= format_number(@user_stats.new_users) %>
          </div>
          <div class="stat-desc text-yellow-600/70">
            本周新增
          </div>
        </div>

        <div class="stat-card bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200">
          <div class="stat-figure text-purple-600">
            <.icon name="hero-star" class="size-8" />
          </div>
          <div class="stat-title text-purple-800">VIP用户</div>
          <div class="stat-value text-purple-600 text-2xl lg:text-3xl">
            <%= format_number(@user_stats.vip_users) %>
          </div>
          <div class="stat-desc text-purple-600/70">
            VIP会员数量
          </div>
        </div>
      </div>

      <!-- 用户分布和最近用户 -->
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <!-- 用户分布 -->
        <div class="card bg-base-100 shadow-xl border border-base-300">
          <div class="card-body">
            <h2 class="card-title flex items-center gap-2">
              <.icon name="hero-chart-pie" class="size-5 text-info" />
              用户分布
            </h2>
            <div class="grid grid-cols-2 gap-4 mt-4">
              <%= for distribution <- @user_distribution do %>
                <div class="stat bg-base-200 rounded-lg p-4">
                  <div class="stat-title text-xs"><%= distribution.category %></div>
                  <div class="stat-value text-lg"><%= distribution.count %></div>
                  <div class="stat-desc text-xs">
                    <%= distribution.percentage %>%
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- 最近注册用户 -->
        <div class="card bg-base-100 shadow-xl border border-base-300">
          <div class="card-body">
            <h2 class="card-title flex items-center gap-2">
              <.icon name="hero-clock" class="size-5 text-success" />
              最近注册用户
            </h2>
            <div class="space-y-3 mt-4">
              <%= for user <- @recent_users do %>
                <div class="activity-item">
                  <div class="flex items-center gap-3">
                    <div class="avatar placeholder">
                      <div class="bg-primary text-primary-content rounded-full w-10 h-10">
                        <span class="text-xs font-bold">
                          <%= String.first(user.username) %>
                        </span>
                      </div>
                    </div>
                    <div>
                      <div class="font-medium text-sm"><%= user.username %></div>
                      <div class="text-xs text-base-content/60">
                        <%= Calendar.strftime(user.created_at, "%Y-%m-%d %H:%M") %>
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class={[
                      "badge badge-sm",
                      user.status == "active" && "badge-success",
                      user.status == "pending" && "badge-warning",
                      user.status == "inactive" && "badge-neutral"
                    ]}>
                      <%= user.status_text %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="card bg-base-100 shadow-xl border border-base-300">
        <div class="card-body">
          <h2 class="card-title flex items-center gap-2">
            <.icon name="hero-bolt" class="size-5 text-warning" />
            快速操作
          </h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <.link navigate="/admin/users" class="quick-action-btn btn-outline btn-primary">
              <.icon name="hero-eye" class="size-4" />
              查看所有用户
            </.link>
            <.link navigate="/admin/user-bans" class="quick-action-btn btn-outline btn-error">
              <.icon name="hero-no-symbol" class="size-4" />
              封禁管理
            </.link>
            <.link navigate="/admin/user-devices" class="quick-action-btn btn-outline btn-info">
              <.icon name="hero-device-phone-mobile" class="size-4" />
              设备管理
            </.link>
            <.link navigate="/admin/user-management" class="quick-action-btn btn-outline btn-secondary">
              <.icon name="hero-cog-6-tooth" class="size-4" />
              用户设置
            </.link>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 辅助函数
  defp get_user_stats do
    %{
      total_users: 15_234,
      active_users: 8_567,
      new_users: 234,
      vip_users: 1_234
    }
  end

  defp get_user_distribution do
    [
      %{category: "普通用户", count: 12_000, percentage: 78.8},
      %{category: "VIP用户", count: 1_234, percentage: 8.1},
      %{category: "新用户", count: 1_500, percentage: 9.8},
      %{category: "封禁用户", count: 500, percentage: 3.3}
    ]
  end

  defp get_recent_users do
    now = DateTime.now!("Asia/Shanghai")

    [
      %{
        username: "user123",
        created_at: DateTime.add(now, -1, :hour),
        status: "active",
        status_text: "活跃"
      },
      %{
        username: "player456",
        created_at: DateTime.add(now, -2, :hour),
        status: "pending",
        status_text: "待验证"
      },
      %{
        username: "gamer789",
        created_at: DateTime.add(now, -3, :hour),
        status: "active",
        status_text: "活跃"
      },
      %{
        username: "user101",
        created_at: DateTime.add(now, -4, :hour),
        status: "inactive",
        status_text: "未激活"
      },
      %{
        username: "vip202",
        created_at: DateTime.add(now, -5, :hour),
        status: "active",
        status_text: "活跃"
      }
    ]
  end

  defp format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end
end
