defmodule Teen.Live.Admin.AnalyticsLive do
  @moduledoc """
  数据分析页面

  提供详细的数据分析和报表功能，包括：
  - 用户行为分析
  - 游戏数据分析
  - 收入分析
  - 趋势分析
  """

  use CypridinaWeb, :live_view
  require Logger

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 设置定时器，每60秒更新一次数据
      :timer.send_interval(60_000, self(), :update_analytics)
    end

    {:ok,
     socket
     |> assign(:page_title, "数据分析")
     |> assign(:current_url, "/admin/analytics")
     |> assign(:fluid?, true)
     |> assign(:loading, true)
     |> assign(:selected_period, "week")
     |> assign(:selected_metric, "revenue")
     |> assign(:date_range, get_default_date_range())
     |> load_analytics_data(),
     layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_info(:update_analytics, socket) do
    {:noreply, load_analytics_data(socket)}
  end

  @impl true
  def handle_event("change_period", %{"period" => period}, socket) do
    {:noreply,
     socket
     |> assign(:selected_period, period)
     |> assign(:loading, true)
     |> load_analytics_data()}
  end

  @impl true
  def handle_event("change_metric", %{"metric" => metric}, socket) do
    {:noreply,
     socket
     |> assign(:selected_metric, metric)
     |> assign(:loading, true)
     |> load_analytics_data()}
  end

  @impl true
  def handle_event("refresh_data", _params, socket) do
    {:noreply,
     socket
     |> assign(:loading, true)
     |> load_analytics_data()}
  end

  @impl true
  def handle_event("export_data", %{"format" => format}, socket) do
    # 导出数据功能
    Logger.info("导出数据格式: #{format}")
    {:noreply, put_flash(socket, :info, "数据导出功能开发中...")}
  end

  defp load_analytics_data(socket) do
    period = socket.assigns[:selected_period] || "week"
    metric = socket.assigns[:selected_metric] || "revenue"

    socket
    |> assign(:analytics_data, get_analytics_data(period, metric))
    |> assign(:user_metrics, get_user_metrics(period))
    |> assign(:game_metrics, get_game_metrics(period))
    |> assign(:revenue_metrics, get_revenue_metrics(period))
    |> assign(:trend_data, get_trend_data(period, metric))
    |> assign(:top_performers, get_top_performers(period))
    |> assign(:loading, false)
  end

  # 获取分析数据
  defp get_analytics_data(period, metric) do
    # 这里应该从数据库获取真实数据
    base_data = %{
      total_revenue: get_revenue_by_period(period),
      total_users: get_users_by_period(period),
      total_games: get_games_by_period(period),
      conversion_rate: get_conversion_rate(period),
      retention_rate: get_retention_rate(period),
      avg_session_duration: get_avg_session_duration(period)
    }

    case metric do
      "revenue" -> Map.put(base_data, :primary_value, base_data.total_revenue)
      "users" -> Map.put(base_data, :primary_value, base_data.total_users)
      "games" -> Map.put(base_data, :primary_value, base_data.total_games)
      _ -> Map.put(base_data, :primary_value, base_data.total_revenue)
    end
  end

  defp get_user_metrics(period) do
    %{
      new_users: get_new_users_count(period),
      active_users: get_active_users_count(period),
      returning_users: get_returning_users_count(period),
      user_growth_rate: calculate_user_growth_rate(period),
      avg_user_value: calculate_avg_user_value(period)
    }
  end

  defp get_game_metrics(period) do
    %{
      total_sessions: get_total_sessions(period),
      avg_session_length: get_avg_session_length(period),
      popular_games: get_popular_games(period),
      game_revenue: get_game_revenue_breakdown(period)
    }
  end

  defp get_revenue_metrics(period) do
    %{
      gross_revenue: get_gross_revenue(period),
      net_revenue: get_net_revenue(period),
      revenue_per_user: get_revenue_per_user(period),
      payment_methods: get_payment_methods_breakdown(period)
    }
  end

  defp get_trend_data(period, metric) do
    # 生成趋势数据点
    case period do
      "today" -> generate_hourly_trend(metric)
      "week" -> generate_daily_trend(metric, 7)
      "month" -> generate_daily_trend(metric, 30)
      "year" -> generate_monthly_trend(metric, 12)
    end
  end

  defp get_top_performers(period) do
    %{
      top_users: get_top_users_by_revenue(period),
      top_games: get_top_games_by_revenue(period),
      top_channels: get_top_channels_by_users(period)
    }
  end

  # 辅助函数 - 模拟数据
  defp get_revenue_by_period("today"), do: 45_678
  defp get_revenue_by_period("week"), do: 298_456
  defp get_revenue_by_period("month"), do: 1_234_567
  defp get_revenue_by_period("year"), do: 12_345_678

  defp get_users_by_period("today"), do: 234
  defp get_users_by_period("week"), do: 1_456
  defp get_users_by_period("month"), do: 5_678
  defp get_users_by_period("year"), do: 45_678

  defp get_games_by_period("today"), do: 1_234
  defp get_games_by_period("week"), do: 8_765
  defp get_games_by_period("month"), do: 34_567
  defp get_games_by_period("year"), do: 234_567

  defp get_conversion_rate(_period), do: 12.5
  defp get_retention_rate(_period), do: 68.3
  defp get_avg_session_duration(_period), do: 25.4

  defp get_new_users_count(_period), do: 156
  defp get_active_users_count(_period), do: 1_234
  defp get_returning_users_count(_period), do: 890

  defp calculate_user_growth_rate(_period), do: 15.6
  defp calculate_avg_user_value(_period), do: 234.56

  defp get_total_sessions(_period), do: 5_678
  defp get_avg_session_length(_period), do: 18.5

  defp get_popular_games(_period) do
    [
      %{name: "Teen Patti", sessions: 2_345, revenue: 123_456},
      %{name: "Slot 777", sessions: 1_234, revenue: 89_012},
      %{name: "Jhandi Munda", sessions: 987, revenue: 56_789}
    ]
  end

  defp get_game_revenue_breakdown(_period) do
    %{
      "Teen Patti" => 45.6,
      "Slot 777" => 32.1,
      "Jhandi Munda" => 22.3
    }
  end

  defp get_gross_revenue(_period), do: 456_789
  defp get_net_revenue(_period), do: 398_234
  defp get_revenue_per_user(_period), do: 189.45

  defp get_payment_methods_breakdown(_period) do
    %{
      "银行卡" => 45.2,
      "支付宝" => 32.8,
      "微信支付" => 22.0
    }
  end

  defp generate_hourly_trend(_metric) do
    0..23
    |> Enum.map(fn hour ->
      %{
        time: "#{hour}:00",
        value: :rand.uniform(1000) + 500
      }
    end)
  end

  defp generate_daily_trend(_metric, days) do
    1..days
    |> Enum.map(fn day ->
      date = Date.add(Date.utc_today(), -day)
      %{
        time: Date.to_string(date),
        value: :rand.uniform(10000) + 5000
      }
    end)
    |> Enum.reverse()
  end

  defp generate_monthly_trend(_metric, months) do
    1..months
    |> Enum.map(fn month ->
      date = Date.add(Date.utc_today(), -month * 30)
      %{
        time: "#{date.year}-#{String.pad_leading(to_string(date.month), 2, "0")}",
        value: :rand.uniform(100000) + 50000
      }
    end)
    |> Enum.reverse()
  end

  defp get_top_users_by_revenue(_period) do
    [
      %{name: "用户001", revenue: 12_345, games: 234},
      %{name: "用户002", revenue: 9_876, games: 189},
      %{name: "用户003", revenue: 7_654, games: 156}
    ]
  end

  defp get_top_games_by_revenue(_period) do
    [
      %{name: "Teen Patti", revenue: 123_456, players: 2_345},
      %{name: "Slot 777", revenue: 89_012, players: 1_234},
      %{name: "Jhandi Munda", revenue: 56_789, players: 987}
    ]
  end

  defp get_top_channels_by_users(_period) do
    [
      %{name: "官方渠道", users: 1_234, conversion: 15.6},
      %{name: "推广渠道A", users: 987, conversion: 12.3},
      %{name: "推广渠道B", users: 654, conversion: 18.9}
    ]
  end

  defp get_default_date_range do
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -7)
    %{start_date: start_date, end_date: end_date}
  end

  # 格式化辅助函数
  defp format_currency(amount) when is_integer(amount) do
    amount
    |> Integer.to_string()
    |> String.replace(~r/\B(?=(\d{3})+(?!\d))/, ",")
  end

  defp format_currency(amount) when is_float(amount) do
    amount
    |> :erlang.float_to_binary(decimals: 0)
    |> String.replace(~r/\B(?=(\d{3})+(?!\d))/, ",")
  end

  defp format_percentage(value) when is_integer(value) do
    "#{value}.0%"
  end

  defp format_percentage(value) when is_float(value) do
    "#{:erlang.float_to_binary(value, decimals: 1)}%"
  end

  defp format_number(number) when is_number(number) do
    number
    |> to_string()
    |> String.replace(~r/\B(?=(\d{3})+(?!\d))/, ",")
  end

  defp format_decimal(value, decimals \\ 2)

  defp format_decimal(value, decimals) when is_integer(value) do
    value
    |> Integer.to_string()
    |> Kernel.<>(".#{String.duplicate("0", decimals)}")
  end

  defp format_decimal(value, decimals) when is_float(value) do
    :erlang.float_to_binary(value, decimals: decimals)
  end

  defp format_decimal(value, decimals) when is_number(value) do
    value
    |> to_string()
    |> case do
      str when is_binary(str) ->
        if String.contains?(str, ".") do
          str
        else
          str <> ".#{String.duplicate("0", decimals)}"
        end
    end
  end

  defp period_label("today"), do: "今日"
  defp period_label("week"), do: "本周"
  defp period_label("month"), do: "本月"
  defp period_label("year"), do: "本年"

  defp metric_label("revenue"), do: "收入"
  defp metric_label("users"), do: "用户"
  defp metric_label("games"), do: "游戏"
end
