<div class="p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">数据分析</h1>
    <p class="text-gray-600">深入分析业务数据，洞察运营趋势</p>
  </div>

  <!-- 控制面板 -->
  <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <div class="flex flex-wrap items-center justify-between gap-4">
      <!-- 时间段选择 -->
      <div class="flex items-center space-x-4">
        <label class="text-sm font-medium text-gray-700">时间段:</label>
        <div class="flex space-x-2">
          <%= for period <- ["today", "week", "month", "year"] do %>
            <button
              phx-click="change_period"
              phx-value-period={period}
              class={[
                "px-3 py-1 text-sm rounded-md transition-colors",
                if(@selected_period == period,
                  do: "bg-blue-600 text-white",
                  else: "bg-gray-100 text-gray-700 hover:bg-gray-200"
                )
              ]}
            >
              <%= period_label(period) %>
            </button>
          <% end %>
        </div>
      </div>

      <!-- 指标选择 -->
      <div class="flex items-center space-x-4">
        <label class="text-sm font-medium text-gray-700">主要指标:</label>
        <select
          phx-change="change_metric"
          name="metric"
          class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="revenue" selected={@selected_metric == "revenue"}>收入分析</option>
          <option value="users" selected={@selected_metric == "users"}>用户分析</option>
          <option value="games" selected={@selected_metric == "games"}>游戏分析</option>
        </select>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center space-x-2">
        <button
          phx-click="refresh_data"
          class="btn btn-sm btn-outline btn-primary gap-2"
          disabled={@loading}
        >
          <.icon name="hero-arrow-path" class={["size-4", if(@loading, do: "animate-spin")]} />
          刷新
        </button>
        
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-sm btn-outline btn-secondary gap-2">
            <.icon name="hero-arrow-down-tray" class="size-4" />
            导出
          </div>
          <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow border border-gray-200">
            <li><button phx-click="export_data" phx-value-format="excel" class="text-sm">Excel</button></li>
            <li><button phx-click="export_data" phx-value-format="csv" class="text-sm">CSV</button></li>
            <li><button phx-click="export_data" phx-value-format="pdf" class="text-sm">PDF</button></li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <%= if @loading do %>
    <div class="flex items-center justify-center py-12">
      <div class="loading loading-spinner loading-lg text-primary"></div>
      <span class="ml-3 text-gray-600">加载数据中...</span>
    </div>
  <% else %>
    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- 主要指标 -->
      <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-blue-100 text-sm">主要指标</p>
            <p class="text-2xl font-bold">
              <%= case @selected_metric do %>
                <% "revenue" -> %>¥<%= format_currency(@analytics_data.primary_value) %>
                <% "users" -> %><%= format_number(@analytics_data.primary_value) %>
                <% "games" -> %><%= format_number(@analytics_data.primary_value) %>
              <% end %>
            </p>
            <p class="text-blue-100 text-sm"><%= metric_label(@selected_metric) %></p>
          </div>
          <.icon name="hero-chart-bar" class="size-8 text-blue-200" />
        </div>
      </div>

      <!-- 转化率 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">转化率</p>
            <p class="text-2xl font-bold text-gray-900"><%= format_percentage(@analytics_data.conversion_rate) %></p>
          </div>
          <.icon name="hero-arrow-trending-up" class="size-8 text-green-500" />
        </div>
      </div>

      <!-- 留存率 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">用户留存率</p>
            <p class="text-2xl font-bold text-gray-900"><%= format_percentage(@analytics_data.retention_rate) %></p>
          </div>
          <.icon name="hero-users" class="size-8 text-purple-500" />
        </div>
      </div>

      <!-- 平均会话时长 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">平均会话时长</p>
            <p class="text-2xl font-bold text-gray-900"><%= :erlang.float_to_binary(@analytics_data.avg_session_duration, decimals: 1) %>分钟</p>
          </div>
          <.icon name="hero-clock" class="size-8 text-orange-500" />
        </div>
      </div>
    </div>

    <!-- 图表和详细分析 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-6">
      <!-- 趋势图表 -->
      <div class="xl:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <.icon name="hero-chart-bar" class="size-5 text-blue-600" />
            <%= metric_label(@selected_metric) %>趋势
          </h3>
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
              <.icon name="hero-chart-bar" class="size-12 text-gray-400 mx-auto mb-2" />
              <p class="text-gray-500">图表组件待集成</p>
              <p class="text-sm text-gray-400 mt-1">数据点: <%= length(@trend_data) %> 个</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户指标 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <.icon name="hero-users" class="size-5 text-green-600" />
          用户指标
        </h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">新增用户</span>
            <span class="font-semibold text-gray-900"><%= @user_metrics.new_users %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">活跃用户</span>
            <span class="font-semibold text-gray-900"><%= format_number(@user_metrics.active_users) %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">回访用户</span>
            <span class="font-semibold text-gray-900"><%= @user_metrics.returning_users %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">用户增长率</span>
            <span class="font-semibold text-green-600">+<%= format_percentage(@user_metrics.user_growth_rate) %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">平均用户价值</span>
            <span class="font-semibold text-gray-900">¥<%= :erlang.float_to_binary(@user_metrics.avg_user_value, decimals: 2) %></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 游戏和收入分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 热门游戏 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <.icon name="hero-puzzle-piece" class="size-5 text-purple-600" />
          热门游戏
        </h3>
        <div class="space-y-3">
          <%= for game <- @game_metrics.popular_games do %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p class="font-medium text-gray-900"><%= game.name %></p>
                <p class="text-sm text-gray-500"><%= game.sessions %> 场游戏</p>
              </div>
              <div class="text-right">
                <p class="font-semibold text-gray-900">¥<%= format_currency(game.revenue) %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 收入分析 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <.icon name="hero-banknotes" class="size-5 text-green-600" />
          收入分析
        </h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">总收入</span>
            <span class="font-semibold text-gray-900">¥<%= format_currency(@revenue_metrics.gross_revenue) %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">净收入</span>
            <span class="font-semibold text-gray-900">¥<%= format_currency(@revenue_metrics.net_revenue) %></span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">人均收入</span>
            <span class="font-semibold text-gray-900">¥<%= :erlang.float_to_binary(@revenue_metrics.revenue_per_user, decimals: 2) %></span>
          </div>
          
          <div class="mt-4">
            <p class="text-sm font-medium text-gray-700 mb-2">支付方式分布</p>
            <%= for {method, percentage} <- @revenue_metrics.payment_methods do %>
              <div class="flex justify-between items-center mb-1">
                <span class="text-sm text-gray-600"><%= method %></span>
                <span class="text-sm font-medium text-gray-900"><%= format_percentage(percentage) %></span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- 排行榜 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 顶级用户 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <.icon name="hero-trophy" class="size-5 text-yellow-600" />
          顶级用户
        </h3>
        <div class="space-y-3">
          <%= for {user, index} <- Enum.with_index(@top_performers.top_users, 1) do %>
            <div class="flex items-center gap-3">
              <div class={[
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white",
                case index do
                  1 -> "bg-yellow-500"
                  2 -> "bg-gray-400"
                  3 -> "bg-orange-600"
                  _ -> "bg-gray-300"
                end
              ]}>
                <%= index %>
              </div>
              <div class="flex-1">
                <p class="font-medium text-gray-900"><%= user.name %></p>
                <p class="text-sm text-gray-500"><%= user.games %> 场游戏</p>
              </div>
              <div class="text-right">
                <p class="font-semibold text-gray-900">¥<%= format_currency(user.revenue) %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 顶级游戏 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <.icon name="hero-star" class="size-5 text-blue-600" />
          顶级游戏
        </h3>
        <div class="space-y-3">
          <%= for {game, index} <- Enum.with_index(@top_performers.top_games, 1) do %>
            <div class="flex items-center gap-3">
              <div class={[
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white",
                case index do
                  1 -> "bg-yellow-500"
                  2 -> "bg-gray-400"
                  3 -> "bg-orange-600"
                  _ -> "bg-gray-300"
                end
              ]}>
                <%= index %>
              </div>
              <div class="flex-1">
                <p class="font-medium text-gray-900"><%= game.name %></p>
                <p class="text-sm text-gray-500"><%= game.players %> 玩家</p>
              </div>
              <div class="text-right">
                <p class="font-semibold text-gray-900">¥<%= format_currency(game.revenue) %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 顶级渠道 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <.icon name="hero-megaphone" class="size-5 text-green-600" />
          顶级渠道
        </h3>
        <div class="space-y-3">
          <%= for {channel, index} <- Enum.with_index(@top_performers.top_channels, 1) do %>
            <div class="flex items-center gap-3">
              <div class={[
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white",
                case index do
                  1 -> "bg-yellow-500"
                  2 -> "bg-gray-400"
                  3 -> "bg-orange-600"
                  _ -> "bg-gray-300"
                end
              ]}>
                <%= index %>
              </div>
              <div class="flex-1">
                <p class="font-medium text-gray-900"><%= channel.name %></p>
                <p class="text-sm text-gray-500">转化率 <%= format_percentage(channel.conversion) %></p>
              </div>
              <div class="text-right">
                <p class="font-semibold text-gray-900"><%= format_number(channel.users) %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
</div>
