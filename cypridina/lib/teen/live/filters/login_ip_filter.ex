defmodule Teen.Live.Filters.LoginIpFilter do
  @moduledoc """
  登录IP过滤器

  用于在设备管理页面中按登录IP过滤设备
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "登录IP"

  @impl Backpex.Filter
  def type, do: :text

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render(assigns) do
    ~H"""
    <div>
      <.input field={@form[@field]} type="text" placeholder="输入IP地址" />
    </div>
    """
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="text" placeholder="输入IP地址" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource, _socket) do
    import Ash.Query

    query
    |> filter(contains(last_login_ip, ^value))
  end
end
