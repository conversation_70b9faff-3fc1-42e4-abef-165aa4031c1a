defmodule Teen.Live.Filters.UsernameFilter do
  @moduledoc """
  用户名过滤器

  用于在用户管理页面中按用户名过滤用户
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "用户名"

  @impl Backpex.Filter
  def type, do: :text

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render(assigns) do
    ~H"""
    <div>
      <.input field={@form[@field]} type="text" placeholder="输入用户名" />
    </div>
    """
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="text" placeholder="输入用户名" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource, _socket) do
    import Ash.Query

    query
    |> filter(contains(username, ^value))
  end
end
