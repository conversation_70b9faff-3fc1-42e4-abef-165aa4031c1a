defmodule Teen.Filters.AuditStatusSelect do
  @moduledoc """
  审核状态选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "审核状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择审核状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待审核", "0"},
      {"审核通过", "1"},
      {"审核拒绝", "2"}
    ]
  end
end
