defmodule Teen.Live.Filters.ChannelStatusFilter do
  @moduledoc """
  渠道状态过滤器

  用于在渠道管理页面中按状态过滤渠道
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"全部", nil},
      {"启用", 1},
      {"禁用", 0}
    ]
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="select" options={options()} prompt="选择状态" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource) do
    import Ash.Query

    query
    |> filter(status == ^value)
  end
end
