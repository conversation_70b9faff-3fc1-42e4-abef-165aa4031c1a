defmodule Teen.Live.Filters.UserAgentLevelFilter do
  @moduledoc """
  用户代理等级过滤器

  用于在用户管理页面中按代理等级过滤用户
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "代理等级"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"全部", nil},
      {"非代理", -1},
      {"根代理", 0},
      {"一级代理", 1},
      {"二级代理", 2},
      {"三级代理", 3}
    ]
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="select" options={options()} prompt="选择代理等级" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource) do
    import Ash.Query

    query
    |> filter(agent_level == ^value)
  end
end
