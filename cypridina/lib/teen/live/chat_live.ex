defmodule Teen.Live.ChatLive do
  @moduledoc """
  通用聊天系统LiveView

  支持用户间的一对一聊天和群聊功能，包括：
  - 聊天会话管理
  - 实时消息发送和接收
  - 用户搜索和添加
  - 在线状态显示
  - 消息已读状态
  """

  use Phoenix.LiveView, layout: {CypridinaWeb.Layouts, :root}
  use Phoenix.Component
  use CypridinaWeb.Components.MishkaComponents
  require Logger
  require Ash.Query
  import Ash.Expr

  import CypridinaWeb.CoreComponents

  alias Cypridina.Chat.{ChatService, ChatParticipant, MessageReadReceipt}
  alias Cypridina.Accounts.User
  alias Cypridina.Utils.TimeHelper
  alias Teen.UserPresence

  @impl Phoenix.LiveView
  def mount(_params, session, socket) do
    # 获取当前用户 - 从socket.assigns中获取，这是由认证系统设置的
    current_user = socket.assigns[:current_user]
    Logger.info("🔍 [CHAT] 当前用户: #{inspect(socket)}")

    if connected?(socket) do
      # 订阅聊天更新
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "chat_updates")
      # 订阅用户在线状态变化
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "user_presence")
    end

    socket =
      socket
      |> assign(:current_user, current_user)
      |> assign(:current_session, nil)
      |> assign(:current_session_id, nil)
      |> assign(:sessions, [])
      |> assign(:new_message, "")
      |> assign(:user_search_query, "")
      |> assign(:search_mode, false)
      |> assign(:available_users, [])
      |> assign(:search_results, [])
      |> assign(:sending_message, false)
      |> assign(:unread_counts, %{})
      |> assign(:messages_loaded, false)
      |> assign(:loading_more_messages, false)
      |> assign(:has_more_messages, true)
      |> stream(:messages, [])
      |> load_user_sessions()
      |> load_available_users()

    {:ok, socket}
  end

  @impl Phoenix.LiveView
  def handle_params(%{"session_id" => session_id}, _uri, socket) do
    # 如果之前订阅了其他会话，先取消订阅
    if socket.assigns[:current_session_id] && connected?(socket) do
      Phoenix.PubSub.unsubscribe(
        Cypridina.PubSub,
        "chat_session:#{socket.assigns.current_session_id}"
      )
    end

    # 订阅新会话的消息更新
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "chat_session:#{session_id}")
      Logger.info("💬 [CHAT] 订阅会话消息 - 会话: #{session_id}")
    end

    socket =
      socket
      |> assign(:current_session_id, session_id)
      |> load_session_messages(session_id)
      |> mark_session_as_read(session_id)

    {:noreply, socket}
  end

  def handle_params(_params, _uri, socket) do
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def render(assigns) do
    ~H"""
    <div class="h-screen flex bg-white overflow-hidden">
      <!-- 左侧会话列表 - Telegram风格，常驻显示 -->
      <div class="bg-white border-r border-gray-200 flex flex-col w-96 flex-shrink-0 h-full">
        <!-- 头部 - Telegram风格 -->
        <div class="bg-blue-600 text-white flex-shrink-0">
          <div class="p-4">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-semibold">聊天</h2>
                <div class="text-sm text-blue-200">
                  <%= if @current_user do %>
                    {@current_user.profile.nickname || "用户"}
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
        
    <!-- 用户搜索框 -->
        <div class="p-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
          <div class="relative">
            <input
              id="search-input"
              type="text"
              placeholder="搜索用户开始聊天..."
              value={@user_search_query || ""}
              phx-keyup="search_users"
              phx-hook="SearchFocus"
              phx-debounce="300"
              class="w-full pl-10 pr-10 py-2 bg-white border border-gray-300 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <!-- 搜索图标 -->
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <!-- 清空按钮 -->
            <%= if @user_search_query && String.trim(@user_search_query) != "" do %>
              <button
                type="button"
                phx-click="clear_search"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                title="清空搜索"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            <% end %>
          </div>
        </div>
        
    <!-- 聊天会话列表或搜索结果 -->
        <div class="flex-1 overflow-y-auto">
          <%= if @search_mode || (@user_search_query && String.trim(@user_search_query) != "") do %>
            <!-- 搜索模式：隐藏会话列表，显示搜索界面 -->
            <div class="p-3 bg-blue-50 border-b border-blue-200">
              <p class="text-sm text-blue-600">
                <%= if @user_search_query && String.trim(@user_search_query) != "" do %>
                  🔍 搜索 "{@user_search_query}" 的结果 ({length(@search_results)} 个)
                <% else %>
                  🔍 搜索用户和会话
                <% end %>
              </p>
            </div>
            
    <!-- 搜索结果显示 -->
            <%= if @user_search_query && String.trim(@user_search_query) != "" do %>
              <!-- 有搜索查询时显示搜索结果 -->
              <%= if length(@search_results) > 0 do %>
                <%= for result <- @search_results do %>
                  <%= case result.type do %>
                    <% :user -> %>
                      <div
                        class="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors"
                        phx-click="start_chat_with_user"
                        phx-value-user_id={result.data.id}
                      >
                        <!-- 用户头像 -->
                        <div class="relative mr-3 flex-shrink-0">
                          <%= if result.data.profile && result.data.profile.avatar_url && result.data.profile.avatar_url != "" do %>
                            <img
                              src={result.data.profile.avatar_url}
                              alt="头像"
                              class="w-12 h-12 rounded-full object-cover"
                            />
                          <% else %>
                            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                              <%= if result.data.profile && result.data.profile.nickname do %>
                                {String.slice(result.data.profile.nickname, 0, 2)}
                              <% else %>
                                用户
                              <% end %>
                            </div>
                          <% end %>
                          <!-- 在线状态 -->
                          <%= if is_user_online?(result.data.id) do %>
                            <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 border-2 border-white rounded-full">
                            </div>
                          <% else %>
                            <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-gray-400 border-2 border-white rounded-full">
                            </div>
                          <% end %>
                        </div>
                        
    <!-- 用户信息 -->
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-gray-900 truncate">
                              <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full mr-2">
                                用户
                              </span>
                              {(result.data.profile && result.data.profile.nickname) ||
                                "用户#{result.data.id}"}
                            </h4>
                            <div class="flex items-center gap-2">
                              <%= if is_user_online?(result.data.id) do %>
                                <span class="text-xs text-green-600">在线</span>
                              <% else %>
                                <span class="text-xs text-gray-500">离线</span>
                              <% end %>
                            </div>
                          </div>
                          <p class="text-sm text-gray-600 truncate">
                            点击开始聊天
                          </p>
                        </div>
                      </div>
                    <% :session -> %>
                      <!-- 会话搜索结果 -->
                      <div
                        class={[
                          "flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors",
                          @current_session_id == result.data.id &&
                            "bg-blue-50 border-l-4 border-l-blue-500"
                        ]}
                        phx-click="select_session"
                        phx-value-session_id={result.data.id}
                      >
                        <!-- 会话头像 -->
                        <div class="relative mr-3 flex-shrink-0">
                          <%= if result.data.session_type == :private do %>
                            <!-- 私聊头像 -->
                            <% avatar_url = get_session_avatar_url(result.data, @current_user.id) %>
                            <%= if avatar_url && avatar_url != "" do %>
                              <img
                                src={avatar_url}
                                alt="头像"
                                class="w-12 h-12 rounded-full object-cover"
                              />
                            <% else %>
                              <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                                {get_session_avatar(result.data, @current_user.id)}
                              </div>
                            <% end %>
                          <% else %>
                            <!-- 群聊头像 -->
                            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                              {get_session_avatar(result.data, @current_user.id)}
                            </div>
                          <% end %>
                          
    <!-- 在线状态（仅私聊显示） -->
                          <%= if result.data.session_type == :private do %>
                            <%= if is_other_user_online?(result.data, @current_user.id) do %>
                              <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 border-2 border-white rounded-full">
                              </div>
                            <% else %>
                              <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-gray-400 border-2 border-white rounded-full">
                              </div>
                            <% end %>
                          <% end %>
                        </div>
                        
    <!-- 会话信息 -->
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-gray-900 truncate">
                              <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full mr-2">
                                会话
                              </span>
                              {get_session_title(result.data, @current_user.id)}
                            </h4>
                            <div class="flex items-center gap-2">
                              <span class="text-xs text-gray-500">
                                {TimeHelper.format_telegram_time(
                                  result.data.last_message_at || result.data.inserted_at
                                )}
                              </span>
                              <%= if Map.get(@unread_counts, result.data.id, 0) > 0 do %>
                                <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                  <span class="text-xs text-white font-bold">
                                    {Map.get(@unread_counts, result.data.id)}
                                  </span>
                                </div>
                              <% end %>
                            </div>
                          </div>
                          <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-600 truncate">
                              {get_last_message_preview(result.data)}
                            </p>
                          </div>
                        </div>
                      </div>
                  <% end %>
                <% end %>
              <% else %>
                <div class="flex flex-col items-center justify-center h-64 text-gray-400">
                  <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <h3 class="text-lg font-medium text-gray-600 mb-2">没有找到匹配的结果</h3>
                  <p class="text-sm text-gray-500 text-center">
                    没有找到包含 "{@user_search_query}" 的用户或会话<br /> 尝试使用不同的关键词搜索
                  </p>
                </div>
              <% end %>
            <% else %>
              <!-- 搜索模式但无查询时的提示 -->
              <%= if length(@search_results) > 0 do %>
                <%= for result <- @search_results do %>
                  <%= case result.type do %>
                    <% :user -> %>
                      <div
                        class="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors"
                        phx-click="start_chat_with_user"
                        phx-value-user_id={result.data.id}
                      >
                        <!-- 用户头像 -->
                        <div class="relative mr-3 flex-shrink-0">
                          <%= if result.data.profile && result.data.profile.avatar_url && result.data.profile.avatar_url != "" do %>
                            <img
                              src={result.data.profile.avatar_url}
                              alt="头像"
                              class="w-12 h-12 rounded-full object-cover"
                            />
                          <% else %>
                            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                              <%= if result.data.profile && result.data.profile.nickname do %>
                                {String.slice(result.data.profile.nickname, 0, 2)}
                              <% else %>
                                用户
                              <% end %>
                            </div>
                          <% end %>
                          <!-- 在线状态 -->
                          <%= if is_user_online?(result.data.id) do %>
                            <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 border-2 border-white rounded-full">
                            </div>
                          <% else %>
                            <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-gray-400 border-2 border-white rounded-full">
                            </div>
                          <% end %>
                        </div>
                        
    <!-- 用户信息 -->
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-gray-900 truncate">
                              <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full mr-2">
                                用户
                              </span>
                              {(result.data.profile && result.data.profile.nickname) ||
                                "用户#{result.data.id}"}
                            </h4>
                            <div class="flex items-center gap-2">
                              <%= if is_user_online?(result.data.id) do %>
                                <span class="text-xs text-green-600">在线</span>
                              <% else %>
                                <span class="text-xs text-gray-500">离线</span>
                              <% end %>
                            </div>
                          </div>
                          <p class="text-sm text-gray-600 truncate">
                            点击开始聊天
                          </p>
                        </div>
                      </div>
                    <% :session -> %>
                      <!-- 会话搜索结果 -->
                      <div
                        class={[
                          "flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors",
                          @current_session_id == result.data.id &&
                            "bg-blue-50 border-l-4 border-l-blue-500"
                        ]}
                        phx-click="select_session"
                        phx-value-session_id={result.data.id}
                      >
                        <!-- 会话头像 -->
                        <div class="relative mr-3 flex-shrink-0">
                          <%= if result.data.session_type == :private do %>
                            <!-- 私聊头像 -->
                            <% avatar_url = get_session_avatar_url(result.data, @current_user.id) %>
                            <%= if avatar_url && avatar_url != "" do %>
                              <img
                                src={avatar_url}
                                alt="头像"
                                class="w-12 h-12 rounded-full object-cover"
                              />
                            <% else %>
                              <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                                {get_session_avatar(result.data, @current_user.id)}
                              </div>
                            <% end %>
                          <% else %>
                            <!-- 群聊头像 -->
                            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                              {get_session_avatar(result.data, @current_user.id)}
                            </div>
                          <% end %>
                          
    <!-- 在线状态（仅私聊显示） -->
                          <%= if result.data.session_type == :private do %>
                            <%= if is_other_user_online?(result.data, @current_user.id) do %>
                              <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 border-2 border-white rounded-full">
                              </div>
                            <% else %>
                              <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-gray-400 border-2 border-white rounded-full">
                              </div>
                            <% end %>
                          <% end %>
                        </div>
                        
    <!-- 会话信息 -->
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-gray-900 truncate">
                              <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full mr-2">
                                会话
                              </span>
                              {get_session_title(result.data, @current_user.id)}
                            </h4>
                            <div class="flex items-center gap-2">
                              <span class="text-xs text-gray-500">
                                {TimeHelper.format_telegram_time(
                                  result.data.last_message_at || result.data.inserted_at
                                )}
                              </span>
                              <%= if Map.get(@unread_counts, result.data.id, 0) > 0 do %>
                                <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                  <span class="text-xs text-white font-bold">
                                    {Map.get(@unread_counts, result.data.id)}
                                  </span>
                                </div>
                              <% end %>
                            </div>
                          </div>
                          <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-600 truncate">
                              {get_last_message_preview(result.data)}
                            </p>
                          </div>
                        </div>
                      </div>
                  <% end %>
                <% end %>
              <% else %>
                <div class="flex flex-col items-center justify-center h-64 text-gray-400">
                  <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <h3 class="text-lg font-medium text-gray-600 mb-2">开始搜索</h3>
                  <p class="text-sm text-gray-500 text-center">
                    输入用户名或关键词<br /> 搜索用户和会话
                  </p>
                </div>
              <% end %>
            <% end %>
          <% else %>
            <!-- 聊天会话列表 -->
            <%= if length(@sessions) > 0 do %>
              <%= for session <- @sessions do %>
                <div
                  class={[
                    "flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 transition-colors",
                    @current_session_id == session.id && "bg-blue-50 border-l-4 border-l-blue-500"
                  ]}
                  phx-click="select_session"
                  phx-value-session_id={session.id}
                >
                  <!-- 会话头像 -->
                  <div class="relative mr-3 flex-shrink-0">
                    <%= if session.session_type == :private do %>
                      <!-- 私聊头像 -->
                      <% avatar_url = get_session_avatar_url(session, @current_user.id) %>
                      <%= if avatar_url && avatar_url != "" do %>
                        <img
                          src={avatar_url}
                          alt="头像"
                          class="w-12 h-12 rounded-full object-cover"
                        />
                      <% else %>
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                          {get_session_avatar(session, @current_user.id)}
                        </div>
                      <% end %>
                    <% else %>
                      <!-- 群聊头像 -->
                      <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                        {get_session_avatar(session, @current_user.id)}
                      </div>
                    <% end %>
                    
    <!-- 在线状态（仅私聊显示） -->
                    <%= if session.session_type == :private do %>
                      <%= if is_other_user_online?(session, @current_user.id) do %>
                        <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 border-2 border-white rounded-full">
                        </div>
                      <% else %>
                        <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-gray-400 border-2 border-white rounded-full">
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                  
    <!-- 会话信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                      <h4 class="font-semibold text-gray-900 truncate">
                        {get_session_title(session, @current_user.id)}
                      </h4>
                      <div class="flex items-center gap-2">
                        <span class="text-xs text-gray-500">
                          {TimeHelper.format_telegram_time(
                            session.last_message_at || session.inserted_at
                          )}
                        </span>
                        <%= if Map.get(@unread_counts, session.id, 0) > 0 do %>
                          <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-xs text-white font-bold">
                              {Map.get(@unread_counts, session.id)}
                            </span>
                          </div>
                        <% end %>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <p class="text-sm text-gray-600 truncate">
                        {get_last_message_preview(session)}
                      </p>
                    </div>
                  </div>
                </div>
              <% end %>
            <% else %>
              <div class="flex flex-col items-center justify-center h-64 text-gray-400">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-600 mb-2">暂无聊天记录</h3>
                <p class="text-sm text-gray-500 text-center">在上方搜索框中输入用户名开始聊天</p>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
      
    <!-- 右侧聊天区域 - Telegram风格 -->
      <div class="flex-1 flex flex-col h-full overflow-hidden">
        <%= if @current_session do %>
          <!-- 聊天头部 - Telegram风格 -->
          <div class="bg-white border-b border-gray-200 shadow-sm flex-shrink-0">
            <div class="flex items-center justify-between p-4">
              <div class="flex items-center gap-3">
                <!-- 会话头像 -->
                <div class="relative">
                  <%= if @current_session.session_type == :private do %>
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {get_session_avatar(@current_session, @current_user.id)}
                    </div>
                    <%= if is_other_user_online?(@current_session, @current_user.id) do %>
                      <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full">
                      </div>
                    <% else %>
                      <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-gray-400 border-2 border-white rounded-full">
                      </div>
                    <% end %>
                  <% else %>
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {String.first(@current_session.title || "群")}
                    </div>
                  <% end %>
                </div>
                
    <!-- 会话信息 -->
                <div>
                  <h3 class="font-semibold text-gray-900">
                    {get_session_title(@current_session, @current_user.id)}
                  </h3>
                  <%= if @current_session.session_type == :private do %>
                    <%= if is_other_user_online?(@current_session, @current_user.id) do %>
                      <p class="text-sm text-green-600 flex items-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span> 在线
                      </p>
                    <% else %>
                      <p class="text-sm text-gray-500 flex items-center">
                        <span class="w-2 h-2 bg-gray-400 rounded-full mr-1.5"></span> 离线
                      </p>
                    <% end %>
                  <% else %>
                    <p class="text-sm text-gray-500">
                      {length(@current_session.participants)} 位成员
                    </p>
                  <% end %>
                </div>
              </div>
              
    <!-- 聊天操作按钮 -->
              <div class="flex items-center gap-2">
                <button
                  phx-click="scroll_to_bottom"
                  class="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  title="滚动到底部"
                >
                  <svg
                    class="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                </button>
                <button
                  class="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  title="更多选项"
                >
                  <svg
                    class="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
    <!-- 聊天消息区域 - Telegram风格绿色渐变背景 -->
          <div
            id="chat-messages"
            phx-hook="ChatScroll"
            class="flex-1 overflow-y-auto p-4"
            style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 25%, #7fcdcd 50%, #74b9ff 75%, #6c5ce7 100%); scroll-behavior: smooth;"
          >
            <div class="space-y-3">
              <!-- 加载更多消息按钮 -->
              <%= if @streams.messages do %>
                <div class="flex justify-center py-2">
                  <button
                    type="button"
                    phx-click="load_more_messages"
                    class="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-white/20 rounded-full transition-colors"
                  >
                    加载更多消息
                  </button>
                </div>
              <% end %>

              {render_chat_messages(assigns)}
              <!-- 消息底部锚点 -->
              <div id="messages-bottom" class="h-1"></div>
            </div>
          </div>
          
    <!-- 消息输入区域 - Telegram风格 -->
          <div class="p-4 bg-white border-t border-gray-200 flex-shrink-0">
            <form phx-submit="send_message">
              <div class="flex items-end gap-3">
                <!-- 附件按钮 -->
                <button
                  type="button"
                  class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                  title="添加附件"
                >
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                    />
                  </svg>
                </button>
                
    <!-- 输入框 -->
                <div class="flex-1">
                  <div class="relative">
                    <textarea
                      id="message-input"
                      name="message"
                      placeholder="输入消息..."
                      phx-keyup="update_message"
                      phx-hook="AutoResize"
                      maxlength="1000"
                      rows="1"
                      autofocus
                      class="w-full px-4 py-3 pr-12 bg-gray-50 border border-gray-200 rounded-2xl text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 min-h-[48px] max-h-[120px] overflow-y-auto leading-relaxed"
                    ><%= @new_message %></textarea>
                    <!-- 表情按钮 -->
                    <button
                      type="button"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-500 hover:text-blue-600 rounded-full transition-colors"
                      title="表情"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </button>
                  </div>
                  <!-- 提示文字 -->
                  <div class="flex justify-between items-center mt-1 px-2">
                    <div class="text-xs text-gray-400">
                      Enter 发送，Shift+Enter 换行
                    </div>
                    <div class="text-xs text-gray-400">
                      {String.length(@new_message || "")}/1000
                    </div>
                  </div>
                </div>
                
    <!-- 发送按钮 -->
                <button
                  type="submit"
                  disabled={String.trim(@new_message || "") == ""}
                  class={[
                    "p-3 rounded-full transition-all duration-200",
                    (String.trim(@new_message || "") == "" &&
                       "bg-gray-200 text-gray-400 cursor-not-allowed") ||
                      "bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl"
                  ]}
                  title="发送消息"
                >
                  <%= if @sending_message do %>
                    <svg
                      class="w-5 h-5 animate-spin"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                  <% else %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                      />
                    </svg>
                  <% end %>
                </button>
              </div>
            </form>
          </div>
        <% else %>
          <!-- 空状态 - Telegram风格 -->
          <div
            class="flex-1 flex items-center justify-center"
            style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 25%, #7fcdcd 50%, #74b9ff 75%, #6c5ce7 100%);"
          >
            <div class="text-center max-w-md mx-auto p-8">
              <div class="mx-auto mb-8">
                <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                  </svg>
                </div>
              </div>
              <h3 class="text-2xl font-semibold text-white mb-3">选择聊天开始对话</h3>
              <p class="text-white/80 leading-relaxed">从左侧列表中选择一个聊天开始对话<br />或者点击左上角按钮创建新的聊天</p>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # 事件处理函数

  @impl Phoenix.LiveView
  def handle_event("send_message", %{"message" => message}, socket) do
    message = String.trim(message)

    case message do
      "" ->
        {:noreply, socket}

      _ ->
        socket = assign(socket, :sending_message, true)

        case send_chat_message(
               socket.assigns.current_session_id,
               socket.assigns.current_user.id,
               message
             ) do
          {:ok, _message} ->
            # 发送消息后不需要重新加载，因为会通过PubSub接收到新消息
            socket =
              socket
              |> assign(:new_message, "")
              |> assign(:sending_message, false)

            {:noreply, socket}

          {:error, reason} ->
            Logger.error("💬 [CHAT] 发送消息失败: #{inspect(reason)}")

            socket =
              socket
              |> assign(:sending_message, false)
              |> put_flash(:error, "发送消息失败")

            {:noreply, socket}
        end
    end
  end

  @impl Phoenix.LiveView
  def handle_event("update_message", %{"value" => message}, socket) do
    {:noreply, assign(socket, :new_message, message)}
  end

  @impl Phoenix.LiveView
  def handle_event("load_more_messages", _params, socket) do
    Logger.info("💬 [CHAT] 📜 用户请求加载更多消息")

    if socket.assigns.loading_more_messages or not socket.assigns.has_more_messages do
      Logger.info(
        "💬 [CHAT] ⏸️ 跳过加载 - loading: #{socket.assigns.loading_more_messages}, has_more: #{socket.assigns.has_more_messages}"
      )

      {:noreply, socket}
    else
      # 计算当前已加载的消息数量作为 offset
      current_message_count = socket.assigns.streams.messages |> Enum.count()

      Logger.info("💬 [CHAT] 📊 当前已加载消息数: #{current_message_count}")

      socket =
        socket
        |> assign(:loading_more_messages, true)
        |> load_session_messages(socket.assigns.current_session_id,
          limit: 20,
          offset: current_message_count,
          reset: false
        )

      {:noreply, socket}
    end
  end

  # 新的键盘事件处理器 - 来自 JavaScript Hook，包含正确的组合键信息
  @impl Phoenix.LiveView
  def handle_event("handle_keydown_with_modifiers", %{"key" => "Enter"} = event, socket) do
    Logger.info("💬 [CHAT] 🎯 Hook Enter 键盘事件: #{inspect(event)}")

    # 检查组合键
    shift_key = Map.get(event, "shiftKey", false)
    ctrl_key = Map.get(event, "ctrlKey", false)
    # Mac 的 Cmd 键
    meta_key = Map.get(event, "metaKey", false)
    alt_key = Map.get(event, "altKey", false)

    Logger.info(
      "💬 [CHAT] 🎯 组合键状态 - Shift: #{shift_key}, Ctrl: #{ctrl_key}, Meta: #{meta_key}, Alt: #{alt_key}"
    )

    cond do
      # Enter 键（无组合键）- 发送消息
      !shift_key && !ctrl_key && !meta_key && !alt_key ->
        Logger.info("💬 [CHAT] ✅ Enter 键发送消息")
        message = String.trim(Map.get(event, "value", ""))

        if message != "" do
          # 触发表单提交
          {:noreply, push_event(socket, "submit_form", %{})}
        else
          {:noreply, socket}
        end

      # Shift+Enter - 换行（不做任何处理，让默认行为发生）
      shift_key ->
        Logger.info("💬 [CHAT] ✅ Shift+Enter 换行")
        {:noreply, socket}

      # Ctrl+Enter 或 Cmd+Enter - 也可以发送消息（可选）
      ctrl_key || meta_key ->
        Logger.info("💬 [CHAT] ✅ Ctrl/Cmd+Enter 发送消息")
        message = String.trim(Map.get(event, "value", ""))

        if message != "" do
          {:noreply, push_event(socket, "submit_form", %{})}
        else
          {:noreply, socket}
        end

      # 其他组合键 - 不处理
      true ->
        Logger.info("💬 [CHAT] ❓ 其他组合键，不处理")
        {:noreply, socket}
    end
  end

  # 保留原有的处理器作为备用
  @impl Phoenix.LiveView
  def handle_event("handle_keydown", %{"key" => "Enter"} = event, socket) do
    Logger.info("💬 [CHAT] ⚠️ 原始 Enter 键盘事件（可能缺少组合键信息）: #{inspect(event)}")
    {:noreply, socket}
  end

  def handle_event("handle_keydown", params, socket) do
    # 处理其他键盘事件（非 Enter 键）
    Logger.info("💬 [CHAT] 其他键盘事件: #{inspect(params)}")
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("select_session", %{"session_id" => session_id}, socket) do
    Logger.info("💬 [CHAT] 选择会话 - 退出搜索模式")

    socket =
      socket
      |> assign(:search_mode, false)
      |> assign(:user_search_query, "")
      |> assign(:search_results, [])

    {:noreply, push_patch(socket, to: "/admin/chat/#{session_id}")}
  end

  @impl Phoenix.LiveView
  def handle_event("search_focus", _params, socket) do
    Logger.info("🔍 [SEARCH] 搜索框获得焦点 - 进入搜索模式")

    socket =
      socket
      |> assign(:search_mode, true)
      |> perform_user_and_session_search(socket.assigns.user_search_query || "")

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("search_blur", _params, socket) do
    # 失去焦点时退出搜索模式（但如果有内容，界面逻辑会保持搜索显示）
    Logger.info("🔍 [SEARCH] 搜索框失去焦点 - 退出搜索模式")
    socket = assign(socket, :search_mode, false)
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("search_clear", _params, socket) do
    Logger.info("🔍 [SEARCH] 搜索框已清空 - 清理搜索状态")

    socket =
      socket
      |> assign(:user_search_query, "")
      |> assign(:search_results, [])

    # 注意：不设置 search_mode = false，因为清空时搜索框可能仍有焦点
    # 界面逻辑会根据 search_mode 和 user_search_query 来决定显示

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("clear_search", _params, socket) do
    Logger.info("🔍 [SEARCH] 用户点击清空按钮 - 清空搜索内容")

    socket =
      socket
      |> assign(:user_search_query, "")
      |> assign(:search_results, [])

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("search_users", %{"value" => query}, socket) do
    current_user = socket.assigns.current_user
    Logger.info("🔍 [SEARCH] 搜索用户 - 查询: '#{query}'")

    socket =
      socket
      |> assign(:user_search_query, query)
      |> perform_user_and_session_search(query)

    # 界面显示逻辑：有焦点 OR 有内容 → 显示搜索页面
    has_content = String.trim(query) != ""
    has_focus = socket.assigns.search_mode
    show_search = has_focus || has_content

    Logger.info("🔍 [SEARCH] 有内容: #{has_content}, 有焦点: #{has_focus}, 显示搜索: #{show_search}")
    Logger.info("🔍 [SEARCH] 搜索结果数量: #{length(socket.assigns.search_results)}")

    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("start_chat_with_user", %{"user_id" => user_id}, socket) do
    case socket.assigns.current_user do
      nil ->
        socket = put_flash(socket, :error, "用户未登录")
        {:noreply, socket}

      current_user ->
        user_id = parse_user_id(user_id)
        current_user_id = current_user.id

        case ChatService.create_or_get_private_session(current_user_id, user_id) do
          {:ok, session} ->
            Logger.info("💬 [CHAT] 开始聊天 - 退出搜索模式")

            socket =
              socket
              |> assign(:search_mode, false)
              |> assign(:user_search_query, "")
              |> assign(:search_results, [])
              |> load_user_sessions()

            {:noreply, push_patch(socket, to: "/admin/chat/#{session.id}")}

          {:error, reason} ->
            Logger.error("💬 [CHAT] 创建聊天会话失败: #{inspect(reason)}")
            socket = put_flash(socket, :error, "创建聊天会话失败")
            {:noreply, socket}
        end
    end
  end

  @impl Phoenix.LiveView
  def handle_event("scroll_to_bottom", _params, socket) do
    {:noreply, push_event(socket, "scroll_to_bottom", %{})}
  end

  # 消息处理

  @impl Phoenix.LiveView
  def handle_info({:new_message, message}, socket) do
    Logger.info("💬 [CHAT] 📨 收到新消息广播 - 会话: #{message.session_id}, 发送者: #{message.sender_id}")

    # 如果是当前会话的消息，使用 stream_insert 添加到消息流
    if message.session_id == socket.assigns.current_session_id do
      Logger.info("💬 [CHAT] ➕ 添加新消息到当前会话流")

      # 使用 Ash.load 加载新消息的发送者信息
      message_with_sender =
        case Ash.load(message, [:sender]) do
          {:ok, loaded_message} ->
            Logger.info("💬 [CHAT] ✅ 加载发送者信息成功")
            loaded_message

          {:error, reason} ->
            Logger.warning("💬 [CHAT] ⚠️ 加载发送者信息失败: #{inspect(reason)}")
            message
        end

      # 获取消息的已读状态（仅对自己发送的消息）
      read_status =
        if message.sender_id == socket.assigns.current_user.id do
          get_message_read_status(message.id, message.session_id, socket.assigns.current_user.id)
        else
          nil
        end

      new_message = Map.put(message_with_sender, :read_status, read_status)

      # 使用 stream_insert 将新消息添加到流的末尾
      socket = stream_insert(socket, :messages, new_message, at: -1)

      Logger.info("💬 [CHAT] ✅ 新消息已添加到消息流")
      {:noreply, socket}
    else
      # 更新会话列表和未读计数
      Logger.info("💬 [CHAT] 🔄 更新其他会话的未读计数")
      socket = load_user_sessions(socket)
      {:noreply, socket}
    end
  end

  @impl Phoenix.LiveView
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: "user_presence", event: "presence_diff"},
        socket
      ) do
    # 用户在线状态发生变化，重新加载数据
    socket = load_user_sessions(socket)
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_info({:read_receipt, read_info}, socket) do
    Logger.info("📖 [READ] 收到已读回执广播 - 会话: #{read_info.session_id}, 用户: #{read_info.user_id}")

    # 如果是当前会话的已读回执，更新相关消息的已读状态
    if read_info.session_id == socket.assigns.current_session_id do
      Logger.info("📖 [READ] 🔄 更新当前会话消息的已读状态")

      # 使用 stream 更新相关消息的已读状态
      # 注意：这里可以进一步优化，只更新特定消息而不是重新加载
      # 但为了简化实现，暂时保持重新加载
      socket = load_session_messages(socket, read_info.session_id, reset: true)
      {:noreply, socket}
    else
      # 更新会话列表
      Logger.info("📖 [READ] 🔄 更新其他会话的未读计数")
      socket = load_user_sessions(socket)
      {:noreply, socket}
    end
  end

  def handle_info(_message, socket) do
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def terminate(_reason, socket) do
    # 取消订阅会话消息
    if socket.assigns[:current_session_id] do
      Phoenix.PubSub.unsubscribe(
        Cypridina.PubSub,
        "chat_session:#{socket.assigns.current_session_id}"
      )

      Logger.info("💬 [CHAT] 取消订阅会话消息 - 会话: #{socket.assigns.current_session_id}")
    end

    :ok
  end

  # 私有辅助函数

  defp load_user_sessions(socket) do
    case socket.assigns.current_user do
      nil ->
        socket

      user ->
        case ChatService.list_user_sessions(user.id) do
          {:ok, sessions} ->
            # 加载会话的参与者信息，包括用户和用户资料
            sessions_with_participants =
              Enum.map(sessions, fn session ->
                case Ash.load(session, participants: [user: :profile]) do
                  {:ok, loaded_session} -> loaded_session
                  _ -> session
                end
              end)

            # 计算未读消息数量
            unread_counts = calculate_unread_counts(sessions_with_participants, user.id)

            socket
            |> assign(:sessions, sessions_with_participants)
            |> assign(:unread_counts, unread_counts)

          {:error, reason} ->
            Logger.error("💬 [CHAT] 加载用户会话失败: #{inspect(reason)}")
            socket
        end
    end
  end

  defp load_session_messages(socket, session_id, opts \\ []) do
    case socket.assigns.current_user do
      nil ->
        socket

      user ->
        # 默认加载最新的20条消息
        limit = Keyword.get(opts, :limit, 20)
        offset = Keyword.get(opts, :offset, 0)
        reset_stream = Keyword.get(opts, :reset, true)

        Logger.info(
          "💬 [CHAT] 🔄 加载会话消息 - 会话: #{session_id}, limit: #{limit}, offset: #{offset}, reset: #{reset_stream}"
        )

        case ChatService.list_session_messages(session_id, user.id, limit: limit, offset: offset) do
          {:ok, messages} ->
            Logger.info("💬 [CHAT] ✅ 获取到 #{length(messages)} 条消息")

            # 使用 Ash.load 批量加载发送者信息
            messages_with_sender =
              case Ash.load(messages, [:sender]) do
                {:ok, loaded_messages} ->
                  Logger.info("💬 [CHAT] ✅ 批量加载发送者信息成功")
                  loaded_messages

                {:error, reason} ->
                  Logger.warning("💬 [CHAT] ⚠️ 批量加载发送者信息失败: #{inspect(reason)}")
                  messages
              end

            # 批量获取已读状态（仅对自己发送的消息）
            messages_with_data =
              Enum.map(messages_with_sender, fn message ->
                read_status =
                  if message.sender_id == user.id do
                    get_message_read_status(message.id, session_id, user.id)
                  else
                    nil
                  end

                Map.put(message, :read_status, read_status)
              end)

            # 获取当前会话信息
            current_session = Enum.find(socket.assigns.sessions, &(&1.id == session_id))

            # 使用 stream 更新消息列表
            socket =
              if reset_stream do
                # 重置 stream 并添加新消息（用于切换会话）
                Logger.info("💬 [CHAT] 🔄 重置消息流")

                socket
                |> stream(:messages, messages_with_data, reset: true)
              else
                # 在现有 stream 前面插入新消息（用于加载更多历史消息）
                Logger.info("💬 [CHAT] ➕ 在消息流前面插入 #{length(messages_with_data)} 条历史消息")

                Enum.reduce(messages_with_data, socket, fn message, acc_socket ->
                  stream_insert(acc_socket, :messages, message, at: 0)
                end)
              end

            # 检查是否还有更多消息
            has_more = length(messages) == limit

            socket
            |> assign(:current_session, current_session)
            |> assign(:messages_loaded, true)
            |> assign(:loading_more_messages, false)
            |> assign(:has_more_messages, has_more)

          {:error, reason} ->
            Logger.error("💬 [CHAT] ❌ 加载会话消息失败: #{inspect(reason)}")

            socket
            |> assign(:messages_loaded, true)
            |> assign(:loading_more_messages, false)
        end
    end
  end

  defp mark_session_as_read(socket, session_id) do
    case socket.assigns.current_user do
      nil ->
        socket

      user ->
        ChatService.mark_session_messages_as_read(session_id, user.id)
        socket
    end
  end

  defp send_chat_message(session_id, sender_id, content) do
    ChatService.send_message(session_id, sender_id, content)
  end

  defp load_available_users(socket) do
    # 获取所有用户，排除当前用户
    case socket.assigns.current_user do
      nil ->
        socket

      current_user ->
        current_user_id = current_user.id

        case User
             |> Ash.Query.filter(expr(id != ^current_user_id))
             |> Ash.Query.load(:profile)
             |> Ash.Query.limit(50)
             |> Ash.read() do
          {:ok, users} ->
            assign(socket, :available_users, users)

          {:error, reason} ->
            Logger.error("💬 [CHAT] 加载可用用户失败: #{inspect(reason)}")
            socket
        end
    end
  end

  defp calculate_unread_counts(sessions, user_id) do
    Logger.info("💬 [CHAT] 计算未读消息数量 - 用户: #{user_id}, 会话数: #{length(sessions)}")

    sessions
    |> Enum.reduce(%{}, fn session, acc ->
      case ChatService.get_unread_message_count(user_id, session.id) do
        {:ok, count} ->
          Logger.info("💬 [CHAT] 会话 #{session.id} 未读消息数: #{count}")
          Map.put(acc, session.id, count)

        {:error, reason} ->
          Logger.error("💬 [CHAT] 获取会话 #{session.id} 未读消息数失败: #{inspect(reason)}")
          Map.put(acc, session.id, 0)
      end
    end)
  end

  defp parse_user_id(user_id) when is_binary(user_id) do
    case Integer.parse(user_id) do
      {id, ""} -> id
      _ -> user_id
    end
  end

  defp parse_user_id(user_id), do: user_id

  defp is_user_online?(user_id) do
    UserPresence.user_online?(user_id)
  end

  # 会话相关辅助函数

  defp get_session_title(session, current_user_id) do
    case session.session_type do
      :private ->
        # 私聊显示对方用户名
        other_participant = Enum.find(session.participants, &(&1.user_id != current_user_id))

        cond do
          other_participant && other_participant.user && other_participant.user.profile ->
            other_participant.user.profile.nickname || "用户#{other_participant.user_id}"

          other_participant ->
            "用户#{other_participant.user_id}"

          true ->
            "私聊"
        end

      :group ->
        session.title || "群聊"
    end
  end

  defp get_session_avatar(session, current_user_id) do
    case session.session_type do
      :private ->
        other_participant = Enum.find(session.participants, &(&1.user_id != current_user_id))

        cond do
          other_participant && other_participant.user && other_participant.user.profile &&
              other_participant.user.profile.nickname ->
            String.slice(other_participant.user.profile.nickname, 0, 2)

          other_participant ->
            "用户"

          true ->
            "?"
        end

      :group ->
        title = session.title || "群聊"
        String.slice(title, 0, 2)
    end
  end

  defp get_session_avatar_url(session, current_user_id) do
    case session.session_type do
      :private ->
        other_participant = Enum.find(session.participants, &(&1.user_id != current_user_id))

        if other_participant && other_participant.user && other_participant.user.profile do
          other_participant.user.profile.avatar_url
        else
          nil
        end

      :group ->
        # 群聊暂时没有头像URL
        nil
    end
  end

  defp is_other_user_online?(session, current_user_id) do
    case session.session_type do
      :private ->
        other_participant = Enum.find(session.participants, &(&1.user_id != current_user_id))

        if other_participant do
          is_user_online?(other_participant.user_id)
        else
          false
        end

      :group ->
        false
    end
  end

  defp get_last_message_preview(session) do
    # TODO: 实现最后消息预览
    # 这里需要从session中获取最后一条消息的预览
    "暂无消息"
  end

  # 渲染函数

  defp render_chat_messages(assigns) do
    ~H"""
    <!-- 加载更多消息按钮 -->
    <%= if @has_more_messages and @messages_loaded do %>
      <div class="flex justify-center mb-4">
        <button
          type="button"
          phx-click="load_more_messages"
          disabled={@loading_more_messages}
          class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <%= if @loading_more_messages do %>
            <svg
              class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600 inline"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
              </circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              >
              </path>
            </svg>
            加载中...
          <% else %>
            📜 加载更多消息
          <% end %>
        </button>
      </div>
    <% end %>

    <!-- 消息列表 - 使用 stream 优化 -->
    <div id="messages-container" phx-update="stream" class="space-y-4">
      <%= for {message_id, message} <- @streams.messages do %>
        <div id={message_id} class="message-item">
          <.chat
            position={if message.sender_id == @current_user.id, do: "flipped", else: "normal"}
            variant="default"
            color="natural"
            class="mb-4"
          >
            <!-- 头像 -->
            <%= if message.sender && message.sender.profile && message.sender.profile.avatar_url && message.sender.profile.avatar_url != "" do %>
              <!-- 有头像URL时显示图片 -->
              <.avatar
                src={message.sender.profile.avatar_url}
                size="extra_large"
                rounded="full"
                border="small"
                class="flex items-center justify-center"
              />
            <% else %>
              <!-- 无头像URL时显示纯色底和昵称 -->
              <.avatar
                src=""
                size="extra_large"
                rounded="full"
                border="small"
                class={"flex items-center justify-center text-white text-sm font-semibold #{if message.sender_id == @current_user.id, do: "bg-green-500", else: "bg-blue-500"}"}
              >
                <%= if message.sender && message.sender.profile && message.sender.profile.nickname do %>
                  {String.slice(message.sender.profile.nickname, 0, 2)}
                <% else %>
                  用户
                <% end %>
              </.avatar>
            <% end %>

            <.chat_section class={[
              "rounded-2xl px-3 py-2 shadow-sm",
              if message.sender_id == @current_user.id do
                "bg-green-100 text-gray-900"
              else
                "bg-white text-gray-900 border border-gray-200"
              end
            ]}>
              <!-- 发送者名称（仅对方消息显示） -->
              <%= if message.sender_id != @current_user.id do %>
                <div class="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                  <div class="text-sm font-medium text-blue-600">
                    <%= if message.sender do %>
                      {(message.sender.profile && message.sender.profile.nickname) || "用户"}
                    <% else %>
                      用户
                    <% end %>
                  </div>
                </div>
              <% end %>
              
    <!-- 消息内容 -->
              <p class="text-sm leading-relaxed break-words whitespace-pre-wrap text-gray-900">
                {message.content}
              </p>
              
    <!-- 状态信息：时间和已读状态 -->
              <:status
                time={TimeHelper.time_ago(message.inserted_at)}
                deliver={
                  if message.sender_id == @current_user.id && message.read_status do
                    if message.read_status.is_read_by_all do
                      "已读"
                    else
                      if message.read_status.read_by_count > 0, do: "部分已读", else: "未读"
                    end
                  else
                    nil
                  end
                }
              />
            </.chat_section>
            
    <!-- 已读状态图标 -->
            <%= if message.sender_id == @current_user.id && message.read_status do %>
              <div class="flex items-center">
                <%= if message.read_status.is_read_by_all do %>
                  <!-- 全部已读 - 双勾 -->
                  <span class="inline-flex items-center" title="已读">
                    <svg class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-blue-500 -ml-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </span>
                <% else %>
                  <!-- 部分已读或未读 - 单勾 -->
                  <span
                    class="inline-flex items-center"
                    title={if message.read_status.read_by_count > 0, do: "部分已读", else: "未读"}
                  >
                    <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </span>
                <% end %>
              </div>
            <% end %>
          </.chat>
        </div>
      <% end %>
    </div>
    """
  end

  defp perform_user_and_session_search(socket, query) do
    current_user = socket.assigns.current_user

    if current_user do
      trimmed_query = String.trim(query)

      if trimmed_query == "" do
        Logger.info("🔍 [SEARCH] 空查询，显示最近用户和会话")
        # 空查询时显示最近的用户和会话
        search_results = get_recent_users_and_sessions(current_user)
        Logger.info("🔍 [SEARCH] 最近用户和会话数量: #{length(search_results)}")
        assign(socket, :search_results, search_results)
      else
        Logger.info("🔍 [SEARCH] 开始搜索 - 用户: #{current_user.id}, 查询: '#{trimmed_query}'")
        search_results = search_users_and_sessions(current_user, trimmed_query)
        Logger.info("🔍 [SEARCH] 搜索完成 - 结果数量: #{length(search_results)}")
        assign(socket, :search_results, search_results)
      end
    else
      Logger.warn("🔍 [SEARCH] 当前用户为空，无法执行搜索")
      assign(socket, :search_results, [])
    end
  end

  defp get_recent_users_and_sessions(current_user) do
    # 获取最近的用户（限制10个）
    user_results = get_recent_users(current_user, 10)

    # 获取最近的会话（限制10个）
    session_results = get_recent_sessions(current_user, 10)

    # 合并结果，会话在前，用户在后
    session_results ++ user_results
  end

  defp search_users_and_sessions(current_user, query) do
    query_lower = String.downcase(query)

    # 搜索用户
    user_results = search_matching_users(current_user, query_lower)

    # 搜索会话
    session_results = search_matching_sessions(current_user, query_lower)

    # 合并结果，用户在前，会话在后
    user_results ++ session_results
  end

  defp search_matching_users(current_user, query_lower) do
    current_user_id = current_user.id
    Logger.info("🔍 [SEARCH] 开始搜索用户 - 排除用户: #{current_user_id}, 查询: '#{query_lower}'")

    case User
         |> Ash.Query.filter(expr(id != ^current_user_id))
         |> Ash.Query.load(:profile)
         |> Ash.Query.limit(20)
         |> Ash.read() do
      {:ok, users} ->
        Logger.info("🔍 [SEARCH] 数据库查询成功 - 总用户数: #{length(users)}")

        filtered_users =
          users
          |> Enum.filter(fn user ->
            display_name = (user.profile && user.profile.nickname) || "用户#{user.id}"
            matches = String.contains?(String.downcase(display_name), query_lower)

            if matches do
              Logger.info("🔍 [SEARCH] 匹配用户: #{display_name}")
            end

            matches
          end)

        Logger.info("🔍 [SEARCH] 过滤后用户数: #{length(filtered_users)}")

        filtered_users
        |> Enum.map(fn user ->
          %{type: :user, data: user}
        end)

      {:error, reason} ->
        Logger.error("🔍 [SEARCH] 用户搜索失败: #{inspect(reason)}")
        []
    end
  end

  defp search_matching_sessions(current_user, query_lower) do
    Logger.info("🔍 [SEARCH] 开始搜索会话 - 用户: #{current_user.id}, 查询: '#{query_lower}'")

    case ChatService.list_user_sessions(current_user.id, limit: 50) do
      {:ok, sessions} ->
        Logger.info("🔍 [SEARCH] 会话查询成功 - 总会话数: #{length(sessions)}")

        sessions_with_participants =
          Enum.map(sessions, fn session ->
            case Ash.load(session, participants: [user: :profile]) do
              {:ok, loaded_session} -> loaded_session
              _ -> session
            end
          end)

        filtered_sessions =
          sessions_with_participants
          |> Enum.filter(fn session ->
            session_title = get_session_title(session, current_user.id)
            matches = String.contains?(String.downcase(session_title), query_lower)

            if matches do
              Logger.info("🔍 [SEARCH] 匹配会话: #{session_title}")
            end

            matches
          end)

        Logger.info("🔍 [SEARCH] 过滤后会话数: #{length(filtered_sessions)}")

        filtered_sessions
        |> Enum.map(fn session ->
          %{type: :session, data: session}
        end)

      {:error, reason} ->
        Logger.error("🔍 [SEARCH] 会话搜索失败: #{inspect(reason)}")
        []
    end
  end

  defp get_recent_users(current_user, limit) do
    current_user_id = current_user.id
    Logger.info("🔍 [SEARCH] 获取最近用户 - 排除用户: #{current_user_id}, 限制: #{limit}")

    case User
         |> Ash.Query.filter(expr(id != ^current_user_id))
         |> Ash.Query.load(:profile)
         |> Ash.Query.sort(inserted_at: :desc)
         |> Ash.Query.limit(limit)
         |> Ash.read() do
      {:ok, users} ->
        Logger.info("🔍 [SEARCH] 获取最近用户成功 - 数量: #{length(users)}")

        users
        |> Enum.map(fn user ->
          %{type: :user, data: user}
        end)

      {:error, reason} ->
        Logger.error("🔍 [SEARCH] 获取最近用户失败: #{inspect(reason)}")
        []
    end
  end

  defp get_recent_sessions(current_user, limit) do
    Logger.info("🔍 [SEARCH] 获取最近会话 - 用户: #{current_user.id}, 限制: #{limit}")

    case ChatService.list_user_sessions(current_user.id, limit: limit) do
      {:ok, sessions} ->
        Logger.info("🔍 [SEARCH] 获取最近会话成功 - 数量: #{length(sessions)}")

        sessions_with_participants =
          Enum.map(sessions, fn session ->
            case Ash.load(session, participants: [user: :profile]) do
              {:ok, loaded_session} -> loaded_session
              _ -> session
            end
          end)

        sessions_with_participants
        |> Enum.map(fn session ->
          %{type: :session, data: session}
        end)

      {:error, reason} ->
        Logger.error("🔍 [SEARCH] 获取最近会话失败: #{inspect(reason)}")
        []
    end
  end

  defp get_message_read_status(message_id, session_id, sender_id) do
    # 获取会话中除发送者外的其他参与者
    case ChatParticipant
         |> Ash.Query.filter(expr(session_id == ^session_id and user_id != ^sender_id))
         |> Ash.Query.load(:user)
         |> Ash.read() do
      {:ok, participants} ->
        # 检查每个参与者是否已读该消息
        read_by =
          Enum.filter(participants, fn participant ->
            case MessageReadReceipt
                 |> Ash.Query.filter(
                   expr(message_id == ^message_id and user_id == ^participant.user_id)
                 )
                 |> Ash.read_one() do
              {:ok, _receipt} -> true
              _ -> false
            end
          end)

        %{
          total_participants: length(participants),
          read_by_count: length(read_by),
          is_read_by_all: length(read_by) == length(participants)
        }

      _ ->
        %{total_participants: 0, read_by_count: 0, is_read_by_all: false}
    end
  end

  defp filter_available_users(users, query) when query == "" or is_nil(query) do
    users
  end

  defp filter_available_users(users, query) do
    query_lower = String.downcase(query)

    Enum.filter(users, fn user ->
      display_name = user.profile.nickname || "用户#{user.id}"
      String.contains?(String.downcase(display_name), query_lower)
    end)
  end
end
