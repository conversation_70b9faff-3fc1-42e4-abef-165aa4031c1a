defmodule Teen.Adapters.FixedAshAdapter do
  @moduledoc """
  Fixed version of Backpex.Adapters.Ash that handles invalid items gracefully.
  
  This adapter fixes the issue where Ash.Changeset.for_update receives %{} 
  instead of a proper record, which causes ArgumentError.
  """
  
  @config_schema [
    resource: [
      doc: "The `Ash.Resource` that will be used to perform CRUD operations.",
      type: :atom,
      required: true
    ],
    create_action: [
      doc: "The action name to use for creating items. Defaults to `:create`.",
      type: :atom,
      default: :create
    ],
    update_action: [
      doc: "The action name to use for updating items. Defaults to `:update`.",
      type: :atom,
      default: :update
    ]
  ]

  use Backpex.Adapter, config_schema: @config_schema
  require Ash.Query

  @doc """
  Gets a database record with the given primary key value.

  Returns `nil` if no result was found.
  """
  @impl Backpex.Adapter
  def get(primary_value, _assigns, live_resource) do
    config = live_resource.config(:adapter_config)
    primary_key = live_resource.config(:primary_key)

    result = config[:resource]
    |> Ash.Query.filter(^Ash.Expr.ref(primary_key) == ^primary_value)
    |> Ash.read_one()

    case result do
      {:ok, record} when is_map(record) and not is_nil(record) -> 
        {:ok, record}
      {:ok, nil} -> 
        {:ok, nil}
      {:error, _} = error -> 
        error
      # 处理意外的返回值
      other -> 
        {:error, "Unexpected result from Ash.read_one: #{inspect(other)}"}
    end
  end

  @doc """
  Returns a list of items by given criteria.
  """
  @impl Backpex.Adapter
  def list(_criteria, _assigns, live_resource) do
    config = live_resource.config(:adapter_config)

    config[:resource]
    |> Ash.read()
  end

  @doc """
  Returns the number of items matching the given criteria.
  """
  @impl Backpex.Adapter
  def count(_criteria, _assigns, live_resource) do
    config = live_resource.config(:adapter_config)

    config[:resource]
    |> Ash.count()
  end

  @doc """
  Deletes multiple items.
  """
  @impl Backpex.Adapter
  def delete_all(items, live_resource) do
    config = live_resource.config(:adapter_config)
    primary_key = live_resource.config(:primary_key)

    ids = Enum.map(items, &Map.fetch!(&1, primary_key))

    result =
      config[:resource]
      |> Ash.Query.filter(^Ash.Expr.ref(primary_key) in ^ids)
      |> Ash.bulk_destroy(:destroy, %{}, return_records?: true)

    {:ok, result.records}
  end

  @doc """
  Inserts given item.
  """
  @impl Backpex.Adapter
  def insert(changeset, live_resource) do
    config = live_resource.config(:adapter_config)
    create_action = config[:create_action] || :create

    # 处理不同类型的changeset
    case changeset do
      %Ash.Changeset{} ->
        # 标准的Ash.Changeset - 直接使用changeset而不是只使用attributes
        Ash.create(changeset, action: create_action)

      %{__struct__: _} = struct_changeset ->
        # 可能是AshPhoenix.Form或其他结构体
        # 尝试提取attributes
        attributes = Map.get(struct_changeset, :attributes, %{})
        config[:resource]
        |> Ash.create(attributes, action: create_action)

      _ ->
        # 其他情况，直接使用
        config[:resource]
        |> Ash.create(changeset, action: create_action)
    end
  end

  @doc """
  Updates given item.
  """
  @impl Backpex.Adapter
  def update(changeset, live_resource) do
    config = live_resource.config(:adapter_config)
    update_action = config[:update_action] || :update

    # 处理不同类型的changeset
    case changeset do
      %Ash.Changeset{} ->
        # 标准的Ash.Changeset - 直接使用changeset而不是只使用attributes
        Ash.update(changeset, action: update_action)

      %{__struct__: _} = struct_changeset ->
        # 可能是AshPhoenix.Form或其他结构体
        # 尝试提取data和attributes
        data = Map.get(struct_changeset, :data)
        attributes = Map.get(struct_changeset, :attributes, %{})

        if data do
          data |> Ash.update(attributes, action: update_action)
        else
          {:error, "No data found in changeset"}
        end

      _ ->
        {:error, "Invalid changeset type"}
    end
  end

  @doc """
  Updates given items.
  """
  @impl Backpex.Adapter
  def update_all(_items, _updates, _live_resource) do
    raise "not implemented yet"
  end

  @doc """
  Applies a change to a given item.
  
  This is the fixed version that handles invalid items gracefully.
  """
  @impl Backpex.Adapter
  def change(item, attrs, _fields, assigns, live_resource, opts) do
    config = live_resource.config(:adapter_config)
    action = Keyword.get(opts, :action, :validate)

    # 根据live_action确定使用的Ash action
    ash_action = get_ash_action(assigns.live_action, live_resource)

    case assigns.live_action do
      :new ->
        config[:resource]
        |> Ash.Changeset.for_create(ash_action, attrs)
        |> Map.put(:action, action)

      :edit ->
        # 验证item是否为有效的记录 - 这是关键修复
        if valid_record?(item) do
          item
          |> Ash.Changeset.for_update(ash_action, attrs)
          |> Map.put(:action, action)
        else
          # 如果item无效，记录错误并返回一个安全的changeset
          require Logger
          Logger.warning("Invalid item passed to Backpex change function: #{inspect(item)}")
          
          # 创建一个带错误的changeset - 使用create action避免upsert_condition错误
          create_action = config[:create_action] || :create
          config[:resource]
          |> Ash.Changeset.for_create(create_action, %{})
          |> Ash.Changeset.add_error(:item, "Invalid record for update - received #{inspect(item)}")
          |> Map.put(:action, action)
        end

      _ ->
        # 对于其他action，使用通用的changeset，但也要验证item
        if valid_record?(item) do
          item
          |> Ash.Changeset.for_update(ash_action, attrs)
          |> Map.put(:action, action)
        else
          # 如果item无效，返回一个错误changeset
          require Logger
          Logger.warning("Invalid item passed to Backpex change function: #{inspect(item)}")
          
          # 使用create action而不是update action来避免upsert_condition错误
          create_action = config[:create_action] || :create
          config[:resource]
          |> Ash.Changeset.for_create(create_action, %{})
          |> Ash.Changeset.add_error(:item, "Invalid record for update - received #{inspect(item)}")
          |> Map.put(:action, action)
        end
    end
  end

  # 验证item是否为有效的记录
  defp valid_record?(item) do
    case item do
      %{__struct__: module} when is_atom(module) and module != nil ->
        # 检查是否是Ash资源或Ecto schema
        function_exported?(module, :__ash_resource__, 0) or 
        function_exported?(module, :__schema__, 1)
      _ ->
        false
    end
  end

  # 根据live_action获取对应的Ash action名称
  defp get_ash_action(:new, live_resource) do
    # 优先使用配置的create_action，否则使用默认的:create
    live_resource.config(:adapter_config)[:create_action] || :create
  end

  defp get_ash_action(:edit, live_resource) do
    # 优先使用配置的update_action，否则使用默认的:update
    live_resource.config(:adapter_config)[:update_action] || :update
  end

  defp get_ash_action(_, live_resource) do
    # 对于其他action，使用默认的update action
    live_resource.config(:adapter_config)[:update_action] || :update
  end
end