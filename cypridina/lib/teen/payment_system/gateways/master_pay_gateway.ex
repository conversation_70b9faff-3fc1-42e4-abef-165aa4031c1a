defmodule Teen.PaymentSystem.Gateways.MasterPayGateway do
  @moduledoc """
  MasterPay支付网关集成

  支持的功能：
  - 代收（充值）
  - 代付（提现）
  - 订单查询
  - 回调处理
  - 签名验证

  网关信息：
  - 商户ID: 10236
  - 网关地址: https://api.masterpay88.in/app-api
  - 代收通道: 3021
  - 代付通道: 3020
  - 回调IP: *************
  """

  require Logger

  # 可以从环境变量或配置文件读取
  @merchant_id Application.compile_env(:cypridina, [__MODULE__, :merchant_id], "10236")
  @merchant_key Application.compile_env(
                  :cypridina,
                  [__MODULE__, :merchant_key],
                  "ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM"
                )
  @gateway_url Application.compile_env(
                 :cypridina,
                 [__MODULE__, :gateway_url],
                 "https://api.masterpay88.in/app-api"
               )
  @create_order_path "/v1.0/api/order/create"
  @query_order_path "/v1.0/api/order/query"
  @callback_ip Application.compile_env(:cypridina, [__MODULE__, :callback_ip], "*************")

  # 通道配置
  # 代收综合通道
  @recharge_channel "3021"
  # 代付综合通道
  @withdrawal_channel "3020"

  @doc """
  创建充值订单
  """
  def create_recharge_order(order_params) do
    params = %{
      "mchId" => @merchant_id,
      "productId" => @recharge_channel,
      "mchOrderNo" => order_params.order_id,
      "amount" => format_amount_in_cents(order_params.amount),
      "clientIp" => order_params.client_ip || "0.0.0.0",
      "notifyUrl" => build_notify_url("recharge", order_params.order_id),
      "returnUrl" => build_return_url("recharge", order_params.order_id),
      "subject" => order_params.subject || "Recharge",
      "body" => order_params.body || "Recharge Order",
      "param1" => order_params.param1 || "",
      "param2" => order_params.param2 || ""
    }

    # 生成签名
    params_with_sign = Map.put(params, "sign", generate_signature(params))

    # 发送请求
    case send_request(@create_order_path, params_with_sign) do
      {:ok, response} ->
        case response["retCode"] do
          "SUCCESS" ->
            {:ok,
             %{
               gateway_order_id: response["payOrderId"],
               payment_url: response["payUrl"],
               qr_code: response["payParams"]["qrCode"] || nil,
               amount: order_params.amount,
               status: :pending
             }}

          _ ->
            {:error, response["retMsg"] || "Create order failed"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  创建提现订单
  """
  def create_withdrawal_order(order_params) do
    params = %{
      "mchId" => @merchant_id,
      "productId" => @withdrawal_channel,
      "mchOrderNo" => order_params.order_id,
      "amount" => format_amount_in_cents(order_params.amount),
      "clientIp" => order_params.client_ip || "0.0.0.0",
      "notifyUrl" => build_notify_url("withdrawal", order_params.order_id),
      "userName" => order_params.account_name,
      "cardNumber" => order_params.account_number,
      "ifscCode" => order_params.ifsc_code,
      "bankName" => order_params.bank_name || "",
      "param1" => order_params.param1 || "",
      "param2" => order_params.param2 || ""
    }

    # 生成签名
    params_with_sign = Map.put(params, "sign", generate_signature(params))

    # 发送请求
    case send_request(@create_order_path, params_with_sign) do
      {:ok, response} ->
        case response["retCode"] do
          "SUCCESS" ->
            {:ok,
             %{
               gateway_order_id: response["payOrderId"],
               status: parse_order_status(response["status"]),
               amount: order_params.amount,
               fee: 0,
               estimated_time: nil
             }}

          _ ->
            {:error, response["retMsg"] || "Create withdrawal order failed"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  查询订单状态
  """
  def query_order(order_id) do
    params = %{
      "mchId" => @merchant_id,
      "mchOrderNo" => order_id
    }

    # 生成签名
    params_with_sign = Map.put(params, "sign", generate_signature(params))

    # 发送请求
    case send_request(@query_order_path, params_with_sign) do
      {:ok, response} ->
        case response["retCode"] do
          "SUCCESS" ->
            {:ok,
             %{
               gateway_order_id: response["payOrderId"],
               merchant_order_id: response["mchOrderNo"],
               amount: response["amount"],
               real_amount: response["realAmount"],
               status: parse_order_status(response["status"]),
               utr: response["utr"],
               pay_success_time: response["paySuccessTime"]
             }}

          _ ->
            {:error, response["retMsg"] || "Query order failed"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  处理支付回调
  """
  def handle_callback(callback_params, callback_type) do
    Logger.info("收到MasterPay回调: #{inspect(callback_params)}")

    # 验证回调来源IP
    case verify_callback_ip(callback_params["client_ip"]) do
      :ok ->
        # 验证签名
        case verify_callback_signature(callback_params) do
          :ok ->
            process_callback(callback_params, callback_type)

          {:error, reason} ->
            Logger.error("回调签名验证失败: #{reason}")
            {:error, :invalid_signature}
        end

      {:error, reason} ->
        Logger.error("回调IP验证失败: #{reason}")
        {:error, :invalid_ip}
    end
  end

  @doc """
  生成签名
  """
  def generate_signature(params) do
    # 排除sign字段和空值参数
    params
    |> Map.drop(["sign"])
    |> Enum.reject(fn {_k, v} -> is_nil(v) or v == "" end)
    |> Enum.sort_by(fn {k, _v} -> String.downcase(k) end)
    |> Enum.map(fn {k, v} -> "#{k}=#{v}&" end)
    |> Enum.join("")
    |> Kernel.<>("secretKey=#{@merchant_key}")
    |> then(&:crypto.hash(:md5, &1))
    |> Base.encode16(case: :upper)
  end

  @doc """
  验证回调签名
  """
  def verify_callback_signature(params) do
    received_sign = params["sign"]
    calculated_sign = generate_signature(params)

    if received_sign == calculated_sign do
      :ok
    else
      Logger.error("签名验证失败: 收到=#{received_sign}, 计算=#{calculated_sign}")
      {:error, :signature_mismatch}
    end
  end

  @doc """
  验证回调IP
  """
  def verify_callback_ip(client_ip) do
    if client_ip == @callback_ip do
      :ok
    else
      Logger.warning("回调IP不匹配: 收到=#{client_ip}, 期望=#{@callback_ip}")
      {:error, :ip_mismatch}
    end
  end

  @doc """
  获取网关配置信息
  """
  def get_gateway_config do
    %{
      merchant_id: @merchant_id,
      gateway_url: @gateway_url,
      recharge_channel: @recharge_channel,
      withdrawal_channel: @withdrawal_channel,
      callback_ip: @callback_ip,
      supported_currencies: ["INR"],
      supported_payment_methods: ["UPI", "Net Banking", "Wallet", "Card"],
      # 最小金额（分）
      min_amount: 100,
      # 最大金额（分）
      max_amount: 10_000_000,
      # 手续费率 2%
      fee_rate: 0.02
    }
  end

  # ==================== 私有函数 ====================

  defp send_request(path, params) do
    url = @gateway_url <> path

    headers = [
      {"Content-Type", "application/json"},
      {"Accept", "application/json"}
    ]

    Logger.info("发送请求到MasterPay: #{url}")
    Logger.debug("请求参数: #{inspect(params)}")

    # 验证必传字段
    with :ok <- validate_required_params(params, path),
         {:ok, body} <- Jason.encode(params) do
      # Use Req for HTTP requests
      case Req.post(url, body: body, headers: headers, receive_timeout: 30_000) do
        {:ok, %Req.Response{status: 200, body: response_body}} when is_binary(response_body) ->
          case Jason.decode(response_body) do
            {:ok, response} ->
              Logger.debug("网关响应: #{inspect(response)}")
              handle_gateway_response(response)

            {:error, _} ->
              Logger.error("解析响应失败: #{response_body}")
              {:error, "Invalid JSON response"}
          end

        {:ok, %Req.Response{status: 200, body: response}} when is_map(response) ->
          Logger.debug("网关响应: #{inspect(response)}")
          handle_gateway_response(response)

        {:ok, %Req.Response{status: 500, body: body}} ->
          Logger.error("Internal Server Error: #{inspect(body)}")
          {:error, "Internal Server Error - 请检查参数名称拼写和必传字段"}

        {:ok, %Req.Response{status: status, body: body}} ->
          Logger.error("HTTP错误: #{status}, 响应: #{inspect(body)}")
          {:error, {:http_error, status}}

        {:error, reason} ->
          Logger.error("网络请求失败: #{inspect(reason)}")
          {:error, :network_error}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp validate_required_params(params, path) do
    required_fields =
      case path do
        @create_order_path ->
          ["mchId", "productId", "mchOrderNo", "amount", "clientIp", "notifyUrl"]

        @query_order_path ->
          ["mchId", "mchOrderNo"]

        _ ->
          []
      end

    missing_fields =
      Enum.filter(required_fields, fn field ->
        is_nil(params[field]) or params[field] == ""
      end)

    if Enum.empty?(missing_fields) do
      :ok
    else
      Logger.error("缺少必填字段: #{inspect(missing_fields)}")
      {:error, "缺少必填字段: #{Enum.join(missing_fields, ", ")}"}
    end
  end

  defp handle_gateway_response(response) do
    case response do
      %{"retCode" => "FAIL", "retMsg" => msg} ->
        if String.contains?(msg || "", "资源不足") do
          Logger.error("资源不足错误: 请检查后台码商或卡的设置")
          {:error, "资源不足 - 请检查后台配置"}
        else
          {:ok, response}
        end

      _ ->
        {:ok, response}
    end
  end

  defp format_amount_in_cents(amount) when is_integer(amount) do
    # 金额已经是分为单位，直接转换为字符串
    Integer.to_string(amount)
  end

  defp format_amount_in_cents(amount) when is_float(amount) do
    # 将元转换为分
    Integer.to_string(round(amount * 100))
  end

  defp format_amount_in_cents(amount) when is_binary(amount) do
    # 假设输入是元，转换为分
    {float_amount, _} = Float.parse(amount)
    Integer.to_string(round(float_amount * 100))
  end

  defp build_notify_url(type, order_id) do
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")
    # 确保使用公网可访问的URL
    public_url = Application.get_env(:cypridina, :public_url, base_url)
    "#{public_url}/api/payment/callback/masterpay/#{type}/#{order_id}"
  end

  defp build_return_url(type, order_id) do
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")
    # 确保使用公网可访问的URL
    public_url = Application.get_env(:cypridina, :public_url, base_url)
    "#{public_url}/payment/#{type}/result/#{order_id}"
  end

  defp parse_order_status(status) do
    case to_string(status) do
      # 支付中/处理中
      "0" -> :processing
      # 已完成
      "1" -> :success
      # 已超时
      "3" -> :timeout
      # 驳回中
      "5" -> :rejecting
      # 已驳回
      "7" -> :rejected
      # 冲正
      "9" -> :reversed
      _ -> :unknown
    end
  end

  defp process_callback(callback_params, callback_type) do
    order_status = parse_order_status(callback_params["status"])

    callback_result = %{
      gateway_order_id: callback_params["payOrderId"],
      merchant_order_id: callback_params["mchOrderNo"],
      amount: String.to_integer(callback_params["amount"] || "0"),
      real_amount:
        String.to_integer(callback_params["realAmount"] || callback_params["amount"] || "0"),
      income: String.to_integer(callback_params["income"] || "0"),
      status: order_status,
      gateway_fee: String.to_integer(callback_params["fee"] || "0"),
      pay_success_time: callback_params["paySuccessTime"],
      utr: callback_params["utr"],
      param1: callback_params["param1"],
      param2: callback_params["param2"],
      reject_reason: callback_params["rejectReason"]
    }

    Logger.info(
      "处理#{callback_type}回调: 订单#{callback_params["merchantOrderId"]}, 状态#{order_status}"
    )

    # 根据回调类型处理
    case callback_type do
      "recharge" ->
        # 直接处理充值回调
        handle_recharge_callback(callback_params["merchantOrderId"], callback_result)

      "withdrawal" ->
        # 直接处理提现回调
        handle_withdrawal_callback(callback_params["merchantOrderId"], callback_result)

      _ ->
        Logger.error("未知的回调类型: #{callback_type}")
        {:error, :unknown_callback_type}
    end
  end

  defp handle_recharge_callback(order_id, callback_result) do
    case Teen.Services.RechargeService.complete_recharge(order_id, callback_result) do
      {:ok, _} -> {:ok, :processed}
      error -> error
    end
  end

  defp handle_withdrawal_callback(order_id, callback_result) do
    case Teen.Services.WithdrawalService.complete_withdrawal(order_id, callback_result) do
      {:ok, _} -> {:ok, :processed}
      error -> error
    end
  end
end
