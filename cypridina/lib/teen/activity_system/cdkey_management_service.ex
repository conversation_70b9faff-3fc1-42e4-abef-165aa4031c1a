defmodule Teen.ActivitySystem.CdkeyManagementService do
  @moduledoc """
  CDKEY管理服务

  提供CDKEY模板管理、批次管理和高级配置功能
  """

  alias Teen.ActivitySystem.{CdkeyTemplate, CdkeyBatch, CdkeyActivity, CdkeyService}
  alias Cypridina.Accounts.User

  require Logger

  @doc """
  创建CDKEY模板
  """
  def create_template(attrs) do
    with {:ok, template} <- CdkeyTemplate.create(attrs) do
      Logger.info("创建CDKEY模板: #{template.name}")
      {:ok, template}
    else
      {:error, reason} ->
        Logger.error("创建CDKEY模板失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  根据模板生成CDKEY批次
  """
  def generate_batch_from_template(template_id, attrs) do
    with {:ok, template} <- get_template(template_id),
         :ok <- validate_template_status(template),
         {:ok, batch_config} <- build_batch_config(template, attrs),
         {:ok, batch} <- create_batch_record(template, batch_config),
         {:ok, cdkeys} <- generate_cdkeys_for_batch(template, batch, batch_config) do
      
      # 更新模板统计
      update_template_statistics(template, length(cdkeys))
      
      Logger.info("从模板生成CDKEY批次: 模板=#{template.name}, 批次=#{batch.batch_name}, 数量=#{length(cdkeys)}")
      
      {:ok, %{
        batch: batch,
        cdkeys: cdkeys,
        total_generated: length(cdkeys)
      }}
    else
      {:error, reason} ->
        Logger.error("从模板生成CDKEY批次失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  验证用户兑换条件
  """
  def validate_user_eligibility(user_id, cdkey) do
    with {:ok, user} <- get_user(user_id),
         {:ok, template} <- get_template_by_cdkey(cdkey),
         :ok <- check_level_requirement(user, template),
         :ok <- check_vip_requirement(user, template),
         :ok <- check_registration_requirement(user, template),
         :ok <- check_usage_limit(user, template),
         :ok <- check_cooldown(user, template) do
      :ok
    else
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取模板统计信息
  """
  def get_template_statistics(template_id) do
    with {:ok, template} <- get_template(template_id),
         {:ok, batches} <- CdkeyBatch.by_template(template_id) do
      
      total_generated = Enum.sum(Enum.map(batches, & &1.total_count))
      total_used = Enum.sum(Enum.map(batches, & &1.used_count))
      total_expired = Enum.sum(Enum.map(batches, & &1.expired_count))
      
      {:ok, %{
        template: template,
        total_batches: length(batches),
        total_generated: total_generated,
        total_used: total_used,
        total_expired: total_expired,
        usage_rate: if(total_generated > 0, do: Float.round(total_used / total_generated * 100, 2), else: 0),
        remaining: total_generated - total_used - total_expired
      }}
    end
  end

  @doc """
  获取批次详细统计
  """
  def get_batch_statistics(batch_id) do
    with {:ok, batch} <- CdkeyBatch.read(batch_id),
         {:ok, updated_batch} <- CdkeyBatch.update_statistics(batch) do
      
      {:ok, cdkeys} = CdkeyActivity.by_batch(batch.batch_name)
      
      # 按状态分组统计
      status_stats = 
        cdkeys
        |> Enum.group_by(& &1.status)
        |> Enum.map(fn {status, codes} -> {status, length(codes)} end)
        |> Enum.into(%{})

      # 按时间统计使用情况
      daily_usage = get_daily_usage_stats(cdkeys)
      
      {:ok, %{
        batch: updated_batch,
        status_distribution: status_stats,
        daily_usage: daily_usage,
        top_users: get_top_users_by_usage(cdkeys)
      }}
    end
  end

  @doc """
  批量管理操作
  """
  def batch_operation(batch_id, operation, params \\ %{}) do
    case operation do
      :deactivate ->
        deactivate_batch(batch_id)
        
      :extend_validity ->
        extend_batch_validity(batch_id, params)
        
      :update_rewards ->
        update_batch_rewards(batch_id, params)
        
      :export ->
        export_batch_data(batch_id, params)
        
      _ ->
        {:error, :unsupported_operation}
    end
  end

  # 私有函数

  defp get_template(template_id) do
    case CdkeyTemplate.read(template_id) do
      {:ok, template} -> {:ok, template}
      _ -> {:error, :template_not_found}
    end
  end

  defp validate_template_status(template) do
    if template.status == :active do
      :ok
    else
      {:error, :template_inactive}
    end
  end

  defp build_batch_config(template, attrs) do
    count = Map.get(attrs, :count, 1)
    prefix = Map.get(attrs, :prefix, "")
    
    # 验证生成数量限制
    if template.max_generate_count && template.generated_count + count > template.max_generate_count do
      {:error, :exceed_generation_limit}
    else
      batch_name = generate_batch_name(template, prefix)
      valid_hours = get_in(template.generation_config, ["default_validity_hours"]) || 720
      
      {:ok, %{
        count: count,
        prefix: prefix,
        batch_name: batch_name,
        valid_from: DateTime.utc_now(),
        valid_to: DateTime.add(DateTime.utc_now(), valid_hours * 3600, :second),
        creator_id: Map.get(attrs, :creator_id),
        creator_name: Map.get(attrs, :creator_name),
        description: Map.get(attrs, :description, "")
      }}
    end
  end

  defp create_batch_record(template, batch_config) do
    CdkeyBatch.create(%{
      batch_name: batch_config.batch_name,
      template_id: template.id,
      template_name: template.name,
      description: batch_config.description,
      total_count: batch_config.count,
      valid_from: batch_config.valid_from,
      valid_to: batch_config.valid_to,
      creator_id: batch_config.creator_id,
      creator_name: batch_config.creator_name,
      generation_config: %{
        template_id: template.id,
        template_name: template.name,
        generated_at: DateTime.utc_now(),
        generation_params: batch_config
      }
    })
  end

  defp generate_cdkeys_for_batch(template, batch, batch_config) do
    reward_config = %{
      type: get_in(template.reward_config, ["type"]) |> String.to_atom(),
      amount: get_in(template.reward_config, ["amount"]) || 0,
      items: get_in(template.reward_config, ["items"]) || %{},
      max_uses: get_in(template.generation_config, ["max_uses"]) || 1,
      valid_from: batch_config.valid_from,
      valid_to: batch_config.valid_to,
      creator: batch_config.creator_name,
      purpose: "batch_#{batch.batch_name}"
    }

    if batch_config.prefix != "" do
      CdkeyService.generate_batch_with_prefix(
        batch_config.prefix,
        batch_config.count,
        reward_config
      )
    else
      CdkeyService.generate_codes(
        batch_config.batch_name,
        batch_config.count,
        reward_config
      )
    end
    |> case do
      results when is_list(results) ->
        successful_codes = Enum.filter(results, fn {status, _} -> status == :ok end)
        {:ok, Enum.map(successful_codes, fn {:ok, cdkey} -> cdkey end)}
        
      {:ok, %{codes: codes}} ->
        # 获取实际创建的CDKEY记录
        {:ok, cdkeys} = CdkeyActivity.by_batch(batch.batch_name)
        {:ok, cdkeys}
        
      error ->
        error
    end
  end

  defp update_template_statistics(template, generated_count) do
    CdkeyTemplate.update(template, %{
      generated_count: template.generated_count + generated_count,
      last_generated_at: DateTime.utc_now()
    })
  end

  defp generate_batch_name(template, prefix) do
    timestamp = DateTime.to_unix(DateTime.utc_now())
    base_name = "#{template.name}_#{timestamp}"
    
    if prefix != "" do
      "#{prefix}_#{base_name}"
    else
      base_name
    end
  end

  defp get_user(user_id) do
    case User.get_by_id(user_id) do
      {:ok, user} -> {:ok, user}
      _ -> {:error, :user_not_found}
    end
  end

  defp get_template_by_cdkey(cdkey) do
    # 从CDKEY的元数据或批次信息中获取模板
    case CdkeyBatch.by_batch_name(cdkey.batch_name) do
      {:ok, [batch | _]} ->
        get_template(batch.template_id)
      _ ->
        {:error, :template_not_found}
    end
  end

  defp check_level_requirement(user, template) do
    conditions = template.usage_conditions
    min_level = get_in(conditions, ["min_level"])
    max_level = get_in(conditions, ["max_level"])
    
    user_level = Map.get(user, :level, 1)
    
    cond do
      min_level && user_level < min_level ->
        {:error, :level_too_low}
        
      max_level && user_level > max_level ->
        {:error, :level_too_high}
        
      true ->
        :ok
    end
  end

  defp check_vip_requirement(user, template) do
    required_vip = get_in(template.usage_conditions, ["vip_level_required"])
    
    if required_vip do
      user_vip_level = Map.get(user, :vip_level, 0)
      if user_vip_level >= required_vip do
        :ok
      else
        {:error, :vip_level_insufficient}
      end
    else
      :ok
    end
  end

  defp check_registration_requirement(user, template) do
    conditions = template.usage_conditions
    min_days = get_in(conditions, ["registration_days_min"])
    max_days = get_in(conditions, ["registration_days_max"])
    
    if min_days || max_days do
      days_since_registration = 
        DateTime.diff(DateTime.utc_now(), user.inserted_at, :day)
      
      cond do
        min_days && days_since_registration < min_days ->
          {:error, :registration_too_recent}
          
        max_days && days_since_registration > max_days ->
          {:error, :registration_too_old}
          
        true ->
          :ok
      end
    else
      :ok
    end
  end

  defp check_usage_limit(user, template) do
    max_uses = get_in(template.usage_conditions, ["max_uses_per_user"]) || 1
    
    # 查询用户使用同类型模板的次数
    # TODO: 实现具体的查询逻辑
    :ok
  end

  defp check_cooldown(user, template) do
    cooldown_hours = get_in(template.usage_conditions, ["cooldown_hours"]) || 24
    
    # 查询用户最后使用同类型模板的时间
    # TODO: 实现具体的冷却时间检查
    :ok
  end

  defp get_daily_usage_stats(cdkeys) do
    cdkeys
    |> Enum.filter(fn c -> c.used_count > 0 end)
    |> Enum.group_by(fn c ->
      c.updated_at
      |> DateTime.to_date()
      |> Date.to_string()
    end)
    |> Enum.map(fn {date, codes} -> {date, length(codes)} end)
    |> Enum.sort()
  end

  defp get_top_users_by_usage(cdkeys) do
    cdkeys
    |> Enum.filter(fn c -> c.used_count > 0 end)
    |> Enum.group_by(& &1.used_by_user_id)
    |> Enum.map(fn {user_id, codes} -> 
      {user_id, length(codes)}
    end)
    |> Enum.sort_by(fn {_, count} -> count end, :desc)
    |> Enum.take(10)
  end

  defp deactivate_batch(batch_id) do
    with {:ok, batch} <- CdkeyBatch.read(batch_id) do
      CdkeyService.deactivate_batch(batch.batch_name)
      CdkeyBatch.update(batch, %{status: :inactive})
    end
  end

  defp extend_batch_validity(batch_id, params) do
    with {:ok, batch} <- CdkeyBatch.read(batch_id),
         {:ok, new_valid_to} <- parse_new_validity(params) do
      
      # 更新批次的有效期
      CdkeyBatch.update(batch, %{valid_to: new_valid_to})
      
      # 更新该批次下所有CDKEY的有效期
      {:ok, cdkeys} = CdkeyActivity.by_batch(batch.batch_name)
      
      Enum.each(cdkeys, fn cdkey ->
        CdkeyActivity.update(cdkey, %{valid_to: new_valid_to})
      end)
      
      {:ok, %{updated_count: length(cdkeys)}}
    end
  end

  defp update_batch_rewards(batch_id, params) do
    # TODO: 实现批次奖励更新逻辑
    {:error, :not_implemented}
  end

  defp export_batch_data(batch_id, params) do
    with {:ok, batch} <- CdkeyBatch.read(batch_id) do
      CdkeyService.export_codes(batch.batch_name)
    end
  end

  defp parse_new_validity(params) do
    case Map.get(params, :valid_to) do
      nil ->
        {:error, :missing_valid_to}
      
      datetime when is_binary(datetime) ->
        case DateTime.from_iso8601(datetime) do
          {:ok, dt, _} -> {:ok, dt}
          _ -> {:error, :invalid_datetime_format}
        end
        
      %DateTime{} = dt ->
        {:ok, dt}
        
      _ ->
        {:error, :invalid_datetime_format}
    end
  end
end