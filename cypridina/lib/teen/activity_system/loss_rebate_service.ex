defmodule Teen.ActivitySystem.LossRebateService do
  @moduledoc """
  损失返水服务

  处理用户游戏损失的返水计算和发放
  """

  alias Teen.ActivitySystem.{LossRebateJar, RewardClaimRecord, UserActivityParticipation}
  alias Cypridina.Accounts.User
  alias Teen.GameManagement.GameRecord
  alias Teen.VipSystem.UserVipInfo
  alias Phoenix.PubSub
  
  import Ecto.Query

  require Logger

  @doc """
  获取用户损失返水信息
  """
  def get_user_rebate_info(user_id) do
    today = Date.utc_today()
    yesterday = Date.add(today, -1)

    # 获取用户昨日游戏统计
    yesterday_stats = get_user_game_stats(user_id, yesterday)

    # 获取用户VIP等级和返水率
    vip_level =
      case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
        {:ok, info} -> info.vip_level
        _ -> 0
      end

    rebate_rate = get_rebate_rate(vip_level)

    # 计算可领取的返水金额
    rebate_amount = calculate_rebate_amount(yesterday_stats, rebate_rate)

    # 检查是否已经领取
    claimed = check_if_claimed(user_id, yesterday)

    %{
      date: yesterday,
      total_bet: yesterday_stats.total_bet,
      total_win: yesterday_stats.total_win,
      net_loss: yesterday_stats.net_loss,
      vip_level: vip_level,
      rebate_rate: rebate_rate,
      rebate_amount: rebate_amount,
      claimed: claimed,
      can_claim: rebate_amount > 0 and not claimed
    }
  end

  @doc """
  领取损失返水
  """
  def claim_rebate(user_id, date \\ nil) do
    target_date = date || Date.add(Date.utc_today(), -1)

    with {:ok, rebate_info} <- calculate_user_rebate(user_id, target_date),
         :ok <- validate_rebate_claim(user_id, target_date, rebate_info),
         {:ok, record} <- create_rebate_record(user_id, target_date, rebate_info),
         {:ok, _} <- distribute_rebate(user_id, rebate_info.rebate_amount) do
      # 发送通知
      notify_rebate_claimed(user_id, rebate_info.rebate_amount)

      Logger.info("损失返水领取成功: 用户=#{user_id}, 日期=#{target_date}, 金额=#{rebate_info.rebate_amount}")

      {:ok,
       %{
         amount: rebate_info.rebate_amount,
         date: target_date,
         record_id: record.id,
         message: "返水领取成功"
       }}
    else
      {:error, reason} = error ->
        error_message = format_error_message(reason)
        Logger.error("损失返水领取失败: 用户=#{user_id}, 日期=#{target_date}, 原因=#{inspect(reason)}")
        {:error, error_message}
    end
  end

  defp format_error_message(reason) do
    case reason do
      :no_rebate_available -> "暂无可领取的返水"
      :already_claimed -> "已经领取过了"
      :invalid_date -> "不能领取今日或未来的返水"
      :claim_expired -> "返水已过期，只能领取近7天内的返水"
      :loss_below_threshold -> "损失金额未达到最低要求"
      :insufficient_funds -> "系统资金不足"
      _ -> "系统错误，请稍后再试"
    end
  end

  defp notify_rebate_claimed(user_id, amount) do
    # 发送返水领取成功通知
    PubSub.broadcast(
      Cypridina.PubSub,
      "user:#{user_id}",
      {:rebate_claimed, %{amount: amount, type: :loss_rebate}}
    )
  end

  @doc """
  计算指定日期的返水
  """
  def calculate_user_rebate(user_id, date) do
    # 获取用户游戏统计
    stats = get_user_game_stats(user_id, date)

    # 获取VIP等级和返水率
    vip_level =
      case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
        {:ok, info} -> info.vip_level
        _ -> 0
      end

    rebate_rate = get_rebate_rate(vip_level)

    # 计算返水金额
    rebate_amount = calculate_rebate_amount(stats, rebate_rate)

    {:ok,
     %{
       date: date,
       vip_level: vip_level,
       rebate_rate: rebate_rate,
       total_bet: stats.total_bet,
       total_win: stats.total_win,
       net_loss: stats.net_loss,
       rebate_amount: rebate_amount
     }}
  end

  @doc """
  批量计算昨日返水（定时任务调用）
  """
  def calculate_daily_rebates(date \\ nil) do
    target_date = date || Date.add(Date.utc_today(), -1)

    Logger.info("开始计算 #{target_date} 的损失返水")

    try do
      # 检查是否有有效的返水配置
      case get_rebate_config() do
        {:ok, _config} ->
          process_daily_rebates(target_date)

        {:error, _} ->
          Logger.warning("无有效的返水配置，跳过计算")
          {:ok, 0}
      end
    rescue
      e ->
        Logger.error("计算每日返水失败: #{inspect(e)}")
        {:error, :calculation_failed}
    end
  end

  defp process_daily_rebates(date) do
    # 获取所有有损失的用户
    users_with_loss = get_users_with_loss(date)

    Logger.info("找到 #{length(users_with_loss)} 个用户有损失")

    # 并发处理用户返水
    results =
      users_with_loss
      |> Task.async_stream(
        fn user_id -> calculate_and_prepare_rebate(user_id, date) end,
        max_concurrency: 10,
        timeout: 30_000
      )
      |> Enum.to_list()

    # 统计结果
    {success_count, error_count} =
      Enum.reduce(results, {0, 0}, fn
        {:ok, {:ok, _}}, {s, e} -> {s + 1, e}
        {:ok, {:error, _}}, {s, e} -> {s, e + 1}
        {:exit, _}, {s, e} -> {s, e + 1}
      end)

    Logger.info("返水计算完成: 成功=#{success_count}, 失败=#{error_count}")

    # 发送通知给有返水的用户
    notify_users_with_available_rebate(users_with_loss)

    {:ok, success_count}
  end

  defp notify_users_with_available_rebate(user_ids) do
    Enum.each(user_ids, fn user_id ->
      PubSub.broadcast(
        Cypridina.PubSub,
        "user:#{user_id}",
        {:rebate_available, %{date: Date.add(Date.utc_today(), -1)}}
      )
    end)
  end

  @doc """
  获取返水排行榜
  """
  def get_rebate_ranking(limit \\ 10) do
    # 获取本月返水排行
    start_date = Date.beginning_of_month(Date.utc_today())
    end_date = Date.utc_today()

    case RewardClaimRecord.read() do
      {:ok, records} ->
        ranking =
          records
          |> Enum.filter(fn r ->
            r.activity_type == :loss_rebate and
              Date.compare(r.inserted_at, start_date) != :lt and
              Date.compare(r.inserted_at, end_date) != :gt
          end)
          |> Enum.group_by(& &1.user_id)
          |> Enum.map(fn {user_id, user_records} ->
            total =
              Enum.reduce(user_records, 0, fn r, acc ->
                acc + Decimal.to_integer(r.reward_amount)
              end)

            %{user_id: user_id, total_rebate: total}
          end)
          |> Enum.sort_by(& &1.total_rebate, :desc)
          |> Enum.take(limit)

        {:ok, ranking}

      _ ->
        {:ok, []}
    end
  end

  # 私有函数

  defp get_user_game_stats(user_id, date) do
    # 使用数据库聚合查询获取用户指定日期的游戏统计
    start_datetime = DateTime.new!(date, ~T[00:00:00])
    end_datetime = DateTime.new!(date, ~T[23:59:59])

    query =
      from g in GameRecord,
        where:
          g.user_id == ^user_id and
            g.completed_at >= ^start_datetime and
            g.completed_at <= ^end_datetime and
            not is_nil(g.completed_at),
        select: {
          sum(g.bet_amount),
          sum(g.win_amount),
          count(g.id)
        }

    case Cypridina.Repo.one(query) do
      {total_bet, total_win, game_count} when not is_nil(total_bet) ->
        total_bet = total_bet || Decimal.new("0")
        total_win = total_win || Decimal.new("0")
        net_loss = Decimal.sub(total_bet, total_win)

        %{
          total_bet: total_bet,
          total_win: total_win,
          net_loss: if(Decimal.compare(net_loss, 0) == :gt, do: net_loss, else: Decimal.new("0")),
          game_count: game_count || 0
        }

      _ ->
        Logger.info("用户 #{user_id} 在 #{date} 无游戏记录")

        %{
          total_bet: Decimal.new("0"),
          total_win: Decimal.new("0"),
          net_loss: Decimal.new("0"),
          game_count: 0
        }
    end
  rescue
    e ->
      Logger.error("获取用户游戏统计失败: 用户=#{user_id}, 日期=#{date}, 错误=#{inspect(e)}")

      %{
        total_bet: Decimal.new("0"),
        total_win: Decimal.new("0"),
        net_loss: Decimal.new("0"),
        game_count: 0
      }
  end

  defp get_rebate_rate(vip_level) do
    # 优先从配置中获取返水率，否则使用默认值
    case get_rebate_config() do
      {:ok, config} ->
        # 使用配置的返水率
        config.rebate_percentage

      {:error, _} ->
        # 使用VIP等级默认返水率（百分比）
        case vip_level do
          0 -> Decimal.new("0")
          1 -> Decimal.new("0.1")
          2 -> Decimal.new("0.2")
          3 -> Decimal.new("0.3")
          4 -> Decimal.new("0.4")
          5 -> Decimal.new("0.5")
          6 -> Decimal.new("0.6")
          7 -> Decimal.new("0.7")
          8 -> Decimal.new("0.8")
          9 -> Decimal.new("0.9")
          10 -> Decimal.new("1.0")
          _ -> Decimal.new("0")
        end
    end
  end

  defp get_rebate_config do
    case LossRebateJar.get_rebate_config() do
      {:ok, config} -> {:ok, config}
      _ -> {:error, :no_config}
    end
  end

  defp calculate_rebate_amount(stats, rebate_rate) do
    # 只有净损失才有返水
    if Decimal.compare(stats.net_loss, 0) == :gt do
      rebate_amount =
        stats.net_loss
        |> Decimal.mult(rebate_rate)
        |> Decimal.div(100)
        |> Decimal.round(0)

      # 应用返水上限
      apply_rebate_limit(rebate_amount)
    else
      Decimal.new(0)
    end
  end

  defp apply_rebate_limit(amount) do
    case get_rebate_config() do
      {:ok, config} ->
        if Decimal.compare(amount, config.max_rebate) == :gt do
          config.max_rebate
        else
          amount
        end

      _ ->
        # 默认上限 10000 分
        max_default = Decimal.new("10000")

        if Decimal.compare(amount, max_default) == :gt do
          max_default
        else
          amount
        end
    end
  end

  defp check_if_claimed(user_id, date) do
    # 检查是否已经领取过指定日期的返水
    case RewardClaimRecord.read() do
      {:ok, records} ->
        Enum.any?(records, fn r ->
          r.user_id == user_id and
            r.activity_type == :loss_rebate and
            Map.get(r.reward_data || %{}, "date") == Date.to_string(date)
        end)

      _ ->
        false
    end
  end

  defp validate_rebate_claim(user_id, date, rebate_info) do
    with :ok <- validate_rebate_amount(rebate_info.rebate_amount),
         :ok <- validate_claim_date(date),
         :ok <- validate_not_claimed(user_id, date),
         :ok <- validate_loss_threshold(rebate_info) do
      :ok
    end
  end

  defp validate_rebate_amount(amount) do
    if Decimal.compare(amount, 0) == :gt do
      :ok
    else
      {:error, :no_rebate_available}
    end
  end

  defp validate_claim_date(date) do
    today = Date.utc_today()
    yesterday = Date.add(today, -1)

    cond do
      Date.compare(date, today) != :lt ->
        {:error, :invalid_date}

      Date.compare(date, Date.add(today, -7)) == :lt ->
        {:error, :claim_expired}

      true ->
        :ok
    end
  end

  defp validate_not_claimed(user_id, date) do
    if check_if_claimed(user_id, date) do
      {:error, :already_claimed}
    else
      :ok
    end
  end

  defp validate_loss_threshold(rebate_info) do
    case get_rebate_config() do
      {:ok, config} ->
        if Decimal.compare(rebate_info.net_loss, config.loss_threshold) != :lt do
          :ok
        else
          {:error, :loss_below_threshold}
        end

      _ ->
        :ok
    end
  end

  defp create_rebate_record(user_id, date, rebate_info) do
    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: :loss_rebate,
      activity_id: nil,
      reward_type: :coins,
      reward_amount: rebate_info.rebate_amount,
      reward_data: %{
        "date" => Date.to_string(date),
        "vip_level" => rebate_info.vip_level,
        "rebate_rate" => Decimal.to_string(rebate_info.rebate_rate),
        "net_loss" => Decimal.to_string(rebate_info.net_loss),
        "total_bet" => Decimal.to_string(rebate_info.total_bet),
        "total_win" => Decimal.to_string(rebate_info.total_win)
      }
    })
  end

  defp distribute_rebate(user_id, amount) do
    amount_int = Decimal.to_integer(amount)

    if amount_int > 0 do
      Cypridina.Accounts.add_points(user_id, amount_int,
        transaction_type: :bonus,
        description: "loss_rebate"
      )
    else
      {:ok, :no_rebate}
    end
  end

  defp get_users_with_loss(date) do
    # 使用数据库聚合查询获取有损失的用户
    start_datetime = DateTime.new!(date, ~T[00:00:00])
    end_datetime = DateTime.new!(date, ~T[23:59:59])

    query =
      from g in GameRecord,
        where:
          g.completed_at >= ^start_datetime and
            g.completed_at <= ^end_datetime and
            not is_nil(g.completed_at),
        group_by: g.user_id,
        having: sum(g.bet_amount) > sum(g.win_amount),
        select: g.user_id

    try do
      Cypridina.Repo.all(query)
    rescue
      e ->
        Logger.error("获取有损失用户失败: 日期=#{date}, 错误=#{inspect(e)}")
        []
    end
  end

  defp calculate_and_prepare_rebate(user_id, date) do
    with {:ok, rebate_info} <- calculate_user_rebate(user_id, date),
         :ok <- validate_rebate_amount(rebate_info) do
      # 创建待领取的返水记录
      participation_data = %{
        date: Date.to_string(date),
        rebate_amount: rebate_info.rebate_amount,
        prepared_at: DateTime.utc_now()
      }

      case UserActivityParticipation.create(%{
             user_id: user_id,
             activity_type: :loss_rebate,
             activity_id: nil,
             progress: 0,
             status: :claimable,
             participation_data: participation_data
           }) do
        {:ok, _} ->
          {:ok, %{user_id: user_id, amount: rebate_info.rebate_amount}}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  defp validate_rebate_amount(rebate_info) do
    if Decimal.compare(rebate_info.rebate_amount, 0) == :gt do
      :ok
    else
      {:error, :no_loss}
    end
  end

  @doc """
  获取用户历史返水记录
  """
  def get_user_rebate_history(user_id, options \\ []) do
    start_date = Keyword.get(options, :start_date) || Date.add(Date.utc_today(), -30)
    end_date = Keyword.get(options, :end_date) || Date.utc_today()
    limit = Keyword.get(options, :limit, 50)

    try do
      query =
        from r in RewardClaimRecord,
          where:
            r.user_id == ^user_id and
              r.activity_type == :loss_rebate and
              r.inserted_at >= ^start_date and
              r.inserted_at <= ^end_date,
          order_by: [desc: r.inserted_at],
          limit: ^limit

      records = Cypridina.Repo.all(query)

      history =
        Enum.map(records, fn r ->
          %{
            id: r.id,
            date: Map.get(r.reward_data || %{}, "date"),
            amount: Decimal.to_integer(r.reward_amount),
            claimed_at: r.inserted_at,
            vip_level: Map.get(r.reward_data || %{}, "vip_level", 0),
            rebate_rate: Map.get(r.reward_data || %{}, "rebate_rate", "0"),
            net_loss: Map.get(r.reward_data || %{}, "net_loss", "0"),
            total_bet: Map.get(r.reward_data || %{}, "total_bet", "0")
          }
        end)

      {:ok, history}
    rescue
      e ->
        Logger.error("获取用户返水历史失败: 用户=#{user_id}, 错误=#{inspect(e)}")
        {:ok, []}
    end
  end

  @doc """
  获取用户返水统计
  """
  def get_user_rebate_stats(user_id, period \\ :current_month) do
    {start_date, end_date} = get_period_range(period)

    try do
      query =
        from r in RewardClaimRecord,
          where:
            r.user_id == ^user_id and
              r.activity_type == :loss_rebate and
              r.inserted_at >= ^start_date and
              r.inserted_at <= ^end_date,
          select: {
            count(r.id),
            sum(r.reward_amount)
          }

      case Cypridina.Repo.one(query) do
        {count, total_amount} ->
          {:ok,
           %{
             period: period,
             claim_count: count || 0,
             total_amount: Decimal.to_integer(total_amount || Decimal.new("0")),
             average_amount:
               if(count > 0,
                 do: Decimal.to_integer(Decimal.div(total_amount || Decimal.new("0"), count)),
                 else: 0
               )
           }}

        _ ->
          {:ok, %{period: period, claim_count: 0, total_amount: 0, average_amount: 0}}
      end
    rescue
      e ->
        Logger.error("获取用户返水统计失败: 用户=#{user_id}, 错误=#{inspect(e)}")
        {:ok, %{period: period, claim_count: 0, total_amount: 0, average_amount: 0}}
    end
  end

  defp get_period_range(:current_month) do
    today = Date.utc_today()
    start_date = Date.beginning_of_month(today)
    end_date = today
    {start_date, end_date}
  end

  defp get_period_range(:last_month) do
    today = Date.utc_today()
    last_month = Date.add(today, -30)
    start_date = Date.beginning_of_month(last_month)
    end_date = Date.end_of_month(last_month)
    {start_date, end_date}
  end

  defp get_period_range(:last_7_days) do
    today = Date.utc_today()
    start_date = Date.add(today, -7)
    {start_date, today}
  end
end
