defmodule Teen.ActivitySystem.DailyRefreshService do
  @moduledoc """
  每日刷新服务

  提供共享的业务逻辑函数，供各个资源的定时任务调用
  现在每个资源负责自己的定时任务调度，这个服务只提供通用的业务逻辑
  """

  require Logger

  @doc """
  格式化卡片类型显示名称
  """
  def format_card_type(:weekly_card), do: "周卡"
  def format_card_type(:monthly_card), do: "月卡"
  def format_card_type(:season_card), do: "季卡"
  def format_card_type(_), do: "卡片"

  @doc """
  计算奖励过期时间（默认24小时）
  """
  def calculate_reward_expiry(hours \\ 24) do
    DateTime.add(DateTime.utc_now(), hours, :hour)
  end

  @doc """
  记录刷新任务执行日志
  """
  def log_refresh_result(task_name, result) do
    case result do
      {:ok, data} ->
        Logger.info("✅ #{task_name}执行成功: #{inspect(data)}")

      {:error, reason} ->
        Logger.error("❌ #{task_name}执行失败: #{inspect(reason)}")
    end

    result
  end
end
