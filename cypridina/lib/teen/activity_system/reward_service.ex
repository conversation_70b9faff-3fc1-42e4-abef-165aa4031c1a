defmodule Teen.ActivitySystem.RewardService do
  @moduledoc """
  奖励服务

  统一管理所有奖励的发放和领取逻辑，包括：
  - 自动发放奖励（如游戏胜利奖励）
  - 创建待领取奖励（如月卡、任务奖励）
  - 处理奖励领取
  - 奖励过期处理
  """

  require Logger
  alias Teen.ActivitySystem.{UserReward, UserMonthlyCard, PiggyBank}
  alias Teen.PaymentSystem.UserAccount

  @doc """
  创建自动发放奖励
  """
  def create_auto_reward(user_id, reward_type, source_type, amount, opts \\ []) do
    reward_data = %{
      user_id: user_id,
      reward_type: reward_type,
      source_type: source_type,
      source_id: opts[:source_id],
      reward_amount: amount,
      reward_data: opts[:reward_data],
      description: opts[:description],
      is_pending: false
    }

    case UserReward.create(reward_data) do
      {:ok, reward} ->
        # 自动发放到用户账户
        case distribute_reward_to_account(user_id, reward_type, amount) do
          {:ok, _} ->
            Logger.info("自动发放奖励成功 - 用户: #{user_id}, 类型: #{reward_type}, 金额: #{amount}")
            {:ok, reward}

          {:error, reason} ->
            Logger.error("自动发放奖励失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
            {:error, reason}
        end

      {:error, reason} ->
        Logger.error("创建自动奖励记录失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  创建待领取奖励
  """
  def create_pending_reward(user_id, reward_type, source_type, amount, opts \\ []) do
    reward_data = %{
      user_id: user_id,
      reward_type: reward_type,
      source_type: source_type,
      source_id: opts[:source_id],
      reward_amount: amount,
      reward_data: opts[:reward_data],
      description: opts[:description],
      expires_at: opts[:expires_at],
      is_pending: true
    }

    case UserReward.create(reward_data) do
      {:ok, reward} ->
        Logger.info("创建待领取奖励成功 - 用户: #{user_id}, 类型: #{reward_type}, 金额: #{amount}")
        {:ok, reward}

      {:error, reason} ->
        Logger.error("创建待领取奖励失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  用户领取奖励
  """
  def claim_reward(user_id, reward_id) do
    case UserReward.read(reward_id) do
      {:ok, reward} ->
        if reward.user_id == user_id and reward.status == :pending and reward.is_pending do
          # 检查是否过期
          if is_reward_expired?(reward) do
            {:error, :reward_expired}
          else
            # 发放奖励到用户账户
            case distribute_reward_to_account(user_id, reward.reward_type, reward.reward_amount) do
              {:ok, _} ->
                # 更新奖励状态为已领取
                case UserReward.claim_reward(reward) do
                  {:ok, updated_reward} ->
                    Logger.info("用户领取奖励成功 - 用户: #{user_id}, 奖励ID: #{reward_id}")
                    {:ok, updated_reward}

                  {:error, reason} ->
                    Logger.error("更新奖励状态失败 - 奖励ID: #{reward_id}, 原因: #{inspect(reason)}")
                    {:error, reason}
                end

              {:error, reason} ->
                Logger.error("发放奖励到账户失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
                {:error, reason}
            end
          end
        else
          {:error, :invalid_reward}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量领取用户的所有待领取奖励
  """
  def claim_all_pending_rewards(user_id) do
    case UserReward.list_pending_rewards(%{user_id: user_id}) do
      {:ok, rewards} ->
        results =
          Enum.map(rewards, fn reward ->
            claim_reward(user_id, reward.id)
          end)

        successful = Enum.count(results, fn {status, _} -> status == :ok end)
        total = length(results)

        Logger.info("批量领取奖励完成 - 用户: #{user_id}, 成功: #{successful}/#{total}")
        {:ok, %{successful: successful, total: total, results: results}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户待领取奖励列表
  """
  def get_pending_rewards(user_id) do
    UserReward.list_pending_rewards(%{user_id: user_id})
  end

  @doc """
  获取用户奖励统计
  """
  def get_user_reward_stats(user_id, start_date \\ nil, end_date \\ nil) do
    case UserReward.get_user_total_rewards(%{
           user_id: user_id,
           start_date: start_date,
           end_date: end_date
         }) do
      {:ok, rewards} ->
        stats = %{
          total_rewards: length(rewards),
          total_amount:
            Enum.reduce(rewards, Decimal.new("0"), fn reward, acc ->
              Decimal.add(acc, reward.reward_amount)
            end),
          pending_count: Enum.count(rewards, &(&1.status == :pending and &1.is_pending)),
          claimed_count: Enum.count(rewards, &(&1.status == :claimed)),
          auto_distributed_count:
            Enum.count(rewards, &(&1.status == :distributed and not &1.is_pending))
        }

        {:ok, stats}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  处理过期奖励
  """
  def expire_old_rewards do
    # 这个函数会被定时任务调用
    # 查找所有过期的待领取奖励并标记为过期
    Logger.info("开始处理过期奖励...")

    # 实现逻辑：查找过期的奖励并更新状态
    # 这里需要实现具体的过期处理逻辑

    :ok
  end

  # 私有函数

  defp distribute_reward_to_account(user_id, reward_type, amount) do
    case reward_type do
      :coins ->
        # 发放游戏币到用户账户
        UserAccount.add_coins(user_id, amount)

      :cash ->
        # 发放现金到用户账户
        UserAccount.add_balance(user_id, amount)

      :points ->
        # 发放积分（如果有积分系统）
        {:ok, :points_added}

      :vip_exp ->
        # 发放VIP经验（如果有VIP系统）
        {:ok, :vip_exp_added}

      _ ->
        Logger.warn("未知的奖励类型: #{reward_type}")
        {:error, :unknown_reward_type}
    end
  end

  defp is_reward_expired?(reward) do
    case reward.expires_at do
      nil -> false
      expires_at -> DateTime.compare(expires_at, DateTime.utc_now()) == :lt
    end
  end
end
