defmodule Teen.ActivitySystem.FreeBonusConfig do
  @moduledoc """
  免费积分任务配置管理
  
  提供任务配置的集中管理和动态调整：
  - 任务模板系统
  - 难度调节
  - 奖励平衡
  - A/B测试支持
  - 实时配置更新
  """

  require Logger

  @default_config %{
    # 基础任务配置
    base_tasks: %{
      novice: %{
        share_easy: %{share_count: 1, reward: 50, difficulty: :easy},
        game_intro: %{win_coins: 100, reward: 30, difficulty: :easy},
        first_withdrawal: %{withdraw_count: 1, reward: 100, difficulty: :medium}
      },
      daily: %{
        share_daily: %{share_count: 3, reward: 80, difficulty: :medium},
        game_daily: %{win_coins: 500, reward: 120, difficulty: :medium},
        social_butterfly: %{share_count: 5, reward: 200, difficulty: :hard}
      },
      advanced: %{
        game_master: %{win_coins: 5000, reward: 500, difficulty: :hard},
        withdrawal_expert: %{withdraw_count: 3, reward: 800, difficulty: :hard},
        completionist: %{share_count: 10, win_coins: 10000, withdraw_count: 5, reward: 2000, difficulty: :expert}
      }
    },
    
    # 奖励倍数配置
    reward_multipliers: %{
      new_user: 1.5,      # 新用户奖励倍数
      vip_1: 1.1,         # VIP1 奖励倍数
      vip_2: 1.2,         # VIP2 奖励倍数  
      vip_3: 1.3,         # VIP3 奖励倍数
      weekend: 1.2,       # 周末奖励倍数
      holiday: 1.5        # 节假日奖励倍数
    },
    
    # 任务解锁条件
    unlock_conditions: %{
      daily: %{completed_novice_tasks: 2},
      advanced: %{completed_daily_tasks: 5, user_level: 3},
      expert: %{completed_advanced_tasks: 3, total_win_coins: 50000}
    },
    
    # 任务重置配置
    reset_schedule: %{
      daily_reset_time: "00:00:00",
      weekly_reset_day: :monday,
      monthly_reset_day: 1
    },
    
    # 特殊事件配置
    special_events: %{
      double_reward_weekends: true,
      bonus_share_events: %{
        duration_hours: 24,
        reward_multiplier: 2.0
      },
      seasonal_tasks: %{
        spring_festival: %{additional_reward: 500},
        independence_day: %{additional_reward: 300}
      }
    }
  }

  @doc """
  获取当前任务配置
  """
  def get_config do
    # 从数据库或缓存获取配置，如果不存在则使用默认配置
    case get_stored_config() do
      {:ok, config} -> config
      {:error, _} -> @default_config
    end
  end

  @doc """
  获取特定类型的任务配置
  """
  def get_task_config(task_type) do
    config = get_config()
    get_in(config, [:base_tasks, task_type]) || %{}
  end

  @doc """
  获取奖励倍数
  """
  def get_reward_multiplier(user_id, context \\ %{}) do
    config = get_config()
    base_multiplier = 1.0
    
    # 新用户检查
    base_multiplier = if is_new_user?(user_id) do
      base_multiplier * config.reward_multipliers.new_user
    else
      base_multiplier
    end
    
    # VIP等级检查
    base_multiplier = case get_user_vip_level(user_id) do
      level when level > 0 ->
        vip_key = String.to_atom("vip_#{level}")
        multiplier = Map.get(config.reward_multipliers, vip_key, 1.0)
        base_multiplier * multiplier
      _ -> base_multiplier
    end
    
    # 时间相关加成
    base_multiplier = apply_time_multipliers(base_multiplier, config, context)
    
    # 特殊事件加成
    base_multiplier = apply_special_event_multipliers(base_multiplier, config, context)
    
    base_multiplier
  end

  @doc """
  检查任务是否解锁
  """
  def is_task_unlocked?(task_type, user_id) do
    config = get_config()
    unlock_conditions = get_in(config, [:unlock_conditions, task_type])
    
    case unlock_conditions do
      nil -> true  # 没有解锁条件，默认解锁
      conditions -> check_unlock_conditions(conditions, user_id)
    end
  end

  @doc """
  生成用户特定的任务配置
  """
  def generate_user_tasks(user_id) do
    config = get_config()
    user_profile = get_user_profile(user_id)
    
    tasks = []
    
    # 新手任务
    tasks = if user_profile.is_new_user do
      tasks ++ generate_novice_tasks(config, user_profile)
    else
      tasks
    end
    
    # 每日任务
    tasks = if is_task_unlocked?(:daily, user_id) do
      tasks ++ generate_daily_tasks(config, user_profile)
    else
      tasks
    end
    
    # 进阶任务
    tasks = if is_task_unlocked?(:advanced, user_id) do
      tasks ++ generate_advanced_tasks(config, user_profile)
    else
      tasks
    end
    
    # 应用个性化调整
    tasks
    |> apply_difficulty_adjustment(user_profile)
    |> apply_reward_adjustment(user_id)
  end

  @doc """
  更新任务配置
  """
  def update_config(new_config) do
    # 验证配置
    case validate_config(new_config) do
      :ok ->
        store_config(new_config)
        Logger.info("Free bonus task config updated successfully")
        {:ok, new_config}
      
      {:error, reason} ->
        Logger.error("Failed to update config: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  执行A/B测试配置
  """
  def apply_ab_test_config(user_id, test_name) do
    test_group = determine_test_group(user_id, test_name)
    
    case test_group do
      :control -> get_config()
      :variant_a -> get_ab_test_config(test_name, :variant_a)
      :variant_b -> get_ab_test_config(test_name, :variant_b)
      _ -> get_config()
    end
  end

  # 私有函数

  defp get_stored_config do
    # TODO: 从数据库或Redis获取配置
    {:error, :not_implemented}
  end

  defp store_config(_config) do
    # TODO: 存储配置到数据库或Redis
    :ok
  end

  defp validate_config(_config) do
    # TODO: 验证配置的完整性和正确性
    :ok
  end

  defp is_new_user?(user_id) do
    # TODO: 检查用户是否为新用户（注册7天内）
    case Teen.Accounts.User.get_by_id(user_id) do
      {:ok, user} ->
        days_since_registration = DateTime.diff(DateTime.utc_now(), user.inserted_at, :day)
        days_since_registration <= 7
      _ -> false
    end
  end

  defp get_user_vip_level(user_id) do
    case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
      {:ok, vip_info} -> vip_info.vip_level
      _ -> 0
    end
  end

  defp apply_time_multipliers(base_multiplier, config, context) do
    now = Map.get(context, :current_time, DateTime.utc_now())
    
    # 周末加成
    multiplier = if Date.day_of_week(DateTime.to_date(now)) in [6, 7] and 
                    config.special_events.double_reward_weekends do
      base_multiplier * config.reward_multipliers.weekend
    else
      base_multiplier
    end
    
    # 节假日加成（需要节假日检查逻辑）
    if is_holiday?(now) do
      multiplier * config.reward_multipliers.holiday
    else
      multiplier
    end
  end

  defp apply_special_event_multipliers(base_multiplier, config, context) do
    event_type = Map.get(context, :event_type)
    
    case event_type do
      :share_event ->
        event_config = config.special_events.bonus_share_events
        base_multiplier * event_config.reward_multiplier
      _ -> base_multiplier
    end
  end

  defp check_unlock_conditions(conditions, user_id) do
    user_stats = get_user_activity_stats(user_id)
    
    Enum.all?(conditions, fn {key, required_value} ->
      current_value = Map.get(user_stats, key, 0)
      current_value >= required_value
    end)
  end

  defp get_user_profile(user_id) do
    %{
      is_new_user: is_new_user?(user_id),
      vip_level: get_user_vip_level(user_id),
      activity_level: get_user_activity_level(user_id),
      preferred_games: get_user_preferred_games(user_id),
      completion_rate: get_user_task_completion_rate(user_id)
    }
  end

  defp generate_novice_tasks(config, _user_profile) do
    novice_config = config.base_tasks.novice
    
    [
      create_task_from_config("新手分享", novice_config.share_easy),
      create_task_from_config("游戏入门", novice_config.game_intro),
      create_task_from_config("首次提现", novice_config.first_withdrawal)
    ]
  end

  defp generate_daily_tasks(config, user_profile) do
    daily_config = config.base_tasks.daily
    
    tasks = [
      create_task_from_config("每日分享", daily_config.share_daily),
      create_task_from_config("每日游戏", daily_config.game_daily)
    ]
    
    # 根据用户活跃度添加额外任务
    if user_profile.activity_level >= :medium do
      tasks ++ [create_task_from_config("社交达人", daily_config.social_butterfly)]
    else
      tasks
    end
  end

  defp generate_advanced_tasks(config, user_profile) do
    advanced_config = config.base_tasks.advanced
    
    tasks = []
    
    # 根据用户游戏偏好生成任务
    tasks = if :gaming in user_profile.preferred_games do
      tasks ++ [create_task_from_config("游戏大师", advanced_config.game_master)]
    else
      tasks
    end
    
    # 根据提现历史生成任务
    tasks = if user_profile.completion_rate >= 0.8 do
      tasks ++ [create_task_from_config("提现专家", advanced_config.withdrawal_expert)]
    else
      tasks
    end
    
    # 综合任务
    tasks ++ [create_task_from_config("完美玩家", advanced_config.completionist)]
  end

  defp create_task_from_config(title, config) do
    %{
      title: title,
      share_count: Map.get(config, :share_count, 0),
      required_win_coins: Map.get(config, :win_coins, 0),
      withdraw_count: Map.get(config, :withdraw_count, 0),
      reward_amount: Map.get(config, :reward, 0),
      difficulty: Map.get(config, :difficulty, :medium)
    }
  end

  defp apply_difficulty_adjustment(tasks, user_profile) do
    # 根据用户完成率调整任务难度
    adjustment_factor = case user_profile.completion_rate do
      rate when rate >= 0.9 -> 1.2  # 增加20%难度
      rate when rate >= 0.7 -> 1.0  # 保持原难度
      rate when rate >= 0.5 -> 0.8  # 降低20%难度
      _ -> 0.6  # 大幅降低难度
    end
    
    Enum.map(tasks, fn task ->
      task
      |> Map.update(:share_count, 0, &max(1, round(&1 * adjustment_factor)))
      |> Map.update(:required_win_coins, 0, &max(100, round(&1 * adjustment_factor)))
      |> Map.update(:withdraw_count, 0, &max(1, round(&1 * adjustment_factor)))
    end)
  end

  defp apply_reward_adjustment(tasks, user_id) do
    multiplier = get_reward_multiplier(user_id)
    
    Enum.map(tasks, fn task ->
      Map.update(task, :reward_amount, 0, &round(&1 * multiplier))
    end)
  end

  # 存根函数，需要具体实现
  defp is_holiday?(_datetime), do: false
  defp get_user_activity_stats(_user_id), do: %{}
  defp get_user_activity_level(_user_id), do: :medium
  defp get_user_preferred_games(_user_id), do: [:gaming, :social]
  defp get_user_task_completion_rate(_user_id), do: 0.7
  defp determine_test_group(_user_id, _test_name), do: :control
  defp get_ab_test_config(_test_name, _variant), do: get_config()
end