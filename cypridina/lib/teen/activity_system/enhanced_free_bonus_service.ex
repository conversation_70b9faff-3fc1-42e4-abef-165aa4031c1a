defmodule Teen.ActivitySystem.EnhancedFreeBonusService do
  @moduledoc """
  增强版免费积分任务服务
  
  提供更完善的任务进度跟踪和奖励系统：
  - 实时进度更新
  - 任务链系统
  - 成就解锁
  - 奖励优化
  - 用户体验提升
  """

  require Logger
  alias Teen.ActivitySystem.{FreeBonusTask, UserActivityParticipation}

  @doc """
  获取用户免费积分任务数据（增强版）
  """
  def get_enhanced_free_bonus_data(user_id) do
    try do
      case FreeBonusTask.list_active_tasks() do
        {:ok, tasks} when length(tasks) > 0 ->
          # 获取用户进度数据
          user_progress = get_user_comprehensive_progress(user_id)
          
          # 构建增强的任务数据
          task_data = build_enhanced_task_data(tasks, user_progress, user_id)
          
          # 计算统计信息
          stats = calculate_user_stats(tasks, user_progress)
          
          {:ok, %{
            bonustask: task_data,
            stats: stats,
            recommendations: get_task_recommendations(tasks, user_progress),
            next_rewards: get_next_available_rewards(tasks, user_progress)
          }}

        {:ok, []} ->
          {:ok, build_default_enhanced_data()}

        {:error, reason} ->
          Logger.error("Failed to get enhanced free bonus tasks: #{inspect(reason)}")
          {:ok, build_default_enhanced_data()}
      end
    rescue
      e ->
        Logger.error("Exception getting enhanced free bonus data: #{inspect(e)}")
        {:ok, build_default_enhanced_data()}
    end
  end

  @doc """
  更新用户任务进度（增强版）
  """
  def update_task_progress(user_id, event_type, event_data) do
    case event_type do
      :user_share ->
        update_share_progress(user_id, event_data)
      
      :game_win ->
        update_game_progress(user_id, event_data)
      
      :withdrawal_completed ->
        update_withdrawal_progress(user_id, event_data)
      
      _ ->
        Logger.debug("Unknown event type for free bonus tasks: #{event_type}")
        :ok
    end
  end

  @doc """
  检查并解锁新任务
  """
  def check_task_unlocks(user_id) do
    user_progress = get_user_comprehensive_progress(user_id)
    
    case FreeBonusTask.list_active_tasks() do
      {:ok, tasks} ->
        unlocked_tasks = check_unlock_conditions(tasks, user_progress)
        
        if length(unlocked_tasks) > 0 do
          Logger.info("User #{user_id} unlocked #{length(unlocked_tasks)} new tasks")
          notify_task_unlocks(user_id, unlocked_tasks)
        end
        
        {:ok, unlocked_tasks}
      
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  处理任务奖励领取（增强版）
  """
  def process_enhanced_bonus_claim(user_id, data) do
    task_id = Map.get(data, "task_id")
    
    try do
      case FreeBonusTask.read(task_id) do
        {:ok, task} ->
          # 验证任务完成状态
          case verify_task_completion(user_id, task) do
            {:ok, :completed} ->
              # 检查是否已领取
              case check_reward_claimed(user_id, task.id) do
                false ->
                  # 计算奖励（可能有加成）
                  final_reward = calculate_final_reward(user_id, task)
                  
                  # 发放奖励
                  case distribute_enhanced_reward(user_id, task, final_reward) do
                    {:ok, _} ->
                      # 记录领取
                      record_enhanced_claim(user_id, task, final_reward)
                      
                      # 检查任务链解锁
                      check_task_unlocks(user_id)
                      
                      {:ok, %{
                        code: 0, 
                        fetchaward: Decimal.to_integer(final_reward), 
                        bonus_multiplier: calculate_bonus_multiplier(user_id),
                        msg: "任务奖励领取成功",
                        next_task: get_next_recommended_task(user_id, task)
                      }}
                    
                    {:error, reason} ->
                      {:error, %{code: 1, msg: "奖励发放失败: #{inspect(reason)}"}}
                  end
                
                true ->
                  {:error, %{code: 1, msg: "任务奖励已领取"}}
              end
            
            {:error, reason} ->
              {:error, %{code: 1, msg: reason}}
          end
        
        {:error, _} ->
          {:error, %{code: 1, msg: "任务配置不存在"}}
      end
    rescue
      e ->
        Logger.error("Exception processing enhanced bonus claim: #{inspect(e)}")
        {:error, %{code: 1, msg: "系统错误"}}
    end
  end

  # 私有函数

  defp get_user_comprehensive_progress(user_id) do
    base_progress = get_basic_user_progress(user_id)
    
    %{
      shares: Map.get(base_progress, :shares, 0),
      total_win_coins: Map.get(base_progress, :total_win_coins, 0),
      withdrawals: Map.get(base_progress, :withdrawals, 0),
      game_wins: get_user_game_wins(user_id),
      completed_tasks: get_user_completed_tasks(user_id),
      claim_history: get_user_claim_history(user_id),
      achievement_level: calculate_user_achievement_level(user_id)
    }
  end

  defp build_enhanced_task_data(tasks, user_progress, user_id) do
    tasks
    |> Enum.with_index()
    |> Enum.map(fn {task, index} ->
      progress = calculate_task_progress(task, user_progress)
      completion_status = calculate_completion_status(task, progress)
      
      task_key = "task_#{task.id}"
      
      {task_key, %{
        "id" => task.id,
        "title" => task.title,
        "description" => generate_task_description(task),
        "category" => categorize_task(task),
        "difficulty" => calculate_task_difficulty(task),
        "progress" => progress,
        "completion_status" => completion_status,
        "reward" => Decimal.to_integer(task.reward_amount),
        "bonus_multiplier" => calculate_task_bonus_multiplier(user_id, task),
        "estimated_time" => estimate_completion_time(task, progress),
        "prerequisites" => get_task_prerequisites(task),
        "unlocked" => is_task_unlocked(task, user_progress),
        "status" => if(task.status == :enabled, do: 1, else: 0)
      }}
    end)
    |> Enum.into(%{})
  end

  defp calculate_task_progress(task, user_progress) do
    %{
      "shares" => %{
        "current" => min(user_progress.shares, task.share_count),
        "required" => task.share_count,
        "percentage" => calculate_percentage(user_progress.shares, task.share_count)
      },
      "win_coins" => %{
        "current" => min(user_progress.total_win_coins, Decimal.to_integer(task.required_win_coins)),
        "required" => Decimal.to_integer(task.required_win_coins),
        "percentage" => calculate_percentage(user_progress.total_win_coins, Decimal.to_integer(task.required_win_coins))
      },
      "withdrawals" => %{
        "current" => min(user_progress.withdrawals, task.withdraw_count),
        "required" => task.withdraw_count,
        "percentage" => calculate_percentage(user_progress.withdrawals, task.withdraw_count)
      },
      "overall_percentage" => calculate_overall_progress(task, user_progress)
    }
  end

  defp calculate_percentage(current, required) when required > 0 do
    min(100, round(current / required * 100))
  end
  defp calculate_percentage(_, 0), do: 100

  defp calculate_overall_progress(task, user_progress) do
    total_requirements = 0
    completed_requirements = 0
    
    # 分享要求
    if task.share_count > 0 do
      total_requirements = total_requirements + 1
      if user_progress.shares >= task.share_count do
        completed_requirements = completed_requirements + 1
      end
    end
    
    # 金币要求
    required_coins = Decimal.to_integer(task.required_win_coins)
    if required_coins > 0 do
      total_requirements = total_requirements + 1
      if user_progress.total_win_coins >= required_coins do
        completed_requirements = completed_requirements + 1
      end
    end
    
    # 提现要求
    if task.withdraw_count > 0 do
      total_requirements = total_requirements + 1
      if user_progress.withdrawals >= task.withdraw_count do
        completed_requirements = completed_requirements + 1
      end
    end
    
    if total_requirements > 0 do
      round(completed_requirements / total_requirements * 100)
    else
      100
    end
  end

  defp calculate_completion_status(task, progress) do
    overall_percentage = progress["overall_percentage"]
    
    cond do
      overall_percentage >= 100 -> "completed"
      overall_percentage >= 80 -> "almost_done"
      overall_percentage >= 50 -> "in_progress"
      overall_percentage >= 25 -> "started"
      true -> "not_started"
    end
  end

  defp generate_task_description(task) do
    parts = []
    
    parts = if task.share_count > 0 do
      parts ++ ["分享#{task.share_count}次"]
    else
      parts
    end
    
    parts = if Decimal.to_integer(task.required_win_coins) > 0 do
      parts ++ ["赢取#{Decimal.to_integer(task.required_win_coins)}金币"]
    else
      parts
    end
    
    parts = if task.withdraw_count > 0 do
      parts ++ ["完成#{task.withdraw_count}次提现"]
    else
      parts
    end
    
    game_desc = if task.game_id && task.game_id != "all" do
      "在#{task.game_name || task.game_id}游戏中"
    else
      ""
    end
    
    "#{game_desc}#{Enum.join(parts, "、")}"
  end

  defp categorize_task(task) do
    cond do
      task.share_count > 0 && Decimal.to_integer(task.required_win_coins) == 0 && task.withdraw_count == 0 ->
        "social"
      
      task.share_count == 0 && Decimal.to_integer(task.required_win_coins) > 0 && task.withdraw_count == 0 ->
        "gaming"
      
      task.share_count == 0 && Decimal.to_integer(task.required_win_coins) == 0 && task.withdraw_count > 0 ->
        "financial"
      
      true ->
        "comprehensive"
    end
  end

  defp calculate_task_difficulty(task) do
    score = 0
    
    # 分享难度
    score = score + min(task.share_count * 10, 50)
    
    # 金币难度
    win_coins = Decimal.to_integer(task.required_win_coins)
    score = score + min(div(win_coins, 100), 50)
    
    # 提现难度
    score = score + task.withdraw_count * 30
    
    cond do
      score <= 30 -> "easy"
      score <= 60 -> "medium"
      score <= 90 -> "hard"
      true -> "expert"
    end
  end

  defp calculate_user_stats(tasks, user_progress) do
    total_tasks = length(tasks)
    completed_tasks = length(user_progress.completed_tasks)
    
    %{
      "total_tasks" => total_tasks,
      "completed_tasks" => completed_tasks,
      "completion_rate" => if(total_tasks > 0, do: round(completed_tasks / total_tasks * 100), else: 0),
      "total_rewards_earned" => calculate_total_rewards_earned(user_progress.claim_history),
      "achievement_level" => user_progress.achievement_level,
      "next_milestone" => calculate_next_milestone(completed_tasks)
    }
  end

  defp get_task_recommendations(tasks, user_progress) do
    tasks
    |> Enum.filter(fn task -> 
      not Enum.member?(user_progress.completed_tasks, task.id) &&
      is_task_unlocked(task, user_progress)
    end)
    |> Enum.sort_by(&calculate_recommendation_score(&1, user_progress), :desc)
    |> Enum.take(3)
    |> Enum.map(fn task ->
      %{
        "id" => task.id,
        "title" => task.title,
        "reason" => generate_recommendation_reason(task, user_progress),
        "estimated_time" => estimate_completion_time(task, %{}),
        "reward" => Decimal.to_integer(task.reward_amount)
      }
    end)
  end

  defp get_next_available_rewards(tasks, user_progress) do
    tasks
    |> Enum.filter(fn task ->
      not Enum.member?(user_progress.completed_tasks, task.id) &&
      is_task_nearly_complete(task, user_progress)
    end)
    |> Enum.sort_by(&Decimal.to_integer(&1.reward_amount), :desc)
    |> Enum.take(3)
    |> Enum.map(fn task ->
      progress = calculate_task_progress(task, user_progress)
      %{
        "id" => task.id,
        "title" => task.title,
        "reward" => Decimal.to_integer(task.reward_amount),
        "progress_percentage" => progress["overall_percentage"],
        "missing_requirements" => calculate_missing_requirements(task, user_progress)
      }
    end)
  end

  # 更多辅助函数...
  
  defp build_default_enhanced_data do
    %{
      bonustask: %{},
      stats: %{
        "total_tasks" => 0,
        "completed_tasks" => 0,
        "completion_rate" => 0,
        "total_rewards_earned" => 0,
        "achievement_level" => 1
      },
      recommendations: [],
      next_rewards: []
    }
  end

  # 实现其他私有函数的存根
  defp get_basic_user_progress(_user_id), do: %{}
  defp get_user_game_wins(_user_id), do: []
  defp get_user_completed_tasks(_user_id), do: []
  defp get_user_claim_history(_user_id), do: []
  defp calculate_user_achievement_level(_user_id), do: 1
  defp calculate_task_bonus_multiplier(_user_id, _task), do: 1.0
  defp estimate_completion_time(_task, _progress), do: "1-2小时"
  defp get_task_prerequisites(_task), do: []
  defp is_task_unlocked(_task, _user_progress), do: true
  defp check_unlock_conditions(_tasks, _user_progress), do: []
  defp notify_task_unlocks(_user_id, _tasks), do: :ok
  defp verify_task_completion(_user_id, _task), do: {:ok, :completed}
  defp check_reward_claimed(_user_id, _task_id), do: false
  defp calculate_final_reward(_user_id, task), do: task.reward_amount
  defp distribute_enhanced_reward(_user_id, _task, _amount), do: {:ok, :distributed}
  defp record_enhanced_claim(_user_id, _task, _amount), do: :ok
  defp calculate_bonus_multiplier(_user_id), do: 1.0
  defp get_next_recommended_task(_user_id, _task), do: nil
  defp calculate_total_rewards_earned(_claim_history), do: 0
  defp calculate_next_milestone(_completed_tasks), do: 5
  defp calculate_recommendation_score(_task, _user_progress), do: 1
  defp generate_recommendation_reason(_task, _user_progress), do: "推荐完成"
  defp is_task_nearly_complete(_task, _user_progress), do: false
  defp calculate_missing_requirements(_task, _user_progress), do: []

  defp update_share_progress(_user_id, _event_data), do: :ok
  defp update_game_progress(_user_id, _event_data), do: :ok  
  defp update_withdrawal_progress(_user_id, _event_data), do: :ok
end