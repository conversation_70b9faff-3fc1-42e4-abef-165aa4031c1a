defmodule Teen.ActivitySystem.ActivityService do
  @moduledoc """
  活动系统业务逻辑服务

  提供各种活动的业务逻辑处理，包括：
  - 奖励计算
  - 条件判断
  - 状态管理
  - 活动参与
  - 奖励发放
  """

  alias Teen.ActivitySystem.{
    GameTask,
    WeeklyCard,
    SevenDayTask,
    VipGift,
    RechargeTask,
    RechargeWheel,
    WheelPrizeConfig,
    ScratchCardActivity,
    FirstRechargeGift,
    LossRebateJar,
    InviteCashActivity,
    BindingReward,
    FreeBonusTask,
    CdkeyActivity,
    UserActivityParticipation,
    RewardClaimRecord,
    SignInActivity,
    UserReward
  }

  alias Cypridina.Accounts.User
  alias Teen.ActivitySystem.{ScratchCardService, LossRebateService}

  require Logger

  @activity_types [
    :game_task,
    :seven_day_task,
    :weekly_card,
    :vip_gift,
    :recharge_task,
    :recharge_wheel,
    :scratch_card,
    :first_recharge_gift,
    :loss_rebate,
    :invite_cash,
    :binding_reward,
    :free_bonus_task,
    :cdkey
  ]

  @doc """
  获取用户活动参与信息 - 用于30次刮卡活动
  """
  def get_user_activity_participations(user_id, "scratch_card") do
    case ScratchCardService.get_user_activity_info(user_id) do
      {:ok, data} ->
        data

      {:error, reason} ->
        Logger.error("获取用户刮卡活动参与信息失败: #{inspect(reason)}")
        %{"code" => 1000, "msg" => "获取失败"}
    end
  end

  @doc """
  领取活动奖励 - 用于30次刮卡活动
  """
  def claim_activity_reward(user_id, "scratch_card", fetch_data) when is_map(fetch_data) do
    level = Map.get(fetch_data, "level", 1)
    card_index = Map.get(fetch_data, "card_index", 1)

    case ScratchCardService.claim_card_reward(user_id, card_index, level) do
      {:ok, result} -> {:ok, %{reward_amount: result["fetchaward"]}}
      {:error, reason} -> {:error, reason}
    end
  end

  def claim_activity_reward(user_id, "scratch_card", fetch_type) when is_integer(fetch_type) do
    # 兼容旧的整数格式
    claim_activity_reward(user_id, "scratch_card", %{"level" => fetch_type, "card_index" => 1})
  end

  @doc """
  检查用户是否可以参与指定活动
  """
  def can_participate?(user_id, activity_type, activity_id \\ nil) do
    case get_user_participation(user_id, activity_type, activity_id) do
      {:ok, nil} ->
        true

      {:ok, participation} ->
        participation.status in [:active, :paused]

      {:error, _} ->
        false
    end
  end

  @doc """
  用户参与活动
  """
  def participate_activity(user_id, activity_type, activity_id \\ nil, initial_data \\ %{}) do
    case can_participate?(user_id, activity_type, activity_id) do
      true ->
        UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: activity_type,
          activity_id: activity_id,
          progress: 0,
          status: :active,
          participation_data: initial_data
        })

      false ->
        {:error, :already_participating}
    end
  end

  @doc """
  更新用户活动进度
  """
  def update_progress(user_id, activity_type, activity_id \\ nil, progress_delta \\ 1) do
    case get_user_participation(user_id, activity_type, activity_id) do
      {:ok, participation} when not is_nil(participation) ->
        new_progress = participation.progress + progress_delta

        UserActivityParticipation.update_progress(participation, %{
          progress: new_progress,
          participation_data:
            Map.put(participation.participation_data || %{}, :last_updated, DateTime.utc_now())
        })

      {:ok, nil} ->
        {:error, :not_participating}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查用户是否可以领取奖励
  """
  def can_claim_reward?(user_id, activity_type, activity_id \\ nil) do
    case get_user_participation(user_id, activity_type, activity_id) do
      {:ok, participation} when not is_nil(participation) ->
        check_reward_eligibility(participation, activity_type, activity_id)

      {:ok, nil} ->
        false

      {:error, _} ->
        false
    end
  end

  @doc """
  用户领取奖励
  """
  def claim_reward(user_id, activity_type, activity_id \\ nil) do
    case can_claim_reward?(user_id, activity_type, activity_id) do
      true ->
        with {:ok, reward_amount} <- calculate_reward(user_id, activity_type, activity_id),
             {:ok, _record} <-
               create_reward_record(user_id, activity_type, activity_id, reward_amount),
             {:ok, _} <- distribute_reward(user_id, reward_amount) do
          {:ok, reward_amount}
        else
          {:error, reason} -> {:error, reason}
        end

      false ->
        {:error, :not_eligible}
    end
  end

  @doc """
  获取用户活动参与记录
  """
  def get_user_participation(user_id, activity_type, activity_id \\ nil) do
    UserActivityParticipation.get_user_progress(%{
      user_id: user_id,
      activity_type: activity_type,
      activity_id: activity_id
    })
  end

  @doc """
  获取用户活动统计
  """
  def get_user_activity_stats(user_id, start_date \\ nil, end_date \\ nil) do
    RewardClaimRecord.get_user_total_rewards(%{
      user_id: user_id,
      start_date: start_date,
      end_date: end_date
    })
  end

  # 私有函数

  defp check_reward_eligibility(participation, activity_type, activity_id) do
    case activity_type do
      :game_task -> check_game_task_eligibility(participation, activity_id)
      :seven_day_task -> check_seven_day_eligibility(participation)
      :weekly_card -> check_weekly_card_eligibility(participation, activity_id)
      :vip_gift -> check_vip_gift_eligibility(participation)
      :recharge_task -> check_recharge_task_eligibility(participation, activity_id)
      :first_recharge_gift -> check_first_recharge_eligibility(participation, activity_id)
      :binding_reward -> check_binding_reward_eligibility(participation, activity_id)
      _ -> false
    end
  end

  defp check_game_task_eligibility(participation, activity_id) do
    case GameTask.read(activity_id) do
      {:ok, task} -> participation.progress >= task.required_count
      {:error, _} -> false
    end
  end

  defp check_seven_day_eligibility(participation) do
    # 检查连续登录天数
    participation.progress >= 1
  end

  defp check_weekly_card_eligibility(participation, activity_id) do
    case WeeklyCard.read(activity_id) do
      {:ok, card} ->
        # 检查是否已充值且在有效期内
        participation.status == :active

      {:error, _} ->
        false
    end
  end

  defp check_vip_gift_eligibility(participation) do
    # 检查VIP等级和时间限制
    participation.status == :active
  end

  defp check_recharge_task_eligibility(participation, activity_id) do
    case RechargeTask.read(activity_id) do
      {:ok, task} -> participation.progress >= task.recharge_amount
      {:error, _} -> false
    end
  end

  defp check_first_recharge_eligibility(participation, activity_id) do
    case FirstRechargeGift.read(activity_id) do
      {:ok, gift} ->
        # 检查用户注册天数和首充状态
        participation.status == :active

      {:error, _} ->
        false
    end
  end

  defp check_binding_reward_eligibility(participation, activity_id) do
    case BindingReward.read(activity_id) do
      {:ok, reward} -> participation.status == :active
      {:error, _} -> false
    end
  end

  defp calculate_reward(user_id, activity_type, activity_id) do
    case activity_type do
      :game_task -> calculate_game_task_reward(activity_id)
      :seven_day_task -> calculate_seven_day_reward(user_id)
      :weekly_card -> calculate_weekly_card_reward(activity_id)
      :vip_gift -> calculate_vip_gift_reward(user_id)
      :recharge_task -> calculate_recharge_task_reward(activity_id)
      :first_recharge_gift -> calculate_first_recharge_reward(activity_id)
      :binding_reward -> calculate_binding_reward(activity_id)
      _ -> {:error, :unsupported_activity}
    end
  end

  defp calculate_game_task_reward(activity_id) do
    case GameTask.read(activity_id) do
      {:ok, task} -> {:ok, task.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_seven_day_reward(user_id) do
    # 根据用户连续登录天数计算奖励
    {:ok, Decimal.new("100")}
  end

  defp calculate_weekly_card_reward(activity_id) do
    case WeeklyCard.read(activity_id) do
      {:ok, card} -> {:ok, card.daily_reward}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_vip_gift_reward(user_id) do
    # 根据用户VIP等级计算奖励
    {:ok, Decimal.new("50")}
  end

  defp calculate_recharge_task_reward(activity_id) do
    case RechargeTask.read(activity_id) do
      {:ok, task} -> {:ok, task.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_first_recharge_reward(activity_id) do
    case FirstRechargeGift.read(activity_id) do
      {:ok, gift} -> {:ok, gift.reward_coins}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_binding_reward(activity_id) do
    case BindingReward.read(activity_id) do
      {:ok, reward} -> {:ok, reward.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp create_reward_record(user_id, activity_type, activity_id, reward_amount) do
    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: activity_type,
      activity_id: activity_id,
      reward_type: :coins,
      reward_amount: reward_amount,
      reward_data: %{claimed_via: "activity_service"}
    })
  end

  defp distribute_reward(user_id, reward_amount) do
    # 调用积分系统发放活动奖励
    case Cypridina.Accounts.add_points(user_id, reward_amount,
      transaction_type: :bonus,
      description: "活动奖励",
      metadata: %{
        "operation" => "activity_reward",
        "bonus_type" => "general_activity",
        "amount" => Decimal.to_string(reward_amount)
      }
    ) do
      {:ok, _transaction} -> {:ok, :distributed}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取用户所有可用的活动列表
  """
  def get_user_available_activities(user_id) do
    try do
      activities = %{
        game_tasks: get_available_game_tasks(user_id),
        seven_day_login: get_seven_day_login_info(user_id),
        weekly_cards: get_available_weekly_cards(user_id),
        vip_gifts: get_available_vip_gifts(user_id),
        recharge_tasks: get_available_recharge_tasks(user_id),
        recharge_wheel: get_recharge_wheel_info(user_id),
        scratch_cards: get_scratch_card_activities(user_id),
        first_recharge: get_first_recharge_info(user_id),
        loss_rebate: get_loss_rebate_info(user_id),
        invite_cash: get_invite_cash_info(user_id),
        binding_rewards: get_binding_rewards(user_id),
        free_bonus_tasks: get_free_bonus_tasks(user_id),
        cdkey: get_cdkey_info(user_id)
      }

      {:ok, activities}
    rescue
      e ->
        Logger.error("获取用户活动列表失败: #{inspect(e)}")
        {:error, :activity_fetch_error}
    end
  end

  @doc """
  获取特定类型的活动配置
  """
  def get_activity_config(activity_type) do
    case activity_type do
      :scratch_card ->
        case ScratchCardActivity.list() do
          {:ok, activities} -> {:ok, activities}
          _ -> {:ok, []}
        end

      :game_task ->
        case GameTask.list_active_tasks() do
          {:ok, tasks} -> {:ok, tasks}
          _ -> {:ok, []}
        end

      :seven_day_task ->
        case SevenDayTask.list() do
          {:ok, tasks} -> {:ok, tasks}
          _ -> {:ok, []}
        end

      :weekly_card ->
        case WeeklyCard.list() do
          {:ok, cards} -> {:ok, cards}
          _ -> {:ok, []}
        end

      :vip_gift ->
        case VipGift.list() do
          {:ok, gifts} -> {:ok, gifts}
          _ -> {:ok, []}
        end

      :recharge_task ->
        case RechargeTask.list() do
          {:ok, tasks} -> {:ok, tasks}
          _ -> {:ok, []}
        end

      :recharge_wheel ->
        case RechargeWheel.list() do
          {:ok, wheels} -> {:ok, wheels}
          _ -> {:ok, []}
        end

      :first_recharge_gift ->
        case FirstRechargeGift.list() do
          {:ok, gifts} -> {:ok, gifts}
          _ -> {:ok, []}
        end

      :binding_reward ->
        case BindingReward.list() do
          {:ok, rewards} -> {:ok, rewards}
          _ -> {:ok, []}
        end

      :free_bonus_task ->
        case FreeBonusTask.list() do
          {:ok, tasks} -> {:ok, tasks}
          _ -> {:ok, []}
        end

      :cdkey ->
        case CdkeyActivity.list() do
          {:ok, cdkeys} -> {:ok, cdkeys}
          _ -> {:ok, []}
        end

      _ ->
        {:error, :unknown_activity_type}
    end
  end

  # 私有函数 - 获取各种活动信息

  defp get_available_game_tasks(user_id) do
    case GameTask.list() do
      {:ok, tasks} ->
        Enum.map(tasks, fn task ->
          participation = get_user_participation(user_id, :game_task, task.id)
          Map.put(task, :user_progress, get_participation_progress(participation))
        end)

      _ ->
        []
    end
  end

  defp get_seven_day_login_info(user_id) do
    case get_user_participation(user_id, :seven_day_task) do
      {:ok, participation} when not is_nil(participation) ->
        %{
          current_day: participation.progress,
          claimed_days: Map.get(participation.participation_data || %{}, :claimed_days, []),
          last_login: Map.get(participation.participation_data || %{}, :last_login)
        }

      _ ->
        %{
          current_day: 0,
          claimed_days: [],
          last_login: nil
        }
    end
  end

  defp get_available_weekly_cards(user_id) do
    case WeeklyCard.list() do
      {:ok, cards} ->
        Enum.map(cards, fn card ->
          participation = get_user_participation(user_id, :weekly_card, card.id)
          Map.put(card, :user_status, get_card_status(participation))
        end)

      _ ->
        []
    end
  end

  defp get_available_vip_gifts(user_id) do
    case VipGift.list() do
      {:ok, gifts} ->
        # 获取用户VIP等级
        user_vip_level = case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
          {:ok, info} -> info.vip_level
          _ -> 0
        end

        Enum.filter(gifts, fn gift ->
          gift.required_vip_level <= user_vip_level
        end)
        |> Enum.map(fn gift ->
          participation = get_user_participation(user_id, :vip_gift, gift.id)
          Map.put(gift, :claimed, participation != nil)
        end)

      _ ->
        []
    end
  end

  defp get_available_recharge_tasks(user_id) do
    case RechargeTask.list() do
      {:ok, tasks} ->
        Enum.map(tasks, fn task ->
          participation = get_user_participation(user_id, :recharge_task, task.id)
          Map.put(task, :user_progress, get_participation_progress(participation))
        end)

      _ ->
        []
    end
  end

  defp get_recharge_wheel_info(user_id) do
    case RechargeWheel.list() do
      {:ok, [wheel | _]} ->
        participation = get_user_participation(user_id, :recharge_wheel, wheel.id)

        spins =
          Map.get((participation && participation.participation_data) || %{}, :available_spins, 0)

        %{
          id: wheel.id,
          available_spins: spins,
          recharge_per_spin: wheel.recharge_per_spin
        }

      _ ->
        %{
          id: nil,
          available_spins: 0,
          recharge_per_spin: 100
        }
    end
  end

  defp get_scratch_card_activities(user_id) do
    case ScratchCardActivity.list() do
      {:ok, activities} ->
        Enum.map(activities, fn activity ->
          participation = get_user_participation(user_id, :scratch_card, activity.id)
          Map.put(activity, :user_cards, get_user_scratch_cards(participation))
        end)

      _ ->
        []
    end
  end

  defp get_first_recharge_info(user_id) do
    case FirstRechargeGift.list() do
      {:ok, [gift | _]} ->
        participation = get_user_participation(user_id, :first_recharge_gift, gift.id)

        %{
          id: gift.id,
          claimed: participation != nil,
          reward_coins: gift.reward_coins,
          expire_days: gift.expire_days
        }

      _ ->
        nil
    end
  end

  defp get_loss_rebate_info(user_id) do
    # 获取损失返水信息
    LossRebateService.get_user_rebate_info(user_id)
  end

  defp get_invite_cash_info(user_id) do
    case InviteCashActivity.list() do
      {:ok, [activity | _]} ->
        participation = get_user_participation(user_id, :invite_cash, activity.id)

        invites =
          Map.get((participation && participation.participation_data) || %{}, :invites, [])

        %{
          id: activity.id,
          total_invites: length(invites),
          reward_per_invite: activity.reward_per_invite,
          max_invites: activity.max_invites
        }

      _ ->
        nil
    end
  end

  defp get_binding_rewards(user_id) do
    case BindingReward.list() do
      {:ok, rewards} ->
        Enum.map(rewards, fn reward ->
          participation = get_user_participation(user_id, :binding_reward, reward.id)
          Map.put(reward, :claimed, participation != nil)
        end)

      _ ->
        []
    end
  end

  defp get_free_bonus_tasks(user_id) do
    case FreeBonusTask.list() do
      {:ok, tasks} ->
        Enum.map(tasks, fn task ->
          participation = get_user_participation(user_id, :free_bonus_task, task.id)
          Map.put(task, :completed, participation != nil)
        end)

      _ ->
        []
    end
  end

  defp get_cdkey_info(user_id) do
    # 获取用户已使用的兑换码
    case RewardClaimRecord.read() do
      {:ok, records} ->
        used_codes =
          records
          |> Enum.filter(fn r ->
            r.user_id == user_id and r.activity_type == :cdkey
          end)
          |> Enum.map(fn r ->
            Map.get(r.reward_data || %{}, "code", "")
          end)

        %{
          available: true,
          used_codes: used_codes
        }

      _ ->
        %{
          available: true,
          used_codes: []
        }
    end
  end

  defp get_participation_progress(nil), do: 0
  defp get_participation_progress({:ok, nil}), do: 0
  defp get_participation_progress({:ok, participation}), do: participation.progress
  defp get_participation_progress(_), do: 0

  defp get_card_status(nil), do: :not_purchased
  defp get_card_status({:ok, nil}), do: :not_purchased

  defp get_card_status({:ok, participation}) do
    case participation.status do
      :active -> :active
      :completed -> :expired
      _ -> :not_purchased
    end
  end

  defp get_card_status(_), do: :not_purchased

  defp get_user_scratch_cards(nil), do: []
  defp get_user_scratch_cards({:ok, nil}), do: []

  defp get_user_scratch_cards({:ok, participation}) do
    Map.get(participation.participation_data || %{}, :cards, [])
  end

  defp get_user_scratch_cards(_), do: []

  @doc """
  触发日常登录奖励
  """
  def trigger_daily_login_bonus(user_id, consecutive_days \\ 1) do
    try do
      # 获取当前活跃的签到活动
      case SignInActivity.list_current_activities() do
        {:ok, [activity | _]} ->
          # 计算日常登录奖励
          reward_amount = calculate_daily_login_reward(activity, consecutive_days)
          
          # 创建用户奖励记录
          case UserReward.create(%{
            user_id: user_id,
            source_type: :login_bonus,
            source_id: activity.id,
            reward_type: :points,
            reward_amount: reward_amount,
            distribution_type: :auto,
            metadata: %{
              "consecutive_days" => consecutive_days,
              "reward_date" => Date.utc_today() |> Date.to_string(),
              "activity_name" => activity.activity_name
            }
          }) do
            {:ok, reward} ->
              Logger.info("日常登录奖励发放成功", %{
                user_id: user_id,
                reward_amount: reward_amount,
                consecutive_days: consecutive_days
              })
              
              # 自动发放奖励到用户账户
              distribute_login_reward(user_id, reward_amount)
              {:ok, reward}
              
            {:error, reason} ->
              Logger.error("创建登录奖励失败", %{
                user_id: user_id,
                reason: inspect(reason)
              })
              {:error, reason}
          end
          
        {:ok, []} ->
          Logger.warning("没有活跃的签到活动", %{user_id: user_id})
          {:error, :no_active_activity}
          
        {:error, reason} ->
          Logger.error("获取签到活动失败", %{
            user_id: user_id,
            reason: inspect(reason)
          })
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("触发日常登录奖励异常", %{
          user_id: user_id,
          error: inspect(error)
        })
        {:error, :trigger_failed}
    end
  end

  @doc """
  检查并触发连续登录奖励
  """
  def check_consecutive_login_rewards(user_id, consecutive_days) do
    try do
      case SignInActivity.list_current_activities() do
        {:ok, [activity | _]} ->
          # 检查是否有匹配的连续登录奖励
          consecutive_reward = find_consecutive_reward(activity.consecutive_rewards, consecutive_days)
          
          case consecutive_reward do
            nil ->
              {:ok, :no_reward}
              
            reward_config ->
              # 检查用户是否已经领取过这个连续奖励
              case has_claimed_consecutive_reward?(user_id, activity.id, consecutive_days) do
                false ->
                  # 发放连续登录奖励
                  issue_consecutive_reward(user_id, activity, consecutive_days, reward_config)
                  
                true ->
                  {:ok, :already_claimed}
              end
          end
          
        {:ok, []} ->
          {:error, :no_active_activity}
          
        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("检查连续登录奖励异常", %{
          user_id: user_id,
          consecutive_days: consecutive_days,
          error: inspect(error)
        })
        {:error, :check_failed}
    end
  end

  @doc """
  获取用户登录奖励信息
  """
  def get_user_login_bonus_info(user_id) do
    try do
      alias Teen.ActivitySystem.LoginTrackerService
      
      with {:ok, login_stats} <- LoginTrackerService.get_user_login_stats(user_id),
           {:ok, activities} <- SignInActivity.list_current_activities() do
        
        case activities do
          [activity | _] ->
            # 获取今日是否已领取
            today_claimed = has_claimed_today_reward?(user_id, activity.id)
            
            # 使用登录记录中的连续天数
            consecutive_days = login_stats.consecutive_days
            
            # 计算下次奖励
            next_reward = calculate_daily_login_reward(activity, consecutive_days + 1)
            
            {:ok, %{
              consecutive_days: consecutive_days,
              total_login_days: login_stats.total_login_days,
              today_claimed: today_claimed,
              today_logged_in: login_stats.today_logged_in,
              next_reward_amount: next_reward,
              activity_name: activity.activity_name,
              available_consecutive_rewards: get_available_consecutive_rewards(activity, consecutive_days),
              recent_login_history: login_stats.recent_history
            }}
            
          [] ->
            {:error, :no_active_activity}
        end
      end
    rescue
      error ->
        Logger.error("获取用户登录奖励信息异常", %{
          user_id: user_id,
          error: inspect(error)
        })
        {:error, :get_info_failed}
    end
  end

  # 私有函数

  defp calculate_daily_login_reward(activity, consecutive_days) do
    base_reward = get_base_daily_reward(activity.daily_rewards)
    vip_bonus_rate = activity.vip_bonus_rate || Decimal.new("0")
    
    # 基础奖励
    base_amount = Decimal.new(base_reward)
    
    # VIP加成
    vip_bonus = Decimal.mult(base_amount, Decimal.div(vip_bonus_rate, Decimal.new("100")))
    
    # 连续登录加成（每连续登录一天增加2%，最高50%）
    consecutive_bonus_rate = min(consecutive_days * 2, 50)
    consecutive_bonus = Decimal.mult(base_amount, Decimal.div(Decimal.new(consecutive_bonus_rate), Decimal.new("100")))
    
    base_amount
    |> Decimal.add(vip_bonus)
    |> Decimal.add(consecutive_bonus)
    |> Decimal.round(0)
  end

  defp get_base_daily_reward(daily_rewards) when is_list(daily_rewards) do
    case Enum.find(daily_rewards, fn reward -> 
      Map.get(reward, "type") == "daily" 
    end) do
      nil -> 100  # 默认奖励
      reward -> Map.get(reward, "amount", 100)
    end
  end

  defp get_base_daily_reward(_), do: 100

  defp find_consecutive_reward(consecutive_rewards, consecutive_days) when is_list(consecutive_rewards) do
    Enum.find(consecutive_rewards, fn reward ->
      required_days = Map.get(reward, "required_days", 0)
      required_days == consecutive_days
    end)
  end

  defp find_consecutive_reward(_, _), do: nil

  defp has_claimed_consecutive_reward?(user_id, activity_id, consecutive_days) do
    today = Date.utc_today() |> Date.to_string()
    
    case UserReward.read() do
      {:ok, rewards} ->
        Enum.any?(rewards, fn reward ->
          reward.user_id == user_id and
          reward.source_id == activity_id and
          reward.source_type == :consecutive_login_bonus and
          Map.get(reward.metadata || %{}, "consecutive_days") == consecutive_days and
          Map.get(reward.metadata || %{}, "claim_date") == today
        end)
        
      {:error, _} ->
        false
    end
  end

  defp has_claimed_today_reward?(user_id, activity_id) do
    today = Date.utc_today() |> Date.to_string()
    
    case UserReward.read() do
      {:ok, rewards} ->
        Enum.any?(rewards, fn reward ->
          reward.user_id == user_id and
          reward.source_id == activity_id and
          reward.source_type == :login_bonus and
          Map.get(reward.metadata || %{}, "reward_date") == today
        end)
        
      {:error, _} ->
        false
    end
  end

  defp issue_consecutive_reward(user_id, activity, consecutive_days, reward_config) do
    reward_amount = Map.get(reward_config, "amount", 500)
    
    case UserReward.create(%{
      user_id: user_id,
      source_type: :consecutive_login_bonus,
      source_id: activity.id,
      reward_type: :points,
      reward_amount: Decimal.new(reward_amount),
      distribution_type: :auto,
      metadata: %{
        "consecutive_days" => consecutive_days,
        "claim_date" => Date.utc_today() |> Date.to_string(),
        "activity_name" => activity.activity_name,
        "reward_title" => Map.get(reward_config, "title", "连续登录奖励")
      }
    }) do
      {:ok, reward} ->
        Logger.info("连续登录奖励发放成功", %{
          user_id: user_id,
          consecutive_days: consecutive_days,
          reward_amount: reward_amount
        })
        
        distribute_login_reward(user_id, Decimal.new(reward_amount))
        {:ok, reward}
        
      {:error, reason} ->
        Logger.error("连续登录奖励发放失败", %{
          user_id: user_id,
          consecutive_days: consecutive_days,
          reason: inspect(reason)
        })
        {:error, reason}
    end
  end

  defp get_available_consecutive_rewards(activity, current_consecutive_days) do
    case activity.consecutive_rewards do
      rewards when is_list(rewards) ->
        rewards
        |> Enum.filter(fn reward ->
          required_days = Map.get(reward, "required_days", 0)
          required_days > current_consecutive_days
        end)
        |> Enum.sort_by(fn reward -> Map.get(reward, "required_days", 0) end)
        |> Enum.take(5)  # 返回接下来的5个奖励
        
      _ ->
        []
    end
  end

  defp distribute_login_reward(user_id, reward_amount) do
    try do
      # 调用积分系统发放奖励
      case Cypridina.Accounts.add_points(user_id, reward_amount,
        transaction_type: :bonus,
        description: "登录奖励",
        metadata: %{
          "operation" => "login_bonus",
          "bonus_type" => "daily_login",
          "amount" => Decimal.to_string(reward_amount)
        }
      ) do
        {:ok, _transaction} ->
          Logger.info("登录奖励积分发放成功", %{
            user_id: user_id,
            amount: reward_amount
          })
          :ok
          
        {:error, reason} ->
          Logger.error("登录奖励积分发放失败", %{
            user_id: user_id,
            amount: reward_amount,
            reason: inspect(reason)
          })
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("登录奖励积分发放异常", %{
          user_id: user_id,
          amount: reward_amount,
          error: inspect(error)
        })
        {:error, :distribution_failed}
    end
  end
end
