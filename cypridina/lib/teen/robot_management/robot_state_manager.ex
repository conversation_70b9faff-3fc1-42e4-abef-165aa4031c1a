defmodule Teen.RobotManagement.RobotStateManager do
  @moduledoc """
  机器人状态管理服务 - 处理所有状态转换和验证
  """
  
  require Logger
  alias Teen.RobotManagement.RobotEntity

  @doc """
  检查机器人是否可以被踢出
  """
  def can_kick_robot?(robot_id) do
    case get_robot(robot_id) do
      {:ok, robot} ->
        result = robot.can_be_kicked and 
                 robot.status in [:assigned, :in_game] and 
                 not robot.is_in_round
        
        Logger.info("🤖 [STATE_CHECK] 机器人 #{robot_id} 是否可踢出: #{result}")
        Logger.info("🤖 [STATE_CHECK] 状态: #{robot.status}, 回合中: #{robot.is_in_round}, 可踢出: #{robot.can_be_kicked}")
        
        {:ok, result}
      error -> error
    end
  end

  @doc """
  房间踢出机器人
  """
  def kick_robot_from_room(robot_id, room_id, reason \\ "房间踢出") do
    Logger.info("🤖 [ROOM_KICK] 房间 #{room_id} 尝试踢出机器人 #{robot_id}")
    
    with {:ok, can_kick} <- can_kick_robot?(robot_id),
         true <- can_kick,
         {:ok, robot} <- get_robot(robot_id),
         true <- robot.current_room_id == room_id do
      
      case Ash.update!(robot, %{reason: reason, kicked_by: "room_#{room_id}"}, action: :kick_out) do
        updated_robot ->
          Logger.info("🤖 [ROOM_KICK] 机器人 #{robot_id} 被房间踢出成功")
          
          # 通知房间移除机器人
          notify_room_robot_removed(room_id, robot_id, reason)
          
          {:ok, updated_robot}
      end
    else
      {:ok, false} ->
        Logger.warning("🤖 [ROOM_KICK] 机器人 #{robot_id} 当前不能被踢出（在游戏回合中）")
        {:error, :robot_in_round}
        
      false ->
        Logger.warning("🤖 [ROOM_KICK] 机器人 #{robot_id} 不在房间 #{room_id} 中")
        {:error, :robot_not_in_room}
        
      error ->
        Logger.error("🤖 [ROOM_KICK] 踢出机器人失败: #{inspect(error)}")
        error
    end
  end

  @doc """
  后台回收机器人 - 新的两阶段回收机制
  """
  def recycle_robot_by_admin(robot_id, admin_id, force \\ false) do
    Logger.info("🤖 [ADMIN_RECYCLE] 管理员 #{admin_id} 尝试回收机器人 #{robot_id}, 强制: #{force}")
    
    with {:ok, robot} <- get_robot(robot_id) do
      case robot.status do
        # 如果机器人空闲，直接回收并禁用
        :idle ->
          Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 空闲状态，直接回收")
          
          case Ash.update!(robot, %{admin_id: admin_id}, action: :recycle_robot) do
            updated_robot ->
              Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 被直接回收并禁用")
              {:ok, updated_robot}
          end
        
        # 如果机器人在游戏中，标记为回收中状态
        status when status in [:assigned, :in_game, :in_round] ->
          if force or not robot.is_in_round do
            Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 在游戏中，标记为回收中状态")
            
            # 标记为回收中，游戏结束后会自动禁用
            case Ash.update!(robot, %{admin_id: admin_id}, action: :start_recycling) do
              updated_robot ->
                # 通知房间机器人即将被回收
                if robot.current_room_id do
                  notify_room_robot_recycling(robot.current_room_id, robot_id, "后台回收中")
                end
                
                Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 标记为回收中，等待游戏结束")
                {:ok, updated_robot}
            end
          else
            Logger.warning("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 在游戏回合中，需要强制回收")
            {:error, :robot_in_round}
          end
        
        # 其他状态直接回收
        _ ->
          Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 状态: #{robot.status}，直接回收")
          
          case Ash.update!(robot, %{admin_id: admin_id}, action: :recycle_robot) do
            updated_robot ->
              Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 被回收并禁用")
              {:ok, updated_robot}
          end
      end
    end
  end

  @doc """
  完成机器人回收 - 由游戏房间调用
  当游戏结束时，将回收中的机器人最终回收并禁用
  """
  def complete_robot_recycling(robot_id, reason \\ "游戏结束自动回收") do
    Logger.info("🤖 [COMPLETE_RECYCLE] 完成机器人 #{robot_id} 的回收流程")
    
    with {:ok, robot} <- get_robot(robot_id) do
      if robot.status == :recycling do
        case Ash.update!(robot, %{admin_id: robot.kicked_by}, action: :recycle_robot) do
          updated_robot ->
            Logger.info("🤖 [COMPLETE_RECYCLE] 机器人 #{robot_id} 回收完成并已禁用")
            {:ok, updated_robot}
        end
      else
        Logger.debug("🤖 [COMPLETE_RECYCLE] 机器人 #{robot_id} 不在回收中状态，跳过")
        {:ok, robot}
      end
    end
  end

  @doc """
  检查并处理积分不足的机器人
  """
  def check_insufficient_funds(robot_id, required_amount) do
    Logger.info("🤖 [FUNDS_CHECK] 检查机器人 #{robot_id} 积分，需要: #{required_amount}")
    
    with {:ok, robot} <- get_robot(robot_id) do
      if robot.current_points < required_amount do
        Logger.warning("🤖 [FUNDS_CHECK] 机器人 #{robot_id} 积分不足: #{robot.current_points} < #{required_amount}")
        
        # 标记为积分不足
        updated_robot = Ash.update!(robot, %{}, action: :mark_insufficient_funds)
        
        # 如果在游戏中且不在回合中，踢出游戏
        if robot.current_room_id and not robot.is_in_round do
          kick_robot_from_room(robot_id, robot.current_room_id, "积分不足")
        end
        
        {:error, :insufficient_funds, updated_robot}
      else
        {:ok, :sufficient_funds}
      end
    end
  end

  @doc """
  机器人进入游戏回合
  """
  def robot_enter_round(robot_id) do
    Logger.info("🤖 [ROUND_ENTER] 机器人 #{robot_id} 进入游戏回合")
    
    with {:ok, robot} <- get_robot(robot_id),
         true <- robot.status in [:assigned, :in_game] do
      
      updated_robot = Ash.update!(robot, %{}, action: :enter_round)
      Logger.info("🤖 [ROUND_ENTER] 机器人 #{robot_id} 已进入回合，不可踢出")
      {:ok, updated_robot}
    else
      false ->
        Logger.error("🤖 [ROUND_ENTER] 机器人 #{robot_id} 状态不正确，无法进入回合")
        {:error, :invalid_status}
      error -> error
    end
  end

  @doc """
  机器人退出游戏回合
  """
  def robot_exit_round(robot_id) do
    Logger.info("🤖 [ROUND_EXIT] 机器人 #{robot_id} 退出游戏回合")
    
    with {:ok, robot} <- get_robot(robot_id),
         true <- robot.status == :in_round do
      
      updated_robot = Ash.update!(robot, %{}, action: :exit_round)
      Logger.info("🤖 [ROUND_EXIT] 机器人 #{robot_id} 已退出回合，可以踢出")
      {:ok, updated_robot}
    else
      false ->
        Logger.error("🤖 [ROUND_EXIT] 机器人 #{robot_id} 不在回合中")
        {:error, :not_in_round}
      error -> error
    end
  end

  @doc """
  更新机器人积分并检查
  """
  def update_robot_points(robot_id, new_points, bet_amount \\ 0) do
    Logger.info("🤖 [POINTS_UPDATE] 更新机器人 #{robot_id} 积分: #{new_points}")
    
    with {:ok, robot} <- get_robot(robot_id) do
      updated_robot = Ash.update!(robot, %{points: new_points, bet_amount: bet_amount}, action: :update_points)
      
      # 检查积分是否足够
      if new_points < robot.min_points_threshold do
        Logger.warning("🤖 [POINTS_UPDATE] 机器人 #{robot_id} 积分低于阈值: #{new_points} < #{robot.min_points_threshold}")
        
        # 如果不在回合中，立即踢出
        if not robot.is_in_round and robot.current_room_id do
          spawn(fn -> 
            :timer.sleep(1000)  # 延迟1秒执行，避免影响当前游戏流程
            kick_robot_from_room(robot_id, robot.current_room_id, "积分不足自动踢出")
          end)
        end
      end
      
      {:ok, updated_robot}
    end
  end

  @doc """
  获取机器人状态统计
  """
  def get_robot_status_stats do
    robots = Ash.read!(RobotEntity)
    
    stats = %{
      total: length(robots),
      idle: Enum.count(robots, &(&1.status == :idle and &1.is_enabled)),
      assigned: Enum.count(robots, &(&1.status == :assigned and &1.is_enabled)),
      in_game: Enum.count(robots, &(&1.status == :in_game and &1.is_enabled)),
      in_round: Enum.count(robots, &(&1.status == :in_round and &1.is_enabled)),
      recycling: Enum.count(robots, &(&1.status == :recycling and &1.is_enabled)),
      recycled: Enum.count(robots, &(&1.status == :recycled)),
      insufficient_funds: Enum.count(robots, &(&1.status == :insufficient_funds and &1.is_enabled)),
      enabled: Enum.count(robots, &(&1.is_enabled)),
      disabled: Enum.count(robots, &(not &1.is_enabled))
    }
    
    Logger.debug("🤖 [STATS] 机器人状态统计: #{inspect(stats)}")
    stats
  end

  @doc """
  清理长时间异常状态的机器人
  """
  def cleanup_stuck_robots do
    Logger.info("🤖 [CLEANUP] 开始清理异常状态机器人")
    
    cutoff_time = DateTime.add(DateTime.utc_now(), -30, :minute)
    
    # 查找长时间处于异常状态的机器人
    stuck_robots = 
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot ->
        robot.status in [:kicked_out, :recycled, :insufficient_funds] and
        DateTime.compare(robot.status_changed_at, cutoff_time) == :lt
      end)
    
    # 将它们重置为idle状态
    Enum.each(stuck_robots, fn robot ->
      Logger.info("🤖 [CLEANUP] 重置机器人 #{robot.robot_id} 状态为idle")
      Ash.update!(robot, %{}, action: :release_to_idle)
    end)
    
    Logger.info("🤖 [CLEANUP] 清理完成，重置了 #{length(stuck_robots)} 个机器人")
    {:ok, length(stuck_robots)}
  end

  @doc """
  按游戏类型清理机器人状态
  
  用于处理服务器重启后机器人状态不同步的问题
  将指定游戏类型的所有机器人重置为空闲状态
  
  注意：会检查机器人是否真的在活跃房间中，避免清理正在游戏的机器人
  """
  def cleanup_robots_by_game(game_type, force \\ false) when is_binary(game_type) do
    Logger.info("🤖 [GAME_CLEANUP] 开始清理游戏 #{game_type} 的所有机器人状态，强制模式: #{force}")
    
    # 查找该游戏类型的所有机器人（包括当前在该游戏中的和已分配到该游戏的）
    game_robots = 
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot ->
        robot.current_game_type == game_type or
        (robot.status in [:assigned, :in_game, :in_round, :kicked_out] and robot.current_game_type == game_type)
      end)
    
    Logger.info("🤖 [GAME_CLEANUP] 找到 #{length(game_robots)} 个 #{game_type} 游戏的机器人")
    
    # 统计清理前的状态
    status_counts = 
      game_robots
      |> Enum.group_by(& &1.status)
      |> Enum.map(fn {status, robots} -> {status, length(robots)} end)
      |> Map.new()
    
    Logger.info("🤖 [GAME_CLEANUP] 清理前状态统计: #{inspect(status_counts)}")
    
    # 清理机器人状态
    {cleared_count, skipped_count} = 
      Enum.reduce(game_robots, {0, 0}, fn robot, {cleared, skipped} ->
        try do
          case robot.status do
            :idle ->
              # 已经是空闲状态，不需要处理
              {cleared, skipped}
              
            status when status in [:assigned, :in_game, :kicked_out, :recycled, :insufficient_funds] ->
              # 检查机器人是否真的在活跃房间中
              should_clean = force or not is_robot_in_active_room?(robot)
              
              if should_clean do
                Logger.info("🤖 [GAME_CLEANUP] 重置机器人 #{robot.robot_id} (#{status} -> idle)")
                
                # 强制重置为空闲状态，清除房间信息
                updated_robot = Ash.update!(robot, %{
                  current_room_id: nil,
                  last_room_id: robot.current_room_id,
                  reason: "游戏清理 - #{game_type}",
                  force_reset: true
                }, action: :force_reset_to_idle)
                
                Logger.debug("🤖 [GAME_CLEANUP] 机器人 #{robot.robot_id} 重置成功")
                {cleared + 1, skipped}
              else
                Logger.info("🤖 [GAME_CLEANUP] 跳过机器人 #{robot.robot_id}，正在活跃房间中")
                {cleared, skipped + 1}
              end
              
            :in_round ->
              if force do
                Logger.warning("🤖 [GAME_CLEANUP] 强制重置回合中的机器人 #{robot.robot_id}")
                
                updated_robot = Ash.update!(robot, %{
                  current_room_id: nil,
                  last_room_id: robot.current_room_id,
                  reason: "强制游戏清理 - #{game_type}",
                  force_reset: true
                }, action: :force_reset_to_idle)
                
                {cleared + 1, skipped}
              else
                Logger.info("🤖 [GAME_CLEANUP] 跳过回合中的机器人 #{robot.robot_id}，使用 force=true 来强制清理")
                {cleared, skipped + 1}
              end
              
            _ ->
              Logger.debug("🤖 [GAME_CLEANUP] 跳过机器人 #{robot.robot_id}，状态: #{robot.status}")
              {cleared, skipped}
          end
        rescue
          error ->
            Logger.error("🤖 [GAME_CLEANUP] 重置机器人 #{robot.robot_id} 失败: #{inspect(error)}")
            {cleared, skipped}
        end
      end)
    
    Logger.info("🤖 [GAME_CLEANUP] 游戏 #{game_type} 清理完成，重置了 #{cleared_count} 个机器人，跳过了 #{skipped_count} 个")
    {:ok, %{total: length(game_robots), cleared: cleared_count, skipped: skipped_count, status_before: status_counts}}
  end

  @doc """
  清理所有游戏的机器人状态
  
  用于服务器重启后的全局清理
  """
  def cleanup_all_game_robots do
    Logger.info("🤖 [GLOBAL_CLEANUP] 开始清理所有游戏的机器人状态")
    
    # 定义支持的游戏类型
    game_types = ["jhandi_munda", "teen_patti", "pot_blind", "slot777", "slotcat", "slotniu"]
    
    results = 
      Enum.map(game_types, fn game_type ->
        case cleanup_robots_by_game(game_type) do
          {:ok, result} -> {game_type, result}
          {:error, reason} -> {game_type, %{error: reason}}
        end
      end)
      |> Map.new()
    
    total_cleared = 
      results
      |> Enum.map(fn {_game, result} -> Map.get(result, :cleared, 0) end)
      |> Enum.sum()
    
    Logger.info("🤖 [GLOBAL_CLEANUP] 全局清理完成，总共重置了 #{total_cleared} 个机器人")
    {:ok, %{results: results, total_cleared: total_cleared}}
  end

  @doc """
  启用机器人
  """
  def enable_robot(robot_id) do
    Logger.info("🤖 [ENABLE] 启用机器人 #{robot_id}")
    
    with {:ok, robot} <- get_robot(robot_id) do
      case Ash.update!(robot, %{}, action: :enable_robot) do
        updated_robot ->
          Logger.info("🤖 [ENABLE] 机器人 #{robot_id} 启用成功")
          {:ok, updated_robot}
      end
    end
  end

  @doc """
  禁用机器人
  """
  def disable_robot(robot_id) do
    Logger.info("🤖 [DISABLE] 禁用机器人 #{robot_id}")
    
    with {:ok, robot} <- get_robot(robot_id) do
      case Ash.update!(robot, %{}, action: :disable_robot) do
        updated_robot ->
          Logger.info("🤖 [DISABLE] 机器人 #{robot_id} 禁用成功")
          {:ok, updated_robot}
      end
    end
  end

  @doc """
  批量启用机器人
  """
  def batch_enable_robots(robot_ids) when is_list(robot_ids) do
    Logger.info("🤖 [BATCH_ENABLE] 批量启用 #{length(robot_ids)} 个机器人")
    
    results = 
      Enum.map(robot_ids, fn robot_id ->
        case enable_robot(robot_id) do
          {:ok, robot} -> {:ok, robot.robot_id}
          {:error, reason} -> {:error, robot_id, reason}
        end
      end)
    
    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("🤖 [BATCH_ENABLE] 批量启用完成，成功 #{success_count} 个")
    
    {:ok, %{total: length(robot_ids), success: success_count, results: results}}
  end

  @doc """
  批量禁用机器人
  """
  def batch_disable_robots(robot_ids) when is_list(robot_ids) do
    Logger.info("🤖 [BATCH_DISABLE] 批量禁用 #{length(robot_ids)} 个机器人")
    
    results = 
      Enum.map(robot_ids, fn robot_id ->
        case disable_robot(robot_id) do
          {:ok, robot} -> {:ok, robot.robot_id}
          {:error, reason} -> {:error, robot_id, reason}
        end
      end)
    
    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("🤖 [BATCH_DISABLE] 批量禁用完成，成功 #{success_count} 个")
    
    {:ok, %{total: length(robot_ids), success: success_count, results: results}}
  end

  @doc """
  启用所有机器人
  """
  def enable_all_robots do
    Logger.info("🤖 [ENABLE_ALL] 启用所有机器人")
    
    disabled_robots = 
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot -> not robot.is_enabled end)
    
    robot_ids = Enum.map(disabled_robots, & &1.robot_id)
    
    if length(robot_ids) > 0 do
      batch_enable_robots(robot_ids)
    else
      Logger.info("🤖 [ENABLE_ALL] 没有需要启用的机器人")
      {:ok, %{total: 0, success: 0, results: []}}
    end
  end

  # ==================== 私有函数 ====================

  defp get_robot(robot_id) do
    case Ash.get(RobotEntity, robot_id, action: :by_robot_id) do
      {:ok, robot} -> {:ok, robot}
      {:error, %Ash.Error.Query.NotFound{}} -> {:error, :robot_not_found}
      {:error, _} -> {:error, :robot_not_found}
    end
  rescue
    _ -> {:error, :robot_not_found}
  end

  defp notify_room_robot_removed(room_id, robot_id, reason) do
    # 通知房间进程移除机器人
    # 这里需要根据您的房间注册机制来实现
    Logger.info("🤖 [NOTIFY] 需要通知房间 #{room_id} 移除机器人 #{robot_id}, 原因: #{reason}")
    # 可以通过 Phoenix.PubSub 或直接找到房间进程来通知
  end

  defp notify_room_robot_recycling(room_id, robot_id, reason) do
    # 通知房间进程机器人正在被回收
    Logger.info("🤖 [NOTIFY] 通知房间 #{room_id} 机器人 #{robot_id} 正在回收中, 原因: #{reason}")
    # 房间可以在游戏结束时调用 complete_robot_recycling
  end

  @doc """
  检查机器人是否真的在一个活跃的房间中
  """
  defp is_robot_in_active_room?(robot) do
    case robot.current_room_id do
      nil ->
        false
      
      room_id ->
        # 检查房间是否真的存在
        case Cypridina.RoomSystem.RoomManager.get_room_info(room_id) do
          {:ok, _room_info} ->
            Logger.debug("🤖 [ROOM_CHECK] 机器人 #{robot.robot_id} 在活跃房间 #{room_id} 中")
            true
          
          {:error, _reason} ->
            Logger.debug("🤖 [ROOM_CHECK] 机器人 #{robot.robot_id} 的房间 #{room_id} 不存在")
            false
        end
    end
  rescue
    error ->
      Logger.warning("🤖 [ROOM_CHECK] 检查房间 #{robot.current_room_id} 时出错: #{inspect(error)}")
      false
  end
end