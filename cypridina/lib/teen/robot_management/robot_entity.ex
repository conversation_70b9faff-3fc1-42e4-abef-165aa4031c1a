defmodule Teen.RobotManagement.RobotEntity do
  use Ash.Resource,
    domain: Teen.RobotManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "robot_entities"
    repo Cy<PERSON><PERSON><PERSON>.Repo
  end

  attributes do
    uuid_primary_key :id
    
    attribute :robot_id, :integer, allow_nil?: false
    attribute :nickname, :string, allow_nil?: false
    attribute :avatar_id, :integer, default: 1
    attribute :level, :integer, default: 1
    
    # 详细的状态管理
    attribute :status, :atom, 
      constraints: [one_of: [:idle, :assigned, :in_game, :in_round, :kicked_out, :recycled, :recycling, :insufficient_funds, :offline]], 
      default: :idle
    
    # 游戏状态信息
    attribute :current_game_type, :string
    attribute :current_room_id, :string
    attribute :seat_number, :integer
    attribute :is_in_round, :boolean, default: false
    
    # 积分状态
    attribute :current_points, :integer, default: 100_000
    attribute :min_points_threshold, :integer, default: 5_000
    attribute :last_bet_amount, :integer, default: 0
    
    # 状态时间戳
    attribute :status_changed_at, :utc_datetime_usec
    attribute :game_joined_at, :utc_datetime_usec
    attribute :last_activity_at, :utc_datetime_usec
    
    # 踢出状态
    attribute :kick_reason, :string
    attribute :kicked_by, :string
    attribute :can_be_kicked, :boolean, default: true
    
    # 机器人配置
    attribute :robot_config, :map, default: %{}
    
    # 管理信息
    attribute :is_auto_created, :boolean, default: true
    attribute :creator_admin_id, :string
    attribute :tags, {:array, :string}, default: []
    attribute :is_enabled, :boolean, default: true
    
    timestamps()
  end

  actions do
    defaults [:read, :update, :destroy]
    
    create :create do
      primary? true
      accept [
        :robot_id, :nickname, :avatar_id, :level, :status,
        :current_points, :min_points_threshold, :robot_config,
        :is_auto_created, :creator_admin_id, :tags, :is_enabled,
        :status_changed_at, :last_activity_at
      ]
    end
    
    # 状态查询 - 只有启用且空闲的机器人才可用
    read :available do
      filter expr(status == :idle and is_enabled == true)
    end
    
    read :enabled do
      filter expr(is_enabled == true)
    end
    
    read :disabled do
      filter expr(is_enabled == false)
    end
    
    read :in_games do
      filter expr(status in [:assigned, :in_game, :in_round])
    end
    
    read :kickable do
      filter expr(can_be_kicked == true and status in [:assigned, :in_game] and is_in_round == false)
    end
    
    read :insufficient_funds do
      filter expr(status == :insufficient_funds)
    end
    
    # 根据robot_id查询
    read :by_robot_id do
      argument :robot_id, :integer, allow_nil?: false
      filter expr(robot_id == ^arg(:robot_id))
    end
    
    # 状态转换操作
    update :assign_to_game do
      argument :game_type, :string, allow_nil?: false
      argument :room_id, :string, allow_nil?: false
      argument :seat_number, :integer
      
      change set_attribute(:status, :assigned)
      change set_attribute(:current_game_type, arg(:game_type))
      change set_attribute(:current_room_id, arg(:room_id))
      change set_attribute(:seat_number, arg(:seat_number))
      change set_attribute(:game_joined_at, &DateTime.utc_now/0)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end
    
    update :enter_game do
      change set_attribute(:status, :in_game)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end
    
    update :enter_round do
      change set_attribute(:status, :in_round)
      change set_attribute(:is_in_round, true)
      change set_attribute(:can_be_kicked, false)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end
    
    update :exit_round do
      change set_attribute(:status, :in_game)
      change set_attribute(:is_in_round, false)
      change set_attribute(:can_be_kicked, true)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end
    
    update :kick_out do
      argument :reason, :string, allow_nil?: false
      argument :kicked_by, :string, allow_nil?: false
      
      change set_attribute(:status, :kicked_out)
      change set_attribute(:kick_reason, arg(:reason))
      change set_attribute(:kicked_by, arg(:kicked_by))
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:is_in_round, false)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
    
    update :start_recycling do
      argument :admin_id, :string, allow_nil?: false
      
      change set_attribute(:status, :recycling)
      change set_attribute(:kicked_by, arg(:admin_id))
      change set_attribute(:kick_reason, "后台回收中")
      change set_attribute(:can_be_kicked, false)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end

    update :recycle_robot do
      argument :admin_id, :string, allow_nil?: false
      
      change set_attribute(:status, :recycled)
      change set_attribute(:kicked_by, arg(:admin_id))
      change set_attribute(:kick_reason, "后台回收")
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:is_in_round, false)
      change set_attribute(:is_enabled, false)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
    
    update :mark_insufficient_funds do
      change set_attribute(:status, :insufficient_funds)
      change set_attribute(:kick_reason, "积分不足")
      change set_attribute(:kicked_by, "system")
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
    
    update :release_to_idle do
      change set_attribute(:status, :idle)
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:is_in_round, false)
      change set_attribute(:kick_reason, nil)
      change set_attribute(:kicked_by, nil)
      change set_attribute(:can_be_kicked, true)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
    
    update :update_points do
      argument :points, :integer, allow_nil?: false
      argument :bet_amount, :integer, default: 0
      
      change set_attribute(:current_points, arg(:points))
      change set_attribute(:last_bet_amount, arg(:bet_amount))
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end
    
    update :force_reset_to_idle do
      argument :current_room_id, :string
      argument :last_room_id, :string
      argument :reason, :string, default: "强制重置"
      argument :force_reset, :boolean, default: true
      
      change set_attribute(:status, :idle)
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:is_in_round, false)
      change set_attribute(:kick_reason, arg(:reason))
      change set_attribute(:kicked_by, "system_cleanup")
      change set_attribute(:can_be_kicked, true)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end
    
    update :enable_robot do
      change set_attribute(:is_enabled, true)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
    
    update :disable_robot do
      change set_attribute(:is_enabled, false)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
  end

  validations do
    validate numericality(:current_points, greater_than_or_equal_to: 0)
    validate present(:nickname)
    validate present(:robot_id)
  end
end