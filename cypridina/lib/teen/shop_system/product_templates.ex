defmodule Teen.ShopSystem.ProductTemplates do
  @moduledoc """
  商品模板预设数据

  提供各种商品类型的默认配置模板
  """

  alias Teen.ShopSystem.ProductTemplate

  @doc """
  创建所有预设模板
  """
  def create_default_templates do
    templates = [
      monthly_card_template(),
      weekly_card_template(),
      play_card_template(),
      coin_package_template(),
      vip_package_template(),
      special_item_template(),
      recharge_bonus_template()
    ]

    Enum.each(templates, fn template_data ->
      case ProductTemplate.create(template_data) do
        {:ok, template} ->
          IO.puts("✅ 创建模板成功: #{template.template_name}")

        {:error, reason} ->
          IO.puts("❌ 创建模板失败: #{template_data.template_name}, 原因: #{inspect(reason)}")
      end
    end)
  end

  # 月卡模板
  defp monthly_card_template do
    %{
      template_name: "标准月卡模板",
      product_type: :monthly_card,
      is_default: true,
      default_config: %{
        # 每日奖励1000游戏币
        "daily_reward" => 1000,
        # 30天
        "total_days" => 30,
        "card_benefits" => [
          "每日登录获得1000游戏币",
          "VIP特权加成",
          "专属客服服务"
        ]
      },
      config_schema: %{
        "daily_reward" => %{
          "type" => "number",
          "description" => "每日奖励金额（游戏币）",
          "min" => 100,
          "max" => 10000,
          "default" => 1000
        },
        "total_days" => %{
          "type" => "number",
          "description" => "卡片有效天数",
          "min" => 1,
          "max" => 365,
          "default" => 30
        },
        "card_benefits" => %{
          "type" => "array",
          "description" => "卡片特权列表",
          "items" => %{"type" => "string"}
        }
      }
    }
  end

  # 周卡模板
  defp weekly_card_template do
    %{
      template_name: "标准周卡模板",
      product_type: :weekly_card,
      is_default: true,
      default_config: %{
        # 每日奖励500游戏币
        "daily_reward" => 500,
        # 7天
        "total_days" => 7,
        "card_benefits" => [
          "每日登录获得500游戏币",
          "周卡专属活动"
        ]
      },
      config_schema: %{
        "daily_reward" => %{
          "type" => "number",
          "description" => "每日奖励金额（游戏币）",
          "min" => 100,
          "max" => 5000,
          "default" => 500
        },
        "total_days" => %{
          "type" => "number",
          "description" => "卡片有效天数",
          "min" => 1,
          "max" => 30,
          "default" => 7
        }
      }
    }
  end

  # 次卡模板
  defp play_card_template do
    %{
      template_name: "标准次卡模板",
      product_type: :play_card,
      is_default: true,
      default_config: %{
        # 30次游戏
        "play_count" => 30,
        # 奖励倍率
        "bonus_multiplier" => 1.2,
        # 有效期30天
        "valid_days" => 30
      },
      config_schema: %{
        "play_count" => %{
          "type" => "number",
          "description" => "游戏次数",
          "min" => 1,
          "max" => 1000,
          "default" => 30
        },
        "bonus_multiplier" => %{
          "type" => "number",
          "description" => "奖励倍率",
          "min" => 1.0,
          "max" => 3.0,
          "default" => 1.2
        },
        "valid_days" => %{
          "type" => "number",
          "description" => "有效期（天）",
          "min" => 1,
          "max" => 365,
          "default" => 30
        }
      }
    }
  end

  # 金币礼包模板
  defp coin_package_template do
    %{
      template_name: "标准金币礼包模板",
      product_type: :coin_package,
      is_default: true,
      default_config: %{
        # 10000游戏币
        "coin_amount" => 10000,
        # 20%额外奖励
        "bonus_percentage" => 20,
        # 立即发放
        "instant_delivery" => true
      },
      config_schema: %{
        "coin_amount" => %{
          "type" => "number",
          "description" => "游戏币数量",
          "min" => 1000,
          "max" => 1_000_000,
          "default" => 10000
        },
        "bonus_percentage" => %{
          "type" => "number",
          "description" => "额外奖励百分比",
          "min" => 0,
          "max" => 100,
          "default" => 20
        },
        "instant_delivery" => %{
          "type" => "boolean",
          "description" => "是否立即发放",
          "default" => true
        }
      }
    }
  end

  # VIP礼包模板
  defp vip_package_template do
    %{
      template_name: "标准VIP礼包模板",
      product_type: :vip_package,
      is_default: true,
      default_config: %{
        # VIP等级
        "vip_level" => 1,
        # VIP时长（天）
        "vip_duration" => 30,
        "included_items" => [
          %{"type" => "coins", "amount" => 5000},
          %{"type" => "play_times", "amount" => 10}
        ]
      },
      config_schema: %{
        "vip_level" => %{
          "type" => "number",
          "description" => "VIP等级",
          "min" => 1,
          "max" => 10,
          "default" => 1
        },
        "vip_duration" => %{
          "type" => "number",
          "description" => "VIP时长（天）",
          "min" => 1,
          "max" => 365,
          "default" => 30
        },
        "included_items" => %{
          "type" => "array",
          "description" => "包含的物品",
          "items" => %{
            "type" => "object",
            "properties" => %{
              "type" => %{"type" => "string"},
              "amount" => %{"type" => "number"}
            }
          }
        }
      }
    }
  end

  # 特殊道具模板
  defp special_item_template do
    %{
      template_name: "标准特殊道具模板",
      product_type: :special_item,
      is_default: true,
      default_config: %{
        # 道具类型
        "item_type" => "lucky_charm",
        # 道具数量
        "item_count" => 1,
        # 道具持续时间（天）
        "item_duration" => 7,
        "item_effects" => [
          %{"type" => "luck_boost", "value" => 20}
        ]
      },
      config_schema: %{
        "item_type" => %{
          "type" => "string",
          "description" => "道具类型",
          "enum" => ["lucky_charm", "exp_booster", "coin_multiplier"],
          "default" => "lucky_charm"
        },
        "item_count" => %{
          "type" => "number",
          "description" => "道具数量",
          "min" => 1,
          "max" => 100,
          "default" => 1
        },
        "item_duration" => %{
          "type" => "number",
          "description" => "道具持续时间（天）",
          "min" => 1,
          "max" => 30,
          "default" => 7
        }
      }
    }
  end

  # 充值奖励包模板
  defp recharge_bonus_template do
    %{
      template_name: "标准充值奖励包模板",
      product_type: :recharge_bonus,
      is_default: true,
      default_config: %{
        # 奖励金额
        "bonus_amount" => 2000,
        # 奖励类型
        "bonus_type" => "coins",
        # 需要手动领取
        "requires_claim" => true,
        # 7天过期
        "expires_in_days" => 7
      },
      config_schema: %{
        "bonus_amount" => %{
          "type" => "number",
          "description" => "奖励金额",
          "min" => 100,
          "max" => 100_000,
          "default" => 2000
        },
        "bonus_type" => %{
          "type" => "string",
          "description" => "奖励类型",
          "enum" => ["coins", "cash", "items"],
          "default" => "coins"
        },
        "requires_claim" => %{
          "type" => "boolean",
          "description" => "是否需要手动领取",
          "default" => true
        },
        "expires_in_days" => %{
          "type" => "number",
          "description" => "过期天数",
          "min" => 1,
          "max" => 30,
          "default" => 7
        }
      }
    }
  end
end
