defmodule Teen.ShopSystem do
  @moduledoc """
  商店系统域

  管理游戏中的商品销售，包括：
  - 商品管理（月卡、次卡、金币礼包等）
  - 商品模板配置
  - 用户购买记录
  - 商品发放逻辑
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  import Ash.Query, only: [filter: 2]

  alias Teen.ShopSystem.{
    Product,
    ProductTemplate,
    UserPurchase
  }

  alias Teen.ActivitySystem.UserMonthlyCard
  alias Teen.ActivitySystem.{UserMonthlyCard, UserReward}
  alias Cypridina.Ledger
  alias Cypridina.Ledger.AccountIdentifier

  admin do
    show? true
  end

  resources do
    resource Teen.ShopSystem.Product
    resource Teen.ShopSystem.ProductTemplate
    resource Teen.ShopSystem.UserPurchase
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  获取商店商品列表
  """
  def get_shop_products(category \\ nil) do
    case category do
      nil ->
        Product.list_active_products()

      category ->
        Product.list_by_category(%{category: category})
    end
  end

  @doc """
  根据类型获取商品
  """
  def get_products_by_type(product_type) do
    Product.list_by_type(%{product_type: product_type})
  end

  @doc """
  创建商品购买订单
  """
  def create_purchase_order(user_id, product_id, opts \\ []) do
    with {:ok, product} <- Product.read(product_id),
         :ok <- validate_product_available(product),
         {:ok, purchase_data} <- prepare_purchase_data(user_id, product, opts) do
      case UserPurchase.create(purchase_data) do
        {:ok, purchase} ->
          {:ok,
           %{
             purchase: purchase,
             payment_amount: purchase.paid_amount,
             currency: purchase.currency,
             product_info: %{
               name: product.name,
               type: product.product_type,
               config: product.product_config
             }
           }}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  @doc """
  处理支付完成后的商品发放
  """
  def deliver_purchased_product(purchase_id, payment_data \\ %{}) do
    with {:ok, purchase} <- get_purchase_by_id(purchase_id),
         :ok <- validate_purchase_deliverable(purchase),
         {:ok, delivery_result} <- deliver_product_by_type(purchase),
         {:ok, ledger_record} <- record_purchase_in_ledger(purchase, delivery_result) do
      # 更新购买记录状态
      UserPurchase.deliver_product(purchase, %{
        delivery_data: Map.merge(delivery_result, %{ledger_record: ledger_record})
      })
    end
  end

  @doc """
  根据模板创建商品
  """
  def create_product_from_template(template_id, product_params) do
    with {:ok, template} <- get_template_by_id(template_id),
         {:ok, product_config} <- merge_template_config(template, product_params) do
      Product.create(product_config)
    end
  end

  @doc """
  根据ID获取模板
  """
  def get_template_by_id(template_id) do
    ProductTemplate
    |> Ash.Query.new()
    |> Ash.Query.filter(id == ^template_id)
    |> Ash.read_one()
  end

  @doc """
  根据ID获取产品
  """
  def get_product_by_id(product_id) do
    Product
    |> Ash.Query.new()
    |> Ash.Query.filter(id == ^product_id)
    |> Ash.read_one()
  end

  @doc """
  根据ID获取购买记录
  """
  def get_purchase_by_id(purchase_id) do
    UserPurchase
    |> Ash.Query.new()
    |> Ash.Query.filter(id == ^purchase_id)
    |> Ash.read_one()
  end

  @doc """
  获取用户购买历史
  """
  def get_user_purchase_history(user_id, opts \\ []) do
    UserPurchase.list_by_user(%{user_id: user_id})
  end

  @doc """
  获取商品销售统计
  """
  def get_product_sales_stats(product_id, date_range \\ nil) do
    case UserPurchase.list_by_product(%{product_id: product_id}) do
      {:ok, purchases} ->
        completed_purchases = Enum.filter(purchases, &(&1.payment_status == :completed))

        stats = %{
          total_sales: length(completed_purchases),
          total_revenue:
            Enum.reduce(completed_purchases, Decimal.new("0"), fn purchase, acc ->
              Decimal.add(acc, purchase.paid_amount)
            end),
          pending_orders: Enum.count(purchases, &(&1.payment_status == :pending)),
          refunded_orders: Enum.count(purchases, &(&1.payment_status == :refunded))
        }

        {:ok, stats}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp validate_product_available(product) do
    case product.status do
      :active -> :ok
      :inactive -> {:error, "商品已下架"}
      :sold_out -> {:error, "商品已售罄"}
      _ -> {:error, "商品不可购买"}
    end
  end

  defp prepare_purchase_data(user_id, product, opts) do
    # 计算实际支付金额（可能有折扣）
    discount_rate = Keyword.get(opts, :discount_rate, Decimal.new("0"))
    discount_amount = Decimal.mult(product.price, discount_rate)
    paid_amount = Decimal.sub(product.price, discount_amount)

    purchase_data = %{
      user_id: user_id,
      product_id: product.id,
      product_name: product.name,
      product_type: product.product_type,
      product_config: product.product_config,
      original_price: product.price,
      paid_amount: paid_amount,
      currency: product.currency,
      payment_order_id: Keyword.get(opts, :payment_order_id)
    }

    {:ok, purchase_data}
  end

  defp validate_purchase_deliverable(purchase) do
    case {purchase.payment_status, purchase.delivery_status} do
      {:completed, :pending} -> :ok
      # 允许重新发放
      {:completed, :failed} -> :ok
      {:pending, _} -> {:error, "支付未完成"}
      {_, :delivered} -> {:error, "商品已发放"}
      {_, :cancelled} -> {:error, "订单已取消"}
      _ -> {:error, "订单状态异常"}
    end
  end

  defp deliver_product_by_type(purchase) do
    case purchase.product_type do
      :monthly_card -> deliver_monthly_card(purchase)
      :weekly_card -> deliver_weekly_card(purchase)
      :play_card -> deliver_play_card(purchase)
      :coin_package -> deliver_coin_package(purchase)
      :vip_package -> deliver_vip_package(purchase)
      :special_item -> deliver_special_item(purchase)
      :recharge_bonus -> deliver_recharge_bonus(purchase)
      _ -> {:error, "未知商品类型"}
    end
  end

  defp deliver_monthly_card(purchase) do
    config = purchase.product_config

    card_data = %{
      user_id: purchase.user_id,
      card_type: :monthly_card,
      purchase_amount: purchase.paid_amount,
      daily_reward: Decimal.new(config["daily_coins"] || "0"),
      total_days: config["duration_days"] || 30
    }

    case UserMonthlyCard.create(card_data) do
      {:ok, card} ->
        {:ok, %{type: "monthly_card", card_id: card.id, days: card.total_days}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp deliver_weekly_card(purchase) do
    config = purchase.product_config

    card_data = %{
      user_id: purchase.user_id,
      card_type: :weekly_card,
      purchase_amount: purchase.paid_amount,
      daily_reward: Decimal.new(config["daily_reward"] || "0"),
      total_days: config["total_days"] || 7
    }

    case UserMonthlyCard.create(card_data) do
      {:ok, card} ->
        {:ok, %{type: :weekly_card, card_id: card.id, days: card.total_days}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp deliver_play_card(purchase) do
    config = purchase.product_config
    play_count = config["play_count"] || 30

    # 这里可以创建次卡记录或直接增加用户的游戏次数
    # 具体实现取决于游戏的次数管理系统

    {:ok, %{type: :play_card, play_count: play_count}}
  end

  defp deliver_coin_package(purchase) do
    config = purchase.product_config
    coin_amount = Decimal.to_integer(Decimal.new(config["coin_amount"] || "0"))

    # 通过ledger系统增加用户余额
    system_identifier = AccountIdentifier.system(:shop_rewards, :XAA)
    user_identifier = AccountIdentifier.user(purchase.user_id, :XAA)

    case Ledger.transfer(system_identifier, user_identifier, coin_amount,
           transaction_type: :shop_purchase,
           description: "商品购买: #{purchase.product_name}",
           metadata: %{
             purchase_id: purchase.id,
             product_type: purchase.product_type,
             product_config: config
           }
         ) do
      {:ok, transfer} ->
        {:ok, %{type: :coin_package, coin_amount: coin_amount, transfer_id: transfer.id}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp deliver_vip_package(purchase) do
    config = purchase.product_config

    # 发放VIP经验或直接提升VIP等级
    # 具体实现取决于VIP系统设计

    {:ok, %{type: :vip_package, vip_benefits: config}}
  end

  defp deliver_special_item(purchase) do
    config = purchase.product_config

    # 发放特殊道具到用户背包
    # 具体实现取决于道具系统设计

    {:ok, %{type: :special_item, items: config["items"] || []}}
  end

  defp deliver_recharge_bonus(purchase) do
    config = purchase.product_config
    bonus_amount = Decimal.new(config["bonus_amount"] || "0")

    # 创建充值奖励记录
    reward_data = %{
      source_id: purchase.id,
      description: "充值奖励包",
      # 7天过期
      expires_at: DateTime.add(DateTime.utc_now(), 7, :day)
    }

    case Teen.ActivitySystem.RewardService.create_pending_reward(
           purchase.user_id,
           :coins,
           :recharge_bonus,
           bonus_amount,
           reward_data
         ) do
      {:ok, reward} ->
        {:ok, %{type: :recharge_bonus, reward_id: reward.id, bonus_amount: bonus_amount}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp merge_template_config(template, product_params) do
    # 合并模板的默认配置到产品配置中
    merged_product_config =
      Map.merge(template.default_config, product_params[:product_config] || %{})

    # 创建最终的产品参数
    final_params = Map.put(product_params, :product_config, merged_product_config)

    {:ok, final_params}
  end

  @doc """
  在ledger系统中记录购买交易
  """
  defp record_purchase_in_ledger(purchase, delivery_result) do
    # 为了完整的审计追踪，记录购买交易
    # 这里记录的是用户购买商品的支付记录
    user_identifier = AccountIdentifier.user(purchase.user_id, :XAA)
    shop_identifier = AccountIdentifier.system(:shop_income, :XAA)

    paid_amount = Decimal.to_integer(purchase.paid_amount)

    case Ledger.transfer(user_identifier, shop_identifier, paid_amount,
           transaction_type: :shop_payment,
           description: "商品购买支付: #{purchase.product_name}",
           metadata: %{
             purchase_id: purchase.id,
             product_type: purchase.product_type,
             delivery_result: delivery_result
           }
         ) do
      {:ok, transfer} ->
        {:ok, %{transfer_id: transfer.id, amount: paid_amount}}

      {:error, reason} ->
        Logger.error("记录购买交易失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
