defmodule CypridinaWeb.MockPaymentController do
  @moduledoc """
  模拟支付控制器

  在开发/测试环境中提供模拟支付页面和处理逻辑，支持多种测试场景：
  - 正常支付成功/失败
  - 支付超时
  - 部分支付
  - 网络错误模拟
  - 签名错误模拟
  - 延迟回调模拟
  """

  use CypridinaWeb, :controller
  require Logger

  alias Teen.PaymentSystem.PaymentOrder
  alias Teen.PaymentSystem.Gateways.MasterPayGateway

  @doc """
  显示模拟支付页面
  """
  def show(conn, %{"order_id" => order_id}) do
    case PaymentOrder.get_by_order_id( order_id) do
      {:ok, payment_order} ->
        Logger.info("💰 [MOCK_PAYMENT] 显示支付页面: #{order_id}")

        # 渲染模拟支付页面
        conn
        |> put_resp_content_type("text/html")
        |> send_resp(200, mock_payment_page_html(payment_order))

      {:error, _reason} ->
        Logger.error("💰 [MOCK_PAYMENT] 订单不存在: #{order_id}")

        conn
        |> put_resp_content_type("text/html")
        |> send_resp(404, error_page_html("订单不存在"))
    end
  end

  @doc """
  处理模拟支付成功
  """
  def success(conn, %{"order_id" => order_id} = params) do
    # 获取测试场景参数
    scenario = Map.get(params, "scenario", "normal")
    delay = Map.get(params, "delay", "0") |> String.to_integer()

    # 如果需要延迟
    if delay > 0 do
      Process.sleep(delay * 1000)
    end

    case PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        Logger.info("💰 [MOCK_PAYMENT] 处理支付成功 (场景: #{scenario}): #{order_id}")

        # 根据场景构建不同的回调数据
        callback_data = build_callback_data(payment_order, scenario)

        case payment_order
             |> Ash.Changeset.for_update(:complete_order, %{
               gateway_response: callback_data,
               callback_data: callback_data,
               actual_amount: payment_order.amount
             })
             |> Ash.update() do
          {:ok, updated_order} ->
            Logger.info("💰 [MOCK_PAYMENT] 订单状态更新成功: #{order_id}")

            # 触发充值完成处理
            case Teen.Services.RechargeService.complete_recharge(order_id, %{
              gateway_order_id: callback_data["payOrderId"],
              status: :success,
              amount: updated_order.amount,
              real_amount: updated_order.actual_amount || updated_order.amount
            }) do
              {:ok, result} ->
                Logger.info("💰 [MOCK_PAYMENT] 订单支付完成后发货成功: #{order_id}")
                Logger.info("💰 [MOCK_PAYMENT] 充值处理结果: #{inspect(result)}")
              {:error, reason} ->
                Logger.error("💰 [MOCK_PAYMENT] 订单支付完成后发货失败: #{inspect(reason)}")
                Logger.error("💰 [MOCK_PAYMENT] 失败的订单详情: 用户=#{updated_order.user_id}, 金额=#{updated_order.amount}")
            end

            conn
            |> put_resp_content_type("text/html")
            |> send_resp(200, success_page_html(updated_order))

          {:error, reason} ->
            Logger.error("💰 [MOCK_PAYMENT] 订单状态更新失败: #{inspect(reason)}")

            conn
            |> put_resp_content_type("text/html")
            |> send_resp(500, error_page_html("支付处理失败"))
        end

      {:error, _reason} ->
        Logger.error("💰 [MOCK_PAYMENT] 订单不存在: #{order_id}")

        conn
        |> put_resp_content_type("text/html")
        |> send_resp(404, error_page_html("订单不存在"))
    end
  end

  @doc """
  处理模拟支付失败
  """
  def failure(conn, %{"order_id" => order_id} = params) do
    failure_reason = Map.get(params, "reason", "用户取消支付")

    case PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        Logger.info("💰 [MOCK_PAYMENT] 处理支付失败: #{order_id}")

        # 更新订单状态为失败
        case payment_order
             |> Ash.Changeset.for_update(:fail_order, %{
               error_message: failure_reason,
               gateway_response: %{"status" => "FAILED", "reason" => failure_reason}
             })
             |> Ash.update() do
          {:ok, _updated_order} ->
            Logger.info("💰 [MOCK_PAYMENT] 订单状态更新为失败: #{order_id}")

            conn
            |> put_resp_content_type("text/html")
            |> send_resp(200, failure_page_html(payment_order))

          {:error, reason} ->
            Logger.error("💰 [MOCK_PAYMENT] 订单状态更新失败: #{inspect(reason)}")

            conn
            |> put_resp_content_type("text/html")
            |> send_resp(500, error_page_html("支付处理失败"))
        end

      {:error, _reason} ->
        Logger.error("💰 [MOCK_PAYMENT] 订单不存在: #{order_id}")

        conn
        |> put_resp_content_type("text/html")
        |> send_resp(404, error_page_html("订单不存在"))
    end
  end

  @doc """
  模拟支付回调（用于测试异步回调场景）
  """
  def callback(conn, %{"type" => callback_type, "order_id" => order_id} = params) do
    Logger.info("💰 [MOCK_PAYMENT] 收到模拟回调: #{callback_type}/#{order_id}")

    # 构建MasterPay88格式的回调数据
    callback_params = build_masterpay88_callback(order_id, params)

    # 添加模拟的客户端IP
    callback_params = Map.put(callback_params, "client_ip", "*************")

    # 调用网关的回调处理
    case MasterPay88Gateway.handle_callback(callback_params, callback_type) do
      {:ok, :processed} ->
        conn
        |> put_resp_content_type("text/plain")
        |> send_resp(200, "SUCCESS")

      {:error, reason} ->
        Logger.error("💰 [MOCK_PAYMENT] 回调处理失败: #{inspect(reason)}")

        conn
        |> put_resp_content_type("text/plain")
        |> send_resp(400, "FAIL")
    end
  end

  @doc """
  测试工具页面 - 显示所有可用的测试场景
  """
  def test_tools(conn, _params) do
    conn
    |> put_resp_content_type("text/html")
    |> send_resp(200, test_tools_html())
  end

  # 私有函数：根据场景构建回调数据
  defp build_callback_data(payment_order, scenario) do
    base_data = %{
      "payOrderId" => "MOCK_#{payment_order.order_id}_#{System.unique_integer([:positive])}",
      "mchOrderNo" => payment_order.order_id,
      "amount" => "#{payment_order.amount}",
      "realAmount" => "#{payment_order.amount}",
      "currency" => payment_order.currency,
      "status" => "1",
      "paySuccessTime" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "utr" => "UTR#{System.unique_integer([:positive])}"
    }

    case scenario do
      "partial_payment" ->
        # 部分支付场景 - 实际支付金额少于订单金额
        partial_amount = div(payment_order.amount * 90, 100)
        Map.put(base_data, "realAmount", "#{partial_amount}")

      "overpayment" ->
        # 超额支付场景
        over_amount = div(payment_order.amount * 110, 100)
        Map.put(base_data, "realAmount", "#{over_amount}")

      "invalid_signature" ->
        # 签名错误场景 - 添加一个错误的签名
        Map.put(base_data, "sign", "INVALID_SIGNATURE_FOR_TESTING")

      _ ->
        # 正常场景
        base_data
    end
  end

  # 私有函数：构建MasterPay88格式的回调参数
  defp build_masterpay88_callback(order_id, params) do
    status = Map.get(params, "status", "1")
    amount = Map.get(params, "amount", "10000")
    real_amount = Map.get(params, "real_amount", amount)

    callback_data = %{
      "payOrderId" => "MOCK_#{order_id}_#{System.unique_integer([:positive])}",
      "mchOrderNo" => order_id,
      "amount" => amount,
      "realAmount" => real_amount,
      "status" => status,
      "income" => real_amount,
      "fee" => "0",
      "paySuccessTime" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "utr" => "UTR#{System.unique_integer([:positive])}",
      "param1" => Map.get(params, "param1", ""),
      "param2" => Map.get(params, "param2", "")
    }

    # 生成正确的签名
    sign = MasterPay88Gateway.generate_signature(callback_data)
    Map.put(callback_data, "sign", sign)
  end

  # 私有函数：生成模拟支付页面HTML
  defp mock_payment_page_html(payment_order) do
    amount_inr = Decimal.div(payment_order.amount, 100)

    """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模拟支付 - Cypridina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .payment-card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .amount { font-size: 32px; color: #2196F3; font-weight: bold; margin: 20px 0; }
            .info { margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
            .buttons { margin-top: 30px; }
            .btn { display: block; width: 100%; padding: 15px; margin: 10px 0; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; text-decoration: none; text-align: center; }
            .btn-success { background: #4CAF50; color: white; }
            .btn-danger { background: #f44336; color: white; }
            .btn-warning { background: #ff9800; color: white; }
            .btn:hover { opacity: 0.9; }
            .mock-notice { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin-bottom: 20px; text-align: center; }
        </style>
    </head>
    <body>
        <div class="payment-card">
            <div class="mock-notice">
                🧪 这是模拟支付环境，仅用于测试
            </div>

            <div class="header">
                <h2>💳 支付确认</h2>
            </div>

            <div class="amount">₹#{amount_inr} INR</div>

            <div class="info">
                <strong>订单号:</strong> #{payment_order.order_id}
            </div>

            <div class="info">
                <strong>商品:</strong> 游戏充值
            </div>

            <div class="info">
                <strong>货币:</strong> #{payment_order.currency}
            </div>

            <div class="buttons">
                <h3 style="margin-top: 30px; margin-bottom: 15px;">正常测试场景</h3>
                <a href="/api/mock-payment/#{payment_order.order_id}/success" class="btn btn-success">
                    ✅ 支付成功
                </a>

                <a href="/api/mock-payment/#{payment_order.order_id}/failure" class="btn btn-danger">
                    ❌ 取消支付
                </a>

                <h3 style="margin-top: 30px; margin-bottom: 15px;">特殊测试场景</h3>
                <a href="/api/mock-payment/#{payment_order.order_id}/success?scenario=partial_payment" class="btn btn-warning">
                    💸 部分支付 (90%)
                </a>

                <a href="/api/mock-payment/#{payment_order.order_id}/success?scenario=overpayment" class="btn btn-warning">
                    💰 超额支付 (110%)
                </a>

                <a href="/api/mock-payment/#{payment_order.order_id}/success?delay=5" class="btn btn-warning">
                    ⏱️ 延迟5秒支付
                </a>

                <a href="/api/mock-payment/#{payment_order.order_id}/failure?reason=insufficient_balance" class="btn btn-warning">
                    ❌ 余额不足
                </a>

                <a href="/api/mock-payment/#{payment_order.order_id}/failure?reason=bank_maintenance" class="btn btn-warning">
                    🏦 银行维护
                </a>

                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 5px;">
                    <strong>高级测试:</strong>
                    <a href="/api/mock-payment/test-tools" style="color: #1976d2;">打开测试工具面板</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
  end

  # 私有函数：生成支付成功页面HTML
  defp success_page_html(payment_order) do
    amount_inr = Decimal.div(payment_order.amount, 100)

    """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>支付成功 - Cypridina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .success-card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
            .success-icon { font-size: 64px; color: #4CAF50; margin-bottom: 20px; }
            .amount { font-size: 24px; color: #4CAF50; font-weight: bold; margin: 20px 0; }
            .info { margin: 10px 0; color: #666; }
            .btn { display: inline-block; padding: 12px 24px; background: #2196F3; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
        </style>
        <script>
            // 3秒后自动关闭窗口
            setTimeout(function() {
                if (window.opener) {
                    window.close();
                } else {
                    window.location.href = '/';
                }
            }, 3000);
        </script>
    </head>
    <body>
        <div class="success-card">
            <div class="success-icon">✅</div>
            <h2>支付成功！</h2>
            <div class="amount">₹#{amount_inr} INR</div>
            <div class="info">订单号: #{payment_order.order_id}</div>
            <div class="info">支付时间: #{DateTime.utc_now() |> DateTime.to_string()}</div>
            <div style="margin-top: 20px; color: #999; font-size: 14px;">
                页面将在3秒后自动关闭...
            </div>
        </div>
    </body>
    </html>
    """
  end

  # 私有函数：生成支付失败页面HTML
  defp failure_page_html(payment_order) do
    amount_inr = Decimal.div(payment_order.amount, 100)

    """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>支付取消 - Cypridina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .failure-card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
            .failure-icon { font-size: 64px; color: #f44336; margin-bottom: 20px; }
            .amount { font-size: 24px; color: #666; margin: 20px 0; }
            .info { margin: 10px 0; color: #666; }
        </style>
        <script>
            // 3秒后自动关闭窗口
            setTimeout(function() {
                if (window.opener) {
                    window.close();
                } else {
                    window.location.href = '/';
                }
            }, 3000);
        </script>
    </head>
    <body>
        <div class="failure-card">
            <div class="failure-icon">❌</div>
            <h2>支付已取消</h2>
            <div class="amount">₹#{amount_inr} INR</div>
            <div class="info">订单号: #{payment_order.order_id}</div>
            <div style="margin-top: 20px; color: #999; font-size: 14px;">
                页面将在3秒后自动关闭...
            </div>
        </div>
    </body>
    </html>
    """
  end

  # 私有函数：生成测试工具页面HTML
  defp test_tools_html do
    """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>支付测试工具 - Cypridina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 30px auto; padding: 20px; background: #f5f5f5; }
            .tools-card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
            h1 { color: #2196F3; }
            h2 { color: #666; margin-top: 30px; }
            .scenario { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
            .endpoint { font-family: monospace; background: #e3f2fd; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .param { color: #d32f2f; font-weight: bold; }
            .description { color: #666; margin-top: 5px; }
            .example { background: #263238; color: #aed581; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; overflow-x: auto; }
        </style>
    </head>
    <body>
        <div class="tools-card">
            <h1>🧪 支付测试工具</h1>
            <p>本页面提供了完整的支付测试场景，帮助您测试各种支付情况。</p>

            <h2>📋 基础测试场景</h2>

            <div class="scenario">
                <strong>正常支付成功</strong>
                <div class="endpoint">GET /api/mock-payment/{order_id}/success</div>
                <div class="description">模拟用户正常完成支付，订单金额与实际支付金额一致。</div>
            </div>

            <div class="scenario">
                <strong>支付取消/失败</strong>
                <div class="endpoint">GET /api/mock-payment/{order_id}/failure</div>
                <div class="description">模拟用户取消支付或支付失败。</div>
            </div>

            <h2>🎭 特殊测试场景</h2>

            <div class="scenario">
                <strong>部分支付</strong>
                <div class="endpoint">GET /api/mock-payment/{order_id}/success?<span class="param">scenario=partial_payment</span></div>
                <div class="description">模拟用户支付金额少于订单金额的情况（支付90%）。</div>
            </div>

            <div class="scenario">
                <strong>超额支付</strong>
                <div class="endpoint">GET /api/mock-payment/{order_id}/success?<span class="param">scenario=overpayment</span></div>
                <div class="description">模拟用户支付金额超过订单金额的情况（支付110%）。</div>
            </div>

            <div class="scenario">
                <strong>延迟支付</strong>
                <div class="endpoint">GET /api/mock-payment/{order_id}/success?<span class="param">delay=5</span></div>
                <div class="description">模拟网络延迟，延迟指定秒数后完成支付（最大60秒）。</div>
            </div>

            <div class="scenario">
                <strong>自定义失败原因</strong>
                <div class="endpoint">GET /api/mock-payment/{order_id}/failure?<span class="param">reason=insufficient_balance</span></div>
                <div class="description">模拟特定的支付失败原因。常见原因：insufficient_balance, bank_maintenance, network_error</div>
            </div>

            <h2>🔄 异步回调测试</h2>

            <div class="scenario">
                <strong>模拟支付网关回调</strong>
                <div class="endpoint">POST /api/mock-payment/callback/{type}/{order_id}</div>
                <div class="description">直接触发支付回调，测试异步通知处理。type可以是recharge或withdrawal。</div>
                <div class="example">curl -X POST http://localhost:4000/api/mock-payment/callback/recharge/ORDER123 \\
    -H "Content-Type: application/json" \\
    -d '{"status": "1", "amount": "10000", "real_amount": "10000"}'</div>
            </div>

            <div class="scenario">
                <strong>回调参数说明</strong>
                <ul>
                    <li><code>status</code>: 订单状态 (1=成功, 3=超时, 7=驳回)</li>
                    <li><code>amount</code>: 订单金额（分）</li>
                    <li><code>real_amount</code>: 实际支付金额（分）</li>
                    <li><code>param1</code>, <code>param2</code>: 自定义参数</li>
                </ul>
            </div>

            <h2>⚡ 批量测试</h2>

            <div class="scenario">
                <strong>自动化测试脚本</strong>
                <div class="description">您可以使用以下脚本进行批量测试：</div>
                <div class="example"># 测试所有场景
    ORDER_ID="TEST_$(date +%s)"

    # 创建订单
    curl -X POST http://localhost:4000/api/payment/orders \\
    -H "Content-Type: application/json" \\
    -d '{"amount": 10000, "currency": "INR"}'

    # 测试各种场景
    curl http://localhost:4000/api/mock-payment/$ORDER_ID/success
    curl http://localhost:4000/api/mock-payment/$ORDER_ID/success?scenario=partial_payment
    curl http://localhost:4000/api/mock-payment/$ORDER_ID/failure?reason=network_error</div>
            </div>

            <h2>📝 注意事项</h2>
            <ul>
                <li>模拟支付仅在开发/测试环境可用</li>
                <li>所有金额单位为分（paise）</li>
                <li>支付成功后会触发相应的业务逻辑</li>
                <li>可以通过日志查看详细的处理过程</li>
            </ul>
        </div>
    </body>
    </html>
    """
  end

  # 私有函数：生成错误页面HTML
  defp error_page_html(message) do
    """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>错误 - Cypridina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .error-card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
            .error-icon { font-size: 64px; color: #ff9800; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="error-card">
            <div class="error-icon">⚠️</div>
            <h2>#{message}</h2>
        </div>
    </body>
    </html>
    """
  end
end
