defmodule CypridinaWeb.PaymentController do
  @moduledoc """
  支付回调控制器

  处理支付网关的回调通知
  """

  use CypridinaWeb, :controller
  require Logger

  @doc """
  处理支付通知回调
  """
  def handle_notify(conn, params) do
    Logger.info("收到支付通知回调: #{inspect(params)}")
    # %{"amount" => "10000", "income" => "9570", "mchId" => "10236", "mchOrderNo" => "CYP17525020882518800", "payOrderId" => "1752502123743TN", "paySuccessTime" => "1752502334000", "productId" => "3021", "realAmount" => "10000", "sign" => "5520D9DCC5088E9DBBC1543E80288747", "status" => "1", "utr" => "2002424141"}

    case verify_and_process_payment_callback(params) do
      {:ok, _result} ->
        # 返回成功响应给支付网关 - 按照API文档返回纯文本"SUCCESS"
        conn
        |> put_status(200)
        |> put_resp_content_type("text/plain")
        |> text("SUCCESS")

      {:error, reason} ->
        Logger.error("支付回调处理失败: #{inspect(reason)}")

        # 失败时仍返回200状态码但返回错误消息
        conn
        |> put_status(200)
        |> put_resp_content_type("text/plain")
        |> text("FAILED")
    end
  end

  # 私有函数
  defp verify_and_process_payment_callback(params) do
    Logger.info("开始处理支付回调 - 参数: #{inspect(params)}")

    with {:ok, order_id} <- extract_order_id(params),
         {:ok, status} <- extract_payment_status(params),
         :ok <- verify_callback_signature(params),
         {:ok, result} <- process_payment_callback(order_id, status, params) do
      Logger.info("支付回调处理完成 - 订单号: #{order_id}, 状态: #{status}")
      {:ok, result}
    else
      {:error, reason} = error ->
        Logger.error("支付回调处理失败 - 原因: #{inspect(reason)}, 参数: #{inspect(params)}")
        error

      error ->
        Logger.error("支付回调处理遇到未知错误 - 错误: #{inspect(error)}, 参数: #{inspect(params)}")
        {:error, "Unknown error: #{inspect(error)}"}
    end
  end

  defp extract_order_id(params) do
    # 按照API文档，主订单ID参数为mchOrderNo
    case Map.get(params, "mchOrderNo") do
      nil -> {:error, "Missing mchOrderNo"}
      mchOrderNo when is_binary(mchOrderNo) and mchOrderNo != "" -> {:ok, mchOrderNo}
      _ -> {:error, "Invalid mchOrderNo format"}
    end
  end

  defp extract_payment_status(params) do
    # 按照API文档的状态码规范：0=支付中，1=已完成，3=已超时，5=驳回中，7=已驳回，9=冲正
    case Map.get(params, "status") do
      nil ->
        {:error, "Missing payment status"}

      "1" ->
        {:ok, "success"}

      "0" ->
        {:ok, "pending"}

      "3" ->
        {:ok, "timeout"}

      "5" ->
        {:ok, "rejected"}

      "7" ->
        {:ok, "failed"}

      "9" ->
        {:ok, "correction"}
      _ ->
        {:error, "Invalid payment status format"}
    end
  end

  defp verify_callback_signature(params) do
    # 提取签名
    received_sign = Map.get(params, "sign") || Map.get(params, "signature")

    if received_sign do
      # 生成期望的签名
      expected_sign = Teen.Services.MasterPayService.generate_payment_signature(params)

      if received_sign == expected_sign do
        :ok
      else
        Logger.warning("支付回调签名验证失败: received=#{received_sign}, expected=#{expected_sign}")
        {:error, "Invalid signature"}
      end
    else
      Logger.warning("支付回调缺少签名")
      {:error, "Missing signature"}
    end
  end

  defp process_payment_callback(order_id, status, callback_data) do
    # 按照API文档提取参数
    amount = extract_amount_from_callback(callback_data, "amount")
    real_amount = extract_amount_from_callback(callback_data, "realAmount")
    income = extract_amount_from_callback(callback_data, "income")

    # 提取平台订单ID
    pay_order_id = Map.get(callback_data, "payOrderId")

    # 提取支付成功时间
    pay_success_time = Map.get(callback_data, "paySuccessTime")

    # 提取UTR银行交易参考号
    utr = Map.get(callback_data, "utr")

    # 提取扩展参数
    param1 = Map.get(callback_data, "param1")
    param2 = Map.get(callback_data, "param2")

    # 提取驳回原因
    reject_reason = Map.get(callback_data, "rejectReason")

    # 构建回调数据
    callback_params = %{
      order_id: order_id,
      status: status,
      amount: amount,
      real_amount: real_amount,
      income: income,
      pay_order_id: pay_order_id,
      pay_success_time: pay_success_time,
      utr: utr,
      param1: param1,
      param2: param2,
      reject_reason: reject_reason,
      callback_data: callback_data
    }

    # 调用支付订单处理
    case Teen.PaymentSystem.PaymentOrder.handle_callback(callback_params) do
      {:ok, payment_order} ->
        Logger.info(
          "支付回调处理成功: order_id=#{order_id}, status=#{status}, pay_order_id=#{pay_order_id}"
        )

        {:ok, payment_order}

      {:error, reason} ->
        Logger.error("支付回调处理失败: order_id=#{order_id}, reason=#{inspect(reason)}")
        {:error, reason}
    end
  end

  # 从回调数据中提取金额信息的辅助函数
  defp extract_amount_from_callback(callback_data, key) do
    case Map.get(callback_data, key) do
      nil ->
        nil

      amount_str when is_binary(amount_str) ->
        case Decimal.parse(amount_str) do
          {decimal, _} -> decimal
          :error -> nil
        end

      amount when is_number(amount) ->
        Decimal.new(amount)

      _ ->
        nil
    end
  end
end
