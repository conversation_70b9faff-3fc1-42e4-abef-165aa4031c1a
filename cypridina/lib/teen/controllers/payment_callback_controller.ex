defmodule CypridinaWeb.PaymentCallbackController do
  @moduledoc """
  支付回调控制器

  处理来自支付网关的回调请求，包括：
  - MasterPay回调处理
  - 签名验证
  - IP验证
  - 回调数据处理
  """

  use CypridinaWeb, :controller

  require Logger

  alias Teen.PaymentSystem.{PaymentCallback, PaymentOrder}
  alias Teen.PaymentSystem.Gateways.MasterPayGateway

  @doc """
  处理MasterPay充值回调
  """
  def masterpay_recharge(conn, %{"order_id" => order_id} = params) do
    handle_masterpay_callback(conn, order_id, "recharge", params)
  end

  @doc """
  处理MasterPay提现回调
  """
  def masterpay_withdrawal(conn, %{"order_id" => order_id} = params) do
    handle_masterpay_callback(conn, order_id, "withdrawal", params)
  end

  @doc """
  处理MasterPay通用回调
  """
  def masterpay_callback(conn, %{"type" => callback_type, "order_id" => order_id} = params) do
    handle_masterpay_callback(conn, order_id, callback_type, params)
  end

  # ==================== 私有函数 ====================

  defp handle_masterpay_callback(conn, order_id, callback_type, params) do
    client_ip = get_client_ip(conn)

    Logger.info("收到MasterPay回调: 订单#{order_id}, 类型#{callback_type}, IP#{client_ip}")
    Logger.debug("回调参数: #{inspect(params)}")

    # 获取支付订单
    case PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        # 检查订单是否已经处理 (幂等性检查)
        if payment_order.status in ["completed", "failed", "cancelled"] do
          Logger.info("订单已处理，跳过回调: #{order_id}, 状态: #{payment_order.status}")
          send_success_response(conn)
        else
          # 创建回调记录
          callback_record =
            create_callback_record(payment_order, callback_type, client_ip, params)

          # 验证回调
          case verify_callback(params, client_ip) do
            :ok ->
              # 处理回调
              case process_callback(payment_order, callback_type, params) do
                {:ok, result} ->
                  # 更新回调记录为已处理
                  update_callback_record(callback_record, result)

                  Logger.info("回调处理成功: 订单#{order_id}")
                  send_success_response(conn)

                {:error, reason} ->
                  # 更新回调记录为处理失败
                  update_callback_record(callback_record, %{error: reason})

                  Logger.error("回调处理失败: 订单#{order_id}, 原因: #{inspect(reason)}")
                  # 即使处理失败，也返回SUCCESS避免重复回调
                  send_success_response(conn)
              end

            {:error, reason} ->
              # 更新回调记录为验证失败
              update_callback_record(callback_record, %{error: reason})

              Logger.error("回调验证失败: 订单#{order_id}, 原因: #{inspect(reason)}")
              send_error_response(conn, reason)
          end
        end

      {:error, reason} ->
        Logger.error("找不到订单: #{order_id}, 原因: #{inspect(reason)}")
        send_error_response(conn, "Order not found")
    end
  end

  defp create_callback_record(payment_order, callback_type, client_ip, params) do
    # 先验证签名和IP
    signature_verified =
      case MasterPay88Gateway.verify_callback_signature(params) do
        :ok -> true
        _ -> false
      end

    ip_verified =
      case MasterPay88Gateway.verify_callback_ip(client_ip) do
        :ok -> true
        _ -> false
      end

    callback_data = %{
      payment_order_id: payment_order.id,
      callback_type: callback_type,
      client_ip: client_ip,
      callback_data: params,
      signature_verified: signature_verified,
      ip_verified: ip_verified
    }

    case PaymentCallback.create(callback_data) do
      {:ok, callback} ->
        Logger.debug("创建回调记录成功: #{callback.id}")
        callback

      {:error, reason} ->
        Logger.error("创建回调记录失败: #{inspect(reason)}")
        nil
    end
  end

  defp verify_callback(params, client_ip) do
    with :ok <- MasterPay88Gateway.verify_callback_ip(client_ip),
         :ok <- MasterPay88Gateway.verify_callback_signature(params) do
      :ok
    else
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp process_callback(payment_order, callback_type, params) do
    callback_result = %{
      gateway_order_id: params["payOrderId"],
      merchant_order_id: params["mchOrderNo"],
      amount: String.to_integer(params["amount"] || "0"),
      real_amount: String.to_integer(params["realAmount"] || params["amount"] || "0"),
      income: String.to_integer(params["income"] || "0"),
      status: parse_status(params["status"]),
      gateway_fee: String.to_integer(params["fee"] || "0"),
      pay_success_time: params["paySuccessTime"],
      utr: params["utr"],
      param1: params["param1"],
      param2: params["param2"],
      reject_reason: params["rejectReason"]
    }

    case callback_type do
      "recharge" ->
        handle_recharge_callback(payment_order, callback_result)

      "withdrawal" ->
        handle_withdrawal_callback(payment_order, callback_result)

      _ ->
        {:error, "Unknown callback type"}
    end
  end

  defp handle_recharge_callback(payment_order, callback_result) do
    case callback_result.status do
      :success ->
        # 充值成功，增加用户余额
        with {:ok, _} <-
               PaymentOrder.complete_order(payment_order, %{
                 gateway_response: callback_result,
                 callback_data: callback_result,
                 fee_amount: callback_result.gateway_fee,
                 actual_amount: callback_result.amount
               }),
             {:ok, _} <- add_user_balance(payment_order.user_id, callback_result.amount) do
          Logger.info("充值成功: 用户#{payment_order.user_id}, 金额#{callback_result.amount}")

          # 发送充值成功通知
          Teen.Services.NotificationService.send_recharge_success_notification(
            payment_order.user_id,
            %{
              recharge_id: payment_order.id,
              amount: callback_result.amount,
              bonus_amount: 0,
              new_balance: get_user_balance(payment_order.user_id),
              payment_method: payment_order.payment_method
            }
          )

          {:ok, %{status: :success, order_id: payment_order.order_id}}
        else
          {:error, reason} ->
            Logger.error("充值失败: #{inspect(reason)}")

            PaymentOrder.fail_order(payment_order, %{
              error_message: inspect(reason),
              callback_data: callback_result
            })

            {:error, reason}
        end

      :timeout ->
        # 超时状态
        Logger.info("充值超时: 订单#{payment_order.order_id}")
        {:ok, %{status: :timeout, order_id: payment_order.order_id}}

      :failed ->
        # 充值失败
        PaymentOrder.fail_order(payment_order, %{
          error_message: "Gateway payment failed",
          callback_data: callback_result
        })

        Logger.info("充值失败: 订单#{payment_order.order_id}")
        {:ok, %{status: :failed, order_id: payment_order.order_id}}

      _ ->
        # 其他状态暂时不处理
        Logger.info("充值状态更新: 订单#{payment_order.order_id}, 状态#{callback_result.status}")
        {:ok, %{status: callback_result.status, order_id: payment_order.order_id}}
    end
  end

  defp handle_withdrawal_callback(payment_order, callback_result) do
    case callback_result.status do
      :success ->
        # 提现成功
        PaymentOrder.complete_order(payment_order, %{
          gateway_response: callback_result,
          callback_data: callback_result,
          fee_amount: callback_result.gateway_fee,
          actual_amount: callback_result.amount
        })

        Logger.info("提现成功: 用户#{payment_order.user_id}, 金额#{callback_result.amount}")

        # 发送提现成功通知
        Teen.Services.NotificationService.send_withdrawal_status_notification(
          payment_order.user_id,
          %{
            withdrawal_id: payment_order.id,
            status: :completed,
            amount: callback_result.amount,
            fee_amount: callback_result.gateway_fee,
            actual_amount: callback_result.amount - callback_result.gateway_fee,
            message: "提现成功",
            estimated_time: nil
          }
        )

        {:ok, %{status: :success, order_id: payment_order.order_id}}

      status when status in [:timeout, :rejected, :reversed] ->
        # 提现失败，需要退回用户余额
        with {:ok, _} <-
               PaymentOrder.fail_order(payment_order, %{
                 error_message:
                   "Gateway withdrawal #{status}: #{callback_result.reject_reason || "No reason"}",
                 callback_data: callback_result
               }),
             {:ok, _} <- refund_withdrawal_amount(payment_order.user_id, callback_result.amount) do
          Logger.info("提现失败，已退回余额: 用户#{payment_order.user_id}, 金额#{callback_result.amount}")

          # 发送提现失败通知
          Teen.Services.NotificationService.send_withdrawal_status_notification(
            payment_order.user_id,
            %{
              withdrawal_id: payment_order.id,
              status: :failed,
              amount: callback_result.amount,
              fee_amount: callback_result.gateway_fee,
              actual_amount: callback_result.amount,
              message: "提现失败，已退回余额",
              estimated_time: nil
            }
          )

          {:ok, %{status: :failed, order_id: payment_order.order_id}}
        else
          {:error, reason} ->
            Logger.error("提现失败退款失败: #{inspect(reason)}")
            {:error, reason}
        end

      _ ->
        # 其他状态暂时不处理
        Logger.info("提现状态更新: 订单#{payment_order.order_id}, 状态#{callback_result.status}")
        {:ok, %{status: callback_result.status, order_id: payment_order.order_id}}
    end
  end

  defp update_callback_record(callback_record, result) do
    if callback_record do
      case result do
        %{error: error} ->
          PaymentCallback.mark_failed(callback_record, %{error_message: inspect(error)})

        _ ->
          PaymentCallback.mark_processed(callback_record, %{process_result: result})
      end
    end
  end

  defp add_user_balance(user_id, amount) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    recharge_identifier = Cypridina.Ledger.AccountIdentifier.system(:recharge_income, :XAA)

    case Cypridina.Ledger.transfer(recharge_identifier, user_identifier, amount,
           transaction_type: :deposit,
           description: "充值到账"
         ) do
      {:ok, transfer} ->
        Logger.info("充值到账成功: 用户#{user_id}, 金额#{amount}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("充值到账失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp refund_withdrawal_amount(user_id, amount) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    withdrawal_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)

    case Cypridina.Ledger.transfer(withdrawal_identifier, user_identifier, amount,
           transaction_type: :refund,
           description: "提现失败退款"
         ) do
      {:ok, transfer} ->
        Logger.info("提现失败退款成功: 用户#{user_id}, 金额#{amount}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("提现失败退款失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp get_user_balance(user_id) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)

    case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
      {:ok, balance} -> balance
      {:error, _} -> 0
    end
  end

  defp parse_status(status) do
    case to_string(status) do
      # 支付中
      "0" -> :processing
      # 已完成
      "1" -> :success
      # 已超时
      "3" -> :timeout
      # 驳回中
      "5" -> :rejecting
      # 已驳回
      "7" -> :rejected
      # 冲正
      "9" -> :reversed
      _ -> :unknown
    end
  end

  defp get_client_ip(conn) do
    case get_req_header(conn, "x-forwarded-for") do
      [ip | _] ->
        ip |> String.split(",") |> List.first() |> String.trim()

      [] ->
        case get_req_header(conn, "x-real-ip") do
          [ip | _] -> ip
          [] -> conn.remote_ip |> :inet.ntoa() |> to_string()
        end
    end
  end

  defp send_success_response(conn) do
    conn
    |> put_resp_content_type("text/plain")
    |> send_resp(200, "SUCCESS")
  end

  defp send_error_response(conn, reason) do
    conn
    |> put_resp_content_type("application/json")
    |> send_resp(400, Jason.encode!(%{code: "9999", message: to_string(reason)}))
  end
end
