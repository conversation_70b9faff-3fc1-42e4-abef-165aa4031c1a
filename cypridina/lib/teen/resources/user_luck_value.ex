defmodule Teen.UserLuckValue do
  @moduledoc """
  用户幸运值管理资源
  """

  use Ash.Resource,
    domain: Teen.UserSystem,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "user_luck_values"
    repo Cypridina.Repo
  end

  code_interface do
    define :get_luck, action: :read, get_by: [:user_id, :game_type]
    define :update_luck, args: [:new_luck]
    define :admin_reset, args: [:new_value]
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:user_id, :game_type, :current_luck, :last_updated_at]
    end

    # 更新幸运值
    update :update_luck do
      argument :new_luck, :integer, allow_nil?: false

      change set_attribute(:current_luck, arg(:new_luck))
      change set_attribute(:last_updated_at, &DateTime.utc_now/0)
    end

    # 管理员重置幸运值
    update :admin_reset do
      argument :new_value, :integer, allow_nil?: false

      change set_attribute(:current_luck, arg(:new_value))
      change set_attribute(:last_updated_at, &DateTime.utc_now/0)
    end
  end

  validations do
    validate numericality(:current_luck,
               greater_than_or_equal_to: -1,
               less_than_or_equal_to: 1000
             )

    validate present(:user_id)
    validate present(:game_type)
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :integer, allow_nil?: false
    attribute :game_type, :string, default: "teen_patti"
    attribute :current_luck, :integer, default: -1
    attribute :last_updated_at, :utc_datetime_usec

    timestamps()
  end

  identities do
    identity :unique_user_game, [:user_id, :game_type]
  end
end
