defmodule Teen.VipSystem.VipLevel do
  @moduledoc """
  VIP等级配置资源

  定义每个VIP等级的要求和权益
  """

  use Ash.Resource,
    domain: Teen.VipSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    read_actions [:read]
    create_actions [:create]
    update_actions [:update]
    destroy_actions [:destroy]
  end

  postgres do
    table "vip_levels"
    repo Cypridina.Repo
  end

  code_interface do
    define :read
    define :create
    define :update
    define :destroy
    define :by_level, args: [:level]
    define :get_by_level, args: [:level]
    define :get_level_by_recharge_amount, args: [:recharge_amount]
    define :list_active
  end

  actions do
    defaults [:read, :update, :destroy]
    
    create :create do
      accept [
        :level,
        :level_name,
        :recharge_requirement,
        :daily_bonus,
        :exchange_rate_bonus,
        :recharge_bonus,
        :icon_url,
        :description,
        :privileges,
        :status
      ]
    end

    read :by_level do
      argument :level, :integer, allow_nil?: false
      filter expr(level == ^arg(:level) and status == 1)
    end

    read :get_by_level do
      argument :level, :integer, allow_nil?: false
      filter expr(level == ^arg(:level) and status == 1)
      get? true
    end

    read :list_active do
      filter expr(status == 1)
      prepare build(sort: [level: :asc])
    end

    read :get_level_by_recharge_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      get? true
      filter expr(recharge_requirement <= ^arg(:recharge_amount) and status == 1)
      prepare build(sort: [level: :desc])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :level, :integer do
      allow_nil? false
      description "VIP等级 (0-10)"
      constraints min: 0, max: 10
    end

    attribute :level_name, :string do
      allow_nil? false
      description "VIP等级名称"
    end

    attribute :recharge_requirement, :decimal do
      allow_nil? false
      description "升级所需充值金额"
      constraints min: 0
      default 0
    end

    attribute :daily_bonus, :decimal do
      allow_nil? false
      description "每日登录奖励"
      default 0
    end

    attribute :exchange_rate_bonus, :decimal do
      allow_nil? false
      description "兑换率加成"
      default 0
    end

    attribute :recharge_bonus, :decimal do
      allow_nil? false
      description "充值奖励"
      default 0
    end

    attribute :icon_url, :string do
      allow_nil? true
      description "VIP等级图标URL"
    end

    attribute :description, :string do
      allow_nil? true
      description "VIP等级描述"
    end

    attribute :privileges, {:array, :string} do
      allow_nil? true
      description "VIP权益列表"
      default []
    end

    attribute :status, :integer do
      allow_nil? false
      constraints min: 0, max: 2
      default 1  # 1 = active, 0 = inactive
    end

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  identities do
    identity :unique_level, [:level]
  end
end
