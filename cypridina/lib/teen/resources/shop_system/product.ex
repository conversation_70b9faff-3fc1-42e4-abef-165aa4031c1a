defmodule Teen.ShopSystem.Product do
  @moduledoc """
  商品资源

  管理游戏中的各种商品，包括：
  - 月卡/周卡商品
  - 次卡商品（如30次卡）
  - 金币礼包
  - VIP礼包
  - 特殊道具
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ShopSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :name,
      :product_type,
      :price,
      :currency,
      :status,
      :sort_order,
      :created_at
    ]
  end

  postgres do
    table "products"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_products
    define :list_by_type
    define :list_by_category
    define :enable_product
    define :disable_product
    define :get_product_by_sku
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :name,
        :description,
        :product_type,
        :category,
        :sku,
        :price,
        :currency,
        :product_config,
        :display_config,
        :sort_order,
        :status
      ]

      change fn changeset, _context ->
        # 自动生成SKU如果没有提供
        sku = Ash.Changeset.get_attribute(changeset, :sku) || generate_sku()

        changeset
        |> Ash.Changeset.change_attribute(:sku, sku)
        |> Ash.Changeset.change_attribute(:status, :active)
        |> Ash.Changeset.change_attribute(:created_at, DateTime.utc_now())
      end
    end

    read :list_active_products do
      filter expr(status == :active)
      prepare build(sort: [asc: :sort_order, asc: :created_at])
    end

    read :list_by_type do
      argument :product_type, :atom, allow_nil?: false
      filter expr(product_type == ^arg(:product_type) and status == :active)
      prepare build(sort: [asc: :sort_order])
    end

    read :list_by_category do
      argument :category, :string, allow_nil?: false
      filter expr(category == ^arg(:category) and status == :active)
      prepare build(sort: [asc: :sort_order])
    end

    read :get_product_by_sku do
      argument :sku, :string, allow_nil?: false
      filter expr(sku == ^arg(:sku))
      get? true
    end

    update :enable_product do
      require_atomic? false
      change set_attribute(:status, :active)
    end

    update :disable_product do
      require_atomic? false
      change set_attribute(:status, :inactive)
    end
  end

  validations do
    validate compare(:price, greater_than: Decimal.new("0")), message: "商品价格必须大于0"
    validate compare(:sort_order, greater_than_or_equal_to: 0), message: "排序顺序不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "商品名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "商品描述"
      constraints max_length: 500
    end

    attribute :product_type, :atom do
      allow_nil? false
      public? true
      description "商品类型"

      constraints one_of: [
                    # 月卡
                    :monthly_card,

                    # 周卡
                    :weekly_card,

                    # 次卡（如30次卡）
                    :play_card,

                    # 金币礼包
                    :coin_package,

                    # VIP礼包
                    :vip_package,

                    # 特殊道具
                    :special_item,

                    # 充值奖励包
                    :recharge_bonus
                  ]
    end

    attribute :category, :string do
      allow_nil? true
      public? true
      description "商品分类"
      constraints max_length: 50
    end

    attribute :sku, :string do
      allow_nil? false
      public? true
      description "商品SKU（唯一标识）"
      constraints max_length: 50
    end

    attribute :price, :decimal do
      allow_nil? false
      public? true
      description "商品价格（分）"
      constraints min: Decimal.new("0")
    end

    attribute :currency, :atom do
      allow_nil? false
      public? true
      description "货币类型"

      # 印度卢比、美元、人民币
      constraints one_of: [:inr, :usd, :cny]
      default :inr
    end

    attribute :product_config, :map do
      allow_nil? false
      public? true
      description "商品配置（JSON格式）"
      default %{}
    end

    attribute :display_config, :map do
      allow_nil? true
      public? true
      description "显示配置（图标、颜色等）"
      default %{}
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序顺序"
      constraints min: 0
      default 0
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "商品状态"
      constraints one_of: [:active, :inactive, :sold_out]
      default :active
    end

    attribute :created_at, :utc_datetime do
      allow_nil? false
      public? true
      description "创建时间"
    end

    timestamps()
  end

  calculations do
    calculate :price_display, :string do
      public? true
      description "价格显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          price_value = Decimal.div(record.price, Decimal.new("100")) |> Decimal.to_string()

          currency_symbol =
            case record.currency do
              :inr -> "₹"
              :usd -> "$"
              :cny -> "¥"
              _ -> ""
            end

          "#{currency_symbol}#{price_value}"
        end)
      end
    end

    calculate :product_type_display, :string do
      public? true
      description "商品类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.product_type do
            :monthly_card -> "月卡"
            :weekly_card -> "周卡"
            :play_card -> "次卡"
            :coin_package -> "金币礼包"
            :vip_package -> "VIP礼包"
            :special_item -> "特殊道具"
            :recharge_bonus -> "充值奖励包"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :active -> "上架"
            :inactive -> "下架"
            :sold_out -> "售罄"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :is_card_product, :boolean do
      public? true
      description "是否为卡类商品"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.product_type in [:monthly_card, :weekly_card, :play_card]
        end)
      end
    end
  end

  identities do
    identity :unique_sku, [:sku]
  end

  # 私有函数

  defp generate_sku do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    random = :rand.uniform(9999)
    "SKU#{timestamp}#{random}"
  end
end
