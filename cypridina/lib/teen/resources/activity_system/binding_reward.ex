defmodule Teen.ActivitySystem.BindingReward do
  @moduledoc """
  手机、邮箱绑定奖励资源

  管理绑定奖励配置
  用户绑定手机或邮箱后可获得奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :binding_type, :reward_amount, :status, :updated_at]
  end

  postgres do
    table "binding_rewards"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_rewards
    define :get_by_binding_type, args: [:binding_type]
    define :enable_reward
    define :disable_reward
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:title, :binding_type, :reward_amount, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_rewards do
      filter expr(status == :enabled)
    end

    read :get_by_binding_type do
      argument :binding_type, :atom, allow_nil?: false
      get? true
      filter expr(binding_type == ^arg(:binding_type) and status == :enabled)
    end

    update :enable_reward do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_reward do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :binding_type, :atom do
      allow_nil? false
      public? true
      description "绑定类型"
      constraints one_of: [:phone, :email]
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  identities do
    identity :unique_binding_type, [:binding_type]
  end
end
