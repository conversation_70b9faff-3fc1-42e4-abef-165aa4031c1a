defmodule Teen.ActivitySystem.UserActivityParticipation do
  @moduledoc """
  用户活动参与记录资源

  记录用户参与各种活动的详细情况
  包括参与时间、活动类型、进度、状态等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :activity_type,
      :activity_id,
      :progress,
      :status,
      :participated_at
    ]
  end

  postgres do
    table "user_activity_participations"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_by_activity
    define :list_by_user_and_activity
    define :get_user_progress
    define :update_progress
    define :reset_scratch_card_progress
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:user_id, :activity_type, :activity_id, :progress, :status, :participation_data]

      validate present([:user_id, :activity_type])

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:participated_at, DateTime.utc_now())
      end
    end

    update :update do
      accept [:user_id, :activity_type, :activity_id, :progress, :status, :participation_data]
      validate present([:user_id, :activity_type])
      require_atomic? false
    end

    update :validate do
      accept [:user_id, :activity_type, :activity_id, :progress, :status, :participation_data]
      validate present([:user_id, :activity_type])
      require_atomic? false
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :participated_at])
    end

    read :list_by_activity do
      argument :activity_type, :atom, allow_nil?: false
      argument :activity_id, :uuid, allow_nil?: true

      filter expr(
               activity_type == ^arg(:activity_type) and
                 (is_nil(^arg(:activity_id)) or activity_id == ^arg(:activity_id))
             )
    end

    read :list_by_user_and_activity do
      argument :user_id, :uuid, allow_nil?: false
      argument :activity_type, :atom, allow_nil?: false

      filter expr(user_id == ^arg(:user_id) and activity_type == ^arg(:activity_type))
    end

    read :get_user_progress do
      argument :user_id, :uuid, allow_nil?: false
      argument :activity_type, :atom, allow_nil?: false
      argument :activity_id, :uuid, allow_nil?: true
      get? true

      filter expr(
               user_id == ^arg(:user_id) and activity_type == ^arg(:activity_type) and
                 (is_nil(^arg(:activity_id)) or activity_id == ^arg(:activity_id))
             )
    end

    update :update_progress do
      accept [:progress, :status, :participation_data]
      require_atomic? false
    end

    update :reset_scratch_card_progress do
      description "重置用户刮刮卡进度"
      require_atomic? false

      argument :user_id, :uuid do
        allow_nil? false
        description "用户ID"
      end

      # 只允许对刮刮卡活动进行重置
      filter expr(
        user_id == ^arg(:user_id) and activity_type == :scratch_card
      )

      # 重置进度和状态
      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:progress, 0)
        |> Ash.Changeset.change_attribute(:status, :active)
        |> Ash.Changeset.change_attribute(:participation_data, %{
          last_claim_date: nil,
          claimed_count: 0,
          reset_at: DateTime.utc_now(),
          reset_reason: "管理员重置"
        })
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :activity_type, :atom do
      allow_nil? false
      public? true
      description "活动类型"

      constraints one_of: [
                    :game_task,
                    :weekly_card,
                    :seven_day_task,
                    :vip_gift,
                    :recharge_task,
                    :recharge_wheel,
                    :scratch_card,
                    :first_recharge_gift,
                    :loss_rebate_jar,
                    :invite_cash,
                    :binding_reward,
                    :free_bonus_task,
                    :cdkey_activity,
                    :sign_in_activity
                  ]
    end

    attribute :activity_id, :uuid do
      allow_nil? true
      public? true
      description "活动ID"
    end

    attribute :progress, :integer do
      allow_nil? false
      public? true
      description "进度"
      default 0
      constraints min: 0
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :completed, :expired, :paused]
      default :active
    end

    attribute :participation_data, :map do
      allow_nil? true
      public? true
      description "参与数据（JSON）"
    end

    attribute :participated_at, :utc_datetime do
      allow_nil? false
      public? true
      description "参与时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_activity, [:user_id, :activity_type, :activity_id]
  end
end
