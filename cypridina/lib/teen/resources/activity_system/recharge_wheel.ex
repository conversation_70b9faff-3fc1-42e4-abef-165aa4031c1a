defmodule Teen.ActivitySystem.RechargeWheel do
  @moduledoc """
  充值转盘资源

  管理充值转盘活动配置
  转盘奖金池系统：用户累计充值达到指定金额后可获得转盘次数
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :cumulative_recharge, :wheel_spins, :status, :updated_at]
  end

  postgres do
    table "recharge_wheels"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_wheels
    define :get_by_recharge_amount, args: [:recharge_amount]
    define :enable_wheel
    define :disable_wheel
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:cumulative_recharge, :wheel_spins, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_wheels do
      filter expr(status == :enabled)
    end

    read :get_by_recharge_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(cumulative_recharge <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_wheel do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_wheel do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :cumulative_recharge, :decimal do
      allow_nil? false
      public? true
      description "累计充值金币（分）"
      constraints min: Decimal.new("0")
    end

    attribute :wheel_spins, :integer do
      allow_nil? false
      public? true
      description "转盘次数"
      constraints min: 1
      default 1
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  identities do
    identity :unique_recharge_amount, [:cumulative_recharge]
  end
end
