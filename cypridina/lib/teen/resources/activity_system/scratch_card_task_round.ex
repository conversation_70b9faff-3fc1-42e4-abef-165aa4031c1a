defmodule Teen.ActivitySystem.ScratchCardTaskRound do
  @moduledoc """
  刮刮卡任务轮次配置资源

  管理刮刮卡活动的轮次配置
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :activity_id, :round_number, :recharge_amount, :updated_at]
  end

  postgres do
    table "scratch_card_task_rounds"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_activity
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:activity_id, :round_number, :recharge_amount]
    end

    read :list_by_activity do
      argument :activity_id, :uuid, allow_nil?: false
      filter expr(activity_id == ^arg(:activity_id))
      prepare build(sort: [:round_number])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :round_number, :integer do
      allow_nil? false
      public? true
      description "轮次"
      constraints min: 1
    end

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("0")
    end

    timestamps()
  end

  relationships do
    belongs_to :activity, Teen.ActivitySystem.ScratchCardActivity do
      public? true
      source_attribute :activity_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_activity_round, [:activity_id, :round_number]
  end
end
