defmodule Teen.ActivitySystem.VipGift do
  @moduledoc """
  VIP礼包资源

  管理不同VIP等级的奖励配置
  包括每日奖励、每周奖励、每月奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :vip_level,
      :daily_reward,
      :weekly_reward,
      :monthly_reward,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "vip_gifts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gifts
    define :get_by_vip_level
    define :enable_gift
    define :disable_gift
    define :check_reward_claimable
    define :claim_reward
    define :create_vip_gift_config
    define :auto_distribute_daily_rewards
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:vip_level, :daily_reward, :weekly_reward, :monthly_reward, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_gifts do
      filter expr(status == :enabled)
    end

    read :get_by_vip_level do
      argument :vip_level, :integer, allow_nil?: false
      get? true
      filter expr(vip_level == ^arg(:vip_level) and status == :enabled)
    end

    update :enable_gift do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_gift do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    read :check_reward_claimable do
      argument :user_id, :string, allow_nil?: false
      argument :claim_type, :atom, allow_nil?: false
      
      prepare fn query, context ->
        query
        |> Ash.Query.load([:can_claim_reward])
      end
    end

    update :claim_reward do
      argument :user_id, :string, allow_nil?: false
      argument :claim_type, :atom, allow_nil?: false
      require_atomic? false
      
      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        claim_type = Ash.Changeset.get_argument(changeset, :claim_type)
        vip_gift = changeset.data
        
        # Check if user has the required VIP level
        user_vip_level = get_user_vip_level(user_id)
        if user_vip_level < vip_gift.vip_level do
          {:error, field: :user_id, message: "您的VIP等级不足"}
        else
          # Check if can claim this reward type
          case check_vip_reward_claimable(user_id, claim_type) do
            false -> {:error, field: :claim_type, message: "#{claim_type_name(claim_type)}暂时无法领取"}
            true -> :ok
          end
        end
      end
      
      change fn changeset, context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        claim_type = Ash.Changeset.get_argument(changeset, :claim_type)
        vip_gift = changeset.data
        
        {reward_amount, reward_name} = case claim_type do
          :daily -> {Decimal.to_integer(vip_gift.daily_reward), "每日奖励"}
          :weekly -> {Decimal.to_integer(vip_gift.weekly_reward), "每周奖励"}
          :monthly -> {Decimal.to_integer(vip_gift.monthly_reward), "每月奖励"}
          _ -> {0, "未知奖励"}
        end
        
        # Distribute reward
        case distribute_vip_reward(user_id, reward_amount, "VIP#{vip_gift.vip_level}#{reward_name}") do
          {:ok, _} ->
            # Record claim
            record_vip_claim(user_id, claim_type)
            changeset
          {:error, reason} ->
            Ash.Changeset.add_error(changeset, field: :reward, message: "奖励发放失败: #{inspect(reason)}")
        end
      end
    end

    create :create_vip_gift_config do
      accept [:vip_level, :daily_reward, :weekly_reward, :monthly_reward]
      
      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    update :auto_distribute_daily_rewards do
      require_atomic? false
      
      change fn changeset, _context ->
        vip_gift = changeset.data
        
        # This is a helper action for batch processing
        # The actual distribution logic will be handled by VipService
        changeset
      end
    end
  end

  validations do
    validate compare(:vip_level, greater_than_or_equal_to: 0), message: "VIP等级不能为负数"

    validate compare(:daily_reward, greater_than_or_equal_to: Decimal.new("0")),
      message: "每日奖励不能为负数"

    validate compare(:weekly_reward, greater_than_or_equal_to: Decimal.new("0")),
      message: "每周奖励不能为负数"

    validate compare(:monthly_reward, greater_than_or_equal_to: Decimal.new("0")),
      message: "每月奖励不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :vip_level, :integer do
      allow_nil? false
      public? true
      description "VIP等级"
      constraints min: 0
    end

    attribute :daily_reward, :decimal do
      allow_nil? false
      public? true
      description "每日奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :weekly_reward, :decimal do
      allow_nil? false
      public? true
      description "每周奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :monthly_reward, :decimal do
      allow_nil? false
      public? true
      description "每月奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :vip_level_display, :string do
      public? true
      description "VIP等级显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "VIP#{record.vip_level}"
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :total_monthly_value, :decimal do
      public? true
      description "月度总价值（每日×30 + 每周×4 + 每月）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          daily_monthly = Decimal.mult(record.daily_reward, Decimal.new("30"))
          weekly_monthly = Decimal.mult(record.weekly_reward, Decimal.new("4"))

          daily_monthly
          |> Decimal.add(weekly_monthly)
          |> Decimal.add(record.monthly_reward)
        end)
      end
    end

    calculate :reward_summary, :string do
      public? true
      description "奖励摘要"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "日#{record.daily_reward}/周#{record.weekly_reward}/月#{record.monthly_reward}"
        end)
      end
    end

    calculate :can_claim_reward, :map do
      public? true
      description "是否可以领取各类奖励"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]
        
        Enum.map(records, fn record ->
          user_vip_level = get_user_vip_level(user_id)
          
          # 检查用户VIP等级是否满足
          if user_vip_level >= record.vip_level do
            %{
              daily: check_vip_reward_claimable(user_id, :daily),
              weekly: check_vip_reward_claimable(user_id, :weekly),
              monthly: check_vip_reward_claimable(user_id, :monthly),
              vip_level_sufficient: true
            }
          else
            %{
              daily: false,
              weekly: false,
              monthly: false,
              vip_level_sufficient: false,
              required_vip_level: record.vip_level,
              current_vip_level: user_vip_level
            }
          end
        end)
      end
    end
  end

  identities do
    identity :unique_vip_level, [:vip_level]
  end

  # Helper functions for VIP gift operations
  
  defp get_user_vip_level(user_id) do
    case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
      {:ok, vip_info} -> vip_info.vip_level
      _ -> 0
    end
  end

  defp check_vip_reward_claimable(user_id, claim_type) do
    case get_user_participation(user_id, :vip_gift) do
      nil -> true
      participation ->
        last_claim_key = "last_#{claim_type}_claim"
        last_claim = get_in(participation.participation_data, [last_claim_key])
        
        case last_claim do
          nil -> true
          timestamp -> can_claim_again?(timestamp, claim_type)
        end
    end
  end

  defp can_claim_again?(last_claim_timestamp, claim_type) do
    now = DateTime.utc_now()
    
    case DateTime.from_iso8601(last_claim_timestamp) do
      {:ok, last_time, _} ->
        case claim_type do
          :daily -> DateTime.diff(now, last_time, :hour) >= 24
          :weekly -> DateTime.diff(now, last_time, :day) >= 7
          :monthly -> DateTime.diff(now, last_time, :day) >= 30
        end
      _ -> true
    end
  end

  defp claim_type_name(claim_type) do
    case claim_type do
      :daily -> "每日奖励"
      :weekly -> "每周奖励"
      :monthly -> "每月奖励"
      _ -> "奖励"
    end
  end

  defp distribute_vip_reward(user_id, amount, description) do
    # Use ledger system to distribute reward
    from_account = "system:XAA:rewards"
    to_account = "user:XAA:#{user_id}"
    
    Cypridina.Ledger.game_win(from_account, to_account, amount, description)
  end

  defp record_vip_claim(user_id, claim_type) do
    now = DateTime.utc_now() |> DateTime.to_iso8601()
    claim_key = "last_#{claim_type}_claim"
    
    case get_user_participation(user_id, :vip_gift) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: :vip_gift,
          progress: 1,
          status: :active,
          participation_data: %{claim_key => now}
        })
      
      participation ->
        new_data = Map.put(participation.participation_data || %{}, claim_key, now)
        Teen.ActivitySystem.UserActivityParticipation.update_progress(participation, %{participation_data: new_data})
    end
  end

  defp get_user_participation(user_id, activity_type) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(user_id, activity_type) do
      {:ok, [participation | _]} -> participation
      _ -> nil
    end
  end

  @doc """
  Get VIP gift data for a user
  """
  def get_vip_gift_data(user_id) do
    try do
      # Get user VIP level
      user_vip_level = get_user_vip_level(user_id)
      
      # Get VIP gift config for this level
      case get_by_vip_level(user_vip_level) do
        {:ok, vip_gift} ->
          # Load can_claim_reward calculation with user_id argument
          vip_gift = Ash.load!(vip_gift, [{:can_claim_reward, %{user_id: user_id}}], authorize?: false)
          
          claim_info = vip_gift.can_claim_reward
          
          {:ok, %{
            vip_level: user_vip_level,
            daily_reward: Decimal.to_integer(vip_gift.daily_reward),
            weekly_reward: Decimal.to_integer(vip_gift.weekly_reward),
            monthly_reward: Decimal.to_integer(vip_gift.monthly_reward),
            can_claim_daily: claim_info.daily && claim_info.vip_level_sufficient,
            can_claim_weekly: claim_info.weekly && claim_info.vip_level_sufficient,
            can_claim_monthly: claim_info.monthly && claim_info.vip_level_sufficient,
            vip_level_sufficient: claim_info.vip_level_sufficient,
            required_vip_level: Map.get(claim_info, :required_vip_level),
            current_vip_level: Map.get(claim_info, :current_vip_level),
            status: if(vip_gift.status == :enabled, do: 1, else: 0)
          }}

        {:error, _reason} ->
          require Logger
          Logger.warning("VIP gift config not found for level #{user_vip_level}, using default data")
          {:ok, build_default_vip_data(user_vip_level)}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting VIP gift data: #{inspect(e)}")
        {:ok, build_default_vip_data(0)}
    end
  end

  defp build_default_vip_data(vip_level) do
    %{
      vip_level: vip_level,
      daily_reward: vip_level * 10,
      weekly_reward: vip_level * 50,
      monthly_reward: vip_level * 200,
      can_claim_daily: true,
      can_claim_weekly: true,
      can_claim_monthly: true,
      vip_level_sufficient: true,
      required_vip_level: vip_level,
      current_vip_level: vip_level,
      status: 1
    }
  end

  @doc """
  Process VIP gift reward claim
  """
  def process_vip_gift_claim(user_id, data) do
    fetch_type = Map.get(data, "fetchtype", 0)
    
    claim_type = case fetch_type do
      0 -> :daily
      1 -> :weekly
      2 -> :monthly
      _ -> :unknown
    end
    
    try do
      # Get user VIP level
      user_vip_level = get_user_vip_level(user_id)
      
      # Get VIP gift config
      case get_by_vip_level(user_vip_level) do
        {:ok, vip_gift} ->
          # Use the claim_reward action
          case claim_reward(vip_gift, %{user_id: user_id, claim_type: claim_type}) do
            {:ok, _updated_gift} ->
              {reward_amount, reward_name} = case claim_type do
                :daily -> {Decimal.to_integer(vip_gift.daily_reward), "每日奖励"}
                :weekly -> {Decimal.to_integer(vip_gift.weekly_reward), "每周奖励"}
                :monthly -> {Decimal.to_integer(vip_gift.monthly_reward), "每月奖励"}
                _ -> {0, "未知奖励"}
              end
              
              # Publish event for VIP reward claimed
              Phoenix.PubSub.broadcast(
                Cypridina.PubSub,
                "vip:reward:claimed",
                {:vip_reward_claimed, %{
                  user_id: user_id,
                  vip_level: user_vip_level,
                  claim_type: claim_type,
                  reward_amount: reward_amount,
                  timestamp: DateTime.utc_now()
                }}
              )
              
              {:ok, %{code: 0, fetchaward: reward_amount, msg: "VIP#{reward_name}领取成功"}}
            
            {:error, error} ->
              message = case error do
                %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                _ -> "奖励领取失败"
              end
              {:error, %{code: 1, msg: message}}
          end
        
        {:error, _} ->
          {:error, %{code: 1, msg: "VIP等级配置不存在"}}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception processing VIP gift claim: #{inspect(e)}")
        {:error, %{code: 1, msg: "系统错误"}}
    end
  end

  @doc """
  Auto-claim VIP daily rewards for all eligible users
  """
  def auto_claim_daily_rewards do
    require Logger
    Logger.info("🎁 [VIP_GIFT] Starting auto-claim daily rewards process")
    
    try do
      # Get all active VIP gift configs
      case list_active_gifts() do
        {:ok, vip_gifts} ->
          Enum.each(vip_gifts, fn vip_gift ->
            auto_claim_for_vip_level(vip_gift)
          end)
          
        {:error, reason} ->
          Logger.error("🎁 [VIP_GIFT] Failed to get active gifts: #{inspect(reason)}")
      end
    rescue
      e ->
        Logger.error("🎁 [VIP_GIFT] Exception in auto-claim process: #{inspect(e)}")
    end
  end
  
  defp auto_claim_for_vip_level(vip_gift) do
    require Logger
    Logger.info("🎁 [VIP_GIFT] Processing auto-claim for VIP level #{vip_gift.vip_level}")
    
    # Get all users with this VIP level who haven't claimed today
    users_to_process = get_eligible_users_for_auto_claim(vip_gift.vip_level)
    
    Enum.each(users_to_process, fn user_id ->
      case claim_reward(vip_gift, %{user_id: user_id, claim_type: :daily}) do
        {:ok, _} ->
          Logger.info("🎁 [VIP_GIFT] Auto-claimed daily reward for user #{user_id}")
          
        {:error, reason} ->
          Logger.debug("🎁 [VIP_GIFT] Failed to auto-claim for user #{user_id}: #{inspect(reason)}")
      end
    end)
  end
  
  defp get_eligible_users_for_auto_claim(vip_level) do
    # This would need to be implemented based on your user system
    # For now, return empty list to avoid errors
    []
  end

  @doc """
  Setup default VIP gift configurations
  """
  def setup_default_configs do
    require Logger
    Logger.info("🎁 [VIP_GIFT] Setting up default VIP gift configurations")
    
    default_configs = [
      %{vip_level: 0, daily_reward: Decimal.new(0), weekly_reward: Decimal.new(0), monthly_reward: Decimal.new(0)},
      %{vip_level: 1, daily_reward: Decimal.new(10), weekly_reward: Decimal.new(50), monthly_reward: Decimal.new(200)},
      %{vip_level: 2, daily_reward: Decimal.new(20), weekly_reward: Decimal.new(100), monthly_reward: Decimal.new(400)},
      %{vip_level: 3, daily_reward: Decimal.new(30), weekly_reward: Decimal.new(150), monthly_reward: Decimal.new(600)},
      %{vip_level: 4, daily_reward: Decimal.new(50), weekly_reward: Decimal.new(250), monthly_reward: Decimal.new(1000)},
      %{vip_level: 5, daily_reward: Decimal.new(80), weekly_reward: Decimal.new(400), monthly_reward: Decimal.new(1600)},
      %{vip_level: 6, daily_reward: Decimal.new(120), weekly_reward: Decimal.new(600), monthly_reward: Decimal.new(2400)},
      %{vip_level: 7, daily_reward: Decimal.new(180), weekly_reward: Decimal.new(900), monthly_reward: Decimal.new(3600)},
      %{vip_level: 8, daily_reward: Decimal.new(250), weekly_reward: Decimal.new(1250), monthly_reward: Decimal.new(5000)},
      %{vip_level: 9, daily_reward: Decimal.new(350), weekly_reward: Decimal.new(1750), monthly_reward: Decimal.new(7000)},
      %{vip_level: 10, daily_reward: Decimal.new(500), weekly_reward: Decimal.new(2500), monthly_reward: Decimal.new(10000)}
    ]
    
    Enum.each(default_configs, fn config ->
      case get_by_vip_level(config.vip_level) do
        {:ok, _existing} ->
          Logger.debug("🎁 [VIP_GIFT] VIP level #{config.vip_level} config already exists")
          
        {:error, _} ->
          case create_vip_gift_config(config) do
            {:ok, _vip_gift} ->
              Logger.info("🎁 [VIP_GIFT] Created VIP level #{config.vip_level} config")
              
            {:error, reason} ->
              Logger.error("🎁 [VIP_GIFT] Failed to create VIP level #{config.vip_level} config: #{inspect(reason)}")
          end
      end
    end)
  end
end
