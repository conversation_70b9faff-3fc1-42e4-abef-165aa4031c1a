defmodule Teen.ActivitySystem.WeeklyCard do
  @moduledoc """
  周卡任务资源

  管理周卡配置，用户充值后可获得初始奖励和每日领取奖励

  示例：
  - 充值500：初始奖励10，每日领取50（共7天）
  - 充值1000：初始奖励30，每日领取80
  - 充值5000：初始奖励180，每日领取150
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :title,
      :recharge_amount,
      :initial_reward,
      :daily_reward,
      :claim_days,
      :total_reward,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "weekly_cards"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_cards
    define :list_by_recharge_amount
    define :enable_card
    define :disable_card
    define :get_user_data
    define :check_user_purchased
    define :check_reward_claimed
    define :claim_reward
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:title, :recharge_amount, :initial_reward, :daily_reward, :claim_days, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:claim_days, 7)
      end
    end

    read :list_active_cards do
      filter expr(status == :enabled)
    end

    read :list_by_recharge_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_card do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_card do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    read :get_user_data do
      argument :user_id, :string, allow_nil?: false
      
      prepare fn query, context ->
        user_id = context.arguments[:user_id]
        query
        |> Ash.Query.filter(expr(status == :enabled))
        |> Ash.Query.load([card_fetch_status: %{user_id: user_id}])
      end
    end

    read :check_user_purchased do
      argument :user_id, :string, allow_nil?: false
      argument :card_id, :uuid, allow_nil?: false
      
      prepare fn query, context ->
        card_id = context.arguments[:card_id]
        # This will be enhanced when we have purchase records
        query
        |> Ash.Query.filter(expr(id == ^card_id))
        |> Ash.Query.limit(1)
      end
    end

    read :check_reward_claimed do
      argument :user_id, :string, allow_nil?: false
      argument :card_id, :uuid, allow_nil?: false
      
      prepare fn query, context ->
        card_id = context.arguments[:card_id]
        user_id = context.arguments[:user_id]
        query
        |> Ash.Query.filter(expr(id == ^card_id))
        |> Ash.Query.load([claimed_today: %{user_id: user_id}])
      end
    end

    update :claim_reward do
      argument :user_id, :string, allow_nil?: false
      require_atomic? false
      
      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        card = changeset.data
        
        # Check if user purchased this card
        case check_user_purchased_card(user_id, card.id) do
          false -> {:error, field: :user_id, message: "您还未购买此周卡"}
          true -> 
            # Check if already claimed today
            case check_card_reward_claimed_today(user_id, card.id) do
              true -> {:error, field: :user_id, message: "今日奖励已领取"}
              false -> :ok
            end
        end
      end
      
      change fn changeset, context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        card = changeset.data
        
        # Distribute reward
        reward_amount = Decimal.to_integer(card.daily_reward)
        
        case distribute_weekly_card_reward(user_id, reward_amount, "周卡每日奖励") do
          {:ok, _} ->
            # Record claim
            record_weekly_card_claim(user_id, card.id)
            changeset
          {:error, reason} ->
            Ash.Changeset.add_error(changeset, field: :reward, message: "奖励发放失败: #{inspect(reason)}")
        end
      end
    end
  end

  validations do
    validate compare(:recharge_amount, greater_than: Decimal.new("0")), message: "充值金额必须大于0"

    validate compare(:initial_reward, greater_than_or_equal_to: Decimal.new("0")),
      message: "初始奖励不能为负数"

    validate compare(:daily_reward, greater_than_or_equal_to: Decimal.new("0")),
      message: "每日奖励不能为负数"

    validate compare(:claim_days, greater_than: 0), message: "领取天数必须大于0"
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "周卡标题"
      constraints max_length: 100
    end

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_reward, :decimal do
      allow_nil? false
      public? true
      description "初始奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :daily_reward, :decimal do
      allow_nil? false
      public? true
      description "每日领取奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :claim_days, :integer do
      allow_nil? false
      public? true
      description "可领取天数"
      constraints min: 1
      default 7
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :total_reward, :decimal do
      public? true
      description "总奖励金额（初始奖励 + 每日奖励 × 天数）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          daily_total = Decimal.mult(record.daily_reward, Decimal.new(record.claim_days))
          Decimal.add(record.initial_reward, daily_total)
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_summary, :string do
      public? true
      description "奖励摘要"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "初始#{record.initial_reward}，每日#{record.daily_reward}×#{record.claim_days}天"
        end)
      end
    end

    calculate :card_fetch_status, :integer do
      public? true
      description "周卡领取状态"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]
        
        Enum.map(records, fn record ->
          case get_user_participation(user_id, record.id) do
            nil -> 0
            participation -> 
              claimed_today = get_in(participation.participation_data, ["claimed_today"])
              today = Date.utc_today() |> Date.to_string()
              if claimed_today == today, do: 1, else: 0
          end
        end)
      end
    end

    calculate :claimed_today, :boolean do
      public? true
      description "今日是否已领取"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]
        today = Date.utc_today() |> Date.to_string()
        
        Enum.map(records, fn record ->
          case get_user_participation(user_id, record.id) do
            nil -> false
            participation ->
              get_in(participation.participation_data, ["claimed_today"]) == today
          end
        end)
      end
    end
  end

  identities do
    identity :unique_recharge_amount, [:recharge_amount]
  end

  # Helper functions for weekly card operations
  
  defp check_user_purchased_card(user_id, card_id) do
    case Teen.ActivitySystem.WeeklyCardPurchase.check_user_purchased_card(user_id, card_id) do
      {:ok, []} -> false
      {:ok, [_purchase | _]} -> true
      _ -> false
    end
  end

  defp check_card_reward_claimed_today(user_id, card_id) do
    case Teen.ActivitySystem.WeeklyCardPurchase.get_user_card_purchase(user_id, card_id) do
      {:ok, purchase} ->
        today = Date.utc_today()
        purchase.last_claim_date && Date.compare(purchase.last_claim_date, today) == :eq
      _ -> false
    end
  end

  defp distribute_weekly_card_reward(user_id, amount, description) do
    # Use ledger system to distribute reward
    from_account = "system:XAA:rewards"
    to_account = "user:XAA:#{user_id}"
    
    Cypridina.Ledger.game_win(from_account, to_account, amount, description)
  end

  defp record_weekly_card_claim(user_id, card_id) do
    case Teen.ActivitySystem.WeeklyCardPurchase.get_user_card_purchase(user_id, card_id) do
      {:ok, purchase} ->
        Teen.ActivitySystem.WeeklyCardPurchase.claim_daily_reward(purchase)
      
      {:error, _} ->
        {:error, "未找到有效的周卡购买记录"}
    end
  end

  defp get_user_participation(user_id, card_id) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(user_id, :weekly_card) do
      {:ok, participations} ->
        Enum.find(participations, fn p -> p.activity_id == card_id end)
      _ -> nil
    end
  end

  @doc """
  Get weekly card data for a user
  """
  def get_weekly_card_data(user_id) do
    try do
      # Get active weekly cards
      case list_active_cards() do
        {:ok, cards} when length(cards) > 0 ->
          # Get user's active card purchases
          user_purchases = case Teen.ActivitySystem.WeeklyCardPurchase.get_active_card_purchases(user_id) do
            {:ok, purchases} -> purchases
            _ -> []
          end
          
          # Build card data
          card_data = 
            cards
            |> Enum.map(fn card ->
              # Find user's purchase for this card
              purchase = Enum.find(user_purchases, fn p -> p.card_id == card.id end)
              
              # Check if user can claim today
              can_claim_today = if purchase do
                today = Date.utc_today()
                is_nil(purchase.last_claim_date) or Date.compare(purchase.last_claim_date, today) == :lt
              else
                false
              end
              
              {
                "weekly_card_#{card.id}",
                %{
                  "id" => card.id,
                  "title" => card.title,
                  "price" => Decimal.to_integer(card.recharge_amount),
                  "initial_reward" => Decimal.to_integer(card.initial_reward),
                  "daily_reward" => Decimal.to_integer(card.daily_reward),
                  "days" => card.claim_days,
                  "total_reward" => Decimal.to_integer(card.total_reward),
                  "isfetch" => if(can_claim_today, do: 0, else: 1),
                  "status" => if(card.status == :enabled, do: 1, else: 0),
                  "purchased" => not is_nil(purchase),
                  "days_remaining" => if(purchase, do: Date.diff(purchase.end_date, Date.utc_today()), else: 0)
                }
              }
            end)
            |> Enum.into(%{})

          {:ok, %{usertask: card_data}}

        {:ok, []} ->
          {:ok, build_default_card_data()}

        {:error, reason} ->
          require Logger
          Logger.error("Failed to get weekly card config: #{inspect(reason)}")
          {:ok, build_default_card_data()}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting weekly card data: #{inspect(e)}")
        {:ok, build_default_card_data()}
    end
  end

  defp build_default_card_data do
    %{
      usertask: %{
        "weekly_card" => %{
          "id" => 1,
          "title" => "周卡",
          "price" => 500,
          "daily_reward" => 100,
          "days" => 7,
          "isfetch" => 0,
          "status" => 1
        }
      }
    }
  end

  @doc """
  Process weekly card reward claim
  """
  def process_weekly_card_claim(user_id, data) do
    card_id = Map.get(data, "card_id")
    
    try do
      # Get weekly card config
      case read(card_id) do
        {:ok, card} ->
          # Use the claim_reward action
          case claim_reward(card, %{user_id: user_id}) do
            {:ok, _updated_card} ->
              {:ok, %{code: 0, fetchaward: Decimal.to_integer(card.daily_reward), msg: "周卡奖励领取成功"}}
            
            {:error, error} ->
              message = case error do
                %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                _ -> "奖励领取失败"
              end
              {:error, %{code: 1, msg: message}}
          end
        
        {:error, _} ->
          {:error, %{code: 1, msg: "周卡配置不存在"}}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception processing weekly card claim: #{inspect(e)}")
        {:error, %{code: 1, msg: "系统错误"}}
    end
  end

  @doc """
  Process weekly card purchase
  """
  def process_weekly_card_purchase(user_id, card_id, payment_order_id) do
    try do
      case read(card_id) do
        {:ok, card} ->
          # Check if user already has an active card of this type
          case Teen.ActivitySystem.WeeklyCardPurchase.check_user_purchased_card(user_id, card_id) do
            {:ok, []} ->
              # Calculate start and end dates
              start_date = Date.utc_today()
              end_date = Date.add(start_date, card.claim_days)
              
              # Create purchase record
              case Teen.ActivitySystem.WeeklyCardPurchase.create(%{
                user_id: user_id,
                card_id: card_id,
                purchase_amount: card.recharge_amount,
                payment_order_id: payment_order_id,
                start_date: start_date,
                end_date: end_date
              }) do
                {:ok, purchase} ->
                  {:ok, %{
                    purchase_id: purchase.id,
                    start_date: start_date,
                    end_date: end_date,
                    initial_reward: Decimal.to_integer(card.initial_reward),
                    daily_reward: Decimal.to_integer(card.daily_reward)
                  }}
                
                {:error, error} ->
                  {:error, "创建购买记录失败: #{inspect(error)}"}
              end
            
            {:ok, [_purchase | _]} ->
              {:error, "您已购买此周卡，请勿重复购买"}
            
            {:error, reason} ->
              {:error, "检查购买状态失败: #{inspect(reason)}"}
          end
        
        {:error, _} ->
          {:error, "周卡配置不存在"}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception processing weekly card purchase: #{inspect(e)}")
        {:error, "系统错误"}
    end
  end

  @doc """
  Activate weekly card purchase after payment
  """
  def activate_weekly_card_purchase(purchase_id, transaction_id) do
    try do
      case Teen.ActivitySystem.WeeklyCardPurchase.read(purchase_id) do
        {:ok, purchase} ->
          case Teen.ActivitySystem.WeeklyCardPurchase.activate_card(purchase, %{transaction_id: transaction_id}) do
            {:ok, activated_purchase} ->
              # Get card config to distribute initial reward
              case read(activated_purchase.card_id) do
                {:ok, card} ->
                  if Decimal.compare(card.initial_reward, Decimal.new("0")) == :gt do
                    # Distribute initial reward
                    initial_amount = Decimal.to_integer(card.initial_reward)
                    distribute_weekly_card_reward(activated_purchase.user_id, initial_amount, "周卡购买初始奖励")
                    {:ok, %{activated: true, initial_reward_distributed: initial_amount}}
                  else
                    {:ok, %{activated: true, initial_reward_distributed: 0}}
                  end
                
                {:error, reason} ->
                  {:error, "获取周卡配置失败: #{inspect(reason)}"}
              end
            
            {:error, error} ->
              {:error, "激活周卡失败: #{inspect(error)}"}
          end
        
        {:error, _} ->
          {:error, "购买记录不存在"}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception activating weekly card purchase: #{inspect(e)}")
        {:error, "系统错误"}
    end
  end

  @doc """
  Check and expire weekly cards
  """
  def check_and_expire_weekly_cards do
    today = Date.utc_today()
    
    case Teen.ActivitySystem.WeeklyCardPurchase.read() do
      {:ok, purchases} ->
        expired_count = 
          purchases
          |> Enum.filter(fn p -> 
            p.purchase_status == :active and Date.compare(p.end_date, today) == :lt
          end)
          |> Enum.map(fn purchase ->
            Teen.ActivitySystem.WeeklyCardPurchase.expire_card(purchase)
          end)
          |> Enum.count(fn 
            {:ok, _} -> true
            _ -> false
          end)
        
        {:ok, %{expired_count: expired_count}}
      
      {:error, reason} ->
        {:error, reason}
    end
  end
end
