defmodule Teen.ActivitySystem.WheelPrizeConfig do
  @moduledoc """
  转盘中奖配置资源

  管理转盘的中奖配置，包括中奖类型、奖励值、概率等
  特殊规则：奖金池百分比奖励最后触发
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :prize_type, :prize_value, :probability, :sort_order, :updated_at]
  end

  postgres do
    table "wheel_prize_configs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_prizes
    define :list_by_type
    define :get_random_prize
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:prize_type, :prize_value, :probability, :sort_order]
    end

    read :list_active_prizes do
      prepare build(sort: [:sort_order])
    end

    read :list_by_type do
      argument :prize_type, :atom, allow_nil?: false
      filter expr(prize_type == ^arg(:prize_type))
    end

    read :get_random_prize do
      # 根据概率随机获取奖品
      prepare build(sort: [:sort_order])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :prize_type, :atom do
      allow_nil? false
      public? true
      description "中奖类型"
      constraints one_of: [:coins, :cash, :jackpot_percentage]
    end

    attribute :prize_value, :decimal do
      allow_nil? false
      public? true
      description "中奖类型值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :probability, :decimal do
      allow_nil? false
      public? true
      description "概率（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序号"
      default 0
    end

    timestamps()
  end
end
