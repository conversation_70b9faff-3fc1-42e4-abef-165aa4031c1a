defmodule Teen.ActivitySystem.FreeCashActivity do
  @moduledoc """
  免费提现活动资源

  免费提现奖励，包含邀请推广机制
  对应原Node.js系统的FreeCash功能

  主要功能：
  - getFreeCash.js / fetchFreeCashAward.js - 主活动
  - getFreeCashInvitation.js - 邀请活动
  - 支持分页查询邀请列表
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :activity_name, :base_amount, :invitation_bonus, :min_invites, :status]
  end

  postgres do
    table "free_cash_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_activities
    define :get_user_data
    define :get_invitation_list
    define :claim_free_cash
    define :process_invitation
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_activities do
      filter expr(
        status == :active and 
        start_date <= ^DateTime.utc_now() and 
        end_date >= ^DateTime.utc_now()
      )
    end

    read :get_user_data do
      argument :user_id, :uuid, allow_nil?: false
      
      prepare fn query, context ->
        user_id = context.arguments[:user_id]
        
        query
        |> Ash.Query.filter(expr(status == :active))
        |> Ash.Query.load([user_progress: %{user_id: user_id}])
      end
    end

    read :get_invitation_list do
      argument :user_id, :uuid, allow_nil?: false
      argument :page, :integer, allow_nil?: false, default: 1
      argument :page_size, :integer, allow_nil?: false, default: 20
      
      prepare fn query, context ->
        user_id = context.arguments[:user_id]
        page = context.arguments[:page]
        page_size = context.arguments[:page_size]
        
        query
        |> Ash.Query.filter(expr(status == :active))
        |> Ash.Query.load([invitation_list: %{user_id: user_id, page: page, page_size: page_size}])
      end
    end

    update :claim_free_cash do
      argument :user_id, :uuid, allow_nil?: false
      require_atomic? false
      
      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        activity = changeset.data
        
        case check_claim_eligibility(user_id, activity) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :user_id, message: reason}
        end
      end
      
      change fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        activity = changeset.data
        
        case process_free_cash_claim(user_id, activity) do
          {:ok, reward_amount} ->
            record_free_cash_claim(user_id, activity.id, reward_amount)
            changeset
          
          {:error, reason} ->
            Ash.Changeset.add_error(changeset, field: :reward, message: reason)
        end
      end
    end

    update :process_invitation do
      argument :inviter_id, :uuid, allow_nil?: false
      argument :invitee_id, :uuid, allow_nil?: false
      require_atomic? false
      
      change fn changeset, _context ->
        inviter_id = Ash.Changeset.get_argument(changeset, :inviter_id)
        invitee_id = Ash.Changeset.get_argument(changeset, :invitee_id)
        activity = changeset.data
        
        case record_invitation(inviter_id, invitee_id, activity) do
          {:ok, _} ->
            changeset
          
          {:error, reason} ->
            Ash.Changeset.add_error(changeset, field: :invitation, message: reason)
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_name, :string do
      allow_nil? false
      public? true
      description "活动名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :base_amount, :decimal do
      allow_nil? false
      public? true
      description "基础免费提现金额"
      constraints min: Decimal.new("0")
      default Decimal.new("1000")
    end

    attribute :invitation_bonus, :decimal do
      allow_nil? false
      public? true
      description "每邀请一人奖励金额"
      constraints min: Decimal.new("0")
      default Decimal.new("100")
    end

    attribute :min_invites, :integer do
      allow_nil? false
      public? true
      description "最低邀请人数要求"
      constraints min: 0
      default 0
    end

    attribute :max_invites, :integer do
      allow_nil? false
      public? true
      description "最大有效邀请人数"
      constraints min: 1
      default 50
    end

    attribute :max_claims_per_user, :integer do
      allow_nil? false
      public? true
      description "每用户最大领取次数"
      constraints min: 1
      default 1
    end

    attribute :invite_requirements, :map do
      allow_nil? false
      public? true
      description "邀请要求配置"
      default %{
        "min_recharge" => 100,  # 被邀请人最低充值要求
        "min_play_games" => 5   # 被邀请人最低游戏局数要求
      }
    end

    attribute :start_date, :utc_datetime do
      allow_nil? false
      public? true
      description "开始时间"
    end

    attribute :end_date, :utc_datetime do
      allow_nil? false
      public? true
      description "结束时间"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive]
      default :active
    end

    timestamps()
  end

  calculations do
    calculate :user_progress, :map do
      public? true
      description "用户进度"
      argument :user_id, :uuid
      
      calculation fn records, context ->
        user_id = context.arguments[:user_id]
        
        Enum.map(records, fn activity ->
          get_user_free_cash_progress(user_id, activity)
        end)
      end
    end

    calculate :invitation_list, :map do
      public? true
      description "邀请列表"
      argument :user_id, :uuid
      argument :page, :integer
      argument :page_size, :integer
      
      calculation fn records, context ->
        user_id = context.arguments[:user_id]
        page = context.arguments[:page]
        page_size = context.arguments[:page_size]
        
        Enum.map(records, fn activity ->
          get_user_invitation_list(user_id, activity, page, page_size)
        end)
      end
    end
  end

  # Helper functions

  defp check_claim_eligibility(user_id, activity) do
    with :ok <- check_claim_limit(user_id, activity),
         :ok <- check_invitation_requirement(user_id, activity) do
      :ok
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp check_claim_limit(user_id, activity) do
    case get_user_claim_count(user_id, activity.id) do
      count when count < activity.max_claims_per_user -> :ok
      _ -> {:error, "领取次数已达上限"}
    end
  end

  defp check_invitation_requirement(user_id, activity) do
    valid_invites = count_valid_invitations(user_id, activity)
    
    if valid_invites >= activity.min_invites do
      :ok
    else
      {:error, "邀请人数不足"}
    end
  end

  defp process_free_cash_claim(user_id, activity) do
    # 计算奖励金额
    valid_invites = count_valid_invitations(user_id, activity)
    capped_invites = min(valid_invites, activity.max_invites)
    
    invitation_bonus = Decimal.mult(activity.invitation_bonus, Decimal.new(capped_invites))
    total_amount = Decimal.add(activity.base_amount, invitation_bonus)
    
    case distribute_free_cash_reward(user_id, total_amount) do
      {:ok, _} -> {:ok, total_amount}
      {:error, reason} -> {:error, "奖励发放失败: #{inspect(reason)}"}
    end
  end

  defp distribute_free_cash_reward(user_id, amount) do
    from_account = "system:XAA:free_cash"
    to_account = "user:XAA:#{user_id}"
    
    Cypridina.Ledger.game_win(from_account, to_account, Decimal.to_integer(amount), "免费提现奖励")
  end

  defp record_free_cash_claim(user_id, activity_id, reward_amount) do
    claim_data = %{
      "claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "reward_amount" => Decimal.to_string(reward_amount),
      "invitation_count" => count_valid_invitations(user_id, %{id: activity_id})
    }

    case get_user_free_cash_record(user_id, activity_id) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_id: activity_id,
          activity_type: :free_cash,
          participation_data: %{
            "total_claims" => 1,
            "last_claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
            "claims_history" => [claim_data],
            "invitations" => []
          }
        })
      
      record ->
        current_claims = Map.get(record.participation_data, "total_claims", 0)
        claims_history = Map.get(record.participation_data, "claims_history", [])
        
        Teen.ActivitySystem.UserActivityParticipation.update(record, %{
          participation_data: %{
            record.participation_data |
            "total_claims" => current_claims + 1,
            "last_claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
            "claims_history" => [claim_data | claims_history]
          }
        })
    end
  end

  defp record_invitation(inviter_id, invitee_id, activity) do
    invitation_data = %{
      "invitee_id" => invitee_id,
      "invite_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "status" => "pending",  # pending, valid, invalid
      "requirements_met" => false
    }

    case get_user_free_cash_record(inviter_id, activity.id) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: inviter_id,
          activity_id: activity.id,
          activity_type: :free_cash,
          participation_data: %{
            "total_claims" => 0,
            "invitations" => [invitation_data]
          }
        })
      
      record ->
        invitations = Map.get(record.participation_data, "invitations", [])
        
        # 检查是否已经邀请过此用户
        already_invited = Enum.any?(invitations, fn inv -> 
          Map.get(inv, "invitee_id") == invitee_id 
        end)
        
        if already_invited do
          {:error, "已经邀请过此用户"}
        else
          Teen.ActivitySystem.UserActivityParticipation.update(record, %{
            participation_data: %{
              record.participation_data |
              "invitations" => [invitation_data | invitations]
            }
          })
        end
    end
  end

  defp count_valid_invitations(user_id, activity) do
    case get_user_free_cash_record(user_id, activity.id) do
      nil -> 0
      record ->
        invitations = Map.get(record.participation_data, "invitations", [])
        
        invitations
        |> Enum.count(fn invitation ->
          Map.get(invitation, "requirements_met", false)
        end)
    end
  end

  defp get_user_claim_count(user_id, activity_id) do
    case get_user_free_cash_record(user_id, activity_id) do
      nil -> 0
      record -> Map.get(record.participation_data, "total_claims", 0)
    end
  end

  defp get_user_free_cash_record(user_id, activity_id) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(user_id, :free_cash) do
      {:ok, participations} ->
        Enum.find(participations, fn p -> p.activity_id == activity_id end)
      _ -> nil
    end
  end

  defp get_user_free_cash_progress(user_id, activity) do
    case get_user_free_cash_record(user_id, activity.id) do
      nil ->
        %{
          "invitation_count" => 0,
          "valid_invitations" => 0,
          "total_claims" => 0,
          "can_claim" => false,
          "potential_reward" => Decimal.to_string(activity.base_amount)
        }
      
      record ->
        invitations = Map.get(record.participation_data, "invitations", [])
        valid_invites = count_valid_invitations(user_id, activity)
        total_claims = Map.get(record.participation_data, "total_claims", 0)
        
        can_claim = valid_invites >= activity.min_invites and 
                   total_claims < activity.max_claims_per_user
        
        capped_invites = min(valid_invites, activity.max_invites)
        invitation_bonus = Decimal.mult(activity.invitation_bonus, Decimal.new(capped_invites))
        potential_reward = Decimal.add(activity.base_amount, invitation_bonus)
        
        %{
          "invitation_count" => length(invitations),
          "valid_invitations" => valid_invites,
          "total_claims" => total_claims,
          "can_claim" => can_claim,
          "potential_reward" => Decimal.to_string(potential_reward)
        }
    end
  end

  defp get_user_invitation_list(user_id, activity, page, page_size) do
    case get_user_free_cash_record(user_id, activity.id) do
      nil ->
        %{
          "invitations" => [],
          "total_count" => 0,
          "page" => page,
          "page_size" => page_size
        }
      
      record ->
        invitations = Map.get(record.participation_data, "invitations", [])
        total_count = length(invitations)
        
        # 分页处理
        offset = (page - 1) * page_size
        page_invitations = 
          invitations
          |> Enum.drop(offset)
          |> Enum.take(page_size)
          |> Enum.map(&format_invitation_item/1)
        
        %{
          "invitations" => page_invitations,
          "total_count" => total_count,
          "page" => page,
          "page_size" => page_size
        }
    end
  end

  defp format_invitation_item(invitation) do
    %{
      "invitee_id" => Map.get(invitation, "invitee_id"),
      "invite_time" => Map.get(invitation, "invite_time"),
      "status" => Map.get(invitation, "status"),
      "requirements_met" => Map.get(invitation, "requirements_met", false)
    }
  end

  @doc """
  获取免费提现活动数据
  """
  def get_free_cash_data(user_id) do
    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case get_user_data(activity, %{user_id: user_id}) do
            {:ok, [activity_with_data]} ->
              progress = activity_with_data.user_progress
              
              {:ok, %{
                freecash: %{
                  "base_amount" => Decimal.to_integer(activity.base_amount),
                  "invitation_bonus" => Decimal.to_integer(activity.invitation_bonus),
                  "min_invites" => activity.min_invites,
                  "invitation_count" => Map.get(progress, "invitation_count", 0),
                  "valid_invitations" => Map.get(progress, "valid_invitations", 0),
                  "can_claim" => Map.get(progress, "can_claim", false),
                  "potential_reward" => String.to_integer(Map.get(progress, "potential_reward", "0")),
                  "total_claims" => Map.get(progress, "total_claims", 0),
                  "max_claims" => activity.max_claims_per_user
                }
              }}
            
            _ ->
              {:ok, build_default_free_cash_data()}
          end
        
        _ ->
          {:ok, build_default_free_cash_data()}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting free cash data: #{inspect(e)}")
        {:ok, build_default_free_cash_data()}
    end
  end

  defp build_default_free_cash_data do
    %{
      freecash: %{
        "base_amount" => 1000,
        "invitation_bonus" => 100,
        "min_invites" => 0,
        "invitation_count" => 0,
        "valid_invitations" => 0,
        "can_claim" => true,
        "potential_reward" => 1000,
        "total_claims" => 0,
        "max_claims" => 1
      }
    }
  end

  @doc """
  获取邀请列表
  """
  def get_free_cash_invitation_list(user_id, page \\ 1, page_size \\ 20) do
    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case get_invitation_list(activity, %{user_id: user_id, page: page, page_size: page_size}) do
            {:ok, [activity_with_list]} ->
              {:ok, activity_with_list.invitation_list}
            
            _ ->
              {:ok, %{"invitations" => [], "total_count" => 0, "page" => page, "page_size" => page_size}}
          end
        
        _ ->
          {:ok, %{"invitations" => [], "total_count" => 0, "page" => page, "page_size" => page_size}}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting invitation list: #{inspect(e)}")
        {:ok, %{"invitations" => [], "total_count" => 0, "page" => page, "page_size" => page_size}}
    end
  end

  @doc """
  处理免费提现领取
  """
  def process_free_cash_claim(user_id) do
    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case claim_free_cash(activity, %{user_id: user_id}) do
            {:ok, _} ->
              # 重新获取进度数据计算实际奖励
              case get_user_data(activity, %{user_id: user_id}) do
                {:ok, [activity_with_data]} ->
                  progress = activity_with_data.user_progress
                  reward_amount = String.to_integer(Map.get(progress, "potential_reward", "0"))
                  
                  {:ok, %{code: 0, fetchaward: reward_amount, msg: "免费提现成功"}}
                
                _ ->
                  {:ok, %{code: 0, fetchaward: 1000, msg: "免费提现成功"}}
              end
            
            {:error, error} ->
              message = case error do
                %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                _ -> "领取失败"
              end
              {:error, %{code: 1, msg: message}}
          end
        
        _ ->
          {:error, %{code: 1, msg: "活动不存在"}}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception processing free cash claim: #{inspect(e)}")
        {:error, %{code: 1, msg: "系统错误"}}
    end
  end

  @doc """
  记录用户邀请
  """
  def record_user_invitation(inviter_id, invitee_id) do
    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case process_invitation(activity, %{inviter_id: inviter_id, invitee_id: invitee_id}) do
            {:ok, _} -> {:ok, "邀请记录成功"}
            {:error, error} ->
              message = case error do
                %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                _ -> "邀请记录失败"
              end
              {:error, message}
          end
        
        _ ->
          {:error, "活动不存在"}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception recording invitation: #{inspect(e)}")
        {:error, "系统错误"}
    end
  end

  @doc """
  更新邀请状态（当被邀请人满足条件时调用）
  """
  def update_invitation_status(inviter_id, invitee_id, requirements_met \\ true) do
    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case get_user_free_cash_record(inviter_id, activity.id) do
            nil -> {:error, "未找到邀请记录"}
            record ->
              invitations = Map.get(record.participation_data, "invitations", [])
              
              updated_invitations = 
                Enum.map(invitations, fn invitation ->
                  if Map.get(invitation, "invitee_id") == invitee_id do
                    %{invitation | 
                      "requirements_met" => requirements_met,
                      "status" => if(requirements_met, do: "valid", else: "invalid"),
                      "updated_at" => DateTime.utc_now() |> DateTime.to_iso8601()
                    }
                  else
                    invitation
                  end
                end)
              
              Teen.ActivitySystem.UserActivityParticipation.update(record, %{
                participation_data: %{
                  record.participation_data |
                  "invitations" => updated_invitations
                }
              })
          end
        
        _ ->
          {:error, "活动不存在"}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception updating invitation status: #{inspect(e)}")
        {:error, "系统错误"}
    end
  end
end