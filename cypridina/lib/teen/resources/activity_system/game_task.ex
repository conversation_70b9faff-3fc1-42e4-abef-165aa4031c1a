defmodule Teen.ActivitySystem.GameTask do
  @moduledoc """
  游戏任务资源

  管理每日游戏任务配置，包括：
  - 游戏局数任务
  - 游戏赢的局数任务
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :task_name,
      :game_name,
      :task_type,
      :required_count,
      :max_claims,
      :reward_amount,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "game_tasks"
    repo Cypridina.Repo
  end

  # AshOban 配置 - 每日任务重置
  oban do
    scheduled_actions do
      schedule :daily_task_reset, "0 0 * * *" do
        action :reset_daily_tasks
        worker_module_name(Teen.Workers.DailyTaskReset)
        max_attempts(2)
        queue(:task_reset)
      end
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_game
    define :enable_task
    define :disable_task
    define :reset_daily_tasks
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :task_name,
        :game_id,
        :game_name,
        :task_type,
        :required_count,
        :max_claims,
        :reward_amount,
        :status
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_game do
      argument :game_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    # 每日任务重置的定时任务
    create :reset_daily_tasks do
      accept []

      change fn changeset, _context ->
        case reset_all_daily_tasks() do
          {:ok, result} ->
            require Logger
            Logger.info("每日任务重置完成: #{inspect(result)}")
            changeset

          {:error, reason} ->
            require Logger
            Logger.error("每日任务重置失败: #{inspect(reason)}")

            changeset
            |> Ash.Changeset.add_error(field: :base, message: "每日任务重置失败: #{inspect(reason)}")
        end
      end
    end
  end

  validations do
    validate compare(:required_count, greater_than: 0), message: "所需局数必须大于0"
    validate compare(:max_claims, greater_than: 0), message: "最大领取次数必须大于0"

    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")),
      message: "奖励金额不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :task_name, :string do
      allow_nil? false
      public? true
      description "任务名称"
      constraints max_length: 100
    end

    attribute :game_id, :string do
      allow_nil? false
      public? true
      description "游戏ID"
      constraints max_length: 50
    end

    attribute :game_name, :string do
      allow_nil? false
      public? true
      description "游戏名称"
      constraints max_length: 100
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:game_rounds, :win_rounds]
    end

    attribute :required_count, :integer do
      allow_nil? false
      public? true
      description "所需局数"
      constraints min: 1
      default 1
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "每日最大领取次数"
      constraints min: 1
      default 1
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :game_rounds -> "游戏局数"
            :win_rounds -> "游戏赢的局数"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_game_task, [:game_id, :task_type]
  end

  # 私有函数 - 每日任务重置逻辑

  defp reset_all_daily_tasks do
    require Logger
    Logger.info("📝 开始重置每日任务...")

    # 这里可以实现具体的任务重置逻辑
    # 例如：重置用户任务进度、生成新的每日任务等

    # 示例：重置所有用户的每日任务进度
    # 实际实现需要根据具体的任务进度表结构来调整

    Logger.info("📝 每日任务重置完成")
    {:ok, %{message: "每日任务已重置", reset_time: DateTime.utc_now()}}
  end
end
