defmodule Teen.ActivitySystem.InviteRewardConfig do
  @moduledoc """
  邀请奖励配置资源

  管理邀请活动的奖励配置
  包括轮次、奖励类型、奖励范围等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :activity_id,
      :round_number,
      :reward_type,
      :min_reward,
      :max_reward,
      :updated_at
    ]
  end

  postgres do
    table "invite_reward_configs"
    repo Cyp<PERSON>ina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_activity
    define :list_by_round
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:activity_id, :round_number, :reward_type, :min_reward, :max_reward]
    end

    read :list_by_activity do
      argument :activity_id, :uuid, allow_nil?: false
      filter expr(activity_id == ^arg(:activity_id))
      prepare build(sort: [:round_number])
    end

    read :list_by_round do
      argument :round_number, :integer, allow_nil?: false
      filter expr(round_number == ^arg(:round_number))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :round_number, :integer do
      allow_nil? false
      public? true
      description "轮次"
      constraints min: 1
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:invite_new_friend, :invite_recharge]
    end

    attribute :min_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励最小值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励最大值（分）"
      constraints min: Decimal.new("0")
    end

    timestamps()
  end

  relationships do
    belongs_to :activity, Teen.ActivitySystem.InviteCashActivity do
      public? true
      source_attribute :activity_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_activity_round, [:activity_id, :round_number]
  end
end
