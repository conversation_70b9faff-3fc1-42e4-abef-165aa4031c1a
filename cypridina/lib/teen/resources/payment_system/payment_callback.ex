defmodule Teen.PaymentSystem.PaymentCallback do
  @moduledoc """
  支付回调记录资源

  记录支付网关的回调信息，包括：
  - 回调数据
  - 验证结果
  - 处理状态
  - 错误信息
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :payment_order_id,
      :callback_type,
      :client_ip,
      :signature_verified,
      :processed,
      :inserted_at
    ]
  end

  postgres do
    table "payment_callbacks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_order
    define :list_unprocessed
    define :mark_processed
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :payment_order_id,
        :callback_type,
        :client_ip,
        :callback_data,
        :signature_verified,
        :ip_verified
      ]
    end

    read :list_by_order do
      argument :payment_order_id, :uuid, allow_nil?: false
      filter expr(payment_order_id == ^arg(:payment_order_id))
      prepare build(sort: [desc: :created_at])
    end

    read :list_unprocessed do
      filter expr(processed == false)
      prepare build(sort: [asc: :created_at])
    end

    read :list_by_type do
      argument :callback_type, :string, allow_nil?: false
      filter expr(callback_type == ^arg(:callback_type))
      prepare build(sort: [desc: :created_at])
    end

    update :mark_processed do
      require_atomic? false
      accept [:process_result, :error_message]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:processed, true)
      end
    end

    update :mark_failed do
      require_atomic? false
      accept [:error_message]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:processed, true)
        |> Ash.Changeset.change_attribute(:process_result, %{status: "failed"})
      end
    end
  end

  validations do
    validate present(:payment_order_id), message: "支付订单ID不能为空"
    validate present(:callback_type), message: "回调类型不能为空"
    validate present(:client_ip), message: "客户端IP不能为空"
    validate present(:callback_data), message: "回调数据不能为空"
  end

  attributes do
    uuid_primary_key :id

    attribute :payment_order_id, :uuid do
      allow_nil? false
      public? true
      description "支付订单ID"
    end

    attribute :callback_type, :string do
      allow_nil? false
      public? true
      description "回调类型"
      constraints max_length: 20
    end

    attribute :client_ip, :string do
      allow_nil? false
      public? true
      description "客户端IP"
      constraints max_length: 50
    end

    attribute :callback_data, :map do
      allow_nil? false
      public? true
      description "回调数据"
      default %{}
    end

    attribute :signature_verified, :boolean do
      allow_nil? false
      public? true
      description "签名验证通过"
      default false
    end

    attribute :ip_verified, :boolean do
      allow_nil? false
      public? true
      description "IP验证通过"
      default false
    end

    attribute :processed, :boolean do
      allow_nil? false
      public? true
      description "是否已处理"
      default false
    end

    attribute :process_result, :map do
      allow_nil? false
      public? true
      description "处理结果"
      default %{}
    end

    attribute :error_message, :string do
      allow_nil? true
      public? true
      description "错误信息"
    end

    timestamps()
  end

  relationships do
    belongs_to :payment_order, Teen.PaymentSystem.PaymentOrder do
      public? true
      source_attribute :payment_order_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :is_valid, :boolean do
      public? true
      description "是否有效"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.signature_verified && record.ip_verified
        end)
      end
    end

    calculate :is_pending, :boolean do
      public? true
      description "是否待处理"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.signature_verified && record.ip_verified && !record.processed
        end)
      end
    end

    calculate :verification_status, :string do
      public? true
      description "验证状态"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case {record.signature_verified, record.ip_verified} do
            {true, true} -> "验证通过"
            {false, true} -> "签名验证失败"
            {true, false} -> "IP验证失败"
            {false, false} -> "验证失败"
          end
        end)
      end
    end

    calculate :process_status, :string do
      public? true
      description "处理状态"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          cond do
            !record.signature_verified || !record.ip_verified -> "验证失败"
            record.processed && record.error_message -> "处理失败"
            record.processed -> "处理成功"
            true -> "待处理"
          end
        end)
      end
    end
  end
end
