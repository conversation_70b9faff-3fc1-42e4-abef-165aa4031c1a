defmodule Teen.PaymentSystem.PaymentGateway do
  @moduledoc """
  支付网关资源

  管理各种支付网关的配置和状态
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :name, :gateway_type, :channel_id, :priority, :enabled, :inserted_at]
  end

  postgres do
    table "payment_gateways"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_enabled
    define :list_by_type, args: [:gateway_type]
    define :list_by_priority
    define :find_best_gateway, args: [:gateway_type, :amount, :currency]
    define :enable
    define :disable
    define :test_connection
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :list_enabled do
      filter expr(enabled == true)
    end

    read :list_by_type do
      argument :gateway_type, :string, allow_nil?: false
      filter expr(gateway_type == ^arg(:gateway_type) and enabled == true)
    end

    read :list_by_priority do
      filter expr(enabled == true)
    end

    read :find_best_gateway do
      argument :gateway_type, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      argument :currency, :string, allow_nil?: false, default: "INR"

      filter expr(
               gateway_type == ^arg(:gateway_type) and
                 enabled == true and
                 ^arg(:currency) in supported_currencies and
                 (is_nil(min_amount) or min_amount <= ^arg(:amount)) and
                 (is_nil(max_amount) or max_amount >= ^arg(:amount))
             )
    end

    update :enable do
      change set_attribute(:enabled, true)
    end

    update :disable do
      change set_attribute(:enabled, false)
    end

    update :test_connection do
      change set_attribute(:last_test_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "网关名称"
      constraints max_length: 100
    end

    attribute :gateway_type, :string do
      allow_nil? false
      public? true
      description "网关类型：recharge-代收，withdraw-代付"
      constraints max_length: 50
    end

    attribute :gateway_url, :string do
      allow_nil? false
      public? true
      description "网关基础URL"
      constraints max_length: 500
    end

    attribute :create_order_path, :string do
      allow_nil? false
      public? true
      description "下单接口路径"
      default "/v1.0/api/order/create"
      constraints max_length: 200
    end

    attribute :query_order_path, :string do
      allow_nil? false
      public? true
      description "查询接口路径"
      default "/v1.0/api/order/query"
      constraints max_length: 200
    end

    attribute :channel_id, :string do
      allow_nil? false
      public? true
      description "通道ID"
      constraints max_length: 50
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越小优先级越高）"
      default 100
      constraints min: 1, max: 999
    end

    attribute :min_amount, :decimal do
      allow_nil? true
      public? true
      description "最小金额（元）"
      constraints min: Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? true
      public? true
      description "最大金额（元）"
      constraints min: Decimal.new("0")
    end

    attribute :supported_currencies, {:array, :string} do
      allow_nil? false
      public? true
      description "支持的币种"
      default ["INR"]
    end

    attribute :enabled, :boolean do
      allow_nil? false
      public? true
      description "是否启用"
      default true
    end

    attribute :rate_limit_per_minute, :integer do
      allow_nil? true
      public? true
      description "每分钟请求限制"
      constraints min: 1
    end

    attribute :timeout_seconds, :integer do
      allow_nil? false
      public? true
      description "请求超时时间（秒）"
      default 30
      constraints min: 5, max: 300
    end

    attribute :config_data, :map do
      allow_nil? true
      public? true
      description "额外配置数据（JSON）"
    end

    attribute :last_test_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后测试时间"
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述"
      constraints max_length: 500
    end

    timestamps()
  end

  relationships do
    has_many :payment_configs, Teen.PaymentSystem.PaymentConfig do
      public? true
      source_attribute :id
      destination_attribute :gateway_id
    end
  end

  identities do
    identity :unique_gateway_name, [:name]
    identity :unique_gateway_type_channel, [:gateway_type, :channel_id]
  end
end

defimpl Phoenix.HTML.Safe, for: Teen.PaymentSystem.PaymentGateway do
  def to_iodata(gateway) do
    Phoenix.HTML.Safe.to_iodata(gateway.name || "未知网关")
  end
end

defimpl String.Chars, for: Teen.PaymentSystem.PaymentGateway do
  def to_string(gateway) do
    gateway.name || "未知网关"
  end
end
