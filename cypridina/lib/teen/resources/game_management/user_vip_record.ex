defmodule Teen.GameManagement.UserVipRecord do
  @moduledoc """
  用户VIP经验值记录资源

  记录用户的VIP相关数据，包括：
  - VIP经验值
  - 充值总数
  - 获取奖励总数
  - 当前VIP等级
  - VIP等级变更历史
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.GameManagement,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :current_vip_level,
      :vip_experience,
      :total_recharge_amount,
      :total_rewards_received,
      :updated_at
    ]
  end

  postgres do
    table "user_vip_records"
    repo Cyp<PERSON><PERSON>.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_by_user_id, args: [:user_id]
    define :update_vip_experience
    define :update_recharge_total
    define :update_rewards_total
    define :level_up
    define :get_user_vip_stats
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :user_id,
        :current_vip_level,
        :vip_experience,
        :total_recharge_amount,
        :total_rewards_received
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:current_vip_level, 0)
        |> Ash.Changeset.change_attribute(:vip_experience, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:total_recharge_amount, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:total_rewards_received, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:last_level_up_at, nil)
      end
    end

    read :get_by_user_id do
      argument :user_id, :uuid, allow_nil?: false
      get? true
      filter expr(user_id == ^arg(:user_id))
    end

    update :update_vip_experience do
      argument :experience_amount, :decimal, allow_nil?: false

      argument :operation, :atom,
        allow_nil?: false,
        constraints: [one_of: [:add, :subtract, :set]]

      require_atomic? false

      change fn changeset, _context ->
        experience_amount = Ash.Changeset.get_argument(changeset, :experience_amount)
        operation = Ash.Changeset.get_argument(changeset, :operation)

        current_experience =
          Ash.Changeset.get_attribute(changeset, :vip_experience) || Decimal.new("0")

        new_experience =
          case operation do
            :add ->
              Decimal.add(current_experience, experience_amount)

            :subtract ->
              Decimal.max(Decimal.sub(current_experience, experience_amount), Decimal.new("0"))

            :set ->
              experience_amount
          end

        Ash.Changeset.change_attribute(changeset, :vip_experience, new_experience)
      end
    end

    update :update_recharge_total do
      argument :recharge_amount, :decimal, allow_nil?: false

      require_atomic? false

      change fn changeset, _context ->
        recharge_amount = Ash.Changeset.get_argument(changeset, :recharge_amount)

        current_total =
          Ash.Changeset.get_attribute(changeset, :total_recharge_amount) || Decimal.new("0")

        new_total = Decimal.add(current_total, recharge_amount)

        changeset
        |> Ash.Changeset.change_attribute(:total_recharge_amount, new_total)
        |> Ash.Changeset.change_attribute(:last_recharge_at, DateTime.utc_now())
      end
    end

    update :update_rewards_total do
      argument :reward_amount, :decimal, allow_nil?: false

      require_atomic? false

      change fn changeset, _context ->
        reward_amount = Ash.Changeset.get_argument(changeset, :reward_amount)

        current_total =
          Ash.Changeset.get_attribute(changeset, :total_rewards_received) || Decimal.new("0")

        new_total = Decimal.add(current_total, reward_amount)

        changeset
        |> Ash.Changeset.change_attribute(:total_rewards_received, new_total)
        |> Ash.Changeset.change_attribute(:last_reward_at, DateTime.utc_now())
      end
    end

    update :level_up do
      argument :new_level, :integer, allow_nil?: false

      require_atomic? false

      change fn changeset, _context ->
        new_level = Ash.Changeset.get_argument(changeset, :new_level)
        current_level = Ash.Changeset.get_attribute(changeset, :current_vip_level) || 0

        if new_level > current_level do
          changeset
          |> Ash.Changeset.change_attribute(:current_vip_level, new_level)
          |> Ash.Changeset.change_attribute(:last_level_up_at, DateTime.utc_now())
          |> Ash.Changeset.change_attribute(
            :level_up_count,
            (Ash.Changeset.get_attribute(changeset, :level_up_count) || 0) + 1
          )
        else
          changeset
        end
      end
    end

    read :get_user_vip_stats do
      argument :user_id, :uuid, allow_nil?: false
      get? true
      filter expr(user_id == ^arg(:user_id))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :current_vip_level, :integer do
      allow_nil? false
      public? true
      description "当前VIP等级"
      default 0
      constraints min: 0
    end

    attribute :vip_experience, :decimal do
      allow_nil? false
      public? true
      description "VIP经验值"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "累计充值总额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_rewards_received, :decimal do
      allow_nil? false
      public? true
      description "累计获得奖励总额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :level_up_count, :integer do
      allow_nil? false
      public? true
      description "升级次数"
      default 0
      constraints min: 0
    end

    attribute :last_level_up_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后升级时间"
    end

    attribute :last_recharge_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后充值时间"
    end

    attribute :last_reward_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后获得奖励时间"
    end

    attribute :daily_login_streak, :integer do
      allow_nil? false
      public? true
      description "连续登录天数"
      default 0
      constraints min: 0
    end

    attribute :last_daily_bonus_at, :date do
      allow_nil? true
      public? true
      description "最后领取每日奖励日期"
    end

    attribute :monthly_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "本月充值金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :monthly_rewards_received, :decimal do
      allow_nil? false
      public? true
      description "本月获得奖励总额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    # 计算距离下一级所需经验
    calculate :experience_to_next_level, :decimal do
      public? true
      description "距离下一级所需经验值"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case Teen.VipSystem.VipLevel.get_by_level(record.current_vip_level + 1) do
            {:ok, [next_level]} ->
              required_exp = next_level.recharge_requirement
              current_exp = record.vip_experience
              Decimal.max(Decimal.sub(required_exp, current_exp), Decimal.new("0"))

            _ ->
              Decimal.new("0")
          end
        end)
      end
    end

    # 计算VIP等级进度百分比
    calculate :level_progress_percentage, :decimal do
      public? true
      description "当前等级进度百分比"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          current_level = record.current_vip_level
          current_exp = record.vip_experience

          with {:ok, [current_level_info]} <-
                 Teen.VipSystem.VipLevel.get_by_level(current_level),
               {:ok, [next_level_info]} <-
                 Teen.VipSystem.VipLevel.get_by_level(current_level + 1) do
            current_requirement = current_level_info.recharge_requirement
            next_requirement = next_level_info.recharge_requirement
            level_range = Decimal.sub(next_requirement, current_requirement)
            progress = Decimal.sub(current_exp, current_requirement)

            if Decimal.compare(level_range, Decimal.new("0")) == :gt do
              percentage = Decimal.div(progress, level_range)
              Decimal.mult(percentage, Decimal.new("100"))
            else
              Decimal.new("100")
            end
          else
            _ -> Decimal.new("0")
          end
        end)
      end
    end
  end

  identities do
    identity :unique_user_vip_record, [:user_id]
  end
end
