defmodule Teen.GameManagement.GameRecord do
  @moduledoc """
  游戏记录资源

  记录用户的游戏信息，包括游戏类型、结果、下注金额等
  配置了Ash Notifiers来自动发布游戏事件
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.GameManagement,
    extensions: [AshAdmin.Resource],
    notifiers: [Ash.Notifier.PubSub]

  admin do
    table_columns [
      :id,
      :user_id,
      :game_id,
      :game_type,
      :result_status,
      :bet_amount,
      :win_amount,
      :inserted_at
    ]
  end

  postgres do
    table "game_records"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user, args: [:user_id]
    define :list_by_game, args: [:game_id]
    define :list_by_game_type, args: [:game_type]
    define :complete_game
    define :start_game_session
    define :get_user_history
    define :get_user_stats
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :user_id,
        :game_id,
        :game_type,
        :bet_amount,
        :result_status,
        :win_amount,
        :game_data
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:started_at, DateTime.utc_now())
      end
    end

    create :start_game_session do
      accept [:user_id, :game_type, :bet_amount]
      
      change fn changeset, _context ->
        # 生成游戏ID
        game_id = generate_game_id()
        
        changeset
        |> Ash.Changeset.change_attribute(:game_id, game_id)
        |> Ash.Changeset.change_attribute(:result_status, :in_progress)
        |> Ash.Changeset.change_attribute(:started_at, DateTime.utc_now())
      end
      
      # 开始游戏前验证余额并扣除下注金额
      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_attribute(changeset, :user_id)
        bet_amount = Ash.Changeset.get_attribute(changeset, :bet_amount)
        
        case check_and_deduct_bet(user_id, bet_amount) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :bet_amount, message: reason}
        end
      end
      
      # 创建后发布下注事件
      after_action fn changeset, record ->
        Teen.Events.EventPublisher.handle_game_bet_placed(
          record.user_id, 
          record.game_type, 
          record.bet_amount
        )
        {:ok, record}
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :inserted_at])
    end

    read :list_by_game do
      argument :game_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id))
      prepare build(sort: [desc: :inserted_at])
    end

    read :list_by_game_type do
      argument :game_type, :string, allow_nil?: false
      filter expr(game_type == ^arg(:game_type))
      prepare build(sort: [desc: :inserted_at])
    end
    
    read :get_user_history do
      argument :user_id, :uuid, allow_nil?: false
      argument :limit, :integer, default: 20
      argument :offset, :integer, default: 0
      argument :game_type, :string
      
      filter expr(user_id == ^arg(:user_id))
      
      prepare fn query, context ->
        query = Ash.Query.limit(query, context.arguments.limit)
        query = Ash.Query.offset(query, context.arguments.offset)
        
        if context.arguments[:game_type] do
          Ash.Query.filter(query, expr(game_type == ^context.arguments.game_type))
        else
          query
        end
        |> Ash.Query.sort(inserted_at: :desc)
      end
    end
    
    read :get_user_stats do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :datetime
      argument :end_date, :datetime
      
      filter expr(user_id == ^arg(:user_id))
      
      prepare fn query, context ->
        query = if context.arguments[:start_date] do
          Ash.Query.filter(query, expr(started_at >= ^context.arguments.start_date))
        else
          query
        end
        
        if context.arguments[:end_date] do
          Ash.Query.filter(query, expr(started_at <= ^context.arguments.end_date))
        else
          query
        end
      end
    end

    update :complete_game do
      accept [:result_status, :win_amount, :game_data, :duration]
      require_atomic? false

      change fn changeset, _context ->
        # 计算游戏时长
        started_at = changeset.data.started_at
        duration = if started_at do
          DateTime.diff(DateTime.utc_now(), started_at, :second)
        else
          0
        end
        
        changeset
        |> Ash.Changeset.change_attribute(:completed_at, DateTime.utc_now())
        |> Ash.Changeset.change_attribute(:duration, duration)
      end
      
      # 完成后处理奖金和发布事件
      after_action fn changeset, record ->
        case process_game_completion(record) do
          {:ok, _} -> 
            {:ok, record}
          {:error, reason} ->
            require Logger
            Logger.error("Failed to process game completion: #{inspect(reason)}")
            {:error, Ash.Error.Invalid.exception(field: :win_amount, message: "Failed to process winnings")}
        end
      end
    end
  end

  # 配置PubSub通知，当游戏记录变更时自动发布事件
  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "game"

    # 发布游戏完成事件
    publish :complete_game, ["game_completed", [:user_id]]

    # 发布所有创建和更新事件
    publish_all :create, [["game_started", :user_id]]
    publish_all :update, [["game_updated", :user_id]]
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :game_id, :string do
      allow_nil? false
      public? true
      description "游戏ID"
      constraints max_length: 100
    end

    attribute :game_type, :string do
      allow_nil? false
      public? true
      description "游戏类型"
      constraints max_length: 50
    end

    attribute :bet_amount, :decimal do
      allow_nil? false
      public? true
      description "下注金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :win_amount, :decimal do
      allow_nil? false
      public? true
      description "获胜金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :result_status, :atom do
      allow_nil? false
      public? true
      description "游戏结果"
      constraints one_of: [:win, :lose, :draw, :cancelled]
    end

    attribute :game_data, :map do
      allow_nil? true
      public? true
      description "游戏详细数据"
      default %{}
    end

    attribute :duration, :integer do
      allow_nil? true
      public? true
      description "游戏时长（秒）"
      constraints min: 0
    end

    attribute :started_at, :utc_datetime do
      allow_nil? true
      public? true
      description "游戏开始时间"
    end

    attribute :completed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "游戏完成时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :net_result, :decimal do
      public? true
      description "净结果（赢得金额 - 下注金额）"
      
      calculation fn records, _ ->
        Enum.map(records, fn record ->
          Decimal.sub(record.win_amount, record.bet_amount)
        end)
      end
    end
    
    calculate :is_win, :boolean do
      public? true
      description "是否赢得游戏"
      
      calculation fn records, _ ->
        Enum.map(records, fn record ->
          record.result_status == :win
        end)
      end
    end
  end

  # 私有辅助函数
  defp generate_game_id do
    timestamp = System.system_time(:millisecond)
    random = :rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")
    "GAME#{timestamp}#{random}"
  end

  defp check_and_deduct_bet(user_id, bet_amount) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    
    # 检查余额
    case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
      {:ok, balance} ->
        if Decimal.compare(balance, bet_amount) != :lt do
          # 扣除下注金额
          case deduct_bet_amount(user_id, bet_amount) do
            {:ok, _} -> :ok
            {:error, reason} -> {:error, "扣除下注金额失败: #{inspect(reason)}"}
          end
        else
          {:error, "余额不足"}
        end
      {:error, _} ->
        {:error, "无法获取余额信息"}
    end
  end

  defp deduct_bet_amount(user_id, bet_amount) do
    system_identifier = Cypridina.Ledger.AccountIdentifier.system(:game_bet, :XAA)
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    
    amount_integer = if is_struct(bet_amount, Decimal) do
      Decimal.to_integer(bet_amount)
    else
      bet_amount
    end
    
    Cypridina.Ledger.transfer(
      user_identifier,
      system_identifier,
      amount_integer,
      transaction_type: :game_bet,
      description: "游戏下注"
    )
  end

  defp process_game_completion(record) do
    # 处理奖金
    with {:ok, _} <- process_winnings(record),
         {:ok, _} <- update_user_statistics(record) do
      
      # 发布游戏完成事件
      Teen.Events.EventPublisher.handle_game_completed(
        record.user_id,
        record.game_type,
        record.bet_amount,
        record.win_amount,
        record.result_status
      )
      
      {:ok, record}
    end
  end

  defp process_winnings(record) do
    if Decimal.compare(record.win_amount, Decimal.new(0)) == :gt do
      system_identifier = Cypridina.Ledger.AccountIdentifier.system(:game_win, :XAA)
      user_identifier = Cypridina.Ledger.AccountIdentifier.user(record.user_id, :XAA)
      
      amount_integer = if is_struct(record.win_amount, Decimal) do
        Decimal.to_integer(record.win_amount)
      else
        record.win_amount
      end
      
      Cypridina.Ledger.transfer(
        system_identifier,
        user_identifier,
        amount_integer,
        transaction_type: :game_win,
        description: "游戏奖金"
      )
    else
      {:ok, nil}
    end
  end

  defp update_user_statistics(record) do
    # 更新用户游戏统计
    is_win = record.result_status == :win
    wins_delta = if is_win, do: 1, else: 0
    
    Teen.ActivitySystem.UserGameStatistics.update_user_game_stats(
      record.user_id,
      1,  # games_delta
      wins_delta
    )
    
    # 更新连胜统计
    Teen.ActivitySystem.UserGameStatistics.update_user_win_streak(record.user_id, is_win)
    
    # 如果输了，更新损失金额
    if record.result_status == :lose do
      net_loss = Decimal.sub(record.bet_amount, record.win_amount)
      Teen.ActivitySystem.UserGameStatistics.update_user_loss(record.user_id, net_loss)
    end
    
    {:ok, record}
  end

  @doc """
  开始游戏会话
  """
  def start_game(user_id, game_type, bet_amount) do
    require Logger
    Logger.info("Starting game for user #{user_id}, type: #{game_type}, bet: #{bet_amount}")
    
    case start_game_session(%{
      user_id: user_id,
      game_type: game_type,
      bet_amount: bet_amount
    }) do
      {:ok, record} ->
        {:ok, %{game_id: record.game_id, status: :started}}
      {:error, reason} ->
        Logger.error("Failed to start game: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  完成游戏
  """
  def complete_game_session(game_id, result_data) do
    require Logger
    Logger.info("Completing game #{game_id} with result: #{inspect(result_data)}")
    
    with {:ok, [record | _]} <- list_by_game(game_id),
         {:ok, game_result} <- calculate_game_result(record, result_data),
         {:ok, completed} <- complete_game(record, game_result) do
      {:ok, game_result}
    else
      {:ok, []} -> {:error, "Game not found"}
      error -> error
    end
  end

  defp calculate_game_result(game_record, result_data) do
    win_amount =
      case result_data[:outcome] do
        :win -> Decimal.mult(game_record.bet_amount, Decimal.new(2))
        :lose -> Decimal.new(0)
        _ -> game_record.bet_amount  # 平局返还本金
      end

    result_status =
      cond do
        Decimal.compare(win_amount, game_record.bet_amount) == :gt -> :win
        Decimal.compare(win_amount, Decimal.new(0)) == :eq -> :lose
        true -> :draw
      end

    {:ok,
     %{
       result_status: result_status,
       win_amount: win_amount,
       game_data: result_data
     }}
  end

  @doc """
  获取用户游戏历史
  """
  def get_game_history(user_id, opts \\ []) do
    params = %{
      user_id: user_id,
      limit: Keyword.get(opts, :limit, 20),
      offset: Keyword.get(opts, :offset, 0)
    }
    
    params = if game_type = Keyword.get(opts, :game_type) do
      Map.put(params, :game_type, game_type)
    else
      params
    end
    
    get_user_history(params)
  end

  @doc """
  获取用户游戏统计
  """
  def get_game_statistics(user_id, date_range \\ nil) do
    params = %{user_id: user_id}
    params = if date_range do
      params
      |> Map.put(:start_date, date_range.start_date)
      |> Map.put(:end_date, date_range.end_date)
    else
      params
    end

    case get_user_stats(params) do
      {:ok, records} ->
        total_bet = Enum.reduce(records, Decimal.new(0), fn r, acc ->
          Decimal.add(acc, r.bet_amount)
        end)
        
        total_win = Enum.reduce(records, Decimal.new(0), fn r, acc ->
          Decimal.add(acc, r.win_amount)
        end)
        
        win_count = Enum.count(records, fn r -> r.result_status == :win end)
        total_games = length(records)
        win_rate = if total_games > 0, do: win_count / total_games, else: 0
        
        # 找出最常玩的游戏
        favorite_game = records
        |> Enum.group_by(& &1.game_type)
        |> Enum.max_by(fn {_, games} -> length(games) end, fn -> {nil, []} end)
        |> elem(0)
        
        last_played = records
        |> Enum.map(& &1.started_at)
        |> Enum.max(DateTime, fn -> nil end)

        stats = %{
          total_games: total_games,
          total_bet: total_bet,
          total_win: total_win,
          win_rate: win_rate,
          favorite_game: favorite_game,
          last_played_at: last_played
        }

        {:ok, stats}

      {:error, reason} = error ->
        require Logger
        Logger.error("Failed to get game stats for user #{user_id}: #{inspect(reason)}")
        error
    end
  end
end
