defmodule Teen.Statistics.SystemReport do
  @moduledoc """
  系统报表资源

  管理系统各种统计报表数据
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.Statistics,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :report_type, :report_date, :total_users, :active_users, :revenue]
  end

  postgres do
    table "system_reports"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_type
    define :list_by_date_range
    define :get_latest_report
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_type do
      argument :report_type, :string, allow_nil?: false
      filter expr(report_type == ^arg(:report_type))
    end

    read :list_by_date_range do
      argument :start_date, :date, allow_nil?: false
      argument :end_date, :date, allow_nil?: false
      filter expr(report_date >= ^arg(:start_date) and report_date <= ^arg(:end_date))
    end

    read :get_latest_report do
      argument :report_type, :string, allow_nil?: false
      filter expr(report_type == ^arg(:report_type))
      prepare build(sort: [report_date: :desc], limit: 1)
    end

    create :generate_daily_report do
      argument :report_date, :date, allow_nil?: false

      change fn changeset, _context ->
        report_date = Ash.Changeset.get_argument(changeset, :report_date)

        # 这里应该实现实际的统计逻辑
        # 暂时使用模拟数据
        changeset
        |> Ash.Changeset.change_attribute(:report_type, "daily")
        |> Ash.Changeset.change_attribute(:report_date, report_date)
        |> Ash.Changeset.change_attribute(:total_users, 1000)
        |> Ash.Changeset.change_attribute(:active_users, 500)
        |> Ash.Changeset.change_attribute(:new_users, 50)
        |> Ash.Changeset.change_attribute(:revenue, Decimal.new("10000"))
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :report_type, :string do
      allow_nil? false
      public? true
      description "报表类型：daily-日报，weekly-周报，monthly-月报"
      constraints max_length: 20
    end

    attribute :report_date, :date do
      allow_nil? false
      public? true
      description "报表日期"
    end

    attribute :total_users, :integer do
      allow_nil? false
      public? true
      description "总用户数"
      default 0
      constraints min: 0
    end

    attribute :active_users, :integer do
      allow_nil? false
      public? true
      description "活跃用户数"
      default 0
      constraints min: 0
    end

    attribute :new_users, :integer do
      allow_nil? false
      public? true
      description "新增用户数"
      default 0
      constraints min: 0
    end

    attribute :revenue, :decimal do
      allow_nil? false
      public? true
      description "收入（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_recharge, :decimal do
      allow_nil? false
      public? true
      description "总充值金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_exchange, :decimal do
      allow_nil? false
      public? true
      description "总兑换金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :game_rounds, :integer do
      allow_nil? false
      public? true
      description "游戏局数"
      default 0
      constraints min: 0
    end

    attribute :online_peak, :integer do
      allow_nil? false
      public? true
      description "在线峰值"
      default 0
      constraints min: 0
    end

    attribute :retention_rate, :decimal do
      allow_nil? true
      public? true
      description "留存率（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :report_data, :map do
      allow_nil? true
      public? true
      description "详细报表数据（JSON）"
    end

    timestamps()
  end

  identities do
    identity :unique_report_type_date, [:report_type, :report_date]
  end
end
