defmodule Teen.SystemSettings.AdminUser do
  @moduledoc """
  管理员用户资源

  管理后台管理员账户，包括权限级别、角色分配等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.SystemSettings,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :username,
      :email,
      :role_id,
      :permission_level,
      :status,
      :last_login_at,
      :inserted_at
    ]
  end

  postgres do
    table "admin_users"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_role
    define :list_by_status
    define :authenticate
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_role do
      argument :role_id, :uuid, allow_nil?: false
      filter expr(role_id == ^arg(:role_id))
    end

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :authenticate do
      argument :username, :string, allow_nil?: false
      argument :password, :string, allow_nil?: false, sensitive?: true
      get? true
      filter expr(username == ^arg(:username) and status == 1)
    end

    update :activate do
      accept []
      change set_attribute(:status, 1)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    update :deactivate do
      accept []
      change set_attribute(:status, 0)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    update :update_last_login do
      accept []
      change set_attribute(:last_login_at, &DateTime.utc_now/0)
    end

    update :change_password do
      accept [:password_hash]
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end
  end

  validations do
    validate match(:email, ~r/^[^\s]+@[^\s]+\.[^\s]+$/) do
      where present(:email)
      message "邮箱格式不正确"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :username, :string do
      allow_nil? false
      public? true
      description "管理员用户名"
      constraints min_length: 3, max_length: 50
    end

    attribute :email, :string do
      allow_nil? true
      public? true
      description "管理员邮箱"
      constraints max_length: 255
    end

    attribute :password_hash, :string do
      allow_nil? false
      sensitive? true
      description "密码哈希"
    end

    attribute :role_id, :uuid do
      allow_nil? true
      public? true
      description "角色ID"
    end

    attribute :permission_level, :integer do
      allow_nil? false
      public? true
      description "权限级别：0=普通管理员，1=高级管理员，2=超级管理员"
      default 0
      constraints min: 0, max: 2
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :last_login_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后登录时间"
    end

    attribute :login_ip, :string do
      allow_nil? true
      public? true
      description "最后登录IP"
      constraints max_length: 45
    end

    attribute :remarks, :string do
      allow_nil? true
      public? true
      description "备注信息"
      constraints max_length: 500
    end

    timestamps()
  end

  relationships do
    belongs_to :role, Teen.SystemSettings.Role do
      attribute_writable? true
    end

    has_many :operation_logs, Teen.SystemSettings.OperationLog do
      destination_attribute :admin_user_id
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)
    calculate :is_super_admin, :boolean, expr(permission_level >= 2)
    calculate :can_manage_users, :boolean, expr(permission_level >= 1)
  end

  identities do
    identity :unique_username, [:username]
    identity :unique_email, [:email]
  end
end
