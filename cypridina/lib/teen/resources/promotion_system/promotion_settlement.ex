defmodule Teen.PromotionSystem.PromotionSettlement do
  @moduledoc """
  推广结算资源

  管理推广员的佣金结算记录，包括结算周期、佣金计算、支付状态等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PromotionSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :promoter_id,
      :settlement_period,
      :commission_amount,
      :status,
      :settlement_date,
      :payment_date,
      :inserted_at
    ]
  end

  postgres do
    table "promotion_settlements"
    repo Cypridina.Repo
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :promoter_id,
        :settlement_period,
        :commission_amount,
        :bonus_amount,
        :deduction_amount,
        :remarks
      ]

      change fn changeset, _context ->
        commission =
          Ash.Changeset.get_attribute(changeset, :commission_amount) || Decimal.new("0")

        bonus = Ash.Changeset.get_attribute(changeset, :bonus_amount) || Decimal.new("0")
        deduction = Ash.Changeset.get_attribute(changeset, :deduction_amount) || Decimal.new("0")

        total =
          commission
          |> Decimal.add(bonus)
          |> Decimal.sub(deduction)

        Ash.Changeset.change_attribute(changeset, :total_amount, total)
      end
    end

    update :update do
      accept [:commission_amount, :bonus_amount, :deduction_amount, :payment_method, :remarks]

      change fn changeset, _context ->
        commission =
          Ash.Changeset.get_attribute(changeset, :commission_amount) ||
            changeset.data.commission_amount

        bonus =
          Ash.Changeset.get_attribute(changeset, :bonus_amount) || changeset.data.bonus_amount

        deduction =
          Ash.Changeset.get_attribute(changeset, :deduction_amount) ||
            changeset.data.deduction_amount

        total =
          commission
          |> Decimal.add(bonus)
          |> Decimal.sub(deduction)

        Ash.Changeset.change_attribute(changeset, :total_amount, total)
      end
    end

    update :settle do
      accept [:settlement_date]

      change fn changeset, _context ->
        settlement_date =
          Ash.Changeset.get_attribute(changeset, :settlement_date) || Date.utc_today()

        changeset
        |> Ash.Changeset.change_attribute(:status, 1)
        |> Ash.Changeset.change_attribute(:settlement_date, settlement_date)
      end
    end

    update :pay do
      accept [:payment_date, :payment_method, :payment_reference]

      change fn changeset, _context ->
        payment_date = Ash.Changeset.get_attribute(changeset, :payment_date) || Date.utc_today()

        changeset
        |> Ash.Changeset.change_attribute(:status, 2)
        |> Ash.Changeset.change_attribute(:payment_date, payment_date)
      end
    end

    update :cancel do
      accept [:remarks]

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 3)
      end
    end

    update :reopen do
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, 0)
        |> Ash.Changeset.change_attribute(:settlement_date, nil)
        |> Ash.Changeset.change_attribute(:payment_date, nil)
        |> Ash.Changeset.change_attribute(:payment_reference, nil)
      end
    end
  end

  validations do
    validate match(:settlement_period, ~r/^\d{4}-\d{2}$/) do
      message "结算周期格式应为：YYYY-MM"
    end

    validate compare(:total_amount, greater_than_or_equal_to: 0) do
      message "总结算金额不能为负数"
    end

    validate match(:payment_method, ~r/^(bank|alipay|wechat|crypto)$/) do
      message "支付方式必须是：bank, alipay, wechat, crypto 之一"
      where present(:payment_method)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :promoter_id, :uuid do
      allow_nil? false
      public? true
      description "推广员ID"
    end

    attribute :settlement_period, :string do
      allow_nil? false
      public? true
      description "结算周期（如：2024-01）"
      constraints max_length: 20
    end

    attribute :commission_amount, :decimal do
      allow_nil? false
      public? true
      description "佣金金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :bonus_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :deduction_amount, :decimal do
      allow_nil? false
      public? true
      description "扣除金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_amount, :decimal do
      allow_nil? false
      public? true
      description "总结算金额"
      default Decimal.new("0")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=待结算，1=已结算，2=已支付，3=已取消"
      default 0
      constraints min: 0, max: 3
    end

    attribute :settlement_date, :date do
      allow_nil? true
      public? true
      description "结算日期"
    end

    attribute :payment_date, :date do
      allow_nil? true
      public? true
      description "支付日期"
    end

    attribute :payment_method, :string do
      allow_nil? true
      public? true
      description "支付方式：bank=银行转账，alipay=支付宝，wechat=微信，crypto=数字货币"
      constraints max_length: 20
    end

    attribute :payment_reference, :string do
      allow_nil? true
      public? true
      description "支付参考号"
      constraints max_length: 100
    end

    attribute :remarks, :string do
      allow_nil? true
      public? true
      description "备注"
      constraints max_length: 500
    end

    timestamps()
  end

  relationships do
    belongs_to :promoter, Teen.PromotionSystem.Promoter do
      destination_attribute :id
      source_attribute :promoter_id
    end
  end

  calculations do
    calculate :is_pending, :boolean, expr(status == 0)
    calculate :is_settled, :boolean, expr(status == 1)
    calculate :is_paid, :boolean, expr(status == 2)
    calculate :is_cancelled, :boolean, expr(status == 3)

    calculate :status_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            0 -> "待结算"
            1 -> "已结算"
            2 -> "已支付"
            3 -> "已取消"
            _ -> "未知状态"
          end
        end)
      end
    end

    calculate :payment_method_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.payment_method do
            "bank" -> "银行转账"
            "alipay" -> "支付宝"
            "wechat" -> "微信支付"
            "crypto" -> "数字货币"
            _ -> "未设置"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_promoter_period, [:promoter_id, :settlement_period]
  end

  # 批量结算功能
  def batch_settle_by_period(settlement_period) do
    # 这里可以实现批量结算逻辑
    # 查找指定周期内的所有待结算记录并进行结算
    {:ok, "批量结算功能待实现"}
  end
end
