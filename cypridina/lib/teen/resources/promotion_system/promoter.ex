defmodule Teen.PromotionSystem.Promoter do
  @moduledoc """
  推广员资源

  管理推广员信息，包括推广员等级、佣金比例、推广数据等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PromotionSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :promoter_code,
      :level,
      :status,
      :commission_rate,
      :total_commission,
      :total_invites,
      :inserted_at
    ]
  end

  postgres do
    table "promoters"
    repo Cypridina.Repo
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :user_id,
        :promoter_code,
        :level,
        :commission_rate,
        :parent_promoter_id,
        :description,
        :tags
      ]

      change fn changeset, _context ->
        changeset

        # 设置为审核中
        |> Ash.Changeset.change_attribute(:status, 2)
        |> Ash.Changeset.change_attribute(:promoter_code, generate_promoter_code())
      end
    end

    update :update do
      accept [:level, :commission_rate, :parent_promoter_id, :description, :tags]
    end

    update :approve do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 1)
      end
    end

    update :reject do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 3)
      end
    end

    update :activate do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 1)
      end
    end

    update :deactivate do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 0)
      end
    end

    update :update_activity do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :last_active_at, DateTime.utc_now())
      end
    end

    update :add_commission do
      argument :amount, :decimal, allow_nil?: false

      change fn changeset, _context ->
        amount = Ash.Changeset.get_argument(changeset, :amount) || Decimal.new("0")
        current_total = changeset.data.total_commission || Decimal.new("0")
        new_total = Decimal.add(current_total, amount)

        Ash.Changeset.change_attribute(changeset, :total_commission, new_total)
      end
    end

    update :increment_invites do
      accept []

      change fn changeset, _context ->
        current_total = changeset.data.total_invites || 0
        current_active = changeset.data.active_invites || 0

        changeset
        |> Ash.Changeset.change_attribute(:total_invites, current_total + 1)
        |> Ash.Changeset.change_attribute(:active_invites, current_active + 1)
      end
    end
  end

  validations do
    validate match(:promoter_code, ~r/^[A-Z0-9]{6,20}$/) do
      message "推广员代码必须是6-20位大写字母和数字组合"
    end

    validate compare(:commission_rate, greater_than: 0) do
      message "佣金比例必须大于0"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "关联用户ID"
    end

    attribute :promoter_code, :string do
      allow_nil? false
      public? true
      description "推广员代码"
      constraints max_length: 20
    end

    attribute :level, :integer do
      allow_nil? false
      public? true
      description "推广员等级：1=初级，2=中级，3=高级，4=超级"
      default 1
      constraints min: 1, max: 4
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用，2=审核中，3=已拒绝"
      default 2
      constraints min: 0, max: 3
    end

    attribute :commission_rate, :decimal do
      allow_nil? false
      public? true
      description "佣金比例（百分比）"
      default Decimal.new("5.0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :parent_promoter_id, :uuid do
      allow_nil? true
      public? true
      description "上级推广员ID"
    end

    attribute :total_commission, :decimal do
      allow_nil? false
      public? true
      description "累计佣金"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_invites, :integer do
      allow_nil? false
      public? true
      description "累计邀请人数"
      default 0
      constraints min: 0
    end

    attribute :active_invites, :integer do
      allow_nil? false
      public? true
      description "活跃邀请人数"
      default 0
      constraints min: 0
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "推广员描述"
      constraints max_length: 500
    end

    attribute :tags, {:array, :string} do
      allow_nil? true
      public? true
      description "标签列表"
      default %{}
    end

    attribute :last_active_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后活跃时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      destination_attribute :id
      source_attribute :user_id
    end

    belongs_to :parent_promoter, Teen.PromotionSystem.Promoter do
      destination_attribute :id
      source_attribute :parent_promoter_id
    end

    has_many :sub_promoters, Teen.PromotionSystem.Promoter do
      destination_attribute :parent_promoter_id
    end

    has_many :promotion_channels, Teen.PromotionSystem.PromotionChannel do
      destination_attribute :promoter_id
    end

    has_many :promotion_settlements, Teen.PromotionSystem.PromotionSettlement do
      destination_attribute :promoter_id
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)
    calculate :is_pending, :boolean, expr(status == 2)

    calculate :level_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.level do
            1 -> "初级推广员"
            2 -> "中级推广员"
            3 -> "高级推广员"
            4 -> "超级推广员"
            _ -> "未知等级"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_user_id, [:user_id]
    identity :unique_promoter_code, [:promoter_code]
  end

  defp generate_promoter_code do
    "P" <>
      (:crypto.strong_rand_bytes(4)
       |> Base.encode16()
       |> String.slice(0, 7))
  end
end
