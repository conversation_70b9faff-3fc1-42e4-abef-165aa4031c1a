defmodule Teen.UserSystem do
  @moduledoc """
  用户系统域

  包含用户相关的功能，如：
  - 用户流水管理
  - 用户等级和VIP系统
  - 用户行为记录
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  admin do
    show? true
  end

  resources do
    resource Teen.UserSystem.UserTurnover
    resource Teen.UserLuckValue
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  获取或创建用户流水记录
  """
  def get_or_create_user_turnover(user_id) do
    case Teen.UserSystem.UserTurnover.get_by_user(user_id: user_id) do
      {:ok, turnover} ->
        {:ok, turnover}

      {:error, _} ->
        Teen.UserSystem.UserTurnover.create_for_user(%{
          user_id: user_id,
          required_turnover: Decimal.new("0"),
          completed_turnover: Decimal.new("0")
        })
    end
  end

  @doc """
  添加流水要求
  """
  def add_turnover_requirement(user_id, amount, bonus_type) do
    with {:ok, turnover} <- get_or_create_user_turnover(user_id) do
      Teen.UserSystem.UserTurnover.add_requirement(turnover, %{
        additional_amount: amount,
        bonus_type: bonus_type
      })
    end
  end

  @doc """
  更新已完成流水
  """
  def update_completed_turnover(user_id, bet_amount) do
    with {:ok, turnover} <- get_or_create_user_turnover(user_id) do
      result =
        Teen.UserSystem.UserTurnover.update_completed(turnover, %{
          bet_amount: bet_amount
        })

      # 检查是否需要重置流水
      check_and_reset_turnover_if_needed(user_id)

      result
    end
  end

  @doc """
  重置用户流水
  """
  def reset_user_turnover(user_id, reason \\ "手动重置") do
    with {:ok, turnover} <- get_or_create_user_turnover(user_id) do
      Teen.UserSystem.UserTurnover.reset_turnover(turnover)
    end
  end

  @doc """
  检查并在必要时重置流水
  """
  def check_and_reset_turnover_if_needed(user_id) do
    # 获取用户余额
    identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    total_balance = Cypridina.Ledger.Account.get_balance_by_identifier(identifier)

    # 如果余额低于5卢比（500分），重置流水
    if Decimal.compare(total_balance, Decimal.new("500")) != :gt do
      reset_user_turnover(user_id, "余额低于5卢比自动清零")
    else
      :ok
    end
  end

  @doc """
  获取用户流水信息
  """
  def get_user_turnover_info(user_id) do
    case Teen.UserSystem.UserTurnover.get_by_user(user_id) do
      {:ok, turnover} ->
        {:ok,
         %{
           required_turnover: turnover.required_turnover,
           completed_turnover: turnover.completed_turnover,
           remaining_turnover:
             Decimal.sub(turnover.required_turnover, turnover.completed_turnover),
           is_completed:
             Decimal.compare(turnover.completed_turnover, turnover.required_turnover) != :lt,
           last_updated: turnover.updated_at
         }}

      {:error, _} ->
        {:ok,
         %{
           required_turnover: Decimal.new("0"),
           completed_turnover: Decimal.new("0"),
           remaining_turnover: Decimal.new("0"),
           is_completed: true,
           last_updated: nil
         }}
    end
  end

  @doc """
  验证用户是否满足提现条件
  """
  def validate_withdrawal_eligibility(user_id, withdrawal_amount) do
    with {:ok, turnover_info} <- get_user_turnover_info(user_id) do
      if turnover_info.is_completed do
        # 检查余额是否足够
        identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
        total_balance = Cypridina.Ledger.Account.get_balance_by_identifier(identifier)

        if Decimal.compare(total_balance, withdrawal_amount) != :lt do
          {:ok,
           %{
             eligible: true,
             current_balance: total_balance,
             withdrawable_amount: withdrawal_amount,
             required_turnover: turnover_info.required_turnover,
             completed_turnover: turnover_info.completed_turnover
           }}
        else
          {:error, "余额不足"}
        end
      else
        {:error, "流水要求未满足，还需完成 #{turnover_info.remaining_turnover} 的有效投注"}
      end
    end
  end

  # ==================== 用户余额相关函数 ====================

  @doc """
  获取或创建用户流水记录（用于存储余额兼容数据）
  """
  def get_or_create_user_balance(user_id) do
    get_or_create_user_turnover(user_id)
  end

  @doc """
  获取用户余额信息（客户端兼容格式）
  """
  def get_user_balance_info(user_id) do
    with {:ok, turnover} <- get_or_create_user_turnover(user_id) do
      # 从账本系统获取真实余额作为money字段
      real_points = Cypridina.Accounts.get_user_points(user_id)
      
      # 获取VIP信息
      vip_level = case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
        {:ok, vip_info} -> vip_info.vip_level
        _ -> 0
      end

      {:ok, %{
        money: real_points,  # 主要余额从账本系统获取
        bonusmoney: turnover.bonusmoney,
        bonuscashmoney: turnover.bonuscashmoney,
        winningmoney: turnover.winningmoney,
        vip: vip_level,  # VIP等级从VIP系统获取
        # 添加真实积分用于比较
        points: real_points
      }}
    end
  end

  @doc """
  更新用户余额（只更新存储的奖金字段）
  """
  def update_user_balance(user_id, balance_changes) do
    with {:ok, turnover} <- get_or_create_user_turnover(user_id) do
      Teen.UserSystem.UserTurnover.update_balance(turnover, balance_changes)
    end
  end

  @doc """
  获取用户的Hall协议响应数据
  """
  def get_hall_user_money_data(user_id) do
    with {:ok, balance_info} <- get_user_balance_info(user_id) do
      {:ok, %{
        user: %{
          money: balance_info.money,
          bonusmoney: balance_info.bonusmoney,
          bonuscashmoney: balance_info.bonuscashmoney,
          winningmoney: balance_info.winningmoney,
          vip: balance_info.vip
        }
      }}
    end
  end
end
