defmodule Teen.Protocol.TemplateProtocol do
  @moduledoc """
  协议处理器模板
  
  使用此模板创建新的协议处理器：
  1. 复制此文件并重命名
  2. 修改模块名和协议ID
  3. 实现具体的子协议处理逻辑
  """
  
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  require Logger
  alias Teen.Protocol.ProtocolUtils
  
  # 主协议ID - 修改为实际的协议ID
  @protocol_id 999
  
  # 子协议常量定义
  @cs_example_request_p 0
  @sc_example_response_p 1
  
  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{
      name: "Template",
      description: "协议模板"
    }
  end
  
  @impl true
  def supported_sub_protocols do
    [
      {@cs_example_request_p, "示例请求"},
      {@sc_example_response_p, "示例响应"}
    ]
  end
  
  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id
    
    # 记录协议处理开始
    ProtocolUtils.log_protocol(:debug, "TEMPLATE", sub_protocol, user_id, "开始处理")
    
    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "TEMPLATE", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end
  
  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_example_request_p ->
        # 验证示例请求的参数
        ProtocolUtils.validate_params(data, [
          {:required, [:param1, :param2]},
          {:number_range, :param1, 1, 100, "参数1"}
        ])
        
      _ ->
        # 其他子协议不需要验证
        :ok
    end
  end
  
  # ============================================================================
  # 私有函数
  # ============================================================================
  
  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    case sub_protocol do
      @cs_example_request_p ->
        handle_example_request(data, context)
        
      _ ->
        {:error, :unknown_sub_protocol}
    end
  end
  
  # 处理示例请求
  defp handle_example_request(data, context) do
    user_id = context.user_id
    
    try do
      # 业务逻辑处理
      param1 = Map.get(data, "param1")
      param2 = Map.get(data, "param2")
      
      # 构建响应数据
      response_data = ProtocolUtils.success_response(%{
        "result" => param1 + param2,
        "message" => "处理成功"
      })
      
      # 返回响应
      {:ok, @sc_example_response_p, response_data}
      
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "TEMPLATE", @cs_example_request_p, user_id, 
          "处理异常: #{inspect(error)}")
        
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_example_response_p, error_data}
    end
  end
end