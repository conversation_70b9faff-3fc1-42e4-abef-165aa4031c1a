defmodule Teen.Protocol.BaseInfoProtocol do
  @moduledoc """
  基本信息协议处理器

  处理用户基本信息相关的协议，包括：
  - 获取用户信息
  - 更新用户资料
  - 上传头像
  - 获取系统配置
  - 用户设置管理
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Cypridina.Accounts.{User, UserProfile}
  alias Teen.PaymentSystem

  # 主协议ID
  @protocol_id 6

  # 子协议常量定义
  @cs_get_user_info_p 0
  @sc_get_user_info_p 1
  @cs_update_user_info_p 2
  @sc_update_user_info_p 3
  @cs_upload_avatar_p 4
  @sc_upload_avatar_p 5
  @cs_get_system_config_p 6
  @sc_get_system_config_p 7
  @cs_update_nickname_p 8
  @sc_update_nickname_p 9
  @cs_get_user_settings_p 10
  @sc_get_user_settings_p 11
  @cs_update_user_settings_p 12
  @sc_update_user_settings_p 13
  @cs_get_server_time_p 14
  @sc_get_server_time_p 15
  @cs_check_username_available_p 16
  @sc_check_username_available_p 17

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "BaseInfo",
      description: "基本信息协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_get_user_info_p, "获取用户信息"},
      {@cs_update_user_info_p, "更新用户信息"},
      {@cs_upload_avatar_p, "上传头像"},
      {@cs_get_system_config_p, "获取系统配置"},
      {@cs_update_nickname_p, "更新昵称"},
      {@cs_get_user_settings_p, "获取用户设置"},
      {@cs_update_user_settings_p, "更新用户设置"},
      {@cs_get_server_time_p, "获取服务器时间"},
      {@cs_check_username_available_p, "检查用户名可用性"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "BASE_INFO", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_update_user_info_p ->
        # 只验证必需字段存在，其他字段为可选
        :ok

      @cs_upload_avatar_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:avatar_data]},
          {:string_length, :avatar_data, 1, 500000, "头像数据"}
        ])

      @cs_update_nickname_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:nickname]},
          {:string_length, :nickname, 1, 20, "昵称"}
        ])

      @cs_update_user_settings_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:settings]}
        ])

      @cs_check_username_available_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:username]},
          {:string_length, :username, 3, 20, "用户名"}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      @cs_get_user_info_p ->
        handle_get_user_info(user_id)

      @cs_update_user_info_p ->
        handle_update_user_info(user_id, data)

      @cs_upload_avatar_p ->
        handle_upload_avatar(user_id, data)

      @cs_get_system_config_p ->
        handle_get_system_config()

      @cs_update_nickname_p ->
        handle_update_nickname(user_id, data)

      @cs_get_user_settings_p ->
        handle_get_user_settings(user_id)

      @cs_update_user_settings_p ->
        handle_update_user_settings(user_id, data)

      @cs_get_server_time_p ->
        handle_get_server_time()

      @cs_check_username_available_p ->
        handle_check_username_available(data)

      _ ->
        ProtocolUtils.log_protocol(:warning, "BASE_INFO", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理获取用户信息
  defp handle_get_user_info(user_id) do
    try do
      case User.get_user_with_profile(user_id) do
        {:ok, user} ->
          response_data = ProtocolUtils.success_response(%{
            "userId" => user.id,
            "username" => user.username,
            "nickname" => user.profile.nickname || user.username,
            "avatar" => user.profile.avatar || "",
            "gender" => user.profile.gender || 0,
            "birthday" => format_date(user.profile.birthday),
            "location" => user.profile.location || "",
            "signature" => user.profile.signature || "",
            "phone" => mask_phone(user.phone),
            "email" => mask_email(user.email),
            "points" => user.points || 0,
            "vipLevel" => user.vip_level || 0,
            "registerTime" => format_datetime(user.inserted_at),
            "lastLoginTime" => format_datetime(user.last_login_at)
          })

          ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_get_user_info_p, user_id, "获取用户信息成功")
          {:ok, @sc_get_user_info_p, response_data}

        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "用户不存在")
          {:ok, @sc_get_user_info_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_get_user_info_p, user_id,
            "获取用户信息失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "获取用户信息失败")
          {:ok, @sc_get_user_info_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_get_user_info_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_user_info_p, error_data}
    end
  end

  # 处理更新用户信息
  defp handle_update_user_info(user_id, data) do
    try do
      update_params = build_update_params(data)

      case UserProfile.update_profile(user_id, update_params) do
        {:ok, profile} ->
          response_data = ProtocolUtils.success_response(%{
            "msg" => "用户信息更新成功",
            "profile" => format_profile(profile)
          })

          ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_update_user_info_p, user_id, "更新用户信息成功")
          {:ok, @sc_update_user_info_p, response_data}

        {:error, changeset} ->
          errors = extract_changeset_errors(changeset)
          error_data = ProtocolUtils.error_response(:invalid_params, "更新失败: #{errors}")
          {:ok, @sc_update_user_info_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_update_user_info_p, user_id,
            "更新用户信息失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "更新用户信息失败")
          {:ok, @sc_update_user_info_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_update_user_info_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_update_user_info_p, error_data}
    end
  end

  # 处理上传头像
  defp handle_upload_avatar(user_id, data) do
    avatar_data = Map.get(data, "avatar_data")

    try do
      case UserProfile.upload_avatar(user_id, avatar_data) do
        {:ok, avatar_url} ->
          response_data = ProtocolUtils.success_response(%{
            "avatar" => avatar_url,
            "msg" => "头像上传成功"
          })

          ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_upload_avatar_p, user_id, "头像上传成功")
          {:ok, @sc_upload_avatar_p, response_data}

        {:error, :invalid_format} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "头像格式不支持")
          {:ok, @sc_upload_avatar_p, error_data}

        {:error, :file_too_large} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "头像文件过大")
          {:ok, @sc_upload_avatar_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_upload_avatar_p, user_id,
            "头像上传失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "头像上传失败")
          {:ok, @sc_upload_avatar_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_upload_avatar_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_upload_avatar_p, error_data}
    end
  end

  # 处理获取系统配置
  defp handle_get_system_config() do
      # 获取基础系统配置
      base_config = %{
        "serverName" => "Cypridina Server",
        "version" => "1.0.0",
        "timezone" => "UTC+8",
        "supportedLanguages" => ["zh", "en"],
        "defaultLanguage" => "zh",
        "maxUploadSize" => 5 * 1024 * 1024,  # 5MB
        "supportedImageFormats" => ["jpg", "jpeg", "png", "gif"],
        "gameTypes" => get_supported_game_types(),
        "vipLevels" => get_vip_level_config(),
        "announcementUrl" => "/api/announcements"
      }

      # 获取支付系统配置（包含充值配置）
      payment_config = case PaymentSystem.get_system_config() do
        {:ok, config} -> config
        _ -> %{}
      end

      # 合并配置
      config = Map.merge(base_config, payment_config)

      response_data = ProtocolUtils.success_response(config)
      {:ok, @sc_get_system_config_p, response_data}
  end

  # 处理更新昵称
  defp handle_update_nickname(user_id, data) do
    nickname = Map.get(data, "nickname")

    case UserProfile.update_nickname(user_id, nickname) do
      {:ok, _profile} ->
        response_data = ProtocolUtils.success_response(%{
          "nickname" => nickname,
          "msg" => "昵称更新成功"
        })

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_update_nickname_p, user_id, "昵称更新成功")
        {:ok, @sc_update_nickname_p, response_data}

      {:error, :nickname_taken} ->
        error_data = ProtocolUtils.error_response(:already_exists, "昵称已被使用")
        {:ok, @sc_update_nickname_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_update_nickname_p, user_id,
          "昵称更新失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "昵称更新失败")
        {:ok, @sc_update_nickname_p, error_data}
    end
  end

  # 处理获取用户设置
  defp handle_get_user_settings(user_id) do
    case UserProfile.get_user_settings(user_id) do
      {:ok, settings} ->
        response_data = ProtocolUtils.success_response(%{
          "settings" => settings
        })
        {:ok, @sc_get_user_settings_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_get_user_settings_p, user_id,
          "获取用户设置失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "获取用户设置失败")
        {:ok, @sc_get_user_settings_p, error_data}
    end
  end

  # 处理更新用户设置
  defp handle_update_user_settings(user_id, data) do
    settings = Map.get(data, "settings")

    case UserProfile.update_user_settings(user_id, settings) do
      {:ok, _} ->
        response_data = ProtocolUtils.success_response(%{
          "msg" => "设置更新成功"
        })

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_update_user_settings_p, user_id, "设置更新成功")
        {:ok, @sc_update_user_settings_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_update_user_settings_p, user_id,
          "设置更新失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "设置更新失败")
        {:ok, @sc_update_user_settings_p, error_data}
    end
  end

  # 处理获取服务器时间
  defp handle_get_server_time() do
    current_time = DateTime.utc_now()

    response_data = ProtocolUtils.success_response(%{
      "serverTime" => DateTime.to_unix(current_time, :millisecond),
      "timezone" => "UTC",
      "formatted" => DateTime.to_iso8601(current_time)
    })

    {:ok, @sc_get_server_time_p, response_data}
  end

  # 处理检查用户名可用性
  defp handle_check_username_available(data) do
    username = Map.get(data, "username")

    case User.check_username_available(username) do
      {:ok, true} ->
        response_data = ProtocolUtils.success_response(%{
          "available" => true,
          "msg" => "用户名可用"
        })
        {:ok, @sc_check_username_available_p, response_data}

      {:ok, false} ->
        response_data = ProtocolUtils.success_response(%{
          "available" => false,
          "msg" => "用户名已被使用"
        })
        {:ok, @sc_check_username_available_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_check_username_available_p, "anonymous",
          "检查用户名可用性失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "检查失败")
        {:ok, @sc_check_username_available_p, error_data}
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp build_update_params(data) do
    %{}
    |> maybe_put("nickname", Map.get(data, "nickname"))
    |> maybe_put("gender", Map.get(data, "gender"))
    |> maybe_put("birthday", parse_date(Map.get(data, "birthday")))
    |> maybe_put("location", Map.get(data, "location"))
    |> maybe_put("signature", Map.get(data, "signature"))
  end

  defp maybe_put(map, _key, nil), do: map
  defp maybe_put(map, _key, ""), do: map
  defp maybe_put(map, key, value), do: Map.put(map, key, value)

  defp format_profile(profile) do
    %{
      "nickname" => profile.nickname,
      "avatar" => profile.avatar,
      "gender" => profile.gender,
      "birthday" => format_date(profile.birthday),
      "location" => profile.location,
      "signature" => profile.signature
    }
  end

  defp format_date(nil), do: nil
  defp format_date(date), do: Date.to_string(date)

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)

  defp parse_date(nil), do: nil
  defp parse_date(""), do: nil
  defp parse_date(date_string) do
    case Date.from_iso8601(date_string) do
      {:ok, date} -> date
      _ -> nil
    end
  end

  defp mask_phone(nil), do: nil
  defp mask_phone(phone) do
    if String.length(phone) > 7 do
      String.slice(phone, 0, 3) <> "****" <> String.slice(phone, -4, 4)
    else
      "****"
    end
  end

  defp mask_email(nil), do: nil
  defp mask_email(email) do
    [local, domain] = String.split(email, "@", parts: 2)
    masked_local = String.slice(local, 0, 2) <> "***"
    "#{masked_local}@#{domain}"
  end

  defp extract_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  defp get_supported_game_types() do
    [
      %{"id" => "teen_patti", "name" => "Teen Patti", "minPlayers" => 2, "maxPlayers" => 6},
      %{"id" => "andar_bahar", "name" => "Andar Bahar", "minPlayers" => 1, "maxPlayers" => 8},
      %{"id" => "dragon_tiger", "name" => "Dragon Tiger", "minPlayers" => 1, "maxPlayers" => 8}
    ]
  end

  defp get_vip_level_config() do
    [
      %{"level" => 0, "name" => "普通用户", "benefits" => []},
      %{"level" => 1, "name" => "VIP1", "benefits" => ["每日登录奖励加成"]},
      %{"level" => 2, "name" => "VIP2", "benefits" => ["每日登录奖励加成", "专属客服"]},
      %{"level" => 3, "name" => "VIP3", "benefits" => ["每日登录奖励加成", "专属客服", "提现加速"]}
    ]
  end
end
