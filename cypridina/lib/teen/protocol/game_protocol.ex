defmodule Teen.Protocol.GameProtocol do
  @moduledoc """
  游戏协议处理器
  
  处理游戏相关的协议，包括：
  - 加入游戏
  - 游戏操作
  - 离开游戏
  - 游戏状态查询
  """
  
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Teen.GameManagement
  
  # 主协议ID
  @protocol_id 4
  
  # 子协议常量定义 - 保持与websocket_handler一致
  @cs_join_game_p 0
  @sc_join_game_p 1
  @cs_leave_game_p 2
  @sc_leave_game_p 3
  @cs_room_set_player_state_p 3
  @sc_room_set_player_state_p 4
  @cs_game_action_p 4
  @sc_game_action_p 5
  @cs_room_chat_p 6
  @sc_room_chat_p 7
  @cs_game_state_p 6
  @sc_game_state_p 7
  @cs_quick_start_p 8
  @sc_quick_start_p 9
  @cs_room_zanli_comback_p 10
  @sc_room_zanli_comback_success_p 11
  @cs_game_player_num_p 34
  @sc_game_player_num_p 35
  @cs_quit_p 40
  @cs_huanzhuo_p 43
  @sc_huanzhuo_p 44
  @cs_mode1_enter_p 45
  @cs_mode1_enter_pipei_p 46
  @cs_room_player_ready 1102
  
  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{
      name: "Game",
      description: "游戏协议处理器"
    }
  end
  
  @impl true
  def supported_sub_protocols do
    [
      {@cs_join_game_p, "加入游戏"},
      {@cs_leave_game_p, "离开游戏"},
      {@cs_room_set_player_state_p, "设置玩家状态"},
      {@cs_game_action_p, "游戏操作"},
      {@cs_room_chat_p, "房间聊天"},
      {@cs_quick_start_p, "快速开始"},
      {@cs_room_zanli_comback_p, "暂离回来"},
      {@cs_game_player_num_p, "请求游戏玩家人数"},
      {@cs_quit_p, "退出游戏"},
      {@cs_huanzhuo_p, "换桌"},
      {@cs_mode1_enter_p, "进入模式1"},
      {@cs_mode1_enter_pipei_p, "进入模式1匹配"},
      {@cs_room_player_ready, "玩家准备"}
    ]
  end
  
  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id
    
    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "GAME", sub_protocol, user_id, "开始处理")
    
    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "GAME", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end
  
  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_join_game_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:game_type, :room_id]}
        ])
        
      @cs_game_action_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:action_type, :action_data]}
        ])
        
      @cs_quick_start_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:game_type]},
          {:number_range, :bet_level, 1, 10, "下注等级"}
        ])
        
      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end
  
  # ============================================================================
  # 私有函数
  # ============================================================================
  
  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id
    
    case sub_protocol do
      0 -> # @cs_join_game_p
        handle_join_game(user_id, data)
        
      2 -> # @cs_leave_game_p  
        handle_leave_game(user_id, data)
        
      3 -> # @cs_room_set_player_state_p
        handle_room_set_player_state(user_id, data)
        
      4 -> # @cs_game_action_p
        handle_game_action(user_id, data)
        
      6 -> # @cs_room_chat_p
        handle_room_chat(user_id, data, context)
        
      8 -> # @cs_quick_start_p
        handle_quick_start(user_id, data)
        
      10 -> # @cs_room_zanli_comback_p
        handle_room_zanli_comback(user_id, data)
        
      34 -> # @cs_game_player_num_p
        handle_game_player_num(user_id, data)
        
      40 -> # @cs_quit_p
        handle_quit_game(user_id, data, context)
        
      43 -> # @cs_huanzhuo_p
        handle_huanzhuo(user_id, data, context)
        
      45 -> # @cs_mode1_enter_p
        handle_mode1_enter(user_id, data)
        
      46 -> # @cs_mode1_enter_pipei_p
        handle_mode1_enter_pipei(user_id, data)
        
      1102 -> # @cs_room_player_ready
        handle_room_player_ready(user_id, data)
        
      _ ->
        ProtocolUtils.log_protocol(:warning, "GAME", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end
  
  # 处理加入游戏
  defp handle_join_game(user_id, data) do
    game_type = Map.get(data, "game_type")
    room_id = Map.get(data, "room_id")
    
    try do
      case GameManagement.join_game(user_id, game_type, room_id) do
        {:ok, game_info} ->
          response_data = ProtocolUtils.success_response(%{
            "game_id" => game_info.game_id,
            "room_id" => game_info.room_id,
            "game_type" => game_info.game_type,
            "players" => format_players(game_info.players),
            "game_state" => game_info.state
          })
          
          ProtocolUtils.log_protocol(:info, "GAME", @cs_join_game_p, user_id, 
            "成功加入游戏: #{game_type}/#{room_id}")
          {:ok, @sc_join_game_p, response_data}
          
        {:error, :room_full} ->
          error_data = ProtocolUtils.error_response(:forbidden, "房间已满")
          {:ok, @sc_join_game_p, error_data}
          
        {:error, :insufficient_balance} ->
          error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足")
          {:ok, @sc_join_game_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "GAME", @cs_join_game_p, user_id,
            "加入游戏失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "加入游戏失败")
          {:ok, @sc_join_game_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "GAME", @cs_join_game_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_join_game_p, error_data}
    end
  end
  
  # 处理离开游戏
  defp handle_leave_game(user_id, _data) do
    case GameManagement.leave_game(user_id) do
      {:ok, _} ->
        ProtocolUtils.log_protocol(:info, "GAME", @cs_leave_game_p, user_id, "成功离开游戏")
        response_data = ProtocolUtils.success_response(%{
          "msg" => "已离开游戏"
        })
        {:ok, @sc_leave_game_p, response_data}
        
      {:error, :not_in_game} ->
        error_data = ProtocolUtils.error_response(:not_found, "您不在游戏中")
        {:ok, @sc_leave_game_p, error_data}
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "GAME", @cs_leave_game_p, user_id,
          "离开游戏失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "离开游戏失败")
        {:ok, @sc_leave_game_p, error_data}
    end
  end
  
  # 处理游戏操作
  defp handle_game_action(user_id, data) do
    action_type = Map.get(data, "action_type")
    action_data = Map.get(data, "action_data")
    
    case GameManagement.process_game_action(user_id, action_type, action_data) do
      {:ok, result} ->
        response_data = ProtocolUtils.success_response(%{
          "action_result" => result
        })
        {:ok, @sc_game_action_p, response_data}
        
      {:error, :invalid_action} ->
        error_data = ProtocolUtils.error_response(:invalid_params, "无效的游戏操作")
        {:ok, @sc_game_action_p, error_data}
        
      {:error, :not_your_turn} ->
        error_data = ProtocolUtils.error_response(:forbidden, "还没轮到您操作")
        {:ok, @sc_game_action_p, error_data}
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "GAME", @cs_game_action_p, user_id,
          "游戏操作失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "操作失败")
        {:ok, @sc_game_action_p, error_data}
    end
  end
  
  # 处理游戏状态查询
  defp handle_game_state(user_id) do
    case GameManagement.get_user_game_state(user_id) do
      {:ok, game_state} ->
        response_data = ProtocolUtils.success_response(%{
          "in_game" => true,
          "game_type" => game_state.game_type,
          "room_id" => game_state.room_id,
          "game_state" => game_state.state,
          "your_turn" => game_state.current_turn == user_id
        })
        {:ok, @sc_game_state_p, response_data}
        
      {:error, :not_in_game} ->
        response_data = ProtocolUtils.success_response(%{
          "in_game" => false
        })
        {:ok, @sc_game_state_p, response_data}
    end
  end
  
  # 处理快速开始
  defp handle_quick_start(user_id, data) do
    game_type = Map.get(data, "game_type")
    bet_level = Map.get(data, "bet_level", 1)
    
    case GameManagement.quick_start(user_id, game_type, bet_level) do
      {:ok, game_info} ->
        response_data = ProtocolUtils.success_response(%{
          "game_id" => game_info.game_id,
          "room_id" => game_info.room_id,
          "game_type" => game_info.game_type,
          "players" => format_players(game_info.players),
          "msg" => "快速匹配成功"
        })
        
        ProtocolUtils.log_protocol(:info, "GAME", @cs_quick_start_p, user_id,
          "快速开始成功: #{game_type}")
        {:ok, @sc_quick_start_p, response_data}
        
      {:error, :no_available_room} ->
        error_data = ProtocolUtils.error_response(:not_found, "暂无可用房间，请稍后再试")
        {:ok, @sc_quick_start_p, error_data}
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "GAME", @cs_quick_start_p, user_id,
          "快速开始失败: #{inspect(reason)}")
        error_data = ProtocolUtils.error_response(:internal_error, "匹配失败")
        {:ok, @sc_quick_start_p, error_data}
    end
  end
  
  # 格式化玩家列表
  defp format_players(players) do
    Enum.map(players, fn player ->
      %{
        "user_id" => player.user_id,
        "nickname" => player.nickname,
        "avatar" => player.avatar,
        "seat" => player.seat,
        "chips" => player.chips,
        "status" => player.status
      }
    end)
  end
  
  # 处理请求游戏玩家人数
  defp handle_game_player_num(_user_id, _data) do
    # 模拟各个游戏的玩家人数数据
    game_player_nums = %{
      # Teen Patti
      "1" => %{
        "game_id" => 1,
        "game_name" => "Teen Patti",
        "total_players" => :rand.uniform(500) + 100,
        "online_players" => :rand.uniform(200) + 50,
        "rooms_count" => :rand.uniform(20) + 5
      },
      # Rummy
      "2" => %{
        "game_id" => 2,
        "game_name" => "Rummy",
        "total_players" => :rand.uniform(300) + 80,
        "online_players" => :rand.uniform(150) + 30,
        "rooms_count" => :rand.uniform(15) + 3
      },
      # Andar Bahar
      "3" => %{
        "game_id" => 3,
        "game_name" => "Andar Bahar",
        "total_players" => :rand.uniform(400) + 120,
        "online_players" => :rand.uniform(180) + 40,
        "rooms_count" => :rand.uniform(18) + 4
      },
      # Dragon Tiger
      "4" => %{
        "game_id" => 4,
        "game_name" => "Dragon Tiger",
        "total_players" => :rand.uniform(250) + 60,
        "online_players" => :rand.uniform(120) + 25,
        "rooms_count" => :rand.uniform(12) + 2
      }
    }
    
    response_data = ProtocolUtils.success_response(%{
      "game_player_nums" => game_player_nums,
      "total_games" => map_size(game_player_nums),
      "server_time" => System.system_time(:millisecond),
      "message" => "获取游戏玩家人数成功"
    })
    
    ProtocolUtils.log_protocol(:info, "GAME", @cs_game_player_num_p, "", "请求游戏玩家人数")
    {:ok, @sc_game_player_num_p, response_data}
  end
  
  # 处理退出游戏
  defp handle_quit_game(user_id, _data, context) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_quit_p, user_id, "玩家请求退出")
    
    # 如果玩家在房间中，从房间中移除
    if context[:room_id] do
      ProtocolUtils.log_protocol(:info, "GAME", @cs_quit_p, user_id, "玩家从房间 #{context.room_id} 退出")
      # TODO: 从房间管理器中移除玩家
    end
    
    # 根据原始实现，退出请求通常不需要特定的响应
    {:noreply}
  end
  
  # 处理换桌
  defp handle_huanzhuo(user_id, _data, context) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_huanzhuo_p, user_id, "玩家请求换桌")
    
    # 模拟换桌成功
    new_room_id = "room_#{:rand.uniform(1000)}"
    
    response_data = ProtocolUtils.success_response(%{
      "room_id" => new_room_id,
      "msg" => "换桌成功"
    })
    
    {:ok, @sc_huanzhuo_p, response_data}
  end
  
  # 处理进入模式1
  defp handle_mode1_enter(user_id, data) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_mode1_enter_p, user_id, "玩家请求进入模式1")
    
    game_type = Map.get(data, "game_type", 1)
    room_type = Map.get(data, "room_type", 1)
    
    # 模拟进入房间
    room_id = "room_#{game_type}_#{room_type}_#{:rand.uniform(100)}"
    
    response_data = ProtocolUtils.success_response(%{
      "room_id" => room_id,
      "game_type" => game_type,
      "room_type" => room_type,
      "players" => [],
      "msg" => "进入成功"
    })
    
    {:ok, @cs_mode1_enter_p + 1, response_data}
  end
  
  # 处理进入模式1匹配
  defp handle_mode1_enter_pipei(user_id, data) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_mode1_enter_pipei_p, user_id, "玩家请求进入模式1匹配")
    
    game_type = Map.get(data, "game_type", 1)
    
    # 模拟匹配房间
    room_id = "room_match_#{game_type}_#{:rand.uniform(50)}"
    
    response_data = ProtocolUtils.success_response(%{
      "room_id" => room_id,
      "game_type" => game_type,
      "match_time" => :rand.uniform(5) + 1,
      "players" => [],
      "msg" => "匹配成功"
    })
    
    {:ok, @cs_mode1_enter_pipei_p + 1, response_data}
  end
  
  # 处理玩家准备
  defp handle_room_player_ready(user_id, _data) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_room_player_ready, user_id, "玩家准备游戏")
    
    response_data = ProtocolUtils.success_response(%{
      "msg" => "准备成功"
    })
    
    {:ok, @cs_room_player_ready + 1, response_data}
  end
  
  # 处理房间聊天
  defp handle_room_chat(user_id, data, context) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_room_chat_p, user_id, "玩家聊天")
    
    chat_msg = Map.get(data, "message", "")
    chat_type = Map.get(data, "type", 0)
    
    response_data = ProtocolUtils.success_response(%{
      "user_id" => user_id,
      "message" => chat_msg,
      "type" => chat_type,
      "timestamp" => System.system_time(:millisecond)
    })
    
    {:ok, @sc_room_chat_p, response_data}
  end
  
  # 处理设置玩家状态
  defp handle_room_set_player_state(user_id, data) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_room_set_player_state_p, user_id, "设置玩家状态")
    
    state = Map.get(data, "state", 0)
    
    response_data = ProtocolUtils.success_response(%{
      "user_id" => user_id,
      "state" => state,
      "msg" => "状态设置成功"
    })
    
    {:ok, @sc_room_set_player_state_p, response_data}
  end
  
  # 处理暂离回来
  defp handle_room_zanli_comback(user_id, _data) do
    ProtocolUtils.log_protocol(:info, "GAME", @cs_room_zanli_comback_p, user_id, "玩家暂离回来")
    
    response_data = ProtocolUtils.success_response(%{
      "msg" => "欢迎回来"
    })
    
    {:ok, @sc_room_zanli_comback_success_p, response_data}
  end
end