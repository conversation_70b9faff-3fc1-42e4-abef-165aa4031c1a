defmodule Teen.Protocol.MailProtocol do
  @moduledoc """
  邮件系统协议处理器
  
  处理用户邮件相关的协议，包括：
  - 获取邮件列表
  - 读取邮件详情
  - 发送邮件
  - 删除邮件
  - 标记邮件状态
  - 邮件奖励领取
  """
  
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Teen.MailSystem.{Mail, MailService}
  
  # 主协议ID
  @protocol_id 14
  
  # 子协议常量定义
  @cs_get_mail_list_p 0
  @sc_get_mail_list_p 1
  @cs_read_mail_p 2
  @sc_read_mail_p 3
  @cs_delete_mail_p 4
  @sc_delete_mail_p 5
  @cs_claim_mail_reward_p 6
  @sc_claim_mail_reward_p 7
  @cs_mark_mail_read_p 8
  @sc_mark_mail_read_p 9
  @cs_mark_all_read_p 10
  @sc_mark_all_read_p 11
  @cs_get_unread_count_p 12
  @sc_get_unread_count_p 13
  @cs_send_mail_p 14
  @sc_send_mail_p 15
  @cs_delete_all_read_p 16
  @sc_delete_all_read_p 17
  
  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{
      name: "Mail",
      description: "邮件系统协议处理器"
    }
  end
  
  @impl true
  def supported_sub_protocols do
    [
      {0, "请求邮件内容"},                    # websocket_handler迁移
      {@cs_read_mail_p, "读取邮件详情"},
      {2, "设置邮件已读"},                    # websocket_handler迁移  
      {3, "删除邮件"},                        # websocket_handler迁移
      {@cs_delete_mail_p, "删除邮件"},
      {5, "请求邮件列表"},                    # websocket_handler迁移
      {@cs_claim_mail_reward_p, "领取邮件奖励"},
      {7, "请求新邮件数量"},                  # websocket_handler迁移
      {@cs_mark_mail_read_p, "标记邮件已读"},
      {@cs_mark_all_read_p, "标记全部已读"},
      {@cs_get_unread_count_p, "获取未读邮件数"},
      {@cs_send_mail_p, "发送邮件"},
      {@cs_delete_all_read_p, "删除所有已读邮件"}
    ]
  end
  
  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id
    
    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "MAIL", sub_protocol, user_id, "开始处理")
    
    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "MAIL", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end
  
  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_get_mail_list_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])
        
      @cs_read_mail_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:mail_id]}
        ])
        
      @cs_delete_mail_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:mail_id]}
        ])
        
      @cs_claim_mail_reward_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:mail_id]}
        ])
        
      @cs_mark_mail_read_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:mail_id]}
        ])
        
      @cs_send_mail_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:to_user_id, :title, :content]},
          {:string_length, :title, 1, 100, "邮件标题"},
          {:string_length, :content, 1, 2000, "邮件内容"}
        ])
        
      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end
  
  # ============================================================================
  # 私有函数
  # ============================================================================
  
  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id
    
    case sub_protocol do
      0 -> # 请求邮件内容 (从websocket_handler迁移)
        handle_request_mail_info_legacy(data, context)
        
      2 -> # 设置邮件已读 (从websocket_handler迁移)  
        handle_mail_set_read_legacy(data, context)
        
      3 -> # 删除邮件 (从websocket_handler迁移)
        handle_delete_mail_legacy(data, context)
        
      5 -> # 请求邮件列表 (从websocket_handler迁移)
        handle_request_mail_list_legacy(data, context)
        
      7 -> # 请求新邮件数量 (从websocket_handler迁移)
        handle_request_new_mail_count_legacy(data, context)
        
      @cs_get_mail_list_p ->
        handle_get_mail_list(user_id, data)
        
      @cs_read_mail_p ->
        handle_read_mail(user_id, data)
        
      @cs_delete_mail_p ->
        handle_delete_mail(user_id, data)
        
      @cs_claim_mail_reward_p ->
        handle_claim_mail_reward(user_id, data)
        
      @cs_mark_mail_read_p ->
        handle_mark_mail_read(user_id, data)
        
      @cs_mark_all_read_p ->
        handle_mark_all_read(user_id)
        
      @cs_get_unread_count_p ->
        handle_get_unread_count(user_id)
        
      @cs_send_mail_p ->
        handle_send_mail(user_id, data)
        
      @cs_delete_all_read_p ->
        handle_delete_all_read(user_id)
        
      _ ->
        ProtocolUtils.log_protocol(:warning, "MAIL", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end
  
  # 处理获取邮件列表
  defp handle_get_mail_list(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    mail_type = Map.get(data, "type", "all")  # all, unread, system, user
    
    try do
      case MailService.get_user_mail_list(user_id, mail_type, page, page_size) do
        {:ok, {mails, total_count}} ->
          formatted_mails = Enum.map(mails, &format_mail_item/1)
          
          response_data = ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(formatted_mails, page, page_size, total_count)
          )
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_get_mail_list_p, user_id,
            "获取邮件列表成功，共#{total_count}条")
          {:ok, @sc_get_mail_list_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_get_mail_list_p, user_id,
            "获取邮件列表失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "获取邮件列表失败")
          {:ok, @sc_get_mail_list_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_get_mail_list_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_mail_list_p, error_data}
    end
  end
  
  # 处理读取邮件详情
  defp handle_read_mail(user_id, data) do
    mail_id = Map.get(data, "mail_id")
    
    try do
      case MailService.read_mail(user_id, mail_id) do
        {:ok, mail} ->
          response_data = ProtocolUtils.success_response(%{
            "mail" => format_mail_detail(mail)
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_read_mail_p, user_id,
            "读取邮件成功: #{mail_id}")
          {:ok, @sc_read_mail_p, response_data}
          
        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "邮件不存在")
          {:ok, @sc_read_mail_p, error_data}
          
        {:error, :access_denied} ->
          error_data = ProtocolUtils.error_response(:forbidden, "无权访问此邮件")
          {:ok, @sc_read_mail_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_read_mail_p, user_id,
            "读取邮件失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "读取邮件失败")
          {:ok, @sc_read_mail_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_read_mail_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_read_mail_p, error_data}
    end
  end
  
  # 处理删除邮件
  defp handle_delete_mail(user_id, data) do
    mail_id = Map.get(data, "mail_id")
    
    try do
      case MailService.delete_mail(user_id, mail_id) do
        {:ok, _} ->
          response_data = ProtocolUtils.success_response(%{
            "deleted" => true,
            "msg" => "邮件删除成功"
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_delete_mail_p, user_id,
            "删除邮件成功: #{mail_id}")
          {:ok, @sc_delete_mail_p, response_data}
          
        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "邮件不存在")
          {:ok, @sc_delete_mail_p, error_data}
          
        {:error, :access_denied} ->
          error_data = ProtocolUtils.error_response(:forbidden, "无权删除此邮件")
          {:ok, @sc_delete_mail_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_delete_mail_p, user_id,
            "删除邮件失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "删除邮件失败")
          {:ok, @sc_delete_mail_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_delete_mail_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_delete_mail_p, error_data}
    end
  end
  
  # 处理领取邮件奖励
  defp handle_claim_mail_reward(user_id, data) do
    mail_id = Map.get(data, "mail_id")
    
    try do
      case MailService.claim_mail_reward(user_id, mail_id) do
        {:ok, reward_info} ->
          response_data = ProtocolUtils.success_response(%{
            "claimed" => true,
            "rewards" => format_rewards(reward_info.rewards),
            "totalValue" => reward_info.total_value,
            "msg" => "奖励领取成功"
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_claim_mail_reward_p, user_id,
            "领取邮件奖励成功: #{mail_id}")
          {:ok, @sc_claim_mail_reward_p, response_data}
          
        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "邮件不存在")
          {:ok, @sc_claim_mail_reward_p, error_data}
          
        {:error, :no_reward} ->
          error_data = ProtocolUtils.error_response(:forbidden, "此邮件没有奖励")
          {:ok, @sc_claim_mail_reward_p, error_data}
          
        {:error, :already_claimed} ->
          error_data = ProtocolUtils.error_response(:forbidden, "奖励已领取")
          {:ok, @sc_claim_mail_reward_p, error_data}
          
        {:error, :expired} ->
          error_data = ProtocolUtils.error_response(:forbidden, "奖励已过期")
          {:ok, @sc_claim_mail_reward_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_claim_mail_reward_p, user_id,
            "领取邮件奖励失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "领取奖励失败")
          {:ok, @sc_claim_mail_reward_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_claim_mail_reward_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_claim_mail_reward_p, error_data}
    end
  end
  
  # 处理标记邮件已读
  defp handle_mark_mail_read(user_id, data) do
    mail_id = Map.get(data, "mail_id")
    
    try do
      case MailService.mark_mail_read(user_id, mail_id) do
        {:ok, _} ->
          response_data = ProtocolUtils.success_response(%{
            "marked" => true,
            "msg" => "邮件已标记为已读"
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_mark_mail_read_p, user_id,
            "标记邮件已读成功: #{mail_id}")
          {:ok, @sc_mark_mail_read_p, response_data}
          
        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "邮件不存在")
          {:ok, @sc_mark_mail_read_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_mark_mail_read_p, user_id,
            "标记邮件已读失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "标记失败")
          {:ok, @sc_mark_mail_read_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_mark_mail_read_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_mark_mail_read_p, error_data}
    end
  end
  
  # 处理标记全部已读
  defp handle_mark_all_read(user_id) do
    try do
      case MailService.mark_all_read(user_id) do
        {:ok, count} ->
          response_data = ProtocolUtils.success_response(%{
            "markedCount" => count,
            "msg" => "已标记#{count}封邮件为已读"
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_mark_all_read_p, user_id,
            "标记全部已读成功，共#{count}封")
          {:ok, @sc_mark_all_read_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_mark_all_read_p, user_id,
            "标记全部已读失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "标记失败")
          {:ok, @sc_mark_all_read_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_mark_all_read_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_mark_all_read_p, error_data}
    end
  end
  
  # 处理获取未读邮件数
  defp handle_get_unread_count(user_id) do
    try do
      case MailService.get_unread_count(user_id) do
        {:ok, count} ->
          response_data = ProtocolUtils.success_response(%{
            "unreadCount" => count
          })
          
          {:ok, @sc_get_unread_count_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_get_unread_count_p, user_id,
            "获取未读邮件数失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "获取未读邮件数失败")
          {:ok, @sc_get_unread_count_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_get_unread_count_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_unread_count_p, error_data}
    end
  end
  
  # 处理发送邮件
  defp handle_send_mail(user_id, data) do
    to_user_id = Map.get(data, "to_user_id")
    title = Map.get(data, "title")
    content = Map.get(data, "content")
    attachments = Map.get(data, "attachments", [])
    
    try do
      mail_params = %{
        from_user_id: user_id,
        to_user_id: to_user_id,
        title: title,
        content: content,
        attachments: attachments,
        mail_type: "user"
      }
      
      case MailService.send_mail(mail_params) do
        {:ok, mail} ->
          response_data = ProtocolUtils.success_response(%{
            "mailId" => mail.id,
            "sent" => true,
            "msg" => "邮件发送成功"
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_send_mail_p, user_id,
            "发送邮件成功: #{mail.id}")
          {:ok, @sc_send_mail_p, response_data}
          
        {:error, :user_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "收件人不存在")
          {:ok, @sc_send_mail_p, error_data}
          
        {:error, :blocked} ->
          error_data = ProtocolUtils.error_response(:forbidden, "您已被对方拉黑")
          {:ok, @sc_send_mail_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_send_mail_p, user_id,
            "发送邮件失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "发送邮件失败")
          {:ok, @sc_send_mail_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_send_mail_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_send_mail_p, error_data}
    end
  end
  
  # 处理删除所有已读邮件
  defp handle_delete_all_read(user_id) do
    try do
      case MailService.delete_all_read(user_id) do
        {:ok, count} ->
          response_data = ProtocolUtils.success_response(%{
            "deletedCount" => count,
            "msg" => "已删除#{count}封已读邮件"
          })
          
          ProtocolUtils.log_protocol(:info, "MAIL", @cs_delete_all_read_p, user_id,
            "删除所有已读邮件成功，共#{count}封")
          {:ok, @sc_delete_all_read_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "MAIL", @cs_delete_all_read_p, user_id,
            "删除所有已读邮件失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "删除失败")
          {:ok, @sc_delete_all_read_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "MAIL", @cs_delete_all_read_p, user_id,
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_delete_all_read_p, error_data}
    end
  end
  
  # ============================================================================
  # 工具函数
  # ============================================================================
  
  defp format_mail_item(mail) do
    %{
      "id" => mail.id,
      "title" => mail.title,
      "fromUserId" => mail.from_user_id,
      "fromUsername" => mail.from_username,
      "mailType" => mail.mail_type,
      "isRead" => mail.is_read,
      "hasReward" => not is_nil(mail.rewards) and mail.rewards != [],
      "isRewardClaimed" => mail.is_reward_claimed || false,
      "createdAt" => format_datetime(mail.inserted_at),
      "expiresAt" => format_datetime(mail.expires_at)
    }
  end
  
  defp format_mail_detail(mail) do
    %{
      "id" => mail.id,
      "title" => mail.title,
      "content" => mail.content,
      "fromUserId" => mail.from_user_id,
      "fromUsername" => mail.from_username,
      "mailType" => mail.mail_type,
      "isRead" => mail.is_read,
      "rewards" => format_rewards(mail.rewards || []),
      "isRewardClaimed" => mail.is_reward_claimed || false,
      "attachments" => mail.attachments || [],
      "createdAt" => format_datetime(mail.inserted_at),
      "expiresAt" => format_datetime(mail.expires_at)
    }
  end
  
  defp format_rewards(rewards) when is_list(rewards) do
    Enum.map(rewards, fn reward ->
      %{
        "type" => reward.type,
        "amount" => reward.amount,
        "itemId" => reward.item_id,
        "description" => reward.description
      }
    end)
  end
  defp format_rewards(_), do: []
  
  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)
  
  # ============================================================================
  # 从websocket_handler迁移的处理函数
  # ============================================================================
  
  # 处理请求邮件内容 (sub_id: 0)
  defp handle_request_mail_info_legacy(data, context) do
    user_id = context.user_id || "anonymous"
    
    ProtocolUtils.log_protocol(:info, "MAIL", 0, user_id, "请求邮件内容")
    
    mail_id = Map.get(data, "mail_id", 0)
    
    # 模拟邮件详情
    mail_info = %{
      "id" => mail_id,
      "title" => "系统邮件",
      "sender" => "系统管理员",
      "content" => "这是一封系统邮件，包含重要信息。",
      "send_time" => System.system_time(:millisecond) - 3_600_000,
      "is_read" => false,
      "has_attachment" => true,
      "attachments" => [
        %{"type" => "money", "amount" => 1000},
        %{"type" => "item", "item_id" => 1, "count" => 5}
      ]
    }
    
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "mail_info" => mail_info
    })
    
    {:ok, 1, response_data}
  end
  
  # 处理设置邮件已读 (sub_id: 2)
  defp handle_mail_set_read_legacy(data, context) do
    user_id = context.user_id || "anonymous"
    
    ProtocolUtils.log_protocol(:info, "MAIL", 2, user_id, "设置邮件为已读")
    
    mail_id = Map.get(data, "mail_id", 0)
    
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "mail_id" => mail_id,
      "message" => "邮件已标记为已读"
    })
    
    {:ok, 2, response_data}
  end
  
  # 处理删除邮件 (sub_id: 3)
  defp handle_delete_mail_legacy(data, context) do
    user_id = context.user_id || "anonymous"
    
    ProtocolUtils.log_protocol(:info, "MAIL", 3, user_id, "删除邮件")
    
    mail_id = Map.get(data, "mail_id", 0)
    
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "mail_id" => mail_id,
      "message" => "邮件删除成功"
    })
    
    {:ok, 3, response_data}
  end
  
  # 处理请求邮件列表 (sub_id: 5)
  defp handle_request_mail_list_legacy(data, context) do
    user_id = context.user_id || "anonymous"
    
    ProtocolUtils.log_protocol(:info, "MAIL", 5, user_id, "请求邮件列表")
    
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 10)
    
    # 模拟邮件列表
    mail_list = [
      %{
        "id" => 1,
        "title" => "欢迎来到游戏",
        "sender" => "系统",
        "send_time" => System.system_time(:millisecond) - 86_400_000,
        "is_read" => false,
        "has_attachment" => true
      },
      %{
        "id" => 2,
        "title" => "每日签到奖励",
        "sender" => "系统",
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "is_read" => true,
        "has_attachment" => false
      },
      %{
        "id" => 3,
        "title" => "活动通知",
        "sender" => "活动管理员",
        "send_time" => System.system_time(:millisecond) - 7_200_000,
        "is_read" => false,
        "has_attachment" => true
      }
    ]
    
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "mail_list" => mail_list,
      "page" => page,
      "page_size" => page_size,
      "total_count" => length(mail_list)
    })
    
    {:ok, 6, response_data}
  end
  
  # 处理请求新邮件数量 (sub_id: 7)
  defp handle_request_new_mail_count_legacy(data, context) do
    user_id = context.user_id || "anonymous"
    
    ProtocolUtils.log_protocol(:info, "MAIL", 7, user_id, "请求新邮件数量")
    
    # 模拟新邮件数量
    new_mail_count = :rand.uniform(5)
    
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "new_mail_count" => new_mail_count,
      "total_mail_count" => new_mail_count + :rand.uniform(10)
    })
    
    {:ok, 8, response_data}
  end
end