defmodule Teen.Workers.VipUpdateWorker do
  @moduledoc """
  VIP系统更新工作者
  
  通过Oban异步处理VIP相关更新，确保VIP系统故障不影响充值核心流程
  """
  
  use Oban.Worker, queue: :vip_updates, max_attempts: 3
  
  require Logger
  alias Teen.VipSystem.UserVipInfo
  
  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"event_type" => "recharge_completed", "user_id" => user_id, "amount" => amount}}) do
    Logger.info("VIP Worker: 处理充值完成事件 - 用户=#{user_id}, 金额=#{amount}")
    
    try do
      # 1. 增加VIP经验
      case UserVipInfo.add_experience_from_source(user_id, :recharge, amount) do
        {:ok, _info} ->
          Logger.info("VIP经验更新成功: 用户=#{user_id}")
          
        {:error, reason} ->
          Logger.error("VIP经验更新失败: #{inspect(reason)}")
          # 不抛出异常，让任务成功完成，避免无限重试
      end
      
      # 2. 更新充值记录
      case UserVipInfo.update_recharge_record(user_id, amount) do
        {:ok, _} ->
          Logger.info("VIP充值记录更新成功: 用户=#{user_id}")
          
        {:error, reason} ->
          Logger.error("VIP充值记录更新失败: #{inspect(reason)}")
      end
      
      :ok
      
    rescue
      error ->
        Logger.error("VIP Worker执行异常: #{inspect(error)}")
        Logger.error("Stack trace: #{Exception.format_stacktrace()}")
        
        # 返回错误，让Oban进行重试
        {:error, "VIP处理失败: #{Exception.message(error)}"}
    end
  end
  
  def perform(%Oban.Job{args: %{"event_type" => "vip_level_up", "user_id" => user_id, "old_level" => old_level, "new_level" => new_level}}) do
    Logger.info("VIP Worker: 处理等级提升事件 - 用户=#{user_id}, #{old_level} -> #{new_level}")
    
    # 可以在这里处理等级提升后的额外逻辑
    # 比如发送通知、记录日志、发放升级奖励等
    
    :ok
  end
  
  def perform(%Oban.Job{args: args}) do
    Logger.warning("VIP Worker: 未识别的任务类型: #{inspect(args)}")
    :ok
  end
end