defmodule Teen.Workers.ActivityUpdateWorker do
  @moduledoc """
  活动系统更新工作者
  
  通过Oban异步处理活动任务相关更新，确保活动系统故障不影响充值核心流程
  """
  
  use Oban.Worker, queue: :activity_updates, max_attempts: 3
  
  require Logger
  alias Teen.ActivitySystem.ActivityTaskService
  
  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"event_type" => "recharge_completed", "user_id" => user_id, "amount" => amount, "order_id" => order_id, "payment_method" => payment_method}}) do
    Logger.info("Activity Worker: 处理充值完成事件 - 用户=#{user_id}, 金额=#{amount}")
    
    try do
      # 转换金额为整数（如果是Decimal）
      amount_int = if is_binary(amount) do
        String.to_integer(amount)
      else
        amount
      end
      
      # 更新充值任务进度
      result = ActivityTaskService.update_task_progress(user_id, :recharge_completed, %{
        amount: amount_int,
        order_id: order_id,
        payment_method: payment_method
      })
      
      case result do
        {:ok, :updated} ->
          Logger.info("活动任务进度更新成功: 用户=#{user_id}")
        {:ok, :ignored} ->
          Logger.info("活动任务进度更新被忽略: 用户=#{user_id}")
        _ ->
          Logger.error("活动任务进度更新异常: #{inspect(result)}")
      end
      
      :ok
      
    rescue
      error ->
        Logger.error("Activity Worker执行异常: #{inspect(error)}")
        Logger.error("Stack trace: #{Exception.format_stacktrace()}")
        
        # 返回错误，让Oban进行重试
        {:error, "活动处理失败: #{Exception.message(error)}"}
    end
  end
  
  def perform(%Oban.Job{args: %{"event_type" => "game_completed", "user_id" => user_id, "game_id" => game_id, "is_win" => is_win, "bet_amount" => bet_amount, "win_amount" => win_amount}}) do
    Logger.info("Activity Worker: 处理游戏完成事件 - 用户=#{user_id}, 游戏=#{game_id}")
    
    try do
      # 更新游戏任务进度
      result = ActivityTaskService.update_task_progress(user_id, :game_completed, %{
        game_id: game_id,
        is_win: is_win,
        bet_amount: bet_amount,
        win_amount: win_amount
      })
      
      case result do
        {:ok, :updated} ->
          Logger.info("游戏任务进度更新成功: 用户=#{user_id}")
          
          # 如果是获胜，同时更新获胜任务
          if is_win do
            ActivityTaskService.update_task_progress(user_id, :game_won, %{
              game_id: game_id,
              win_amount: win_amount
            })
          end
          
        {:ok, :ignored} ->
          Logger.info("游戏任务进度更新被忽略: 用户=#{user_id}")
        _ ->
          Logger.error("游戏任务进度更新异常: #{inspect(result)}")
      end
      
      :ok
      
    rescue
      error ->
        Logger.error("Activity Worker执行异常: #{inspect(error)}")
        Logger.error("Stack trace: #{Exception.format_stacktrace()}")
        
        # 返回错误，让Oban进行重试
        {:error, "游戏活动处理失败: #{Exception.message(error)}"}
    end
  end
  
  def perform(%Oban.Job{args: %{"event_type" => "user_login", "user_id" => user_id, "login_time" => login_time}}) do
    Logger.info("Activity Worker: 处理登录事件 - 用户=#{user_id}")
    
    try do
      # 解析登录时间
      parsed_login_time = if is_binary(login_time) do
        {:ok, datetime, _} = DateTime.from_iso8601(login_time)
        datetime
      else
        login_time
      end
      
      # 更新登录任务进度
      result = ActivityTaskService.update_task_progress(user_id, :user_login, %{
        login_time: parsed_login_time
      })
      
      case result do
        {:ok, :updated} ->
          Logger.info("登录任务进度更新成功: 用户=#{user_id}")
        {:ok, :ignored} ->
          Logger.info("登录任务进度更新被忽略: 用户=#{user_id}")
        _ ->
          Logger.error("登录任务进度更新异常: #{inspect(result)}")
      end
      
      :ok
      
    rescue
      error ->
        Logger.error("Activity Worker执行异常: #{inspect(error)}")
        Logger.error("Stack trace: #{Exception.format_stacktrace()}")
        
        # 返回错误，让Oban进行重试
        {:error, "登录活动处理失败: #{Exception.message(error)}"}
    end
  end
  
  def perform(%Oban.Job{args: args}) do
    Logger.warning("Activity Worker: 未识别的任务类型: #{inspect(args)}")
    :ok
  end
end