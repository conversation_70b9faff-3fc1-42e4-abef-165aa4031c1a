defmodule Teen.Events.TaskProgressHandler do
  @moduledoc """
  任务进度事件处理器

  监听游戏事件并自动更新任务进度，整合自原 TaskProgressListener 的功能。
  通过事件驱动的方式来更新任务进度，而不需要在充值和游戏逻辑中直接添加任务更新代码。
  """

  use GenServer
  require Logger

  alias Teen.Events.GameEvent

  # 任务类型常量
  @task_types %{
    recharge_amount: "recharge_amount",
    game_rounds: "game_rounds",
    daily_login: "daily_login",
    consecutive_login: "consecutive_login"
  }

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_opts) do
    # 订阅游戏事件
    subscribe_to_events()

    Logger.info("🎯 [TASK_PROGRESS_HANDLER] 任务进度处理器启动并订阅事件")
    {:ok, %{processed_events: 0}}
  end

  defp subscribe_to_events do
    # 订阅用户登录事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:user_login))

    # 订阅充值事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:user_recharge))

    # 订阅游戏完成事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:game_completed))

    Logger.info("🎯 [TASK_PROGRESS_HANDLER] 事件订阅完成")
  end

  # 处理用户登录事件
  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :user_login} = event}, state) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 处理用户登录事件: #{event.user_id}")

    # 更新登录相关任务
    update_login_tasks(event.user_id, event.timestamp)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理充值事件
  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :user_recharge} = event}, state) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 处理充值事件: #{event.user_id}")

    # 更新充值金额相关任务
    amount = event.event_data[:amount] || event.event_data["amount"] || 0
    update_recharge_tasks(event.user_id, amount)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理游戏完成事件
  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :game_completed} = event}, state) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 处理游戏完成事件: #{event.user_id}")

    # 更新游戏局数相关任务
    update_game_tasks(event.user_id, event.event_data)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理其他事件
  @impl true
  def handle_info({:game_event, event}, state) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 收到其他事件: #{event.event_type}")
    {:noreply, state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  # ==================== 任务更新逻辑 ====================

  defp update_recharge_tasks(user_id, amount) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 更新充值任务: 用户#{user_id}, 金额#{amount}")

    # TODO: 实现充值任务更新逻辑
    # 例如：
    # - 查找用户的充值金额任务
    # - 更新任务进度
    # - 检查任务是否完成

    :ok
  end

  defp update_game_tasks(user_id, game_data) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 更新游戏任务: 用户#{user_id}")

    # TODO: 实现游戏任务更新逻辑
    # 例如：
    # - 查找用户的游戏局数任务
    # - 每完成一局游戏，进度+1
    # - 检查任务是否完成

    :ok
  end

  defp update_login_tasks(user_id, login_time) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 更新登录任务: 用户#{user_id}")

    # 更新每日登录任务
    update_daily_login_task(user_id, login_time)

    # 更新连续登录任务
    update_consecutive_login_task(user_id, login_time)
  end

  defp update_daily_login_task(user_id, login_time) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 更新每日登录任务: 用户#{user_id}")

    # TODO: 实现每日登录任务更新逻辑
    # 例如：
    # - 检查今天是否已经完成登录任务
    # - 如果没有，标记为完成
    # - 发放奖励

    :ok
  end

  defp update_consecutive_login_task(user_id, login_time) do
    Logger.debug("🎯 [TASK_PROGRESS_HANDLER] 更新连续登录任务: 用户#{user_id}")

    # TODO: 实现连续登录任务更新逻辑
    # 例如：
    # - 检查连续登录天数
    # - 更新连续登录记录
    # - 检查连续登录任务是否完成

    :ok
  end

  # ==================== 公共API ====================

  @doc """
  获取处理器统计信息
  """
  def get_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    stats = %{
      processed_events: state.processed_events,
      status: :running
    }

    {:reply, stats, state}
  end
end
