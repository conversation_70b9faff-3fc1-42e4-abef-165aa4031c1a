defmodule Teen.Events.AshEventAdapter do
  @moduledoc """
  Ash事件适配器

  监听Ash资源的PubSub事件，并将其转换为标准化的GameEvent格式
  然后重新发布到游戏事件系统中
  """

  use GenServer
  require Logger
  alias Teen.Events.{GameEvent, EventPublisher}

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_opts) do
    Logger.info("🎯 [ASH_EVENT_ADAPTER] Ash事件适配器启动")

    # 订阅Ash资源的PubSub事件
    subscribe_to_ash_events()

    {:ok, %{processed_events: 0}}
  end

  defp subscribe_to_ash_events do
    # 订阅充值记录事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "recharge:recharge_completed")
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "recharge:recharge_created")

    # 订阅游戏记录事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "game:game_completed")
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "game:game_started")

    # 订阅用户事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "user:create")
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "user:update")

    Logger.info("🎯 [ASH_EVENT_ADAPTER] Ash事件订阅完成")
  end

  # 处理充值完成事件
  @impl true
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: "recharge:recharge_completed", payload: payload},
        state
      ) do
    Logger.debug("🎯 [ASH_EVENT_ADAPTER] 处理充值完成事件: #{inspect(payload)}")

    case extract_recharge_data(payload) do
      {:ok, user_id, amount, opts} ->
        EventPublisher.publish_user_recharge(user_id, amount, opts)

      {:error, reason} ->
        Logger.error("🎯 [ASH_EVENT_ADAPTER] 充值事件数据提取失败: #{inspect(reason)}")
    end

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理游戏完成事件
  @impl true
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: "game:game_completed", payload: payload},
        state
      ) do
    Logger.debug("🎯 [ASH_EVENT_ADAPTER] 处理游戏完成事件: #{inspect(payload)}")

    case extract_game_data(payload) do
      {:ok, user_id, game_id, result, opts} ->
        EventPublisher.publish_game_completed(user_id, game_id, result, opts)

      {:error, reason} ->
        Logger.error("🎯 [ASH_EVENT_ADAPTER] 游戏事件数据提取失败: #{inspect(reason)}")
    end

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理用户创建事件（注册）
  @impl true
  def handle_info(%Phoenix.Socket.Broadcast{topic: "user:create", payload: payload}, state) do
    Logger.debug("🎯 [ASH_EVENT_ADAPTER] 处理用户注册事件: #{inspect(payload)}")

    case extract_user_data(payload) do
      {:ok, user_id, opts} ->
        event =
          GameEvent.new(
            user_id,
            :user_register,
            %{
              registration_time: DateTime.utc_now()
            },
            opts
          )

        EventPublisher.publish_event(event)

      {:error, reason} ->
        Logger.error("🎯 [ASH_EVENT_ADAPTER] 用户注册事件数据提取失败: #{inspect(reason)}")
    end

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理其他事件
  @impl true
  def handle_info(%Phoenix.Socket.Broadcast{} = broadcast, state) do
    Logger.debug("🎯 [ASH_EVENT_ADAPTER] 收到未处理的Ash事件: #{broadcast.topic}")
    {:noreply, state}
  end

  # 处理其他消息
  @impl true
  def handle_info(msg, state) do
    Logger.debug("🎯 [ASH_EVENT_ADAPTER] 收到未知消息: #{inspect(msg)}")
    {:noreply, state}
  end

  # ==================== 私有辅助函数 ====================

  defp extract_recharge_data(payload) do
    case payload do
      %{data: %{user_id: user_id, amount: amount} = data} ->
        opts = [
          source: "payment_system",
          order_id: Map.get(data, :order_id),
          payment_method: Map.get(data, :payment_method),
          currency: Map.get(data, :currency, "XAA")
        ]

        {:ok, user_id, amount, opts}

      %{user_id: user_id, amount: amount} = data ->
        opts = [
          source: "payment_system",
          order_id: Map.get(data, :order_id),
          payment_method: Map.get(data, :payment_method),
          currency: Map.get(data, :currency, "XAA")
        ]

        {:ok, user_id, amount, opts}

      _ ->
        {:error, "Invalid recharge payload format"}
    end
  end

  defp extract_game_data(payload) do
    case payload do
      %{data: %{user_id: user_id, game_id: game_id} = data} ->
        result = %{
          status: Map.get(data, :result_status, :completed),
          bet_amount: Map.get(data, :bet_amount),
          win_amount: Map.get(data, :win_amount, 0)
        }

        opts = [
          source: "game_system",
          game_type: Map.get(data, :game_type),
          duration: Map.get(data, :duration)
        ]

        {:ok, user_id, game_id, result, opts}

      %{user_id: user_id, game_id: game_id} = data ->
        result = %{
          status: Map.get(data, :result_status, :completed),
          bet_amount: Map.get(data, :bet_amount),
          win_amount: Map.get(data, :win_amount, 0)
        }

        opts = [
          source: "game_system",
          game_type: Map.get(data, :game_type),
          duration: Map.get(data, :duration)
        ]

        {:ok, user_id, game_id, result, opts}

      _ ->
        {:error, "Invalid game payload format"}
    end
  end

  defp extract_user_data(payload) do
    case payload do
      %{data: %{id: user_id}} ->
        opts = [source: "user_system"]
        {:ok, user_id, opts}

      %{id: user_id} ->
        opts = [source: "user_system"]
        {:ok, user_id, opts}

      _ ->
        {:error, "Invalid user payload format"}
    end
  end
end
