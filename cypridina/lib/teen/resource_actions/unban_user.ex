defmodule Teen.ResourceActions.UnbanUser do
  @moduledoc """
  解封用户的资源操作
  """

  use Backpex.ResourceAction

  import Ecto.Changeset

  @impl Backpex.ResourceAction
  def title, do: "解封用户"

  @impl Backpex.ResourceAction
  def label, do: "解封用户"

  @impl Backpex.ResourceAction
  def fields do
    [
      unban_reason: %{
        module: Backpex.Fields.Textarea,
        label: "解封原因",
        type: :string
      }
    ]
  end

  @required_fields ~w[unban_reason]a

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> validate_length(:unban_reason, min: 5, max: 500)
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    %{selected_items: selected_items} = socket.assigns

    result = Teen.UserManagement.batch_unban_users(selected_items, data.unban_reason)

    socket =
      if result.success_count > 0 do
        socket
        |> Phoenix.LiveView.put_flash(:info, "成功解封 #{result.success_count} 个用户")
        |> Phoenix.LiveView.assign(selected_items: [])
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "解封失败，请检查选中的封禁记录")
        |> Phoenix.LiveView.assign(selected_items: [])
      end

    {:ok, socket}
  end
end
