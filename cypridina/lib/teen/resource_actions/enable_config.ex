defmodule Teen.ResourceActions.EnableConfig do
  @moduledoc """
  启用支付配置的资源操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def title, do: "启用支付配置"

  @impl Backpex.ResourceAction
  def label, do: "启用配置"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> Ecto.Changeset.cast(attrs, [])
  end

  @impl Backpex.ResourceAction
  def handle(socket, _data) do
    %{selected_items: selected_items} = socket.assigns

    result = Teen.PaymentSystem.batch_update_withdrawal_config_status(selected_items, :enable)

    socket =
      if result.success_count > 0 do
        socket
        |> Phoenix.LiveView.put_flash(:info, "成功启用 #{result.success_count} 个提现配置")
        |> Phoenix.LiveView.assign(selected_items: [])
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "启用失败，请检查选中的配置")
        |> Phoenix.LiveView.assign(selected_items: [])
      end

    {:ok, socket}
  end
end
