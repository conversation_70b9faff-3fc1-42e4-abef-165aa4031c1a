defmodule Teen.Filters.StatusSelect do
  @moduledoc """
  通用状态筛选器

  用于筛选启用/禁用状态的记录
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"启用", 1},
      {"禁用", 0}
    ]
  end
end
