<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <!-- 品牌标识区域 -->
    <div class="flex items-center gap-3">
      <Backpex.HTML.Layout.topbar_branding />
      <!-- 系统标题 -->
      <div class="hidden lg:flex flex-col">
        <span class="text-sm font-semibold text-base-content">Teen Patti 游戏管理系统</span>
        <span class="text-xs text-base-content/60">Game Management</span>
      </div>
    </div>

    <!-- 中间工具栏区域 -->
    <div class="flex-1 flex items-center justify-center">
      <!-- 快速搜索框 -->
      <div class="hidden xl:flex items-center max-w-md w-full">
        <div class="relative w-full">
          <input
            type="text"
            placeholder="搜索游戏、房间、配置..."
            class="input input-sm input-bordered w-full pl-10 pr-4 bg-base-100/50 backdrop-blur-sm border-base-300/50 focus:border-primary/50 focus:bg-base-100"
          />
          <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-base-content/60" />
        </div>
      </div>
    </div>

    <!-- 右侧工具区域 -->
    <div class="flex items-center gap-2">
      <!-- 系统状态指示器 -->
      <div class="hidden md:flex items-center gap-2 px-3 py-1 bg-success/10 text-success rounded-full">
        <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
        <span class="text-xs">系统正常</span>
      </div>

      <!-- 主题选择器 -->
      <Backpex.HTML.Layout.theme_selector
        socket={@socket}
        themes={[
          {"Light", "light"},
          {"Dark", "dark"},
          {"Cyberpunk", "cyberpunk"}
        ]}
      />

      <!-- 用户信息区域 -->
      <div class="flex items-center gap-3 ml-2">
        <!-- 桌面端用户信息文本 -->
        <div class="hidden lg:flex flex-col items-end text-sm">
          <span class="font-medium text-base-content">
            <%= if assigns[:current_user] && @current_user.username do %>
              {@current_user.username}
            <% else %>
              未知用户
            <% end %>
          </span>
          <span class="text-xs text-base-content/60">
            <%= if assigns[:current_user] && @current_user.role_name do %>
              {@current_user.role_name}
            <% else %>
              管理员
            <% end %>
          </span>
        </div>
        
        <!-- 用户头像和下拉菜单 -->
        <Backpex.HTML.Layout.topbar_dropdown class="dropdown-end">
          <:label>
            <label tabindex="0" class="btn btn-ghost flex items-center gap-2 px-3 hover:bg-base-200/50 transition-colors">
              <!-- 用户头像 -->
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-error to-warning text-primary-content rounded-full w-9 h-9 ring-2 ring-primary/20">
                  <span class="text-sm font-bold">
                    <%= if assigns[:current_user] && @current_user.username do %>
                      {String.first(to_string(@current_user.username))}
                    <% else %>
                      A
                    <% end %>
                  </span>
                </div>
              </div>
              <!-- 移动端显示用户名 -->
              <div class="lg:hidden flex flex-col items-start">
                <span class="text-sm font-medium">
                  <%= if assigns[:current_user] && @current_user.username do %>
                    {@current_user.username}
                  <% else %>
                    管理员
                  <% end %>
                </span>
                <span class="text-xs opacity-60">
                  <%= if assigns[:current_user] && @current_user.role_name do %>
                    {@current_user.role_name}
                  <% else %>
                    游戏管理
                  <% end %>
                </span>
              </div>
              <.icon name="hero-chevron-down" class="size-4 transition-transform duration-200" />
            </label>
          </:label>
          <!-- 用户菜单 -->
          <li>
            <.link navigate="/admin/profile" class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors">
              <.icon name="hero-user-circle" class="size-5 text-primary" />
              <div class="flex flex-col">
                <span class="font-medium">个人信息</span>
                <span class="text-xs text-base-content/60">查看和编辑个人资料</span>
              </div>
            </.link>
          </li>
          <li>
            <.link navigate="/admin/system-config" class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors">
              <.icon name="hero-cog-6-tooth" class="size-5 text-info" />
              <div class="flex flex-col">
                <span class="font-medium">系统设置</span>
                <span class="text-xs text-base-content/60">配置系统参数</span>
              </div>
            </.link>
          </li>
          <!-- 分隔线 -->
          <li><hr class="my-2 border-base-300" /></li>
          <!-- 退出登录 -->
          <li>
            <.link
              navigate={~p"/sign-out"}
              class="flex items-center gap-3 px-4 py-3 text-error hover:bg-error/10 transition-colors"
            >
              <.icon name="hero-arrow-right-on-rectangle" class="size-5" />
              <div class="flex flex-col">
                <span class="font-medium">退出登录</span>
                <span class="text-xs text-error/60">安全退出系统</span>
              </div>
            </.link>
          </li>
        </Backpex.HTML.Layout.topbar_dropdown>
      </div>
    </div>
  </:topbar>
  
  <:sidebar>
    <!-- 仪表盘 -->
    <Backpex.HTML.Layout.sidebar_section id="dashboard">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-chart-bar-square" class="size-4 text-primary" />
          <span class="font-semibold">仪表盘</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/dashboard">
        <.icon name="hero-home" class="size-5 text-primary" /> 
        <span class="ml-1">概览</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/inventory_control/">
        <.icon name="hero-chart-bar" class="size-5 text-primary" /> 
        <span class="ml-1">钱包仪表盘</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 游戏管理 -->
    <Backpex.HTML.Layout.sidebar_section id="game-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-puzzle-piece" class="size-4 text-error" />
          <span class="font-semibold">游戏管理</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/games"}>
        <.icon name="hero-squares-2x2" class="size-5 text-error" /> 
        <span class="ml-1">游戏列表</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/game-rooms">
        <.icon name="hero-building-office" class="size-5 text-error" /> 
        <span class="ml-1">游戏房间</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/jackpots">
        <.icon name="hero-trophy" class="size-5 text-warning" /> 
        <span class="ml-1">奖池管理</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 机器人管理 -->
    <Backpex.HTML.Layout.sidebar_section id="robot-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-cpu-chip" class="size-4 text-info" />
          <span class="font-semibold">机器人管理</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/robot-management">
        <.icon name="hero-user-circle" class="size-5 text-info" /> 
        <span class="ml-1">机器人配置</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 系统配置 -->
    <Backpex.HTML.Layout.sidebar_section id="system-config">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-cog-6-tooth" class="size-4 text-base-content/80" />
          <span class="font-semibold">系统配置</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/system-config">
        <.icon name="hero-adjustments-horizontal" class="size-5 text-base-content/80" /> 
        <span class="ml-1">系统设置</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/logs">
        <.icon name="hero-document-text" class="size-5 text-base-content/80" /> 
        <span class="ml-1">系统日志</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 侧边栏底部信息 -->
    <div class="mt-auto p-4 border-t border-base-300">
      <div class="text-center">
        <div class="text-xs text-base-content/60 mb-2">Teen Patti Game Admin</div>
        <div class="text-xs text-base-content/40">v1.0.0</div>
        <div class="flex items-center justify-center gap-2 mt-3">
          <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          <span class="text-xs text-base-content/60">系统运行正常</span>
        </div>
      </div>
    </div>
  </:sidebar>
  
  <!-- Flash消息 -->
  <Backpex.HTML.Layout.flash_messages flash={@flash} />
  
  <!-- 主内容区域 -->
  <div class="min-h-screen bg-gradient-to-br from-base-100 to-base-200">
    {@inner_content}
  </div>
</Backpex.HTML.Layout.app_shell>
