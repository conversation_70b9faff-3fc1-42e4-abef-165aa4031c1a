<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <!-- 品牌标识区域 -->
    <div class="flex items-center gap-3">
      <Backpex.HTML.Layout.topbar_branding />
      <!-- 系统标题 -->
      <div class="hidden lg:flex flex-col">
        <span class="text-sm font-semibold text-base-content">Teen Patti 管理系统</span>
        <span class="text-xs text-base-content/60">Admin Dashboard</span>
      </div>
    </div>

    <!-- 中间工具栏区域 -->
    <div class="flex-1 flex items-center justify-center">
      <!-- 快速搜索框 -->
      <div class="hidden xl:flex items-center max-w-md w-full">
        <div class="relative w-full">
          <input
            type="text"
            placeholder="快速搜索用户、订单..."
            class="input input-sm input-bordered w-full pl-10 pr-4 bg-base-100/50 backdrop-blur-sm border-base-300/50 focus:border-primary/50 focus:bg-base-100"
          />
          <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-base-content/60" />
        </div>
      </div>
    </div>

    <!-- 右侧工具区域 -->
    <div class="flex items-center gap-2">
      <!-- 通知按钮 -->
      <div class="dropdown dropdown-end">
        <label tabindex="0" class="btn btn-ghost btn-sm btn-circle relative">
          <.icon name="hero-bell" class="size-5" />
          <!-- 通知徽章 -->
          <div class="absolute -top-1 -right-1 bg-error text-error-content rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold">
            3
          </div>
        </label>
        <div tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-80 border border-base-300">
          <div class="p-3 border-b border-base-300">
            <h3 class="font-semibold text-base-content">通知中心</h3>
          </div>
          <div class="max-h-64 overflow-y-auto">
            <div class="p-3 hover:bg-base-200 cursor-pointer border-b border-base-300/50">
              <div class="flex items-start gap-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div class="flex-1">
                  <p class="text-sm font-medium">新用户注册</p>
                  <p class="text-xs text-base-content/60">用户 user123 刚刚注册</p>
                  <p class="text-xs text-base-content/40 mt-1">2分钟前</p>
                </div>
              </div>
            </div>
            <div class="p-3 hover:bg-base-200 cursor-pointer border-b border-base-300/50">
              <div class="flex items-start gap-3">
                <div class="w-2 h-2 bg-warning rounded-full mt-2 flex-shrink-0"></div>
                <div class="flex-1">
                  <p class="text-sm font-medium">异常充值订单</p>
                  <p class="text-xs text-base-content/60">订单 #12345 需要人工审核</p>
                  <p class="text-xs text-base-content/40 mt-1">5分钟前</p>
                </div>
              </div>
            </div>
            <div class="p-3 hover:bg-base-200 cursor-pointer">
              <div class="flex items-start gap-3">
                <div class="w-2 h-2 bg-error rounded-full mt-2 flex-shrink-0"></div>
                <div class="flex-1">
                  <p class="text-sm font-medium">系统警告</p>
                  <p class="text-xs text-base-content/60">服务器CPU使用率过高</p>
                  <p class="text-xs text-base-content/40 mt-1">10分钟前</p>
                </div>
              </div>
            </div>
          </div>
          <div class="p-2 border-t border-base-300">
            <button class="btn btn-sm btn-ghost w-full">查看所有通知</button>
          </div>
        </div>
      </div>

      <!-- 主题选择器 -->
      <Backpex.HTML.Layout.theme_selector
        socket={@socket}
        themes={[
          {"Light", "light"},
          {"Dark", "dark"},
          {"Cyberpunk", "cyberpunk"}
        ]}
      />

      <!-- 用户信息区域 -->
      <div class="flex items-center gap-3 ml-2">
        <!-- 桌面端用户信息文本 -->
        <div class="hidden lg:flex flex-col items-end text-sm">
          <span class="font-medium text-base-content">
            <%= if assigns[:current_user] && @current_user.username do %>
              {@current_user.username}
            <% else %>
              未知用户
            <% end %>
          </span>
          <span class="text-xs text-base-content/60">
            <%= if assigns[:current_user] && @current_user.role_name do %>
              {@current_user.role_name}
            <% else %>
              普通用户
            <% end %>
          </span>
        </div>

        <!-- 用户头像和下拉菜单 -->
        <Backpex.HTML.Layout.topbar_dropdown class="dropdown-end">
          <:label>
            <label tabindex="0" class="btn btn-ghost flex items-center gap-2 px-3 hover:bg-base-200/50 transition-colors">
              <!-- 用户头像 -->
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-primary to-secondary text-primary-content rounded-full w-9 h-9 ring-2 ring-primary/20">
                  <span class="text-sm font-bold">
                    <%= if assigns[:current_user] && @current_user.username do %>
                      {String.first(to_string(@current_user.username))}
                    <% else %>
                      U
                    <% end %>
                  </span>
                </div>
              </div>
              <!-- 移动端显示用户名 -->
              <div class="lg:hidden flex flex-col items-start">
                <span class="text-sm font-medium">
                  <%= if assigns[:current_user] && @current_user.username do %>
                    {@current_user.username}
                  <% else %>
                    未知用户
                  <% end %>
                </span>
                <span class="text-xs opacity-60">
                  <%= if assigns[:current_user] && @current_user.role_name do %>
                    {@current_user.role_name}
                  <% else %>
                    普通用户
                  <% end %>
                </span>
              </div>
              <.icon name="hero-chevron-down" class="size-4 transition-transform duration-200" />
            </label>
          </:label>
          <!-- 用户菜单 -->
          <li>
            <.link navigate="/admin/profile" class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors">
              <.icon name="hero-user-circle" class="size-5 text-primary" />
              <div class="flex flex-col">
                <span class="font-medium">个人信息</span>
                <span class="text-xs text-base-content/60">查看和编辑个人资料</span>
              </div>
            </.link>
          </li>
          <li>
            <.link navigate="/admin/system-config" class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors">
              <.icon name="hero-cog-6-tooth" class="size-5 text-info" />
              <div class="flex flex-col">
                <span class="font-medium">系统设置</span>
                <span class="text-xs text-base-content/60">配置系统参数</span>
              </div>
            </.link>
          </li>
          <!-- 分隔线 -->
          <li><hr class="my-2 border-base-300" /></li>
          <!-- 退出登录 -->
          <li>
            <.link
              navigate={~p"/sign-out"}
              class="flex items-center gap-3 px-4 py-3 text-error hover:bg-error/10 transition-colors"
            >
              <.icon name="hero-arrow-right-on-rectangle" class="size-5" />
              <div class="flex flex-col">
                <span class="font-medium">退出登录</span>
                <span class="text-xs text-error/60">安全退出系统</span>
              </div>
            </.link>
          </li>
        </Backpex.HTML.Layout.topbar_dropdown>
      </div>
    </div>
  </:topbar>
  <:sidebar>
    <!-- 仪表盘 -->
    <Backpex.HTML.Layout.sidebar_section id="dashboard">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-chart-bar-square" class="size-4 text-primary" />
          <span class="font-semibold">仪表盘</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/dashboard">
        <.icon name="hero-home" class="size-5 text-primary" />
        <span class="ml-1">概览</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/analytics">
        <.icon name="hero-chart-pie" class="size-5 text-info" />
        <span class="ml-1">数据分析</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 用户管理 -->
    <Backpex.HTML.Layout.sidebar_section id="user-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-users" class="size-4 text-success" />
          <span class="font-semibold">用户管理</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/user-overview">
        <.icon name="hero-chart-bar-square" class="size-5 text-success" />
        <span class="ml-1">用户概览</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/users"}>
        <.icon name="hero-user" class="size-5 text-success" />
        <span class="ml-1">用户列表</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate="/admin/user-management"
      >
        <.icon name="hero-user-plus" class="size-5 text-success" />
        <span class="ml-1">用户管理</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate="/admin/subordinate-management"
      >
        <.icon name="hero-user-group" class="size-5 text-success" />
        <span class="ml-1">下线管理</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/user-bans"}>
        <.icon name="hero-no-symbol" class="size-5 text-warning" />
        <span class="ml-1">封禁管理</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/user-devices"}
      >
        <.icon name="hero-device-phone-mobile" class="size-5 text-info" />
        <span class="ml-1">设备管理</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 活动系统 -->
    <Backpex.HTML.Layout.sidebar_section id="activity-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-gift" class="size-4 text-secondary" />
          <span class="font-semibold">活动系统</span>
        </div>
      </:label>
      <!-- 日常活动 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">日常活动</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/daily-game-tasks"
        >
          <.icon name="hero-calendar-days" class="size-4 text-secondary" />
          <span class="ml-1 text-sm">每日任务</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/seven-day-logins"
        >
          <.icon name="hero-calendar" class="size-4 text-secondary" />
          <span class="ml-1 text-sm">七日登录</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/weekly-cards">
          <.icon name="hero-credit-card" class="size-4 text-secondary" />
          <span class="ml-1 text-sm">周卡活动</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>

      <!-- 充值活动 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">充值活动</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/recharge-tasks"
        >
          <.icon name="hero-banknotes" class="size-4 text-warning" />
          <span class="ml-1 text-sm">充值任务</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/first-recharge-gifts"
        >
          <.icon name="hero-gift" class="size-4 text-warning" />
          <span class="ml-1 text-sm">首充礼包</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/vip-gifts">
          <.icon name="hero-star" class="size-4 text-warning" />
          <span class="ml-1 text-sm">VIP礼包</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>

      <!-- 游戏活动 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">游戏活动</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/recharge-wheels"
        >
          <.icon name="hero-arrow-path" class="size-4 text-accent" />
          <span class="ml-1 text-sm">转盘抽奖</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/scratch-card-activities"
        >
          <.icon name="hero-ticket" class="size-4 text-accent" />
          <span class="ml-1 text-sm">刮刮卡</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/loss-rebates">
          <.icon name="hero-arrow-uturn-left" class="size-4 text-accent" />
          <span class="ml-1 text-sm">亏损返利</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>

      <!-- 推广活动 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">推广活动</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/invite-cash-activities"
        >
          <.icon name="hero-user-plus" class="size-4 text-info" />
          <span class="ml-1 text-sm">邀请奖励</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/binding-rewards"
        >
          <.icon name="hero-link" class="size-4 text-info" />
          <span class="ml-1 text-sm">绑定奖励</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate="/admin/free-bonus-tasks"
        >
          <.icon name="hero-hand-raised" class="size-4 text-info" />
          <span class="ml-1 text-sm">免费任务</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/cdkey-rewards">
          <.icon name="hero-key" class="size-4 text-info" />
          <span class="ml-1 text-sm">CDKey奖励</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 活动记录 -->
    <Backpex.HTML.Layout.sidebar_section id="activity-records">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-clipboard-document-list" class="size-4 text-neutral" />
          <span class="font-semibold">活动记录</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate="/admin/user-activity-participations"
      >
        <.icon name="hero-user-circle" class="size-5 text-neutral" />
        <span class="ml-1">参与记录</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate="/admin/reward-claim-records"
      >
        <.icon name="hero-clipboard-document-check" class="size-5 text-neutral" />
        <span class="ml-1">奖励记录</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 游戏管理 -->
    <Backpex.HTML.Layout.sidebar_section id="game-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-puzzle-piece" class="size-4 text-error" />
          <span class="font-semibold">游戏管理</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/games"}>
        <.icon name="hero-squares-2x2" class="size-5 text-error" />
        <span class="ml-1">游戏列表</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/robots">
        <.icon name="hero-building-office" class="size-5 text-error" />
        <span class="ml-1">游戏房间</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/jackpots">
        <.icon name="hero-trophy" class="size-5 text-warning" />
        <span class="ml-1">奖池管理</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate="/admin/inventory_control/"
      >
        <.icon name="hero-chart-bar" class="size-5 text-info" />
        <span class="ml-1">游戏统计</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 支付系统 -->
    <Backpex.HTML.Layout.sidebar_section id="payment-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-credit-card" class="size-4 text-success" />
          <span class="font-semibold">支付系统</span>
        </div>
      </:label>
      <!-- 支付配置 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">支付配置</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/payment-configs"}
        >
          <.icon name="hero-cog-6-tooth" class="size-4 text-success" />
          <span class="ml-1 text-sm">支付配置</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/payment-gateways"}
        >
          <.icon name="hero-server" class="size-4 text-success" />
          <span class="ml-1 text-sm">支付网关</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/bank-configs"}
        >
          <.icon name="hero-building-library" class="size-4 text-success" />
          <span class="ml-1 text-sm">银行配置</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>

      <!-- 提现管理 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">提现管理</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/withdrawal-configs"}
        >
          <.icon name="hero-arrow-path-rounded-square" class="size-4 text-warning" />
          <span class="ml-1 text-sm">提现配置</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/withdrawal-records"}
        >
          <.icon name="hero-document-text" class="size-4 text-warning" />
          <span class="ml-1 text-sm">提现记录</span>
        </Backpex.HTML.Layout.sidebar_item>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/user-bank-cards"}
        >
          <.icon name="hero-identification" class="size-4 text-warning" />
          <span class="ml-1 text-sm">用户银行卡</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>

      <!-- 订单管理 -->
      <div class="ml-4 border-l-2 border-base-300 pl-3 my-2">
        <div class="text-xs font-medium text-base-content/70 mb-2 uppercase tracking-wide">订单管理</div>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/admin/payment-orders"}
        >
          <.icon name="hero-document-check" class="size-4 text-info" />
          <span class="ml-1 text-sm">支付订单</span>
        </Backpex.HTML.Layout.sidebar_item>
      </div>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 商品系统 -->
    <Backpex.HTML.Layout.sidebar_section id="shop-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-shopping-bag" class="size-4 text-accent" />
          <span class="font-semibold">商品系统</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/products"}>
        <.icon name="hero-cube" class="size-5 text-accent" />
        <span class="ml-1">商品管理</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/product-templates"}
      >
        <.icon name="hero-document-duplicate" class="size-5 text-accent" />
        <span class="ml-1">商品模板</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/user-purchases"}
      >
        <.icon name="hero-receipt-percent" class="size-5 text-accent" />
        <span class="ml-1">购买记录</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 系统管理 -->
    <Backpex.HTML.Layout.sidebar_section id="system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-cog-6-tooth" class="size-4 text-base-content/80" />
          <span class="font-semibold">系统管理</span>
        </div>
      </:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/system-config">
        <.icon name="hero-adjustments-horizontal" class="size-5 text-base-content/80" />
        <span class="ml-1">系统配置</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/channels">
        <.icon name="hero-tv" class="size-5 text-base-content/80" />
        <span class="ml-1">渠道管理</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/logs">
        <.icon name="hero-document-text" class="size-5 text-base-content/80" />
        <span class="ml-1">系统日志</span>
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/profile">
        <.icon name="hero-user-circle" class="size-5 text-base-content/80" />
        <span class="ml-1">个人信息</span>
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <!-- 侧边栏底部信息 -->
    <div class="mt-auto p-4 border-t border-base-300">
      <div class="text-center">
        <div class="text-xs text-base-content/60 mb-2">Teen Patti Admin</div>
        <div class="text-xs text-base-content/40">v1.0.0</div>
        <div class="flex items-center justify-center gap-2 mt-3">
          <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          <span class="text-xs text-base-content/60">系统运行正常</span>
        </div>
      </div>
    </div>
  </:sidebar>

  <!-- Flash消息 -->
  <Backpex.HTML.Layout.flash_messages flash={@flash} />

  <!-- 主内容区域 -->
  <div class="min-h-screen bg-gradient-to-br from-base-100 to-base-200">
    {@inner_content}
  </div>
</Backpex.HTML.Layout.app_shell>
