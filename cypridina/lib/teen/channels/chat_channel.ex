defmodule Teen.Channels.ChatChannel do
  @moduledoc """
  通用聊天系统的Phoenix Channel

  处理实时消息传递、在线状态同步等功能
  """

  use Phoenix.Channel
  require Logger

  alias Cypridina.Chat.ChatService
  alias Teen.UserPresence

  @impl true
  def join("chat:session:" <> session_id, _payload, socket) do
    user_id = socket.assigns[:user_id]

    Logger.info("💬 [CHAT_CHANNEL] 用户加入聊天会话 - 用户: #{user_id}, 会话: #{session_id}")

    # 验证用户是否有权限访问此会话
    case verify_session_access(session_id, user_id) do
      {:ok, session} ->
        socket =
          socket
          |> assign(:session_id, session_id)
          |> assign(:session, session)

        # 跟踪用户在此会话中的状态
        case UserPresence.track_user(self(), user_id, %{
               session_id: session_id,
               joined_at: DateTime.utc_now()
             }) do
          {:ok, _} ->
            Logger.info("💬 [CHAT_CHANNEL] 用户状态跟踪成功 - 用户: #{user_id}, 会话: #{session_id}")

            # 广播用户加入事件
            broadcast(socket, "user_joined", %{
              user_id: user_id,
              session_id: session_id,
              timestamp: DateTime.utc_now()
            })

            {:ok, socket}

          {:error, reason} ->
            Logger.error(
              "💬 [CHAT_CHANNEL] 用户状态跟踪失败 - 用户: #{user_id}, 会话: #{session_id}, 原因: #{inspect(reason)}"
            )

            {:error, %{reason: "presence_tracking_failed"}}
        end

      {:error, reason} ->
        Logger.warn(
          "💬 [CHAT_CHANNEL] 用户无权限访问会话 - 用户: #{user_id}, 会话: #{session_id}, 原因: #{inspect(reason)}"
        )

        {:error, %{reason: "access_denied"}}
    end
  end

  @impl true
  def join("chat:lobby", _payload, socket) do
    user_id = socket.assigns[:user_id]

    Logger.info("💬 [CHAT_CHANNEL] 用户加入聊天大厅 - 用户: #{user_id}")

    # 跟踪用户在聊天大厅的状态
    case UserPresence.track_user(self(), user_id, %{
           location: "chat_lobby",
           joined_at: DateTime.utc_now()
         }) do
      {:ok, _} ->
        Logger.info("💬 [CHAT_CHANNEL] 用户聊天大厅状态跟踪成功 - 用户: #{user_id}")
        {:ok, socket}

      {:error, reason} ->
        Logger.error("💬 [CHAT_CHANNEL] 用户聊天大厅状态跟踪失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        {:error, %{reason: "presence_tracking_failed"}}
    end
  end

  def join(_topic, _payload, _socket) do
    {:error, %{reason: "invalid_topic"}}
  end

  # 处理发送消息
  @impl true
  def handle_in("send_message", %{"content" => content}, socket) do
    user_id = socket.assigns[:user_id]
    session_id = socket.assigns[:session_id]

    Logger.info(
      "💬 [CHAT_CHANNEL] 收到消息 - 用户: #{user_id}, 会话: #{session_id}, 内容: #{String.slice(content, 0, 50)}"
    )

    case ChatService.send_message(session_id, user_id, content) do
      {:ok, message} ->
        Logger.info("💬 [CHAT_CHANNEL] 消息发送成功 - ID: #{message.id}")

        # 加载消息的发送者信息
        message_with_sender =
          case Ash.load(message, [:sender]) do
            {:ok, loaded_message} -> loaded_message
            _ -> message
          end

        # 广播消息给会话中的所有用户
        broadcast(socket, "new_message", %{
          id: message.id,
          content: message.content,
          sender_id: message.sender_id,
          sender: message_with_sender.sender,
          session_id: message.session_id,
          inserted_at: message.inserted_at
        })

        # 通知LiveView更新
        Phoenix.PubSub.broadcast(
          Cypridina.PubSub,
          "chat_updates",
          {:new_message, message_with_sender}
        )

        {:reply, {:ok, %{message_id: message.id}}, socket}

      {:error, reason} ->
        Logger.error(
          "💬 [CHAT_CHANNEL] 消息发送失败 - 用户: #{user_id}, 会话: #{session_id}, 原因: #{inspect(reason)}"
        )

        {:reply, {:error, %{reason: inspect(reason)}}, socket}
    end
  end

  # 处理消息已读状态
  @impl true
  def handle_in("mark_as_read", %{"message_ids" => message_ids}, socket) do
    user_id = socket.assigns[:user_id]
    session_id = socket.assigns[:session_id]

    Logger.info(
      "💬 [CHAT_CHANNEL] 标记消息已读 - 用户: #{user_id}, 会话: #{session_id}, 消息数: #{length(message_ids)}"
    )

    case ChatService.mark_messages_as_read(message_ids, user_id) do
      {:ok, _} ->
        # 广播已读状态更新
        broadcast(socket, "messages_read", %{
          user_id: user_id,
          message_ids: message_ids,
          read_at: DateTime.utc_now()
        })

        {:reply, :ok, socket}

      {:error, reason} ->
        Logger.error("💬 [CHAT_CHANNEL] 标记消息已读失败 - 原因: #{inspect(reason)}")
        {:reply, {:error, %{reason: inspect(reason)}}, socket}
    end
  end

  # 处理正在输入状态
  @impl true
  def handle_in("typing", %{"typing" => typing}, socket) do
    user_id = socket.assigns[:user_id]
    session_id = socket.assigns[:session_id]

    # 广播正在输入状态（不包括发送者）
    broadcast_from(socket, "user_typing", %{
      user_id: user_id,
      session_id: session_id,
      typing: typing,
      timestamp: DateTime.utc_now()
    })

    {:noreply, socket}
  end

  # 处理用户离开
  @impl true
  def terminate(reason, socket) do
    user_id = socket.assigns[:user_id]
    session_id = socket.assigns[:session_id]

    Logger.info(
      "💬 [CHAT_CHANNEL] 用户离开聊天 - 用户: #{user_id}, 会话: #{session_id}, 原因: #{inspect(reason)}"
    )

    # 停止用户状态跟踪
    case UserPresence.untrack_user(self(), user_id) do
      :ok ->
        Logger.info("💬 [CHAT_CHANNEL] 用户状态跟踪已停止 - 用户: #{user_id}")

      {:error, reason} ->
        Logger.error("💬 [CHAT_CHANNEL] 停止用户状态跟踪失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
    end

    # 广播用户离开事件
    if session_id do
      broadcast_from(socket, "user_left", %{
        user_id: user_id,
        session_id: session_id,
        timestamp: DateTime.utc_now()
      })
    end

    :ok
  end

  # 私有辅助函数

  defp verify_session_access(session_id, user_id) do
    case ChatService.get_session_with_participants(session_id) do
      {:ok, session} ->
        # 检查用户是否是会话参与者
        is_participant = Enum.any?(session.participants, &(&1.user_id == user_id))

        if is_participant do
          {:ok, session}
        else
          {:error, :not_participant}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end
end
