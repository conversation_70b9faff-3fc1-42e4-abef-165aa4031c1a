defmodule CypridinaWeb.AuthHelper do
  @moduledoc """
  统一的身份验证和权限检查助手模块

  提供统一的接口用于：
  - 用户身份验证
  - 管理员权限检查
  - 代理权限检查
  - 会话管理

  这个模块被LiveUserAuth和AdminAuthPlug共同使用，确保权限检查逻辑的一致性。
  """

  alias Cypridina.Accounts.{User}
  alias Cypridina.Accounts
  require Logger

  @doc """
  从连接或会话中获取当前用户

  ## 参数
  - `conn_or_session`: Plug.Conn 或 session map

  ## 返回值
  - `{:ok, user}`: 成功获取用户
  - `{:error, :not_authenticated}`: 用户未认证
  """
  def get_current_user(%Plug.Conn{} = conn) do
    case AshAuthentication.Plug.Helpers.retrieve_from_session(conn, :user) do
      {:ok, user} -> {:ok, user}
      _ -> {:error, :not_authenticated}
    end
  end

  def get_current_user(user) when is_struct(user, User) do
    {:ok, user}
  end

  def get_current_user(nil) do
    {:error, :not_authenticated}
  end

  def get_current_user(_) do
    {:error, :not_authenticated}
  end

  @doc """
  检查管理员访问权限

  ## 参数
  - `user_or_conn`: 用户结构体或连接

  ## 返回值
  - `{:ok, user, role}`: 有权限访问，返回用户和角色
  - `{:error, :not_authenticated}`: 用户未认证
  - `{:error, :insufficient_permissions}`: 权限不足
  """
  def check_admin_access(user_or_conn) do
    case get_current_user(user_or_conn) do
      {:ok, user} ->
        if Accounts.is_admin?(user) do
          role = get_user_role(user)
          {:ok, user, role}
        else
          {:error, :insufficient_permissions}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查代理访问权限

  ## 参数
  - `user_or_conn`: 用户结构体或连接

  ## 返回值
  - `{:ok, user, role}`: 有权限访问，返回用户和角色
  - `{:error, :not_authenticated}`: 用户未认证
  - `{:error, :insufficient_permissions}`: 权限不足
  """
  def check_agent_access(user_or_conn) do
    case get_current_user(user_or_conn) do
      {:ok, user} ->
        if Accounts.is_agent?(user) do
          role = get_user_role(user)
          {:ok, user, role}
        else
          {:error, :insufficient_permissions}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查管理员或代理访问权限（用于后台管理）
  现在允许所有已登录用户访问，但权限不同

  ## 参数
  - `user_or_conn`: 用户结构体或连接

  ## 返回值
  - `{:ok, user, role}`: 有权限访问，返回用户和角色
  - `{:error, :not_authenticated}`: 用户未认证
  """
  def check_admin_or_agent_access(user_or_conn) do
    case get_current_user(user_or_conn) do
      {:ok, user} ->
        # 所有已登录用户都可以访问管理后台，但权限不同
        role = get_user_role(user)
        {:ok, user, role}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查用户是否可以访问后台管理

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - `true`: 可以访问（管理员或代理）
  - `false`: 不能访问
  """
  def can_access_admin?(user) do
    Accounts.is_admin?(user) or Accounts.is_agent?(user)
  end

  @doc """
  获取用户的所有角色（支持多重身份）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 用户角色列表：`[:super_admin, :root_agent]` 等
  """
  def get_user_roles(user) do
    roles = []

    roles = if Accounts.is_super_admin?(user), do: [:super_admin | roles], else: roles

    roles =
      if Accounts.is_admin?(user) and not Accounts.is_super_admin?(user),
        do: [:admin | roles],
        else: roles

    roles = if Accounts.is_root_agent?(user), do: [:root_agent | roles], else: roles

    roles =
      if Accounts.is_agent?(user) and not Accounts.is_root_agent?(user),
        do: [:agent | roles],
        else: roles

    if roles == [], do: [:user], else: roles
  end

  @doc """
  获取用户的主要角色（最高权限角色）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 用户主要角色原子：`:super_admin`, `:admin`, `:root_agent`, `:agent`, `:user`
  """
  def get_user_role(user) do
    cond do
      Accounts.is_super_admin?(user) -> :super_admin
      Accounts.is_admin?(user) -> :admin
      Accounts.is_root_agent?(user) -> :root_agent
      Accounts.is_agent?(user) -> :agent
      true -> :user
    end
  end

  @doc """
  获取用户的复合角色描述（用于显示多重身份）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 复合角色描述，如 "超级管理员 + 根代理"
  """
  def get_user_composite_role(user) do
    roles = get_user_roles(user)

    role_names =
      Enum.map(roles, fn role ->
        case role do
          :super_admin -> "超级管理员"
          :admin -> "管理员"
          :root_agent -> "根代理"
          :agent -> "代理"
          :user -> "普通用户"
        end
      end)

    Enum.join(role_names, " + ")
  end

  @doc """
  检查用户是否有特定权限

  ## 参数
  - `user`: 用户结构体
  - `permission`: 权限名称（字符串或原子）

  ## 返回值
  - `true`: 有权限
  - `false`: 无权限
  """
  def has_permission?(user, permission) do
    case permission do
      :super_admin -> Accounts.is_super_admin?(user)
      "super_admin" -> Accounts.is_super_admin?(user)
      :admin -> Accounts.is_admin?(user)
      "admin" -> Accounts.is_admin?(user)
      :root_agent -> Accounts.is_root_agent?(user)
      "root_agent" -> Accounts.is_root_agent?(user)
      :agent -> Accounts.is_agent?(user)
      "agent" -> Accounts.is_agent?(user)
      :admin_or_agent -> can_access_admin?(user)
      "admin_or_agent" -> can_access_admin?(user)
      _ -> false
    end
  end

  @doc """
  检查用户是否可以管理指定的目标用户（管理员权限覆盖代理权限）

  ## 参数
  - `user`: 当前用户
  - `target_user_id`: 目标用户ID

  ## 返回值
  - `true`: 可以管理
  - `false`: 不能管理

  ## 权限覆盖逻辑
  - 超级管理员：可以管理所有用户
  - 管理员：可以管理非管理员用户（管理员权限覆盖代理权限）
  - 代理：只有在不是管理员的情况下，才使用代理权限管理下级
  """
  def can_manage_user?(user, target_user_id) do
    cond do
      # 超级管理员可以管理所有用户
      Accounts.is_super_admin?(user) ->
        true

      # 管理员可以管理非管理员用户（管理员权限优先）
      Accounts.is_admin?(user) ->
        case User |> Ash.get(target_user_id) do
          {:ok, target_user} ->
            # 管理员不能管理其他管理员，但可以管理普通用户和代理
            not Accounts.is_admin?(target_user)

          _ ->
            false
        end

      # 只有在不是管理员的情况下，才使用代理权限
      Accounts.is_agent?(user) ->
        Accounts.can_manage_user?(user, target_user_id)

      # 普通用户不能管理其他用户
      true ->
        false
    end
  end

  @doc """
  记录权限检查日志

  ## 参数
  - `user`: 用户结构体（可选）
  - `action`: 操作名称
  - `result`: 检查结果
  - `details`: 额外详情（可选）
  """
  def log_permission_check(user, action, result, details \\ %{}) do
    log_data = %{
      user_id: user && user.id,
      username: user && user.username,
      user_role: user && get_user_role(user),
      action: action,
      result: result,
      details: details,
      timestamp: DateTime.utc_now()
    }

    case result do
      :granted ->
        Logger.info("Permission granted: #{inspect(log_data)}")

      :denied ->
        Logger.warning("Permission denied: #{inspect(log_data)}")

      :error ->
        Logger.error("Permission check error: #{inspect(log_data)}")
    end

    {:ok, log_data}
  end

  @doc """
  权限检查装饰器函数

  ## 参数
  - `user`: 用户结构体
  - `permission`: 权限名称
  - `fun`: 要执行的函数
  - `args`: 函数参数（可选）

  ## 返回值
  - 如果有权限，执行函数并返回结果
  - 如果无权限，返回 `{:error, :insufficient_permissions}`
  """
  def require_permission(user, permission, fun, args \\ []) do
    if has_permission?(user, permission) do
      log_permission_check(user, permission, :granted)

      case args do
        [] -> fun.()
        _ -> apply(fun, args)
      end
    else
      log_permission_check(user, permission, :denied)
      {:error, :insufficient_permissions}
    end
  end

  @doc """
  获取用户权限级别名称（用于显示）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 权限级别的中文名称
  """
  def get_permission_level_name(user) do
    case get_user_role(user) do
      :super_admin -> "超级管理员"
      :admin -> "管理员"
      :root_agent -> "根代理"
      :agent -> "代理"
      :user -> "普通用户"
    end
  end

  @doc """
  获取用户的所有身份（用于显示多重身份信息）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 身份列表：`[:admin, :root_agent]` 等
  """
  def get_user_identities(user) do
    identities = []

    identities =
      if Accounts.is_super_admin?(user), do: [:super_admin | identities], else: identities

    identities =
      if Accounts.is_admin?(user) and not Accounts.is_super_admin?(user),
        do: [:admin | identities],
        else: identities

    identities =
      if Accounts.is_root_agent?(user), do: [:root_agent | identities], else: identities

    identities =
      if Accounts.is_agent?(user) and not Accounts.is_root_agent?(user),
        do: [:agent | identities],
        else: identities

    if identities == [], do: [:user], else: identities
  end

  @doc """
  获取用户的完整身份描述（显示多重身份）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 完整身份描述，如 "管理员（同时是根代理）"
  """
  def get_full_identity_description(user) do
    primary_role = get_user_role(user)
    all_identities = get_user_identities(user)

    primary_name = get_permission_level_name(user)

    # 如果只有一个身份，直接返回
    if length(all_identities) == 1 do
      primary_name
    else
      # 获取其他身份
      other_identities = all_identities -- [primary_role]

      other_names =
        Enum.map(other_identities, fn identity ->
          case identity do
            :super_admin -> "超级管理员"
            :admin -> "管理员"
            :root_agent -> "根代理"
            :agent -> "代理"
            :user -> "普通用户"
          end
        end)

      if other_names != [] do
        "#{primary_name}（同时是#{Enum.join(other_names, "、")}）"
      else
        primary_name
      end
    end
  end

  @doc """
  检查用户是否具有有效的管理员权限（用于权限覆盖判断）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - `true`: 具有有效的管理员权限
  - `false`: 不具有管理员权限
  """
  def has_effective_admin_permission?(user) do
    Accounts.is_super_admin?(user) or Accounts.is_admin?(user)
  end

  @doc """
  检查用户是否具有有效的代理权限（考虑管理员权限覆盖）

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - `true`: 具有有效的代理权限
  - `false`: 不具有代理权限或被管理员权限覆盖
  """
  def has_effective_agent_permission?(user) do
    # 如果用户是管理员，管理员权限覆盖代理权限
    if has_effective_admin_permission?(user) do
      false
    else
      Accounts.is_agent?(user)
    end
  end

  @doc """
  获取用户的有效权限范围描述

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - 权限范围描述
  """
  def get_effective_permission_scope(user) do
    cond do
      Accounts.is_super_admin?(user) ->
        "全系统权限"

      Accounts.is_admin?(user) ->
        if Accounts.is_agent?(user) do
          "管理员权限（覆盖代理权限）"
        else
          "管理员权限"
        end

      Accounts.is_root_agent?(user) ->
        "根代理权限"

      Accounts.is_agent?(user) ->
        "代理权限"

      true ->
        "普通用户权限"
    end
  end

  @doc """
  检查权限冲突并返回解决方案

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - `{:ok, :no_conflict}`: 无权限冲突
  - `{:conflict, :admin_overrides_agent, details}`: 管理员权限覆盖代理权限
  """
  def check_permission_conflicts(user) do
    is_admin = Accounts.is_admin?(user)
    is_agent = Accounts.is_agent?(user)

    if is_admin and is_agent do
      {:conflict, :admin_overrides_agent,
       %{
         message: "用户同时具有管理员和代理身份，管理员权限将覆盖代理权限",
         effective_role: :admin,
         suppressed_role: :agent,
         recommendation: "用户将以管理员身份操作，代理相关功能将被管理员功能替代"
       }}
    else
      {:ok, :no_conflict}
    end
  end
end
