defmodule CypridinaWeb.ChatChannel do
  @moduledoc """
  聊天频道

  处理实时聊天功能，包括：
  - 加入聊天会话
  - 实时消息推送
  - 在线状态管理
  - 消息状态更新
  """

  use CypridinaWeb, :channel

  alias Cypridina.Chat.ChatService
  alias Cypridina.Chat.{ChatSession, ChatMessage, ChatParticipant}

  @impl true
  def join("chat_session:" <> session_id, _payload, socket) do
    user_id = socket.assigns.user_id

    # 验证用户是否有权限加入此会话
    case verify_session_access(session_id, user_id) do
      {:ok, session} ->
        # 订阅会话相关事件
        Phoenix.PubSub.subscribe(Cypridina.PubSub, "chat_session:#{session_id}")

        socket =
          socket
          |> assign(:session_id, session_id)
          |> assign(:session, session)

        # 广播用户上线
        broadcast_user_presence(socket, :join)

        # 批量标记消息为已送达
        ChatService.mark_session_messages_as_delivered(session_id, user_id)

        # 获取未读消息数
        {:ok, unread_count} = ChatService.get_unread_count(session_id, user_id)

        {:ok, %{session: format_session(session), unread_count: unread_count}, socket}

      {:error, reason} ->
        {:error, %{reason: reason}}
    end
  end

  def join("user_chat:" <> user_id, _payload, socket) do
    current_user_id = socket.assigns.user_id

    # 只允许用户加入自己的频道
    if current_user_id == user_id do
      {:ok, socket}
    else
      {:error, %{reason: "unauthorized"}}
    end
  end

  @impl true
  def handle_in("send_message", %{"content" => content} = payload, socket) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    message_type = Map.get(payload, "message_type", "text") |> String.to_atom()
    reply_to_id = Map.get(payload, "reply_to_id")
    attachments = Map.get(payload, "attachments", [])

    opts = [
      message_type: message_type,
      reply_to_id: reply_to_id,
      attachments: attachments
    ]

    case ChatService.send_message(session_id, user_id, content, opts) do
      {:ok, message} ->
        message = message |> Ash.load!([:sender, :reply_to])

        # 广播消息到会话中的所有用户
        broadcast!(socket, "new_message", %{message: format_message(message)})

        # 发送推送通知给离线用户
        send_push_notifications(session_id, message, user_id)

        {:reply, {:ok, %{message: format_message(message)}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("mark_message_read", %{"message_id" => message_id}, socket) do
    user_id = socket.assigns.user_id

    case ChatService.mark_message_as_read(message_id, user_id) do
      {:ok, _receipt} ->
        # 广播已读状态
        broadcast!(socket, "message_read", %{
          message_id: message_id,
          user_id: user_id,
          read_at: DateTime.utc_now()
        })

        {:reply, {:ok, %{status: "read"}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("mark_session_read", %{"up_to_message_id" => up_to_message_id}, socket) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    case ChatService.mark_session_messages_as_read(session_id, user_id, up_to_message_id) do
      {:ok, _} ->
        # 广播会话已读状态
        broadcast!(socket, "session_read", %{
          session_id: session_id,
          user_id: user_id,
          up_to_message_id: up_to_message_id,
          read_at: DateTime.utc_now()
        })

        {:reply, {:ok, %{status: "read"}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("mark_message_delivered", %{"message_id" => message_id}, socket) do
    user_id = socket.assigns.user_id

    case ChatService.mark_message_as_delivered(message_id, user_id) do
      {:ok, _receipt} ->
        # 广播已送达状态
        broadcast!(socket, "message_delivered", %{
          message_id: message_id,
          user_id: user_id,
          delivered_at: DateTime.utc_now()
        })

        {:reply, {:ok, %{status: "delivered"}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("mark_session_delivered", %{"up_to_message_id" => up_to_message_id}, socket) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    case ChatService.mark_session_messages_as_delivered(session_id, user_id, up_to_message_id) do
      {:ok, _} ->
        # 广播会话已送达状态
        broadcast!(socket, "session_delivered", %{
          session_id: session_id,
          user_id: user_id,
          up_to_message_id: up_to_message_id,
          delivered_at: DateTime.utc_now()
        })

        {:reply, {:ok, %{status: "delivered"}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("get_unread_count", _payload, socket) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    case ChatService.get_unread_count(session_id, user_id) do
      {:ok, count} ->
        {:reply, {:ok, %{unread_count: count}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("typing_start", _payload, socket) do
    user_id = socket.assigns.user_id
    session_id = socket.assigns.session_id

    # 广播用户正在输入状态
    broadcast_from!(socket, "user_typing", %{
      user_id: user_id,
      session_id: session_id,
      typing: true
    })

    {:noreply, socket}
  end

  def handle_in("typing_stop", _payload, socket) do
    user_id = socket.assigns.user_id
    session_id = socket.assigns.session_id

    # 广播用户停止输入状态
    broadcast_from!(socket, "user_typing", %{
      user_id: user_id,
      session_id: session_id,
      typing: false
    })

    {:noreply, socket}
  end

  def handle_in("get_session_info", _payload, socket) do
    session = socket.assigns.session

    {:reply, {:ok, %{session: format_session(session)}}, socket}
  end

  def handle_in("get_online_users", _payload, socket) do
    session_id = socket.assigns.session_id

    # 获取在线用户列表
    online_users = get_online_users_in_session(session_id)

    {:reply, {:ok, %{online_users: online_users}}, socket}
  end

  def handle_in(
        "upload_file",
        %{"base64_data" => base64_data, "filename" => filename} = payload,
        socket
      ) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    caption = Map.get(payload, "caption", "")
    reply_to_id = Map.get(payload, "reply_to_id")

    case ChatService.upload_chat_file_from_base64(session_id, user_id, base64_data, filename) do
      {:ok, file_info} ->
        # 根据文件类型确定消息类型
        message_type =
          case file_info.file_type do
            :image -> :image
            :audio -> :audio
            :video -> :video
            _ -> :file
          end

        # 构建附件信息
        attachments = [
          %{
            file_name: file_info.original_name,
            file_size: file_info.file_size,
            file_type: file_info.file_type,
            file_url: file_info.file_url,
            thumb_url: file_info.thumb_url,
            mime_type: file_info.mime_type
          }
        ]

        # 发送消息
        case ChatService.send_message(session_id, user_id, caption,
               message_type: message_type,
               reply_to_id: reply_to_id,
               attachments: attachments
             ) do
          {:ok, message} ->
            message = message |> Ash.load!([:sender, :reply_to])

            # 广播消息到会话中的所有用户
            broadcast!(socket, "new_message", %{message: format_message(message)})

            # 发送推送通知给离线用户
            send_push_notifications(session_id, message, user_id)

            {:reply, {:ok, %{message: format_message(message)}}, socket}

          {:error, reason} ->
            {:reply, {:error, %{reason: format_error(reason)}}, socket}
        end

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in("generate_upload_url", %{"filename" => filename} = payload, socket) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    content_type = Map.get(payload, "content_type", "application/octet-stream")

    case ChatService.generate_chat_file_upload_url(session_id, user_id, filename, content_type) do
      {:ok, upload_url, file_path} ->
        {:reply,
         {:ok,
          %{
            upload_url: upload_url,
            file_path: file_path,
            expires_in: 3600
          }}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  def handle_in(
        "confirm_upload",
        %{"file_path" => file_path, "filename" => filename} = payload,
        socket
      ) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns.user_id

    file_size = Map.get(payload, "file_size", 0)
    caption = Map.get(payload, "caption", "")
    reply_to_id = Map.get(payload, "reply_to_id")

    # 构建文件信息
    file_info = %{
      filename: Path.basename(file_path),
      original_name: filename,
      file_size: file_size,
      file_type: determine_file_type_from_extension(filename),
      file_url: build_file_url(file_path),
      # 预签名上传暂不支持缩略图
      thumb_url: nil,
      mime_type: MIME.from_path(filename)
    }

    # 根据文件类型确定消息类型
    message_type =
      case file_info.file_type do
        :image -> :image
        :audio -> :audio
        :video -> :video
        _ -> :file
      end

    # 构建附件信息
    attachments = [
      %{
        file_name: file_info.original_name,
        file_size: file_info.file_size,
        file_type: file_info.file_type,
        file_url: file_info.file_url,
        thumb_url: file_info.thumb_url,
        mime_type: file_info.mime_type
      }
    ]

    # 发送消息
    case ChatService.send_message(session_id, user_id, caption,
           message_type: message_type,
           reply_to_id: reply_to_id,
           attachments: attachments
         ) do
      {:ok, message} ->
        message = message |> Ash.load!([:sender, :reply_to])

        # 广播消息到会话中的所有用户
        broadcast!(socket, "new_message", %{message: format_message(message)})

        # 发送推送通知给离线用户
        send_push_notifications(session_id, message, user_id)

        {:reply, {:ok, %{message: format_message(message)}}, socket}

      {:error, reason} ->
        {:reply, {:error, %{reason: format_error(reason)}}, socket}
    end
  end

  @impl true
  def handle_info({:new_message, message}, socket) do
    # 转发消息到客户端
    push(socket, "new_message", %{message: format_message(message)})
    {:noreply, socket}
  end

  def handle_info({:message_read, data}, socket) do
    # 转发已读状态到客户端
    push(socket, "message_read", data)
    {:noreply, socket}
  end

  def handle_info({:session_read, data}, socket) do
    # 转发会话已读状态到客户端
    push(socket, "session_read", data)
    {:noreply, socket}
  end

  def handle_info({:message_delivered, data}, socket) do
    # 转发消息已送达状态到客户端
    push(socket, "message_delivered", data)
    {:noreply, socket}
  end

  def handle_info({:session_delivered, data}, socket) do
    # 转发会话已送达状态到客户端
    push(socket, "session_delivered", data)
    {:noreply, socket}
  end

  def handle_info({:user_joined, user_data}, socket) do
    # 转发用户加入事件到客户端
    push(socket, "user_joined", user_data)
    {:noreply, socket}
  end

  def handle_info({:user_left, user_data}, socket) do
    # 转发用户离开事件到客户端
    push(socket, "user_left", user_data)
    {:noreply, socket}
  end

  @impl true
  def terminate(reason, socket) do
    # 广播用户下线
    broadcast_user_presence(socket, :leave)
    :ok
  end

  # 私有函数

  defp verify_session_access(session_id, user_id) do
    case ChatSession
         |> Ash.Query.for_read(:get_session_with_participants)
         |> Ash.get(session_id) do
      {:ok, session} ->
        # 检查用户是否在会话中
        participant =
          Enum.find(session.participants || [], fn p ->
            p.user_id == user_id and p.status == :active
          end)

        if participant do
          {:ok, session}
        else
          {:error, "not_participant"}
        end

      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "session_not_found"}

      {:error, reason} ->
        {:error, format_error(reason)}
    end
  end

  defp broadcast_user_presence(socket, action) do
    user_id = socket.assigns.user_id
    session_id = socket.assigns.session_id

    event_data = %{
      user_id: user_id,
      session_id: session_id,
      action: action,
      timestamp: DateTime.utc_now()
    }

    case action do
      :join ->
        broadcast_from!(socket, "user_joined", event_data)

      :leave ->
        broadcast_from!(socket, "user_left", event_data)
    end
  end

  defp get_online_users_in_session(session_id) do
    # 通过 Phoenix.Presence 获取在线用户
    # 这里需要实现具体的在线用户获取逻辑
    []
  end

  defp send_push_notifications(session_id, message, sender_id) do
    # 获取会话中的其他参与者
    case ChatParticipant
         |> Ash.Query.for_read(:list_by_session, %{session_id: session_id})
         |> Ash.read() do
      {:ok, participants} ->
        # 过滤掉发送者和静音用户
        target_users =
          Enum.filter(participants, fn p ->
            p.user_id != sender_id and not p.is_muted
          end)

        # 发送推送通知（这里需要实现具体的推送逻辑）
        Enum.each(target_users, fn participant ->
          send_push_notification(participant.user_id, message)
        end)

      _ ->
        :ok
    end
  end

  defp send_push_notification(user_id, message) do
    # 实现推送通知逻辑
    # 可以集成 FCM、APNs 等推送服务
    :ok
  end

  defp format_session(session) do
    %{
      id: session.id,
      session_type: session.session_type,
      title: session.title,
      description: session.description,
      status: session.status,
      creator_id: session.creator_id,
      last_message_at: session.last_message_at,
      message_count: session.message_count,
      participants: Enum.map(session.participants || [], &format_participant/1),
      inserted_at: session.inserted_at,
      updated_at: session.updated_at
    }
  end

  defp format_message(message) do
    # 加载送达和已读回执信息
    message = 
      message
      |> Ash.load!([:delivery_receipts, :read_receipts])

    %{
      id: message.id,
      session_id: message.session_id,
      sender_id: message.sender_id,
      sender: if(message.sender, do: format_user(message.sender)),
      message_type: message.message_type,
      content: message.content,
      attachments: message.attachments,
      reply_to_id: message.reply_to_id,
      reply_to: if(message.reply_to, do: format_message(message.reply_to)),
      status: message.status,
      delivery_receipts: Enum.map(message.delivery_receipts || [], &format_receipt/1),
      read_receipts: Enum.map(message.read_receipts || [], &format_receipt/1),
      metadata: message.metadata,
      edited_at: message.edited_at,
      recalled_at: message.recalled_at,
      inserted_at: message.inserted_at,
      updated_at: message.updated_at
    }
  end

  defp format_participant(participant) do
    %{
      id: participant.id,
      user_id: participant.user_id,
      user: if(participant.user, do: format_user(participant.user)),
      role: participant.role,
      status: participant.status,
      joined_at: participant.joined_at,
      last_read_at: participant.last_read_at,
      is_muted: participant.is_muted
    }
  end

  defp format_user(user) do
    %{
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar
    }
  end

  defp format_receipt(receipt) do
    %{
      user_id: receipt.user_id,
      timestamp: Map.get(receipt, :delivered_at) || Map.get(receipt, :read_at)
    }
  end

  defp format_error(reason) when is_binary(reason), do: reason
  defp format_error(reason) when is_atom(reason), do: Atom.to_string(reason)
  defp format_error(%{__exception__: true} = error), do: Exception.message(error)
  defp format_error(reason), do: inspect(reason)

  # 根据文件扩展名确定文件类型
  defp determine_file_type_from_extension(filename) do
    extension = filename |> Path.extname() |> String.downcase()

    cond do
      extension in ~w(.jpg .jpeg .png .gif .webp .bmp) -> :image
      extension in ~w(.mp3 .wav .aac .ogg .m4a) -> :audio
      extension in ~w(.mp4 .avi .mov .wmv .flv .webm) -> :video
      extension in ~w(.pdf .doc .docx .xls .xlsx .ppt .pptx .txt .rtf) -> :document
      extension in ~w(.zip .rar .7z .tar .gz) -> :archive
      true -> :other
    end
  end

  # 构建文件URL
  defp build_file_url(file_path) do
    bucket = Application.get_env(:waffle, :bucket, "cypridina")
    asset_host = Application.get_env(:waffle, :asset_host, "http://localhost:9000/#{bucket}")
    "#{asset_host}/#{file_path}"
  end
end
