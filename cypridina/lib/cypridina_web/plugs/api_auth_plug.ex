defmodule CypridinaWeb.Plugs.ApiAuthPlug do
  @moduledoc """
  API权限控制插件

  用于API路由的身份验证和权限检查
  使用统一的 AuthHelper 模块进行权限检查
  """

  import Plug.Conn
  import Phoenix.Controller

  alias CypridinaWeb.AuthHelper
  require Logger

  def init(opts), do: opts

  # API用户认证检查
  def call(conn, :user_required) do
    case AuthHelper.get_current_user(conn) do
      {:ok, user} ->
        conn
        |> assign(:current_user, user)
        |> assign(:user_role, AuthHelper.get_user_role(user))

      {:error, :not_authenticated} ->
        Logger.info("API request not authenticated")

        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        |> halt()
    end
  end

  # API管理员权限检查
  def call(conn, :admin_required) do
    case AuthHelper.check_admin_access(conn) do
      {:ok, user, role} ->
        conn
        |> assign(:current_user, user)
        |> assign(:user_role, role)

      {:error, :not_authenticated} ->
        Logger.info("API request not authenticated")

        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        |> halt()

      {:error, :insufficient_permissions} ->
        Logger.warning("API request insufficient permissions")

        conn
        |> put_status(:forbidden)
        |> json(%{error: "Admin access required"})
        |> halt()
    end
  end

  # API代理权限检查
  def call(conn, :agent_required) do
    case AuthHelper.check_agent_access(conn) do
      {:ok, user, role} ->
        conn
        |> assign(:current_user, user)
        |> assign(:user_role, role)

      {:error, :not_authenticated} ->
        Logger.info("API request not authenticated")

        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        |> halt()

      {:error, :insufficient_permissions} ->
        Logger.warning("API request insufficient permissions")

        conn
        |> put_status(:forbidden)
        |> json(%{error: "Agent access required"})
        |> halt()
    end
  end

  # API管理员或代理权限检查
  def call(conn, :admin_or_agent_required) do
    case AuthHelper.check_admin_or_agent_access(conn) do
      {:ok, user, role} ->
        conn
        |> assign(:current_user, user)
        |> assign(:user_role, role)

      {:error, :not_authenticated} ->
        Logger.info("API request not authenticated")

        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        |> halt()

      {:error, :insufficient_permissions} ->
        Logger.warning("API request insufficient permissions")

        conn
        |> put_status(:forbidden)
        |> json(%{error: "Admin or agent access required"})
        |> halt()
    end
  end

  # 可选的用户认证检查（不强制要求）
  def call(conn, :user_optional) do
    case AuthHelper.get_current_user(conn) do
      {:ok, user} ->
        conn
        |> assign(:current_user, user)
        |> assign(:user_role, AuthHelper.get_user_role(user))

      {:error, :not_authenticated} ->
        conn
        |> assign(:current_user, nil)
        |> assign(:user_role, :guest)
    end
  end

  # 默认调用（用户认证检查）
  def call(conn, _opts) do
    call(conn, :user_required)
  end
end
