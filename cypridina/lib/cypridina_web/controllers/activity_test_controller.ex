defmodule CypridinaWeb.ActivityTestController do
  @moduledoc """
  活动系统测试控制器

  提供简单的测试页面用于测试活动系统功能
  """

  use CypridinaWeb, :controller

  def index(conn, _params) do
    render(conn, :index)
  end

  def test_protocol(
        conn,
        %{"main_protocol" => main_protocol, "sub_protocol" => sub_protocol} = params
      ) do
    user_id = Map.get(params, "user_id", "test_user_123")
    data = Map.get(params, "data", %{})

    case Teen.Protocol.ProtocolRouter.route_protocol(
           String.to_integer(main_protocol),
           String.to_integer(sub_protocol),
           data,
           user_id
         ) do
      {:ok, response_sub_protocol, response_data} ->
        json(conn, %{
          status: "success",
          request: %{
            main_protocol: main_protocol,
            sub_protocol: sub_protocol,
            user_id: user_id,
            data: data
          },
          response: %{
            main_protocol: main_protocol,
            sub_protocol: response_sub_protocol,
            data: response_data
          }
        })

      {:error, reason} ->
        json(conn, %{
          status: "error",
          reason: reason,
          request: %{
            main_protocol: main_protocol,
            sub_protocol: sub_protocol,
            user_id: user_id,
            data: data
          }
        })
    end
  end
end
