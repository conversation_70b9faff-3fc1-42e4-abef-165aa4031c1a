defmodule CypridinaWeb.ChatTestController do
  @moduledoc """
  聊天系统测试控制器

  提供简单的聊天功能测试页面
  """

  use CypridinaWeb, :controller

  alias Cypridina.Chat.ChatService
  alias Cypridina.Accounts

  def index(conn, _params) do
    # 获取所有用户用于测试
    case Accounts.list_users() do
      {:ok, users} ->
        render(conn, :index, users: users)

      _ ->
        render(conn, :index, users: [])
    end
  end

  def create_test_session(conn, %{"user1_id" => user1_id, "user2_id" => user2_id}) do
    case ChatService.create_or_get_private_session(user1_id, user2_id) do
      {:ok, session} ->
        conn
        |> put_flash(:info, "聊天会话创建成功！会话ID: #{session.id}")
        |> redirect(to: ~p"/chat_test")

      {:error, reason} ->
        conn
        |> put_flash(:error, "创建聊天会话失败: #{inspect(reason)}")
        |> redirect(to: ~p"/chat_test")
    end
  end

  def send_test_message(conn, %{
        "session_id" => session_id,
        "sender_id" => sender_id,
        "content" => content
      }) do
    case ChatService.send_message(session_id, sender_id, content) do
      {:ok, _message} ->
        conn
        |> put_flash(:info, "消息发送成功！")
        |> redirect(to: ~p"/chat_test")

      {:error, reason} ->
        conn
        |> put_flash(:error, "发送消息失败: #{inspect(reason)}")
        |> redirect(to: ~p"/chat_test")
    end
  end

  def list_sessions(conn, %{"user_id" => user_id}) do
    case ChatService.list_user_sessions(user_id) do
      {:ok, sessions} ->
        json(conn, %{
          success: true,
          sessions:
            Enum.map(sessions, fn session ->
              %{
                id: session.id,
                type: session.session_type,
                title: session.title,
                last_message_at: session.last_message_at,
                participant_count: length(session.participants || [])
              }
            end)
        })

      {:error, reason} ->
        json(conn, %{
          success: false,
          error: inspect(reason)
        })
    end
  end

  def list_messages(conn, %{"session_id" => session_id, "user_id" => user_id}) do
    case ChatService.list_session_messages(session_id, user_id) do
      {:ok, messages} ->
        json(conn, %{
          success: true,
          messages:
            Enum.map(messages, fn message ->
              %{
                id: message.id,
                sender_id: message.sender_id,
                content: message.content,
                message_type: message.message_type,
                attachments: message.attachments,
                inserted_at: message.inserted_at
              }
            end)
        })

      {:error, reason} ->
        json(conn, %{
          success: false,
          error: inspect(reason)
        })
    end
  end

  def upload_test_file(conn, %{"session_id" => session_id, "sender_id" => sender_id} = params) do
    case Map.get(params, "file") do
      %Plug.Upload{} = file ->
        caption = Map.get(params, "caption", "")

        case ChatService.send_file_message(session_id, sender_id, file, caption: caption) do
          {:ok, message} ->
            conn
            |> put_flash(:info, "文件上传并发送成功！消息ID: #{message.id}")
            |> redirect(to: ~p"/chat_test")

          {:error, reason} ->
            conn
            |> put_flash(:error, "文件上传失败: #{inspect(reason)}")
            |> redirect(to: ~p"/chat_test")
        end

      nil ->
        conn
        |> put_flash(:error, "请选择要上传的文件")
        |> redirect(to: ~p"/chat_test")
    end
  end
end
