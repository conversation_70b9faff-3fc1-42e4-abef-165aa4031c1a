defmodule CypridinaWeb.ChatController do
  @moduledoc """
  聊天API控制器

  提供聊天功能的HTTP API接口，包括：
  - 创建聊天会话
  - 发送消息
  - 获取聊天历史
  - 管理参与者
  """

  use CypridinaWeb, :controller

  alias Cypridina.Chat.ChatService
  alias Cypridina.Chat.{ChatSession, ChatMessage, ChatParticipant}

  action_fallback CypridinaWeb.FallbackController

  @doc """
  创建或获取一对一聊天会话
  POST /api/chat/sessions/private
  """
  def create_private_session(conn, %{"target_user_id" => target_user_id}) do
    current_user_id = get_current_user_id(conn)

    case ChatService.create_or_get_private_session(current_user_id, target_user_id) do
      {:ok, session} ->
        session = session |> Ash.load!([:participants, :last_message])

        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: format_session(session)
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  创建群聊会话
  POST /api/chat/sessions/group
  """
  def create_group_session(
        conn,
        %{"participant_ids" => participant_ids, "title" => title} = params
      ) do
    current_user_id = get_current_user_id(conn)
    description = Map.get(params, "description")

    case ChatService.create_group_session(current_user_id, participant_ids, title, description) do
      {:ok, session} ->
        session = session |> Ash.load!([:participants, :last_message])

        conn
        |> put_status(:created)
        |> json(%{
          success: true,
          data: format_session(session)
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  获取用户的聊天会话列表
  GET /api/chat/sessions
  """
  def list_sessions(conn, params) do
    current_user_id = get_current_user_id(conn)
    limit = Map.get(params, "limit", "20") |> String.to_integer()
    offset = Map.get(params, "offset", "0") |> String.to_integer()

    case ChatService.list_user_sessions(current_user_id, limit: limit, offset: offset) do
      {:ok, sessions} ->
        conn
        |> json(%{
          success: true,
          data: Enum.map(sessions, &format_session/1)
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  获取会话详情
  GET /api/chat/sessions/:id
  """
  def get_chat_session(conn, %{"id" => session_id}) do
    current_user_id = get_current_user_id(conn)

    case ChatSession
         |> Ash.Query.for_read(:get_session_with_participants)
         |> Ash.get(session_id) do
      {:ok, session} ->
        # 验证用户是否在会话中
        if user_in_session?(session, current_user_id) do
          conn
          |> json(%{
            success: true,
            data: format_session_detail(session)
          })
        else
          conn
          |> put_status(:forbidden)
          |> json(%{
            success: false,
            error: "无权访问此会话"
          })
        end

      {:error, %Ash.Error.Query.NotFound{}} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          error: "会话不存在"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  发送消息
  POST /api/chat/sessions/:id/messages
  """
  def send_message(conn, %{"id" => session_id, "content" => content} = params) do
    current_user_id = get_current_user_id(conn)

    opts = [
      message_type: Map.get(params, "message_type", "text") |> String.to_atom(),
      reply_to_id: Map.get(params, "reply_to_id"),
      attachments: Map.get(params, "attachments", [])
    ]

    case ChatService.send_message(session_id, current_user_id, content, opts) do
      {:ok, message} ->
        message = message |> Ash.load!([:sender, :reply_to])

        conn
        |> put_status(:created)
        |> json(%{
          success: true,
          data: format_message(message)
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  获取会话消息列表
  GET /api/chat/sessions/:id/messages
  """
  def list_messages(conn, %{"id" => session_id} = params) do
    current_user_id = get_current_user_id(conn)
    limit = Map.get(params, "limit", "50") |> String.to_integer()
    offset = Map.get(params, "offset", "0") |> String.to_integer()

    case ChatService.list_session_messages(session_id, current_user_id,
           limit: limit,
           offset: offset
         ) do
      {:ok, messages} ->
        conn
        |> json(%{
          success: true,
          data: Enum.map(messages, &format_message/1)
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  标记消息为已读
  POST /api/chat/messages/:id/read
  """
  def mark_message_read(conn, %{"id" => message_id}) do
    current_user_id = get_current_user_id(conn)

    case ChatService.mark_message_as_read(message_id, current_user_id) do
      {:ok, _receipt} ->
        conn
        |> json(%{
          success: true,
          message: "消息已标记为已读"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  标记会话消息为已读
  POST /api/chat/sessions/:id/read
  """
  def mark_session_read(conn, %{"id" => session_id} = params) do
    current_user_id = get_current_user_id(conn)
    up_to_message_id = Map.get(params, "up_to_message_id")

    case ChatService.mark_session_messages_as_read(session_id, current_user_id, up_to_message_id) do
      {:ok, _} ->
        conn
        |> json(%{
          success: true,
          message: "会话消息已标记为已读"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  添加参与者到会话
  POST /api/chat/sessions/:id/participants
  """
  def add_participant(conn, %{"id" => session_id, "user_id" => user_id} = params) do
    current_user_id = get_current_user_id(conn)
    role = Map.get(params, "role", "member") |> String.to_atom()

    case ChatService.add_participant_to_session(session_id, user_id, current_user_id, role) do
      {:ok, _participant} ->
        conn
        |> put_status(:created)
        |> json(%{
          success: true,
          message: "参与者添加成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  从会话中移除参与者
  DELETE /api/chat/sessions/:id/participants/:user_id
  """
  def remove_participant(conn, %{"id" => session_id, "user_id" => user_id}) do
    current_user_id = get_current_user_id(conn)

    case ChatService.remove_participant_from_session(session_id, user_id, current_user_id) do
      {:ok, _} ->
        conn
        |> json(%{
          success: true,
          message: "参与者移除成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  获取未读消息数量
  GET /api/chat/unread_count
  """
  def get_unread_count(conn, params) do
    current_user_id = get_current_user_id(conn)
    session_id = Map.get(params, "session_id")

    case ChatService.get_unread_message_count(current_user_id, session_id) do
      {:ok, count} ->
        conn
        |> json(%{
          success: true,
          data: %{unread_count: count}
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  @doc """
  上传聊天文件
  POST /api/chat/sessions/:id/upload
  """
  def upload_file(conn, %{"id" => session_id} = params) do
    current_user_id = get_current_user_id(conn)

    case Map.get(params, "file") do
      %Plug.Upload{} = file ->
        # 文件上传
        upload_and_send_file_message(conn, session_id, current_user_id, file, params)

      nil ->
        # 检查是否为Base64上传
        case Map.get(params, "base64_data") do
          base64_data when is_binary(base64_data) ->
            filename = Map.get(params, "filename", "file")

            upload_and_send_base64_message(
              conn,
              session_id,
              current_user_id,
              base64_data,
              filename,
              params
            )

          _ ->
            conn
            |> put_status(:bad_request)
            |> json(%{
              success: false,
              error: "缺少文件或base64数据"
            })
        end
    end
  end

  @doc """
  生成文件上传预签名URL
  POST /api/chat/sessions/:id/upload_url
  """
  def generate_upload_url(conn, %{"id" => session_id} = params) do
    current_user_id = get_current_user_id(conn)
    filename = Map.get(params, "filename")
    content_type = Map.get(params, "content_type", "application/octet-stream")

    if filename do
      case ChatService.generate_chat_file_upload_url(
             session_id,
             current_user_id,
             filename,
             content_type
           ) do
        {:ok, upload_url, file_path} ->
          conn
          |> json(%{
            success: true,
            data: %{
              upload_url: upload_url,
              file_path: file_path,
              expires_in: 3600
            }
          })

        {:error, reason} ->
          conn
          |> put_status(:bad_request)
          |> json(%{
            success: false,
            error: format_error(reason)
          })
      end
    else
      conn
      |> put_status(:bad_request)
      |> json(%{
        success: false,
        error: "缺少文件名"
      })
    end
  end

  @doc """
  确认文件上传完成并发送消息
  POST /api/chat/sessions/:id/confirm_upload
  """
  def confirm_upload(conn, %{"id" => session_id} = params) do
    current_user_id = get_current_user_id(conn)
    file_path = Map.get(params, "file_path")
    filename = Map.get(params, "filename")
    file_size = Map.get(params, "file_size", 0)
    caption = Map.get(params, "caption", "")
    reply_to_id = Map.get(params, "reply_to_id")

    if file_path && filename do
      # 构建文件信息
      file_info = %{
        filename: Path.basename(file_path),
        original_name: filename,
        file_size: file_size,
        file_type: determine_file_type_from_extension(filename),
        file_url: build_file_url(file_path),
        # 预签名上传暂不支持缩略图
        thumb_url: nil,
        mime_type: MIME.from_path(filename)
      }

      # 根据文件类型确定消息类型
      message_type =
        case file_info.file_type do
          :image -> :image
          :audio -> :audio
          :video -> :video
          _ -> :file
        end

      # 构建附件信息
      attachments = [
        %{
          file_name: file_info.original_name,
          file_size: file_info.file_size,
          file_type: file_info.file_type,
          file_url: file_info.file_url,
          thumb_url: file_info.thumb_url,
          mime_type: file_info.mime_type
        }
      ]

      # 发送消息
      case ChatService.send_message(session_id, current_user_id, caption,
             message_type: message_type,
             reply_to_id: reply_to_id,
             attachments: attachments
           ) do
        {:ok, message} ->
          message = message |> Ash.load!([:sender, :reply_to])

          conn
          |> put_status(:created)
          |> json(%{
            success: true,
            data: format_message(message)
          })

        {:error, reason} ->
          conn
          |> put_status(:bad_request)
          |> json(%{
            success: false,
            error: format_error(reason)
          })
      end
    else
      conn
      |> put_status(:bad_request)
      |> json(%{
        success: false,
        error: "缺少文件路径或文件名"
      })
    end
  end

  # 私有函数

  defp get_current_user_id(conn) do
    # 从连接中获取当前用户ID
    # 这里需要根据实际的认证机制来实现
    conn.assigns[:current_user_id] ||
      Plug.Conn.get_session(conn, :user_id) ||
      raise "用户未登录"
  end

  defp user_in_session?(session, user_id) do
    Enum.any?(session.participants || [], fn participant ->
      participant.user_id == user_id and participant.status == :active
    end)
  end

  defp format_session(session) do
    %{
      id: session.id,
      session_type: session.session_type,
      title: session.title,
      description: session.description,
      status: session.status,
      creator_id: session.creator_id,
      last_message_at: session.last_message_at,
      message_count: session.message_count,
      participant_count: length(session.participants || []),
      participants: Enum.map(session.participants || [], &format_participant/1),
      last_message: if(session.last_message, do: format_message(session.last_message)),
      inserted_at: session.inserted_at,
      updated_at: session.updated_at
    }
  end

  defp format_session_detail(session) do
    session
    |> format_session()
    |> Map.put(:messages, Enum.map(session.messages || [], &format_message/1))
  end

  defp format_message(message) do
    %{
      id: message.id,
      session_id: message.session_id,
      sender_id: message.sender_id,
      sender: if(message.sender, do: format_user(message.sender)),
      message_type: message.message_type,
      content: message.content,
      attachments: message.attachments,
      reply_to_id: message.reply_to_id,
      reply_to: if(message.reply_to, do: format_message(message.reply_to)),
      status: message.status,
      metadata: message.metadata,
      edited_at: message.edited_at,
      recalled_at: message.recalled_at,
      inserted_at: message.inserted_at,
      updated_at: message.updated_at
    }
  end

  defp format_participant(participant) do
    %{
      id: participant.id,
      user_id: participant.user_id,
      user: if(participant.user, do: format_user(participant.user)),
      role: participant.role,
      status: participant.status,
      joined_at: participant.joined_at,
      last_read_at: participant.last_read_at,
      is_muted: participant.is_muted
    }
  end

  defp format_user(user) do
    %{
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar
    }
  end

  defp format_error(reason) when is_binary(reason), do: reason
  defp format_error(reason) when is_atom(reason), do: Atom.to_string(reason)
  defp format_error(%{__exception__: true} = error), do: Exception.message(error)
  defp format_error(reason), do: inspect(reason)

  # 上传文件并发送消息
  defp upload_and_send_file_message(conn, session_id, user_id, file, params) do
    caption = Map.get(params, "caption", "")
    reply_to_id = Map.get(params, "reply_to_id")

    case ChatService.send_file_message(session_id, user_id, file,
           caption: caption,
           reply_to_id: reply_to_id
         ) do
      {:ok, message} ->
        message = message |> Ash.load!([:sender, :reply_to])

        conn
        |> put_status(:created)
        |> json(%{
          success: true,
          data: format_message(message)
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  # 上传Base64文件并发送消息
  defp upload_and_send_base64_message(conn, session_id, user_id, base64_data, filename, params) do
    caption = Map.get(params, "caption", "")
    reply_to_id = Map.get(params, "reply_to_id")

    case ChatService.upload_chat_file_from_base64(session_id, user_id, base64_data, filename) do
      {:ok, file_info} ->
        # 根据文件类型确定消息类型
        message_type =
          case file_info.file_type do
            :image -> :image
            :audio -> :audio
            :video -> :video
            _ -> :file
          end

        # 构建附件信息
        attachments = [
          %{
            file_name: file_info.original_name,
            file_size: file_info.file_size,
            file_type: file_info.file_type,
            file_url: file_info.file_url,
            thumb_url: file_info.thumb_url,
            mime_type: file_info.mime_type
          }
        ]

        # 发送消息
        case ChatService.send_message(session_id, user_id, caption,
               message_type: message_type,
               reply_to_id: reply_to_id,
               attachments: attachments
             ) do
          {:ok, message} ->
            message = message |> Ash.load!([:sender, :reply_to])

            conn
            |> put_status(:created)
            |> json(%{
              success: true,
              data: format_message(message)
            })

          {:error, reason} ->
            conn
            |> put_status(:bad_request)
            |> json(%{
              success: false,
              error: format_error(reason)
            })
        end

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: format_error(reason)
        })
    end
  end

  # 根据文件扩展名确定文件类型
  defp determine_file_type_from_extension(filename) do
    extension = filename |> Path.extname() |> String.downcase()

    cond do
      extension in ~w(.jpg .jpeg .png .gif .webp .bmp) -> :image
      extension in ~w(.mp3 .wav .aac .ogg .m4a) -> :audio
      extension in ~w(.mp4 .avi .mov .wmv .flv .webm) -> :video
      extension in ~w(.pdf .doc .docx .xls .xlsx .ppt .pptx .txt .rtf) -> :document
      extension in ~w(.zip .rar .7z .tar .gz) -> :archive
      true -> :other
    end
  end

  # 构建文件URL
  defp build_file_url(file_path) do
    bucket = Application.get_env(:waffle, :bucket, "cypridina")
    asset_host = Application.get_env(:waffle, :asset_host, "http://localhost:9000/#{bucket}")
    "#{asset_host}/#{file_path}"
  end
end
