defmodule CypridinaWeb.Admin.TestDataController do
  @moduledoc """
  测试数据管理控制器
  
  提供Web界面用于管理测试环境的活动数据
  包括数据清除、时间模拟等功能
  """

  use Cy<PERSON>ridinaWeb, :controller
  
  alias Teen.ActivitySystem.{TestDataManager, TestDataAuth}
  alias Teen.SystemSettings.AdminUser
  
  plug :check_environment
  plug :authenticate_admin
  plug :authorize_test_data_access

  def index(conn, _params) do
    render(conn, :index)
  end

  @doc """
  用户数据清除页面
  """
  def user_clear(conn, _params) do
    activity_types = get_activity_types()
    render(conn, :user_clear, activity_types: activity_types)
  end

  @doc """
  预览清除数据
  """
  def preview_clear(conn, %{"user_id" => user_id, "activity_types" => activity_types}) do
    activity_list = parse_activity_types(activity_types)
    
    case TestDataManager.preview_clear_data(user_id, activity_list) do
      {:ok, preview_data} ->
        json(conn, %{success: true, data: preview_data})
      
      {:error, reason} ->
        json(conn, %{success: false, error: reason})
    end
  end

  @doc """
  执行用户数据清除
  """
  def execute_clear(conn, params) do
    %{
      "user_id" => user_id,
      "activity_types" => activity_types,
      "confirmation_token" => token
    } = params
    
    activity_list = parse_activity_types(activity_types)
    admin_user = conn.assigns[:current_admin_user]
    operator = create_operator_info(conn, admin_user)
    
    with :ok <- TestDataAuth.verify_confirmation_token(admin_user.id, "clear_user_activity_data", token, %{user_id: user_id, activity_types: activity_list}),
         {:ok, result} <- TestDataManager.clear_user_activity_data(user_id, activity_list, operator) do
      
      json(conn, %{success: true, data: result})
    else
      {:error, reason} ->
        json(conn, %{success: false, error: reason})
    end
  end

  @doc """
  批量清除页面
  """
  def batch_clear(conn, _params) do
    activity_types = get_activity_types()
    render(conn, :batch_clear, activity_types: activity_types)
  end

  @doc """
  执行批量清除
  """
  def execute_batch_clear(conn, params) do
    %{
      "user_ids" => user_ids_str,
      "activity_types" => activity_types,
      "confirmation_token" => token
    } = params
    
    user_ids = String.split(user_ids_str, "\n") |> Enum.map(&String.trim/1) |> Enum.reject(&(&1 == ""))
    activity_list = parse_activity_types(activity_types)
    admin_user = conn.assigns[:current_admin_user]
    operator = create_operator_info(conn, admin_user)
    
    with :ok <- TestDataAuth.verify_confirmation_token(admin_user.id, "batch_clear_user_data", token, %{user_ids: user_ids, activity_types: activity_list}),
         {:ok, results} <- TestDataManager.batch_clear_user_data(user_ids, activity_list, operator) do
      
      json(conn, %{success: true, data: results})
    else
      {:error, reason} ->
        json(conn, %{success: false, error: reason})
    end
  end

  @doc """
  活动类型清除页面
  """
  def activity_clear(conn, _params) do
    activity_types = get_activity_types()
    render(conn, :activity_clear, activity_types: activity_types)
  end

  @doc """
  按活动类型清除
  """
  def execute_activity_clear(conn, params) do
    %{
      "activity_type" => activity_type,
      "confirmation_token" => token
    } = params
    
    admin_user = conn.assigns[:current_admin_user]
    operator = create_operator_info(conn, admin_user)
    
    with :ok <- TestDataAuth.verify_confirmation_token(admin_user.id, "clear_by_activity_type", token, %{activity_type: activity_type}),
         {:ok, results} <- TestDataManager.clear_by_activity_type(String.to_atom(activity_type), operator) do
      
      json(conn, %{success: true, data: results})
    else
      {:error, reason} ->
        json(conn, %{success: false, error: reason})
    end
  end

  @doc """
  时间模拟页面
  """
  def time_simulation(conn, _params) do
    render(conn, :time_simulation)
  end

  @doc """
  执行时间模拟
  """
  def execute_time_simulation(conn, params) do
    %{
      "user_id" => user_id,
      "days_forward" => days_str,
      "confirmation_token" => token
    } = params
    
    days_forward = String.to_integer(days_str)
    admin_user = conn.assigns[:current_admin_user]
    operator = create_operator_info(conn, admin_user)
    
    with :ok <- TestDataAuth.verify_confirmation_token(admin_user.id, "simulate_time_advance", token, %{user_id: user_id, days_forward: days_forward}),
         {:ok, result} <- TestDataManager.simulate_time_advance(user_id, days_forward, operator) do
      
      json(conn, %{success: true, data: result})
    else
      {:error, reason} ->
        json(conn, %{success: false, error: reason})
    end
  end

  @doc """
  获取操作确认信息
  """
  def get_confirmation(conn, params) do
    %{
      "operation_type" => operation_type,
      "params" => operation_params
    } = params
    
    admin_user = conn.assigns[:current_admin_user]
    
    with {:ok, confirmation} <- TestDataAuth.authorize_operation(admin_user, operation_type, operation_params) do
      token = TestDataAuth.generate_confirmation_token(admin_user.id, operation_type, operation_params)
      
      json(conn, %{
        success: true, 
        data: Map.put(confirmation, :confirmation_token, token)
      })
    else
      {:error, reason} ->
        json(conn, %{success: false, error: reason})
    end
  end

  # 私有函数

  defp check_environment(conn, _opts) do
    case Application.get_env(:cypridina, :environment, :prod) do
      env when env in [:dev, :test] ->
        conn
      
      _ ->
        conn
        |> put_flash(:error, "测试数据管理功能仅在开发和测试环境中可用")
        |> redirect(to: "/admin")
        |> halt()
    end
  end

  defp authenticate_admin(conn, _opts) do
    # 这里应该集成你们现有的管理员认证逻辑
    # 假设从session中获取管理员信息
    case get_session(conn, :admin_user_id) do
      nil ->
        conn
        |> put_flash(:error, "请先登录管理员账户")
        |> redirect(to: "/admin/login")
        |> halt()
      
      admin_user_id ->
        case AdminUser |> Ash.get(admin_user_id) do
          {:ok, admin_user} ->
            assign(conn, :current_admin_user, admin_user)
          
          {:error, _} ->
            conn
            |> put_flash(:error, "管理员账户无效")
            |> redirect(to: "/admin/login")
            |> halt()
        end
    end
  end

  defp authorize_test_data_access(conn, _opts) do
    admin_user = conn.assigns[:current_admin_user]
    
    case TestDataAuth.authorize_user(admin_user.id) do
      {:ok, _} ->
        conn
      
      {:error, reason} ->
        conn
        |> put_flash(:error, "权限不足: #{reason}")
        |> redirect(to: "/admin")
        |> halt()
    end
  end

  defp get_activity_types do
    [
      {"签到活动", "sign_in_activity"},
      {"周卡任务", "weekly_card"},
      {"游戏任务", "game_task"},
      {"七日任务", "seven_day_task"},
      {"VIP礼包", "vip_gift"},
      {"充值任务", "recharge_task"},
      {"刮刮卡", "scratch_card"},
      {"首充礼包", "first_recharge_gift"},
      {"损失返利", "loss_rebate_jar"},
      {"邀请提现", "invite_cash"},
      {"绑定奖励", "binding_reward"},
      {"免费任务", "free_bonus_task"},
      {"CDKey活动", "cdkey_activity"}
    ]
  end

  defp parse_activity_types(""), do: []
  defp parse_activity_types(activity_types) when is_binary(activity_types) do
    activity_types
    |> String.split(",")
    |> Enum.map(&String.trim/1)
    |> Enum.reject(&(&1 == ""))
    |> Enum.map(&String.to_atom/1)
  end
  defp parse_activity_types(activity_types) when is_list(activity_types), do: activity_types

  defp create_operator_info(conn, admin_user) do
    TestDataAuth.create_operator_info(
      admin_user,
      get_peer_data(conn).address |> :inet.ntoa() |> to_string(),
      get_req_header(conn, "user-agent") |> List.first() || "Unknown"
    )
  end
end