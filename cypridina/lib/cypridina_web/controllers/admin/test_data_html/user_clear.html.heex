<div class="user-clear-page">
  <div class="page-header">
    <h1>用户数据清除</h1>
    <.link navigate={~p"/admin/test-data"} class="btn btn-outline">返回首页</.link>
  </div>

  <div class="main-content">
    <!-- 用户选择区域 -->
    <div class="section">
      <h3>1. 选择用户</h3>
      <div class="form-group">
        <label for="user_id">用户ID或昵称</label>
        <input type="text" id="user_id" placeholder="输入用户ID或昵称" class="form-control">
        <button type="button" onclick="searchUser()" class="btn btn-info">搜索用户</button>
      </div>
      
      <div id="user_info" class="user-info" style="display: none;">
        <h4>用户信息</h4>
        <div class="info-grid">
          <div><strong>用户ID:</strong> <span id="display_user_id"></span></div>
          <div><strong>昵称:</strong> <span id="display_nickname"></span></div>
          <div><strong>注册时间:</strong> <span id="display_register_time"></span></div>
          <div><strong>VIP等级:</strong> <span id="display_vip_level"></span></div>
        </div>
      </div>
    </div>

    <!-- 活动类型选择 -->
    <div class="section">
      <h3>2. 选择活动类型</h3>
      <div class="activity-types">
        <label class="checkbox-group">
          <input type="checkbox" value="" checked onchange="toggleAllActivities(this)">
          <span class="checkmark"></span>
          全部活动类型
        </label>
        
        <%= for {name, value} <- @activity_types do %>
          <label class="checkbox-group">
            <input type="checkbox" name="activity_types" value={value} class="activity-checkbox">
            <span class="checkmark"></span>
            <%= name %>
          </label>
        <% end %>
      </div>
    </div>

    <!-- 预览区域 -->
    <div class="section">
      <h3>3. 数据预览</h3>
      <button type="button" onclick="previewData()" class="btn btn-primary" id="preview_btn" disabled>
        预览将要清除的数据
      </button>
      
      <div id="preview_result" class="preview-result" style="display: none;">
        <h4>预览结果</h4>
        <div class="preview-stats">
          <div class="stat-item">
            <span class="stat-label">活动参与记录:</span>
            <span class="stat-value" id="participations_count">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">活动记录:</span>
            <span class="stat-value" id="records_count">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">奖励记录:</span>
            <span class="stat-value" id="rewards_count">0</span>
          </div>
          <div class="stat-item total">
            <span class="stat-label">总计:</span>
            <span class="stat-value" id="total_count">0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认执行 -->
    <div class="section">
      <h3>4. 确认执行</h3>
      <div class="warning-box">
        <div class="warning-icon">⚠️</div>
        <div class="warning-content">
          <p><strong>警告：此操作不可撤销！</strong></p>
          <p>将清除指定用户的活动数据，包括参与记录、进度、奖励等信息。</p>
          <p>请确认用户ID和活动类型选择正确。</p>
        </div>
      </div>
      
      <button type="button" onclick="requestConfirmation()" class="btn btn-danger" id="execute_btn" disabled>
        获取确认令牌
      </button>
      
      <div id="confirmation_section" style="display: none;">
        <div class="confirmation-info">
          <h4>操作确认</h4>
          <div id="confirmation_details"></div>
          <div class="form-group">
            <label for="confirmation_token">确认令牌</label>
            <input type="text" id="confirmation_token" class="form-control" readonly>
            <small>此令牌已自动生成，点击执行按钮完成操作</small>
          </div>
          <button type="button" onclick="executeOperation()" class="btn btn-danger">
            确认执行清除操作
          </button>
        </div>
      </div>
    </div>

    <!-- 操作结果 -->
    <div id="result_section" class="section" style="display: none;">
      <h3>操作结果</h3>
      <div id="operation_result" class="result-box"></div>
    </div>
  </div>
</div>

<style>
.user-clear-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 20px;
}

.main-content {
  space-y: 30px;
}

.section {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.user-info {
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.activity-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.checkbox-group:hover {
  background-color: #f8f9fa;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
}

.preview-result {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.stat-item.total {
  font-weight: bold;
  background: #cce7ff;
}

.warning-box {
  display: flex;
  background: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.warning-icon {
  font-size: 24px;
  margin-right: 15px;
}

.warning-content p {
  margin: 5px 0;
}

.confirmation-info {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.result-box {
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.result-box.success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.result-box.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-weight: 500;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-outline {
  background: white;
  border: 1px solid #6c757d;
  color: #6c757d;
}
</style>

<script>
let selectedUserId = null;
let previewData = null;
let confirmationToken = null;

function searchUser() {
  const input = document.getElementById('user_id').value.trim();
  if (!input) {
    alert('请输入用户ID或昵称');
    return;
  }
  
  // 这里应该调用API搜索用户
  // 模拟用户数据
  setTimeout(() => {
    selectedUserId = input;
    document.getElementById('display_user_id').textContent = input;
    document.getElementById('display_nickname').textContent = 'TestUser_' + input.slice(-4);
    document.getElementById('display_register_time').textContent = '2024-01-01 10:00:00';
    document.getElementById('display_vip_level').textContent = 'VIP 1';
    
    document.getElementById('user_info').style.display = 'block';
    document.getElementById('preview_btn').disabled = false;
  }, 500);
}

function toggleAllActivities(checkbox) {
  const activityCheckboxes = document.querySelectorAll('.activity-checkbox');
  activityCheckboxes.forEach(cb => {
    cb.checked = checkbox.checked;
  });
}

function getSelectedActivityTypes() {
  const checkboxes = document.querySelectorAll('.activity-checkbox:checked');
  return Array.from(checkboxes).map(cb => cb.value);
}

function previewData() {
  if (!selectedUserId) {
    alert('请先选择用户');
    return;
  }
  
  const activityTypes = getSelectedActivityTypes();
  
  // 调用预览API
  fetch('/admin/test-data/preview-clear', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      user_id: selectedUserId,
      activity_types: activityTypes
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      previewData = data.data;
      displayPreviewResult(data.data);
      document.getElementById('execute_btn').disabled = false;
    } else {
      alert('预览失败: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('请求失败');
  });
}

function displayPreviewResult(data) {
  document.getElementById('participations_count').textContent = data.data_counts.participations;
  document.getElementById('records_count').textContent = data.data_counts.records;
  document.getElementById('rewards_count').textContent = data.data_counts.rewards;
  document.getElementById('total_count').textContent = data.total_records;
  
  document.getElementById('preview_result').style.display = 'block';
}

function requestConfirmation() {
  if (!previewData) {
    alert('请先进行数据预览');
    return;
  }
  
  const activityTypes = getSelectedActivityTypes();
  
  fetch('/admin/test-data/get-confirmation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      operation_type: 'clear_user_activity_data',
      params: {
        user_id: selectedUserId,
        activity_types: activityTypes
      }
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      confirmationToken = data.data.confirmation_token;
      displayConfirmationInfo(data.data);
    } else {
      alert('获取确认令牌失败: ' + data.error);
    }
  });
}

function displayConfirmationInfo(data) {
  const details = `
    <div class="confirmation-details">
      <p><strong>操作类型:</strong> ${data.operation_type}</p>
      <p><strong>影响用户:</strong> ${selectedUserId}</p>
      <p><strong>预计删除记录:</strong> ${previewData.total_records} 条</p>
      <div class="warnings">
        <h5>注意事项:</h5>
        <ul>
          ${data.warnings.map(warning => `<li>${warning}</li>`).join('')}
        </ul>
      </div>
    </div>
  `;
  
  document.getElementById('confirmation_details').innerHTML = details;
  document.getElementById('confirmation_token').value = confirmationToken;
  document.getElementById('confirmation_section').style.display = 'block';
}

function executeOperation() {
  if (!confirmationToken) {
    alert('确认令牌无效');
    return;
  }
  
  const activityTypes = getSelectedActivityTypes();
  
  fetch('/admin/test-data/execute-clear', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      user_id: selectedUserId,
      activity_types: activityTypes,
      confirmation_token: confirmationToken
    })
  })
  .then(response => response.json())
  .then(data => {
    displayOperationResult(data);
  })
  .catch(error => {
    console.error('Error:', error);
    displayOperationResult({success: false, error: '请求失败'});
  });
}

function displayOperationResult(data) {
  const resultBox = document.getElementById('operation_result');
  
  if (data.success) {
    resultBox.className = 'result-box success';
    resultBox.innerHTML = `
      <h4>操作成功完成</h4>
      <p>成功清除用户 ${data.data.user_id} 的活动数据</p>
      <p>删除记录统计：</p>
      <ul>
        <li>活动参与记录：${data.data.deleted_counts.participations} 条</li>
        <li>活动记录：${data.data.deleted_counts.records} 条</li>
        <li>奖励记录：${data.data.deleted_counts.rewards} 条</li>
        <li><strong>总计：${data.data.total_deleted} 条</strong></li>
      </ul>
    `;
  } else {
    resultBox.className = 'result-box error';
    resultBox.innerHTML = `
      <h4>操作失败</h4>
      <p>错误信息：${data.error}</p>
    `;
  }
  
  document.getElementById('result_section').style.display = 'block';
}
</script>