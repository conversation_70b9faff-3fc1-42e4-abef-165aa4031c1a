defmodule CypridinaWeb.Components.PointsHistoryComponent do
  @moduledoc """
  统一的积分流水组件
  支持PC宽屏弹窗显示，提供完整的积分变动历史查看功能
  """
  use CypridinaWeb, :live_component

  alias Cypridina.Ledger
  alias Cypridina.Utils.TimeHelper
  require Logger

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:points_history, [])
      |> assign(:points_history_loading, false)
      |> assign(:show_modal, false)
      |> assign(:current_page, 1)
      |> assign(:page_size, 20)
      |> assign(:total_count, 0)
      |> assign(:has_more, false)
      |> assign(:has_previous, false)
      |> assign(:show_admin_actions, Map.get(assigns, :show_admin_actions, false))

    {:ok, socket}
  end

  def handle_event("show_points_history", _params, socket) do
    user_id = socket.assigns.user_id

    # 同步加载积分流水数据
    socket =
      socket
      |> assign(:show_modal, true)
      |> assign(:points_history_loading, true)
      |> assign(:points_history, [])
      |> assign(:current_page, 1)
      |> assign(:total_count, 0)
      |> assign(:has_more, false)
      |> assign(:has_previous, false)
      |> load_points_history_sync(user_id, 1)

    {:noreply, socket}
  end

  def handle_event("hide_points_history", _params, socket) do
    {:noreply, assign(socket, :show_modal, false)}
  end

  def handle_event("stop_propagation", _params, socket) do
    # 阻止事件冒泡，不做任何操作
    {:noreply, socket}
  end

  def handle_event("load_page", %{"page" => page_str}, socket) do
    user_id = socket.assigns.user_id
    page = String.to_integer(page_str)

    # 同步加载指定页数据
    socket =
      socket
      |> assign(:points_history_loading, true)
      |> load_points_history_sync(user_id, page)

    {:noreply, socket}
  end

  def handle_event("reverse_transfer", %{"transfer_id" => transfer_id}, socket) do
    current_user = socket.assigns.current_user

    case Cypridina.Ledger.reverse_transfer_with_game_items(
           transfer_id,
           current_user.id,
           "管理员撤销转账"
         ) do
      {:ok, result} ->
        # 构建成功消息
        success_message = build_reverse_success_message(result)
        send(self(), {:flash, :info, success_message})

        # 重新加载积分历史
        user_id = socket.assigns.user_id
        socket = load_points_history_sync(socket, user_id, socket.assigns.current_page)

        {:noreply, socket}

      {:error, error} ->
        error_message =
          case error do
            %Ash.Error.Invalid{} -> "撤销失败，请检查转账状态"
            _ -> "撤销失败，请稍后重试"
          end

        send(self(), {:flash, :error, error_message})
        {:noreply, socket}
    end
  end

  # 同步加载积分流水数据 - 使用Ash分页机制
  defp load_points_history_sync(socket, user_id, page) do
    page_size = socket.assigns.page_size

    case Ledger.get_user_points_history(user_id, page: page, page_size: page_size) do
      {:ok, %{results: history, count: total_count, more?: has_more}} ->
        socket
        |> assign(:points_history, history)
        |> assign(:points_history_loading, false)
        |> assign(:current_page, page)
        |> assign(:total_count, total_count)
        |> assign(:has_more, has_more)
        |> assign(:has_previous, page > 1)

      {:error, reason} ->
        Logger.error("积分流水组件: 加载积分流水失败，用户ID: #{user_id}，原因: #{inspect(reason)}")

        socket
        |> assign(:points_history, [])
        |> assign(:points_history_loading, false)
        |> assign(:current_page, page)
        |> assign(:total_count, 0)
        |> assign(:has_more, false)
        |> assign(:has_previous, false)
    end
  end

  def render(assigns) do
    ~H"""
    <div>
      <!-- 积分流水按钮 -->
      <button
        type="button"
        phx-click="show_points_history"
        phx-target={@myself}
        class="btn btn-sm btn-outline btn-info ml-2 points-history-btn"
        title="查看积分流水"
      >
        <.icon name="hero-list-bullet" class="w-4 h-4" /> 积分流水
      </button>
      
    <!-- 积分流水模态框 - 使用 Portal 脱离父容器限制 -->
      <%= if @show_modal do %>
        <div
          class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 modal-backdrop"
          phx-click="hide_points_history"
          phx-target={@myself}
        >
          <div
            class="points-history-modal-box max-w-6xl w-11/12 max-h-[90vh] overflow-hidden flex flex-col bg-base-100 text-base-content rounded-lg shadow-xl"
            phx-click="stop_propagation"
            phx-target={@myself}
          >
            <div class="flex justify-between items-center mb-4 p-4 border-b border-base-300">
              <h3 class="font-bold text-lg text-base-content">
                积分变动流水
              </h3>
              <button
                type="button"
                phx-click="hide_points_history"
                phx-target={@myself}
                class="btn btn-sm btn-circle btn-ghost"
              >
                ✕
              </button>
            </div>
            
    <!-- 用户基本信息 -->
            <%= if assigns[:user_info] do %>
              <div class="bg-base-200 p-4 rounded-lg mb-4">
                <div class="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span class="text-base-content/60">用户名:</span>
                    <span class="font-medium">{@user_info.username}</span>
                  </div>
                  <div>
                    <span class="text-base-content/60">数字ID:</span>
                    <span class="font-mono">{@user_info.numeric_id}</span>
                  </div>
                  <div>
                    <span class="text-base-content/60">当前积分:</span>
                    <span class="font-medium">
                      {@user_info.current_points}
                    </span>
                  </div>
                </div>
              </div>
            <% end %>
            
    <!-- 积分流水列表 -->
            <div class="flex-1 min-h-0 flex flex-col">
              <%= if @points_history_loading do %>
                <div class="flex justify-center items-center py-8">
                  <span class="loading loading-spinner loading-lg"></span>
                  <span class="ml-2 text-base-content">正在加载积分流水...</span>
                </div>
              <% else %>
                <%= if length(@points_history) > 0 do %>
                  <!-- 流水统计信息 -->
                  <div class="bg-base-100 p-4 rounded-lg mb-3 border">
                    <!-- 余额变化趋势 -->
                    <div class="mb-3">
                      <div class="flex items-center justify-between">
                        <h4 class="font-medium text-base-content/80 mb-2">余额变化趋势</h4>
                        <%= if length(@points_history) > 1 do %>
                          <% first_balance = List.last(@points_history).balance_after %>
                          <% last_balance = List.first(@points_history).balance_after %>
                          <% balance_change = last_balance - first_balance %>
                          <div class="text-sm">
                            <span class="text-base-content/60">期间变化: </span>
                            <span class={
                              if balance_change >= 0,
                                do: "text-success font-medium",
                                else: "text-error font-medium"
                            }>
                              {if balance_change >= 0, do: "+#{balance_change}", else: balance_change}
                            </span>
                          </div>
                        <% end %>
                      </div>
                    </div>
                    
    <!-- 统计卡片 -->
                    <div class="grid grid-cols-4 gap-4">
                      <div class="flex items-center space-x-2">
                        <.icon name="hero-arrow-trending-up" class="w-5 h-5 text-success" />
                        <div>
                          <div class="text-base-content/60">收入笔数</div>
                          <div class="font-medium text-lg">
                            {Enum.count(@points_history, fn record -> record.amount > 0 end)}
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <.icon name="hero-arrow-trending-down" class="w-5 h-5 text-error" />
                        <div>
                          <div class="text-base-content/60">支出笔数</div>
                          <div class="font-medium text-lg">
                            {Enum.count(@points_history, fn record -> record.amount < 0 end)}
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <.icon name="hero-banknotes" class="w-5 h-5 text-warning" />
                        <div>
                          <div class="text-base-content/60">总收入</div>
                          <div class="font-medium text-lg text-success">
                            +{@points_history
                            |> Enum.filter(fn r -> r.amount > 0 end)
                            |> Enum.map(& &1.amount)
                            |> Enum.sum()}
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <.icon name="hero-list-bullet" class="w-5 h-5 text-info" />
                        <div>
                          <div class="text-base-content/60">交易笔数</div>
                          <div class="font-medium text-lg">
                            {length(@points_history)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
    <!-- 表格容器 -->
                  <div class="border rounded-lg overflow-hidden flex flex-col flex-1 min-h-0">
                    <!-- 分页信息 -->
                    <div class="bg-info/10 px-3 py-2 text-sm text-info border-b flex justify-between items-center">
                      <div>
                        <.icon name="hero-list-bullet" class="w-4 h-4 inline mr-1" />
                        第 {@current_page} 页，显示 {length(@points_history)} 条记录
                        <%= if @total_count > 0 do %>
                          （共 {@total_count} 条）
                        <% end %>
                      </div>
                      <div class="flex items-center space-x-2">
                        <%= if @has_previous do %>
                          <span class="text-base-content/60">可上翻</span>
                        <% end %>
                        <%= if @has_more do %>
                          <span class="text-warning">
                            <.icon name="hero-arrow-right" class="w-3 h-3 inline mr-1" /> 还有更多
                          </span>
                        <% end %>
                      </div>
                    </div>
                    
    <!-- 表格区域 -->
                    <div class="overflow-auto flex-1 min-h-0">
                      <table class="table table-zebra w-full">
                        <thead class="bg-base-200">
                          <tr>
                            <th class="w-40">时间</th>
                            <th class="w-32">余额变化</th>
                            <th class="w-32">变动后余额</th>
                            <th class="w-28">交易类型</th>
                            <th class="flex-1 min-w-48">原因/描述</th>
                            <th class="w-20">状态</th>
                            <%= if @show_admin_actions do %>
                              <th class="w-24">操作</th>
                            <% end %>
                          </tr>
                        </thead>
                        <tbody>
                          <%= for {record, index} <- Enum.with_index(@points_history) do %>
                            <% previous_balance =
                              if index < length(@points_history) - 1 do
                                Enum.at(@points_history, index + 1).balance_after
                              else
                                record.balance_after - record.amount
                              end %>
                            <% balance_change = record.balance_after - previous_balance %>
                            <tr class="hover">
                              <td class="text-xs">
                                <div class="hidden md:block">
                                  {TimeHelper.format_local_datetime(record.timestamp)}
                                </div>
                                <div class="md:hidden">
                                  {TimeHelper.format_local_datetime(record.timestamp)
                                  |> String.split(" ")
                                  |> Enum.at(1)}
                                  <br />
                                  <span class="text-xs text-base-content/60">
                                    {TimeHelper.format_local_datetime(record.timestamp)
                                    |> String.split(" ")
                                    |> Enum.at(0)}
                                  </span>
                                </div>
                              </td>

                              <td class="font-mono">
                                <span class={
                                  if balance_change >= 0, do: "text-success", else: "text-error"
                                }>
                                  {if balance_change >= 0,
                                    do: "+#{balance_change}",
                                    else: balance_change}
                                </span>
                              </td>

                              <td class="font-mono font-medium">
                                {record.balance_after}
                              </td>
                              <td>
                                <span class={get_transaction_type_class(record.transaction_type)}>
                                  {get_transaction_type_display(record.transaction_type)}
                                </span>
                              </td>
                              <td class="text-xs">
                                {format_transaction_description(record)}
                              </td>
                              <td>
                                <span class="badge badge-success badge-xs">成功</span>
                              </td>
                              <%= if @show_admin_actions do %>
                                <td>
                                  <%= if record.status == :completed &&
                                       !Map.get(record.metadata || %{}, "is_reversed", false) do %>
                                    <button
                                      phx-click="reverse_transfer"
                                      phx-value-transfer_id={record.id}
                                      phx-target={@myself}
                                      class="btn btn-error btn-xs gap-1"
                                      data-confirm={build_reverse_confirm_message(record)}
                                      title="撤销转账及相关游戏物品"
                                    >
                                      <%!-- <.icon name="hero-arrow-uturn-left" class="w-3 h-3" /> --%>
                                      <span>撤销</span>
                                    </button>
                                  <% else %>
                                    <span class="text-base-content/40 text-xs">-</span>
                                  <% end %>
                                </td>
                              <% end %>
                            </tr>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                    
    <!-- 分页控件 -->
                    <%= if @has_previous || @has_more do %>
                      <div class="border-t p-3 flex justify-between items-center bg-base-50 flex-shrink-0">
                        <div class="text-sm text-base-content/60">
                          第 {@current_page} 页
                          <%= if @total_count > 0 do %>
                            / 共 {ceil(@total_count / @page_size)} 页
                          <% end %>
                        </div>
                        <div class="join">
                          <%= if @has_previous do %>
                            <button
                              type="button"
                              phx-click="load_page"
                              phx-value-page={@current_page - 1}
                              phx-target={@myself}
                              class="join-item btn btn-sm"
                              disabled={@points_history_loading}
                            >
                              <%= if @points_history_loading do %>
                                <span class="loading loading-spinner loading-xs"></span>
                              <% else %>
                                <.icon name="hero-chevron-left" class="w-4 h-4" /> 上一页
                              <% end %>
                            </button>
                          <% end %>
                          
    <!-- 当前页码显示 -->
                          <div class="join-item btn btn-sm btn-active">
                            {@current_page}
                          </div>

                          <%= if @has_more do %>
                            <button
                              type="button"
                              phx-click="load_page"
                              phx-value-page={@current_page + 1}
                              phx-target={@myself}
                              class="join-item btn btn-sm"
                              disabled={@points_history_loading}
                            >
                              <%= if @points_history_loading do %>
                                <span class="loading loading-spinner loading-xs"></span>
                              <% else %>
                                下一页 <.icon name="hero-chevron-right" class="w-4 h-4" />
                              <% end %>
                            </button>
                          <% end %>
                        </div>
                      </div>
                    <% else %>
                      <%= if length(@points_history) > 0 do %>
                        <div class="border-t p-2 text-center text-xs text-base-content/60">
                          已显示全部记录（共 {@total_count} 条）
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                <% else %>
                  <div class="text-center py-12">
                    <div class="text-6xl mb-4">📊</div>
                    <h3 class="text-lg font-medium text-base-content/80 mb-2">暂无积分流水记录</h3>
                    <p class="text-base-content/60">当您进行积分相关操作时，记录将显示在这里</p>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # 获取交易类型显示文本
  defp get_transaction_type_display(type) do
    # 使用集中管理的交易类型描述
    Cypridina.Types.TransactionType.description(type)
  end

  # 获取交易类型样式类
  defp get_transaction_type_class(type) do
    base_class = "badge badge-xs"

    # 根据交易类型分类设置不同的颜色
    color_class =
      case Cypridina.Types.TransactionType.category(type) do
        # 游戏相关 - 黄色
        :game ->
          "badge-warning"

        # 股票相关 - 蓝色
        :stock ->
          "badge-info"

        # 管理员操作 - 主色
        :admin ->
          "badge-primary"

        :base ->
          case type do
            # 退款 - 红色
            :refund -> "badge-error"
            # 转账 - 中性色
            :transfer -> "badge-neutral"
            # 佣金 - 强调色
            :commission -> "badge-accent"
            # 系统调整 - 主色
            :system_adjust -> "badge-primary"
            # 其他基础类型 - 透明
            _ -> "badge-ghost"
          end

        # 未知类型 - 透明
        _ ->
          "badge-ghost"
      end

    "#{base_class} #{color_class}"
  end

  # 格式化交易描述，包含详细的股票信息
  defp format_transaction_description(record) do
    base_description = record.description || "无描述"
    metadata = record.metadata || %{}

    case record.transaction_type do
      :buy_stock ->
        format_stock_transaction_description(base_description, metadata, "买入")

      :sell_stock ->
        format_stock_transaction_description(base_description, metadata, "卖出")

      :commission ->
        if Map.get(metadata, "commission_type") == "stock" do
          format_commission_description(base_description, metadata)
        else
          base_description
        end

      :liquidate_all_stocks ->
        format_liquidation_description(base_description, metadata)

      _ ->
        base_description
    end
  end

  # 格式化股票交易描述
  defp format_stock_transaction_description(base_description, metadata, action_type) do
    racer_name = Map.get(metadata, "racer_name", "未知")
    quantity = Map.get(metadata, "quantity", 0)
    stock_price = Map.get(metadata, "stock_price", 0)

    if quantity > 0 and stock_price > 0 do
      "#{action_type}股票: #{racer_name} #{quantity}股@#{stock_price}"
    else
      base_description
    end
  end

  # 格式化佣金描述
  defp format_commission_description(base_description, metadata) do
    racer_name = Map.get(metadata, "racer_name", "未知")
    quantity = Map.get(metadata, "quantity", 0)
    stock_price = Map.get(metadata, "stock_price", 0)
    commission_rate = Map.get(metadata, "commission_rate", 0)

    if quantity > 0 and stock_price > 0 do
      "股票抽水: #{racer_name} #{quantity}股@#{stock_price} (#{commission_rate}%)"
    else
      base_description
    end
  end

  # 格式化清仓描述
  defp format_liquidation_description(base_description, metadata) do
    liquidated_stocks = Map.get(metadata, "liquidated_stocks", [])

    if is_list(liquidated_stocks) and length(liquidated_stocks) > 0 do
      stock_details =
        Enum.map_join(liquidated_stocks, "，", fn stock ->
          racer_name = get_racer_name_by_id(Map.get(stock, "racer_id", ""))
          quantity = Map.get(stock, "quantity", 0)
          avg_price = Map.get(stock, "average_price", 0)
          "#{racer_name}#{quantity}股@#{avg_price}"
        end)

      "清仓股票: #{stock_details}"
    else
      base_description
    end
  end

  # 根据动物ID获取名称
  defp get_racer_name_by_id(racer_id) do
    case racer_id do
      "A" -> "饿小宝"
      "B" -> "盒马"
      "C" -> "票票"
      "D" -> "虾仔"
      "E" -> "支小宝"
      "F" -> "欢猩"
      _ -> racer_id
    end
  end

  # 构建撤销确认消息
  defp build_reverse_confirm_message(record) do
    "确定要撤销这笔转账吗？\n\n" <>
      "转账ID: #{record.id}\n" <>
      "金额: #{record.amount}\n" <>
      "时间: #{TimeHelper.format_local_datetime(record.timestamp)}\n" <>
      "描述: #{record.description || "无描述"}\n\n" <>
      "撤销后将自动处理相关的游戏物品。"
  end

  # 构建撤销成功消息
  defp build_reverse_success_message(result) do
    base_message = "转账撤销成功"

    case result do
      %{reversed_items: items} when is_list(items) and length(items) > 0 ->
        items_summary =
          Enum.map_join(items, ", ", fn item ->
            case item do
              %{type: :stock, racer_id: racer_id, quantity: quantity} ->
                "股票#{racer_id}(#{quantity}股)"

              %{type: :bet} ->
                "游戏下注"

              _ ->
                "游戏物品"
            end
          end)

        "#{base_message}，同时撤销了相关物品：#{items_summary}"

      _ ->
        base_message
    end
  end
end
