defmodule Cy<PERSON><PERSON>inaWeb.Admin.DashboardLive do
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :live_view
  alias <PERSON>.Accounts
  alias Teen.Games
  alias Teen.Payments

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 设置定时器，每30秒更新一次数据
      :timer.send_interval(30_000, self(), :update_stats)
    end

    {:ok,
     socket
     |> assign(:page_title, "仪表盘")
     |> assign(:current_url, "/admin/dashboard")
     |> assign(:loading, true)
     |> assign(:selected_period, "today")
     |> load_dashboard_data()}
  end

  @impl true
  def handle_info(:update_stats, socket) do
    {:noreply, load_dashboard_data(socket)}
  end

  @impl true
  def handle_event("change_period", %{"period" => period}, socket) do
    {:noreply,
     socket
     |> assign(:selected_period, period)
     |> assign(:loading, true)
     |> load_dashboard_data()}
  end

  @impl true
  def handle_event("refresh_data", _params, socket) do
    {:noreply,
     socket
     |> assign(:loading, true)
     |> load_dashboard_data()}
  end

  defp load_dashboard_data(socket) do
    period = socket.assigns[:selected_period] || "today"

    socket
    |> assign(:stats, get_dashboard_stats(period))
    |> assign(:charts_data, get_charts_data(period))
    |> assign(:recent_activities, get_recent_activities())
    |> assign(:system_status, get_system_status())
    |> assign(:top_games, get_top_games(period))
    |> assign(:revenue_breakdown, get_revenue_breakdown(period))
    |> assign(:loading, false)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="admin-main-content p-4 lg:p-6 space-y-6">
      <!-- 页面头部 -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold text-base-content flex items-center gap-3">
            <.icon name="hero-chart-bar-square" class="size-8 text-primary" />
            Teen Patti 仪表盘
          </h1>
          <p class="text-base-content/60 mt-1">实时监控系统运营状况</p>
        </div>

        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <!-- 时间段选择 -->
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-base-content/80">时间段:</span>
            <select
              class="select select-sm select-bordered bg-base-100"
              phx-change="change_period"
              name="period"
            >
              <option value="today" selected={@selected_period == "today"}>今日</option>
              <option value="week" selected={@selected_period == "week"}>本周</option>
              <option value="month" selected={@selected_period == "month"}>本月</option>
              <option value="year" selected={@selected_period == "year"}>本年</option>
            </select>
          </div>

          <!-- 刷新按钮 -->
          <button
            class="btn btn-sm btn-primary gap-2"
            phx-click="refresh_data"
            disabled={@loading}
          >
            <.icon name="hero-arrow-path" class={["size-4", @loading && "animate-spin"]} />
            刷新数据
          </button>

          <!-- 系统状态指示器 -->
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span class="text-xs text-base-content/60">系统正常</span>
            <div class="text-xs text-base-content/40">
              <%= DateTime.now!("Asia/Shanghai") |> Calendar.strftime("%H:%M:%S") %>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <%= if @loading do %>
        <div class="flex items-center justify-center py-12">
          <div class="loading loading-spinner loading-lg text-primary"></div>
          <span class="ml-3 text-base-content/60">正在加载数据...</span>
        </div>
      <% else %>
        <!-- 核心统计卡片 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          <div class="stat-card bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <div class="stat-figure text-primary">
              <.icon name="hero-users" class="size-8" />
            </div>
            <div class="stat-title text-primary/80">总用户数</div>
            <div class="stat-value text-primary text-2xl lg:text-3xl">
              <%= format_number(@stats.total_users) %>
            </div>
            <div class="stat-desc text-primary/60 flex items-center gap-1">
              <.icon name="hero-arrow-trending-up" class="size-3" />
              今日新增 <%= @stats.new_users_today %>
            </div>
          </div>

          <div class="stat-card bg-gradient-to-br from-success/10 to-success/5 border border-success/20">
            <div class="stat-figure text-success">
              <.icon name="hero-banknotes" class="size-8" />
            </div>
            <div class="stat-title text-success/80">
              <%= period_label(@selected_period) %>收入
            </div>
            <div class="stat-value text-success text-2xl lg:text-3xl">
              ¥<%= format_currency(@stats.revenue) %>
            </div>
            <div class="stat-desc text-success/60 flex items-center gap-1">
              <.icon name={trend_icon(@stats.revenue_trend)} class="size-3" />
              较上期 <%= format_percentage(@stats.revenue_change) %>
            </div>
          </div>

          <div class="stat-card bg-gradient-to-br from-warning/10 to-warning/5 border border-warning/20">
            <div class="stat-figure text-warning">
              <.icon name="hero-puzzle-piece" class="size-8" />
            </div>
            <div class="stat-title text-warning/80">活跃游戏</div>
            <div class="stat-value text-warning text-2xl lg:text-3xl">
              <%= @stats.active_games %>
            </div>
            <div class="stat-desc text-warning/60">
              <%= @stats.total_game_sessions %> 场游戏进行中
            </div>
          </div>

          <div class="stat-card bg-gradient-to-br from-info/10 to-info/5 border border-info/20">
            <div class="stat-figure text-info">
              <div class="avatar online placeholder">
                <div class="bg-info text-info-content rounded-full w-12 h-12">
                  <span class="text-lg font-bold"><%= @stats.online_users %></span>
                </div>
              </div>
            </div>
            <div class="stat-title text-info/80">在线用户</div>
            <div class="stat-value text-info text-2xl lg:text-3xl">
              <%= @stats.online_users %>
            </div>
            <div class="stat-desc text-info/60">
              峰值: <%= @stats.peak_online_users %>
            </div>
          </div>
        </div>

        <!-- 图表和详细数据区域 -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <!-- 收入趋势图 -->
          <div class="xl:col-span-2">
            <div class="card bg-base-100 shadow-xl border border-base-300">
              <div class="card-body">
                <h2 class="card-title flex items-center gap-2">
                  <.icon name="hero-chart-bar" class="size-5 text-primary" />
                  收入趋势
                </h2>
                <div class="h-64 flex items-center justify-center bg-base-200 rounded-lg">
                  <div class="text-center">
                    <.icon name="hero-chart-bar" class="size-12 text-base-content/30 mx-auto mb-2" />
                    <p class="text-base-content/60">图表组件待集成</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 热门游戏排行 -->
          <div>
            <div class="card bg-base-100 shadow-xl border border-base-300">
              <div class="card-body">
                <h2 class="card-title flex items-center gap-2">
                  <.icon name="hero-fire" class="size-5 text-error" />
                  热门游戏
                </h2>
                <div class="space-y-3">
                  <%= for {game, index} <- Enum.with_index(@top_games, 1) do %>
                    <div class="flex items-center justify-between p-3 bg-base-200 rounded-lg hover:bg-base-300 transition-colors">
                      <div class="flex items-center gap-3">
                        <div class={[
                          "badge badge-sm",
                          index == 1 && "badge-warning",
                          index == 2 && "badge-neutral",
                          index == 3 && "badge-accent",
                          index > 3 && "badge-ghost"
                        ]}>
                          <%= index %>
                        </div>
                        <div>
                          <div class="font-medium text-sm"><%= game.name %></div>
                          <div class="text-xs text-base-content/60"><%= game.players %> 玩家</div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div class="text-sm font-medium">¥<%= format_currency(game.revenue) %></div>
                        <div class="text-xs text-base-content/60"><%= game.sessions %> 场</div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作和系统状态 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 快速操作面板 -->
          <div class="card bg-base-100 shadow-xl border border-base-300">
            <div class="card-body">
              <h2 class="card-title flex items-center gap-2">
                <.icon name="hero-bolt" class="size-5 text-primary" />
                快速操作
              </h2>
              <div class="grid grid-cols-2 gap-3 mt-4">
                <.link navigate="/admin/users" class="btn btn-outline btn-primary btn-sm gap-2">
                  <.icon name="hero-user-plus" class="size-4" />
                  用户管理
                </.link>
                <.link navigate="/admin/games" class="btn btn-outline btn-secondary btn-sm gap-2">
                  <.icon name="hero-puzzle-piece" class="size-4" />
                  游戏管理
                </.link>
                <.link navigate="/admin/payment-orders" class="btn btn-outline btn-success btn-sm gap-2">
                  <.icon name="hero-banknotes" class="size-4" />
                  订单管理
                </.link>
                <.link navigate="/admin/system-config" class="btn btn-outline btn-warning btn-sm gap-2">
                  <.icon name="hero-cog-6-tooth" class="size-4" />
                  系统设置
                </.link>
                <.link navigate="/admin/daily-game-tasks" class="btn btn-outline btn-info btn-sm gap-2">
                  <.icon name="hero-calendar-days" class="size-4" />
                  活动管理
                </.link>
                <.link navigate="/admin/logs" class="btn btn-outline btn-neutral btn-sm gap-2">
                  <.icon name="hero-document-text" class="size-4" />
                  系统日志
                </.link>
              </div>
            </div>
          </div>

          <!-- 系统状态监控 -->
          <div class="card bg-base-100 shadow-xl border border-base-300">
            <div class="card-body">
              <h2 class="card-title flex items-center gap-2">
                <.icon name="hero-cpu-chip" class="size-5 text-info" />
                系统状态
              </h2>
              <div class="space-y-3 mt-4">
                <%= for status <- @system_status do %>
                  <div class={[
                    "alert",
                    status.level == "success" && "alert-success",
                    status.level == "warning" && "alert-warning",
                    status.level == "error" && "alert-error",
                    status.level == "info" && "alert-info"
                  ]}>
                    <.icon name={status.icon} class="size-4" />
                    <div class="flex-1">
                      <div class="font-medium text-sm"><%= status.title %></div>
                      <div class="text-xs opacity-80"><%= status.description %></div>
                    </div>
                    <div class="text-xs"><%= status.value %></div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # 辅助函数
  defp get_dashboard_stats(period) do
    # 这里应该从数据库获取真实数据
    # 目前使用模拟数据
    base_stats = %{
      total_users: 15_234,
      new_users_today: 45,
      revenue: get_revenue_by_period(period),
      revenue_trend: :up,
      revenue_change: 12.5,
      active_games: 12,
      total_game_sessions: 156,
      online_users: 234,
      peak_online_users: 456
    }

    # 根据时间段调整数据
    case period do
      "today" -> base_stats
      "week" -> %{base_stats | revenue: base_stats.revenue * 7, new_users_today: 312}
      "month" -> %{base_stats | revenue: base_stats.revenue * 30, new_users_today: 1_245}
      "year" -> %{base_stats | revenue: base_stats.revenue * 365, new_users_today: 15_234}
    end
  end

  defp get_revenue_by_period("today"), do: 45_678
  defp get_revenue_by_period("week"), do: 298_456
  defp get_revenue_by_period("month"), do: 1_234_567
  defp get_revenue_by_period("year"), do: 12_345_678

  defp get_charts_data(_period) do
    # 图表数据，后续可以集成 ApexCharts 或其他图表库
    %{
      revenue_trend: [],
      user_growth: [],
      game_popularity: []
    }
  end

  defp get_recent_activities do
    now = DateTime.now!("Asia/Shanghai")

    [
      %{
        timestamp: DateTime.add(now, -5, :minute),
        user: "user123",
        action: "充值",
        status: "success",
        status_text: "成功"
      },
      %{
        timestamp: DateTime.add(now, -8, :minute),
        user: "user456",
        action: "注册",
        status: "success",
        status_text: "完成"
      },
      %{
        timestamp: DateTime.add(now, -12, :minute),
        user: "user789",
        action: "游戏",
        status: "pending",
        status_text: "进行中"
      },
      %{
        timestamp: DateTime.add(now, -15, :minute),
        user: "user101",
        action: "提现",
        status: "pending",
        status_text: "审核中"
      },
      %{
        timestamp: DateTime.add(now, -20, :minute),
        user: "user202",
        action: "充值",
        status: "failed",
        status_text: "失败"
      }
    ]
  end

  defp get_system_status do
    [
      %{
        title: "服务器状态",
        description: "CPU使用率",
        value: "68%",
        level: "success",
        icon: "hero-cpu-chip"
      },
      %{
        title: "数据库连接",
        description: "连接池状态",
        value: "正常",
        level: "success",
        icon: "hero-database"
      },
      %{
        title: "内存使用",
        description: "系统内存",
        value: "78%",
        level: "warning",
        icon: "hero-server"
      },
      %{
        title: "网络状态",
        description: "网络延迟",
        value: "12ms",
        level: "success",
        icon: "hero-signal"
      }
    ]
  end

  defp get_top_games(_period) do
    [
      %{name: "Teen Patti Classic", players: 1234, revenue: 45_678, sessions: 567},
      %{name: "Teen Patti Gold", players: 987, revenue: 34_567, sessions: 432},
      %{name: "Teen Patti Royal", players: 765, revenue: 23_456, sessions: 321},
      %{name: "Teen Patti VIP", players: 543, revenue: 12_345, sessions: 234},
      %{name: "Teen Patti Pro", players: 321, revenue: 8_765, sessions: 156}
    ]
  end

  defp get_revenue_breakdown(_period) do
    [
      %{category: "游戏收入", amount: 234_567, change: 15.2},
      %{category: "充值收入", amount: 123_456, change: 8.7},
      %{category: "VIP收入", amount: 67_890, change: -2.3},
      %{category: "活动收入", amount: 45_678, change: 23.1}
    ]
  end

  # 格式化辅助函数
  defp format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  defp format_currency(amount) when is_integer(amount) do
    amount
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  defp format_percentage(percentage) when is_float(percentage) do
    sign = if percentage >= 0, do: "+", else: ""
    "#{sign}#{:erlang.float_to_binary(percentage, [decimals: 1])}%"
  end

  defp period_label("today"), do: "今日"
  defp period_label("week"), do: "本周"
  defp period_label("month"), do: "本月"
  defp period_label("year"), do: "本年"
  defp period_label(_), do: ""

  defp trend_icon(:up), do: "hero-arrow-trending-up"
  defp trend_icon(:down), do: "hero-arrow-trending-down"
  defp trend_icon(_), do: "hero-minus"
end
