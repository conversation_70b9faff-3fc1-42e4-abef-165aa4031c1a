# Cypridina

## 快速开始

### 同步规则集
mix usage_rules.sync .augment-guidelines --all \
  --link-to-folder deps \
  --builtins elixir,otp

### 使用 Makefile (推荐)

项目提供了便捷的 Makefile 命令来管理开发流程：

```bash
# 初始设置
make setup

# 启动服务器 (自动加载 .env 环境变量)
make server    # 带交互式 shell
make dev       # 纯服务器模式

# 查看所有可用命令
make help
```

### 环境变量配置

项目使用 `.env` 文件来管理环境变量。启动服务器时会自动加载这些变量：

```bash
# .env 文件示例
MIX_ENV="dev"
DATABASE_URL="ecto://postgres:username@localhost/cypridina"
SECRET_KEY_BASE="your-secret-key"
PORT=4000
# ... 其他配置
```

### 传统方式启动

如果不使用 Makefile，也可以传统方式启动：

* Run `mix setup` to install and setup dependencies
* Start Phoenix endpoint with `mix phx.server` or inside IEx with `iex -S mix phx.server`

Now you can visit [`localhost:4000`](http://localhost:4000) from your browser.

Ready to run in production? Please [check our deployment guides](https://hexdocs.pm/phoenix/deployment.html).

## Makefile 命令参考

项目提供了丰富的 Makefile 命令来简化开发流程：

### 🔧 开发和运行
- `make server` - 启动完整的 Phoenix 服务器 (自动加载.env环境变量)
- `make dev` - 启动开发模式 Phoenix 服务器 (自动加载.env环境变量)

### 🛑 停止和重启服务
- `make stop` - 优雅停止 Phoenix 服务
- `make force-stop` - 强制停止所有相关进程
- `make restart` - 重启服务 (优雅停止后重新启动)
- `make force-restart` - 强制重启服务 (强制停止后重新启动)
- `make status` - 检查服务运行状态和资源使用情况

### 🔧 项目管理
- `make setup` - 初始项目设置 (依赖、数据库等)
- `make reset` - 重置数据库
- `make test` - 运行测试

### 📝 日志管理
- `make logs` - 创建日志目录
- `make logs-view` - 查看各种日志文件
- `make logs-clean` - 清理日志文件
- `make logcat 22` - 跟踪指定游戏的日志 (实时)
- `make loglist` - 列出所有游戏日志
- `make logshow 22` - 显示指定游戏的最新日志

### 💡 常用组合
```bash
make setup && make server    # 完整设置并启动服务
make restart                 # 重启开发服务
make status                  # 检查当前状态
make force-restart           # 解决进程卡死问题
```

## Learn more

* Official website: https://www.phoenixframework.org/
* Guides: https://hexdocs.pm/phoenix/overview.html
* Docs: https://hexdocs.pm/phoenix
* Forum: https://elixirforum.com/c/phoenix-forum
* Source: https://github.com/phoenixframework/phoenix


<!-- 参考https://hexdocs.pm/phoenix/up_and_running.html -->

<!-- 在本地安装hex包管理器 -->
###=# 一些常用的mix命令
HEX_MIRROR=http://hexpm.upyun.com mix local.hex
<!-- 在本地安装erlang用的工具rebar -->
HEX_MIRROR=http://hexpm.upyun.com mix local.rebar
 <!--安装依赖  -->
HEX_MIRROR=http://hexpm.upyun.com mix deps.get


#### 初始化项目
ash mix任务见https://hexdocs.pm/ash/Mix.Tasks.Ash.Codegen.html

```bash
<!-- 强制清库 -->
mix ecto.drop  --force-drop
<!-- 根据ash的资源声明，生成迁移文件 -->
mix ash.codegen <xxxx-随便起个名>
<!-- 初始化项目 -->
mix setup
```

#### 启动服务
```bash
<!-- 赋名式启动 -->
MIX_ENV=dev elixir --sname dev_server -S mix phx.server
<!-- 连接到上述节点 -->
iex --remsh dev_server --sname dev

<!-- 正式服 -->
MIX_ENV=prod PORT=4001 elixir --sname prod_server -S mix phx.server
iex --remsh prod_server --sname dev
```