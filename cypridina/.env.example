MIX_ENV="dev"
# 使用16个进程编译
MIX_OS_DEPS_COMPILE_PARTITION_COUNT=16
# 项目模式配置
# 可选值: teen | race | self
# teen: Teen项目模式 (印度游戏平台)
# race: Race项目模式 (赛马游戏平台)
# self: 自用模式 (个人开发测试)
PROJECT_MODE="teen"

# 数据库链接
DATABASE_URL="ecto://postgres:zhp19940805@postgres/cypridina_zhp"
# Minio配置
MINIO_HOST="minio:9000"
MINIO_BUCKET="cypridina"
MINIO_ROOT_USER="minio"
MINIO_ROOT_PASSWORD="zhp19940805"
# Phoenix 配置
SECRET_KEY_BASE="WlYBmT9t/xowdqF0srgFeOLybsyAGGS6Z4ItDnBKH9mPPpGTLJJv2FcX+m2flbjm"
TOKEN_SIGNING_SECRET="TeRJ2hFegTbByzX2H4hQ/60/v/ejitQZ"
PORT=4000
PHX_HOST="*************"
BASE_URL="http://*************:4001"

# 游戏配置
RACING_GAME_URL=http://*************:5000

# 短信配置
SMS_APP_KEY=CpBFSvjCWjU8eGM1ln
SMS_SECRET_KEY=jNErzLjqs8

# 支付配置
PAYMENT_GATEWAY_URL=https://api.masterpay88.in/app-api
PAYMENT_MERCHANT_ID=10236
PAYMENT_SECRET_KEY=ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM
PAYMENT_CREATE_URL=https://api.masterpay88.in/app-api/v1.0/api/order/create
PAYMENT_QUERY_URL=https://api.masterpay88.in/app-api/v1.0/api/order/query
PAYMENT_NOTIFY_URL=http://localhost:4000/api/payment/callback
PAYMENT_RETURN_URL=http://localhost:4000/payment/success

# 支付通道配置
RECHARGE_CHANNEL_ID=3021
WITHDRAW_CHANNEL_ID=3020
